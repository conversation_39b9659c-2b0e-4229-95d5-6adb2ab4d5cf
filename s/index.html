<!DOCTYPE html>
<!--[if IE 8]> <html class="ie ie8"> <![endif]-->
<!--[if IE 9]> <html class="ie ie9"> <![endif]-->
<!--[if gt IE 9]><!-->
<html>
<!--<![endif]-->
<head>
<meta charset="utf-8">
<title>MOBD 车况检测大师官方网站 | APP下载</title>
<meta name="author" content="mobd.cn">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
<link rel="stylesheet" href="../css/bootstrap.css">
<link rel="stylesheet" href="../css/animations.css" media="screen">
<link rel="stylesheet" href="../css/superfish.css" media="screen">
<link rel="stylesheet" href="../css/style.css">
<link rel="stylesheet" href="../css/colors/blue.css" id="colors">
<link rel="stylesheet" href="../css/theme-responsive.css">
<link rel="shortcut icon" href="../img/ico/favicon.ico">
<link rel="apple-touch-icon" href="../img/ico/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/ico/apple-touch-icon-72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/ico/apple-touch-icon-114.png">
<link rel="apple-touch-icon" sizes="144x144" href="../img/ico/apple-touch-icon-144.png">
</head>
<body class="down">
<div class="wrap"> 
  <!-- Header Start -->
  <header id="header"> 
    <!-- Main Header Start -->
    <div class="main-header">
      <div class="container"> 
        <!-- Logo Start -->
        <div class="logo pull-left">
          <h1> <a href="/index.html"> <img src="../img/logo.png" alt="pixma" width="125" height="60"> </a> </h1>
        </div>
        <!-- Logo End --> 

      </div>
    </div>
    <!-- Main Header End --> 
  </header>
  <!-- Header End --> 
  <!-- Content Start -->
  <div id="main" align="center"  style="min-height:400px;"> 
    <!-- Main Content start-->
<p>&nbsp;</p>
<p>&nbsp;</p>
	<script type="text/javascript">
var browser = {
versions: function () {
var u = navigator.userAgent, app = navigator.appVersion;
return { //移动终端浏览器版本信息 
ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), //ios终端 
android: u.indexOf('Android') > -1 || u.indexOf('Linux') > -1, //android终端或uc浏览器 
iPhone: u.indexOf('iPhone') > -1, //是否为iPhone或者QQHD浏览器 
iPad: u.indexOf('iPad') > -1, //是否iPad 
};
}(),
}
if (browser.versions.iPhone || browser.versions.iPad || browser.versions.ios) {
		window.location.href = "http://www.geekobd.com/down/geekobd.pdf"; 

}
else if (browser.versions.android) {

    if(isWeiXin())
	{
		document.write("<h2>用户操作指南下载<h2><h3>微信内不能进行下载<br>请点微信右上角按钮<br>选择[在浏览器打开]</h3>");
    }
	else
	{
		window.location.href = "http://www.geekobd.com/down/geekobd.pdf";
	}
}
else {
window.location.href = "http://www.geekobd.com/down/geekobd.pdf";
//document.write("<h1>车况检测大师</br></h1><a href='http://www.mobd.cn/download/UserGuide_iOS_v5.1.pdf'><H3><U>下载iOS V5.1 用户操作指南</U></H3></a><a href='http://www.mobd.cn/download/vehiclemgr_4.3_UserManual.pdf'><H3><U>下载Android V4.3用户操作指南</U></H3></a>");
}

	
function isWeiXin(){
    var ua = window.navigator.userAgent.toLowerCase();
    if(ua.match(/MicroMessenger/i) == 'micromessenger'){
        return true;
    }else{
        return false;
    }
}
</script>



    <!-- Main Content end--> 
  </div>
  <!-- Content End --> 
  <!-- Footer Start -->
  <footer id="footer"> 
    <!-- Footer Bottom Start -->
    <div class="footer-bottom">
      <div class="container">
        <div class="row">
          <div class="col-lg-6 col-md-6 col-xs-12 col-sm-6 ">Copyright &copy; 2015 MOBD All Right Reserved. 
		  <script type="text/javascript">var cnzz_protocol = (("https:" == document.location.protocol) ? " https://" : " http://");document.write(unescape("%3Cspan id='cnzz_stat_icon_1253146380'%3E%3C/span%3E%3Cscript src='" + cnzz_protocol + "s13.cnzz.com/z_stat.php%3Fid%3D1253146380%26show%3Dpic1' type='text/javascript'%3E%3C/script%3E"));</script>
		  </div>
          <div class="col-lg-6 col-md-6 col-xs-12 col-sm-6 "> </div>
        </div>
      </div>
    </div>
    <!-- Footer Bottom End --> 
  </footer>
  <!-- Scroll To Top --> 
 <div> 
 		<a href="#" class="scrollup"><i class="icon-angle-up"></i></a>
 </div>
<!-- Wrap End --> 
<script src="../js/jquery.min.js"></script> 
<script src="../js/bootstrap.js"></script> 
<script src="../js/jquery.parallax.js"></script> 
<script src="../js/revolution-slider/js/jquery.themepunch.revolution.min.js"></script> 
<script src="../js/jquery.prettyPhoto.js"></script> 
<script src="../js/superfish.js"></script> 
<script src="../js/jquery.sticky.js"></script> 
<script src="../js/jflickrfeed.js"></script> 
<script src="../js/imagesloaded.pkgd.min.js"></script> 
<script src="../js/custom.js"></script>
</body>
</html>


