/* SEO Enhancement Styles - Custom styles for optimized content */

/* Main heading styles for SEO optimized pages */
.content h1,
main h1,
section h1 {
    font-size: 28px;
    font-weight: 600;
    color: #333;
    margin: 20px 0 25px 0;
    line-height: 1.3;
    text-align: center;
}

/* Secondary headings */
.content h2,
main h2,
section h2,
.post-content h2 {
    font-size: 22px;
    font-weight: 500;
    color: #4bc1d2;
    margin: 25px 0 15px 0;
    line-height: 1.4;
    border-bottom: 2px solid #e8f4f8;
    padding-bottom: 8px;
}

/* Third level headings */
.content h3,
main h3,
section h3,
.post-content h3 {
    font-size: 18px;
    font-weight: 500;
    color: #555;
    margin: 20px 0 12px 0;
    line-height: 1.4;
}

/* FAQ Section Styles */
.faq-section {
    background: #f8f9fa;
    padding: 60px 0;
    margin-top: 40px;
}

.faq-section h2 {
    text-align: center;
    margin-bottom: 40px;
    font-size: 26px;
    color: #333;
    font-weight: 600;
}

.faq-content {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    margin-bottom: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: box-shadow 0.3s ease;
}

.faq-item:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.faq-item h3 {
    color: #333;
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: 600;
    line-height: 1.4;
}

.faq-item p {
    color: #666;
    line-height: 1.6;
    margin: 0;
}

/* Product page specific styles */
.product-info h1 {
    font-size: 24px;
    font-weight: 600;
    text-align: center;
    margin-bottom: 10px;
    color: #333;
}

/* Hardware page title styles */
.hardware .title,
.hardware h1.title {
    font-size: 32px;
    font-weight: 600;
    color: #fff;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    margin-bottom: 20px;
}

.hardware h2.title {
    font-size: 24px;
    font-weight: 500;
    color: #fff;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    margin-bottom: 15px;
}

.hardware h3.title {
    font-size: 20px;
    font-weight: 500;
    color: #fff;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    margin-bottom: 12px;
}

/* About page specific styles */
.about .title,
.about h1.title {
    font-size: 30px;
    font-weight: 600;
    color: #333;
    text-align: center;
    margin-bottom: 30px;
}

/* Content sections with better spacing */
.post-content ul {
    margin: 15px 0;
    padding-left: 20px;
}

.post-content ul li {
    margin-bottom: 8px;
    line-height: 1.6;
}

.post-content ul li strong {
    color: #4bc1d2;
    font-weight: 600;
}

/* Improved info sections */
.info {
    font-size: 16px;
    line-height: 1.7;
    color: #666;
}

.info h2,
.info h3,
.info h4 {
    margin-top: 25px;
    margin-bottom: 15px;
}

.info ul {
    margin: 15px auto;
    max-width: 600px;
    text-align: left;
}

.info ul li {
    margin-bottom: 10px;
    padding-left: 5px;
}

/* Screen reader only class for accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0,0,0,0);
    white-space: nowrap;
    border: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .content h1,
    main h1,
    section h1 {
        font-size: 24px;
    }
    
    .content h2,
    main h2,
    section h2,
    .post-content h2 {
        font-size: 20px;
    }
    
    .hardware .title,
    .hardware h1.title {
        font-size: 26px;
    }
    
    .faq-section {
        padding: 40px 0;
    }
    
    .faq-item {
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .info ul {
        max-width: 100%;
        padding-left: 15px;
    }
}

/* Buy page specific styles */
.buy-button {
    background: linear-gradient(135deg, #4bc1d2 0%, #3a9fb0 100%);
    border: none;
    transition: all 0.3s ease;
}

.buy-button:hover {
    background: linear-gradient(135deg, #3a9fb0 0%, #2d7a87 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Enhanced product sections */
.product-details article {
    margin-bottom: 30px;
}

.product-details h3.title {
    color: #333;
    font-size: 22px;
    margin-bottom: 15px;
    text-align: center;
}

/* Color-coded sections for hardware pages */
.col-xs-12.white h1,
.col-xs-12.white h2,
.col-xs-12.white h3,
.col-xs-12.white h4,
.col-xs-12.white p,
.col-xs-12.white .info {
    color: #333 !important;
    text-shadow: none;
}

.col-xs-12.gold h1,
.col-xs-12.gold h2,
.col-xs-12.gold h3,
.col-xs-12.gold h4,
.col-xs-12.gold p,
.col-xs-12.gold .info {
    color: #fff !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.col-xs-12.silver h1,
.col-xs-12.silver h2,
.col-xs-12.silver h3,
.col-xs-12.silver h4,
.col-xs-12.silver p,
.col-xs-12.silver .info {
    color: #333 !important;
    text-shadow: none;
}

.col-xs-12.black h1,
.col-xs-12.black h2,
.col-xs-12.black h3,
.col-xs-12.black h4,
.col-xs-12.black p,
.col-xs-12.black .info {
    color: #fff !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
}

.col-xs-12.silver2 h1,
.col-xs-12.silver2 h2,
.col-xs-12.silver2 h3,
.col-xs-12.silver2 h4,
.col-xs-12.silver2 p,
.col-xs-12.silver2 .info {
    color: #333 !important;
    text-shadow: none;
}

.col-xs-12.gold2 h1,
.col-xs-12.gold2 h2,
.col-xs-12.gold2 h3,
.col-xs-12.gold2 h4,
.col-xs-12.gold2 p,
.col-xs-12.gold2 .info {
    color: #fff !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.col-xs-12.white2 h1,
.col-xs-12.white2 h2,
.col-xs-12.white2 h3,
.col-xs-12.white2 h4,
.col-xs-12.white2 p,
.col-xs-12.white2 .info {
    color: #333 !important;
    text-shadow: none;
}

/* Enhanced text contrast for better readability */
.white .title,
.white2 .title,
.silver .title,
.silver2 .title {
    color: #222 !important;
    font-weight: 600;
}

.gold .title,
.gold2 .title,
.black .title {
    color: #fff !important;
    font-weight: 600;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
}

/* Specific fixes for light backgrounds */
.white .portfolio-desc .title,
.white2 .portfolio-desc .title,
.silver .portfolio-desc .title,
.silver2 .portfolio-desc .title {
    color: #222 !important;
}

.white .portfolio-desc .title2,
.white2 .portfolio-desc .title2,
.silver .portfolio-desc .title2,
.silver2 .portfolio-desc .title2 {
    color: #333 !important;
}

/* Ensure good contrast for all text elements */
.white ul li,
.white2 ul li,
.silver ul li,
.silver2 ul li {
    color: #444 !important;
}

.gold ul li,
.gold2 ul li,
.black ul li {
    color: #fff !important;
}

/* Additional text contrast improvements */
.container .info {
    line-height: 1.6em;
}

.white .container .info,
.white2 .container .info,
.silver .container .info,
.silver2 .container .info {
    color: #333 !important;
    font-weight: 500;
}

.gold .container .info,
.gold2 .container .info,
.black .container .info {
    color: #fff !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.6);
    font-weight: 500;
}

/* Improve readability for small text */
.white small,
.white2 small,
.silver small,
.silver2 small {
    color: #666 !important;
}

.gold small,
.gold2 small,
.black small {
    color: #f0f0f0 !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

/* Button text contrast */
.white .btn,
.white2 .btn,
.silver .btn,
.silver2 .btn {
    color: #333;
}

.gold .btn,
.gold2 .btn,
.black .btn {
    color: #fff;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

/* Enhanced text shadows for better readability on gradient backgrounds */
.gold h2.title,
.gold2 h2.title,
.black h2.title {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8) !important;
}

.gold h3,
.gold2 h3,
.black h3 {
    text-shadow: 1px 1px 3px rgba(0,0,0,0.7) !important;
}

.gold strong,
.gold2 strong,
.black strong {
    text-shadow: 1px 1px 2px rgba(0,0,0,0.6) !important;
}

.gold ul li,
.gold2 ul li,
.black ul li {
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5) !important;
}

/* Specific improvements for list items on colored backgrounds */
.gold ul li strong,
.gold2 ul li strong,
.black ul li strong {
    color: #fff !important;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.8) !important;
}

/* Ensure all paragraph text is readable */
.gold p,
.gold2 p,
.black p {
    color: #fff !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.6) !important;
}

/* Light background improvements */
.white p,
.white2 p,
.silver p,
.silver2 p {
    color: #333 !important;
    text-shadow: none !important;
}

.white strong,
.white2 strong,
.silver strong,
.silver2 strong {
    color: #222 !important;
    text-shadow: none !important;
}

/* Hardware page specific improvements */
.hardware .title {
    font-weight: 600 !important;
}

.hardware .white .title,
.hardware .white2 .title,
.hardware .silver .title,
.hardware .silver2 .title {
    color: #222 !important;
    text-shadow: none !important;
}

.hardware .gold .title,
.hardware .gold2 .title,
.hardware .black .title {
    color: #fff !important;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8) !important;
}

/* Improve visibility of .info class elements */
.white .info,
.white2 .info,
.silver .info,
.silver2 .info {
    color: #333 !important;
    font-weight: 500;
}

.gold .info,
.gold2 .info,
.black .info {
    color: #fff !important;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.7) !important;
    font-weight: 500;
}

/* Ensure line breaks are visible */
.gold .info br,
.gold2 .info br,
.black .info br {
    line-height: 1.8;
}

/* Additional contrast for hardware sections */
#s1, #s3, #s5, #s7 {
    /* Light backgrounds */
}

#s1 .title, #s3 .title, #s5 .title, #s7 .title {
    color: #222 !important;
    text-shadow: none !important;
}

#s2, #s4, #s6 {
    /* Dark backgrounds */
}

#s2 .title, #s4 .title, #s6 .title {
    color: #fff !important;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8) !important;
}

/* Final text readability improvements */
.col-xs-12 h1,
.col-xs-12 h2,
.col-xs-12 h3,
.col-xs-12 h4 {
    font-weight: 600 !important;
}

/* Ensure all text elements have proper contrast */
.white *,
.white2 *,
.silver *,
.silver2 * {
    color: inherit;
}

.gold *,
.gold2 *,
.black * {
    color: inherit;
}

/* Override any conflicting styles */
.white .container *:not(img),
.white2 .container *:not(img),
.silver .container *:not(img),
.silver2 .container *:not(img) {
    color: #333 !important;
    text-shadow: none !important;
}

.gold .container *:not(img),
.gold2 .container *:not(img),
.black .container *:not(img) {
    color: #fff !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.6) !important;
}

/* Special handling for titles */
.white .container .title,
.white2 .container .title,
.silver .container .title,
.silver2 .container .title {
    color: #222 !important;
    font-weight: 700 !important;
}

.gold .container .title,
.gold2 .container .title,
.black .container .title {
    color: #fff !important;
    font-weight: 700 !important;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8) !important;
}
