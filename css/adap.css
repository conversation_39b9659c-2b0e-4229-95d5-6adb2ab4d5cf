#car-info { margin:10px auto 0px; width:90%; text-align:center; }
#car-info p { margin:20px auto 0; }
#car-type { margin: 10px auto 0; width: 90%; }
.table-list { float: left; margin-top: 14px; margin-bottom: 10px; margin-left: 27%; }
.table-list dt { clear: both; height:30px; line-height:30px; }
.table-list dd { min-width: 80px; float: left; margin-right: 5px; height:24px; line-height:24px; }
.car-type { height: auto; overflow: hidden; margin-bottom: 20px; }
div.th { background: #FAFAFA; height: 34px; clear: both; border: 1px solid #DDDDDD; }
.t-content { height: auto; overflow: hidden; border: 1px solid #DDDDDD; position: relative; border-top: 0; min-height: 140px; }
.left-title div { padding-top: 16px; vertical-align: middle; text-align: center; }
.left-title span { color: #656d78; font-size: 14px; font-weight: bold; margin-left: 10px; }
@media screen and (max-width:998px) {
.left-title span { margin: 0; }
.car-nav b { clear:left; margin-right:12px; }
}
.left-title img { width: 38%; max-width:120px; border: 0; margin: 0 auto !important; }
.left-title { border-right: 1px solid #DDDDDD; background: #fdfdfd; float: left; width: 25%; position: absolute; height: 100%; left: 0; top: 0; }
.th { font-family: Arial; float: left; width: 100%; height: 34px; line-height: 34px; text-indent: 40px; color: #656d78; font-size: 20px; font-weight: bold; text-decoration: none; }
.car-nav { width: 90%; text-align: center; margin: 0 auto; padding:20px 0 0; background: #FFF; }
.car-nav a { color: #FFF; margin:2px; display: inline-block; width: 30px; height: 30px; background: #4BC1D2; text-align: center; line-height: 30px;-moz-border-radius: 15px;border-radius: 15px; }
.car-nav a:hover { color: #0A6A76; }