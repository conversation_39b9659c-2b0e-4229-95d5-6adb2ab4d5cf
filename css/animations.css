/* CSS Document */

@keyframes afc {
	from {
		opacity:0;
		transform:scale(0.2)
	}
	to {
		opacity:1;
		transform:scale(1)
	}
}
@-webkit-keyframes afc {
	from {
		opacity:0;
		-webkit-transform:scale(0.2)
	}
	to {
		opacity:1;
		-webkit-transform:scale(1)
	}
}
.animate_afc {
	opacity:0
}
.animate_afc.animate_start {
	-webkit-animation:0.8s cubic-bezier(1,0,0,1) 0s normal backwards 1 afc;
	animation:0.8s cubic-bezier(1,0,0,1) 0s normal backwards 1 afc;
	opacity:1
}
@keyframes afl {
	from {
		opacity:0;
		transform:translateX(-100px)
	}
	to {
		opacity:1;
		transform:translateX(0)
	}
}
@-webkit-keyframes afl {
	from {
		opacity:0;
		-webkit-transform:translateX(-100px)
	}
	to {
		opacity:1;
		-webkit-transform:translateX(0)
	}
}
.animate_afl {
	opacity:0
}
.animate_afl.animate_start {
	-webkit-animation:0.8s cubic-bezier(1,0,0,1) 0s normal backwards 1 afl;
	animation:0.8s cubic-bezier(1,0,0,1) 0s normal backwards 1 afl;
	opacity:1
}
@keyframes afr {
	from {
		opacity:0;
		transform:translateX(100px)
	}
	to {
		opacity:1;
		transform:translateX(0)
	}
}
@-webkit-keyframes afr {
	from {
		opacity:0;
		-webkit-transform:translateX(100px)
	}
	to {
		opacity:1;
		-webkit-transform:translateX(0)
	}
}
.animate_afr {
	opacity:0
}
.animate_afr.animate_start {
	-webkit-animation:0.8s cubic-bezier(1,0,0,1) 0s normal backwards 1 afr;
	animation:0.8s cubic-bezier(1,0,0,1) 0s normal backwards 1 afr;
	opacity:1
}
@keyframes aft {
	from {
		opacity:0;
		transform:translateY(-100px)
	}
	to {
		opacity:1;
		transform:translateY(0)
	}
}
@-webkit-keyframes aft {
	from {
		opacity:0;
		-webkit-transform:translateY(-100px)
	}
	to {
		opacity:1;
		-webkit-transform:translateY(0)
	}
}
.animate_aft {
	opacity:0
}
.animate_aft.animate_start {
	-webkit-animation:0.8s cubic-bezier(1,0,0,1) 0s normal backwards 1 aft;
	animation:0.8s cubic-bezier(1,0,0,1) 0s normal backwards 1 aft;
	opacity:1
}
@keyframes afb {
	from {
		opacity:0;
		transform:translateY(100px)
	}
	to {
		opacity:1;
		transform:translateY(0)
	}
}
@-webkit-keyframes afb {
	from {
		opacity:0;
		-webkit-transform:translateY(100px)
	}
	to {
		opacity:1;
		-webkit-transform:translateY(0)
	}
}
.animate_afb {
	opacity:0
}
.animate_afb.animate_start {
	-webkit-animation:0.8s cubic-bezier(1,0,0,1) 0s normal backwards 1 afb;
	animation:0.8s cubic-bezier(1,0,0,1) 0s normal backwards 1 afb;
	opacity:1
}
@keyframes wfc {
	from {
		opacity:0;
		transform:scaleX(0.01)
	}
	to {
		opacity:1;
		transform:scaleX(1)
	}
}
@-webkit-keyframes wfc {
	from {
		opacity:0;
		-webkit-transform:scaleX(0.01)
	}
	to {
		opacity:1;
		-webkit-transform:scaleX(1)
	}
}
.animate_wfc {
	opacity:0
}
.animate_wfc.animate_start {
	-webkit-animation:0.8s cubic-bezier(1,0,0,1) 0s normal backwards 1 wfc;
	animation:0.8s cubic-bezier(1,0,0,1) 0s normal backwards 1 wfc;
	opacity:1
}
@keyframes hfc {
	from {
		opacity:0;
		transform:scaleY(0.01)
	}
	to {
		opacity:1;
		transform:scaleY(1)
	}
}
@-webkit-keyframes hfc {
	from {
		opacity:0;
		-webkit-transform:scaleY(0.01)
	}
	to {
		opacity:1;
		-webkit-transform:scaleY(1)
	}
}
.animate_hfc {
	opacity:0
}
.animate_hfc.animate_start {
	-webkit-animation:0.8s cubic-bezier(1,0,0,1) 0s normal backwards 1 hfc;
	animation:0.8s cubic-bezier(1,0,0,1) 0s normal backwards 1 hfc;
	opacity:1
}
@keyframes rfc {
	from {
		opacity:0;
		transform:scale(0.01) rotate(360deg)
	}
	to {
		opacity:1;
		transform:scale(1) rotate(0)
	}
}
@-webkit-keyframes rfc {
	from {
		opacity:0;
		-webkit-transform:scale(0.01) rotate(360deg)
	}
	to {
		opacity:1;
		-webkit-transform:scale(1) rotate(0)
	}
}
.animate_rfc {
	opacity:0
}
.animate_rfc.animate_start {
	-webkit-animation:0.8s ease 0s normal backwards 1 rfc;
	animation:0.8s ease 0s normal backwards 1 rfc;
	opacity:1
}
@keyframes rfl {
	from {
		opacity:0;
		transform:translateX(-100px) rotate(-180deg)
	}
	to {
		opacity:1;
		transform:translateX(0) rotate(0)
	}
}
@-webkit-keyframes rfl {
	from {
		opacity:0;
		-webkit-transform:translateX(-100px) rotate(-180deg)
	}
	to {
		opacity:1;
		-webkit-transform:translateX(0) rotate(0)
	}
}
.animate_rfl {
	opacity:0
}
.animate_rfl.animate_start {
	-webkit-animation:0.8s ease 0s normal backwards 1 rfl;
	animation:0.8s ease 0s normal backwards 1 rfl;
	opacity:1
}
@keyframes rfr {
	from {
		opacity:0;
		transform:translateX(100px) rotate(180deg)
	}
	to {
		opacity:1;
		transform:translateX(0) rotate(0)
	}
}
@-webkit-keyframes rfr {
	from {
		opacity:0;
		-webkit-transform:translateX(100px) rotate(180deg)
	}
	to {
		opacity:1;
		-webkit-transform:translateX(0) rotate(0)
	}
}
.animate_rfr {
	opacity:0
}
.animate_rfr.animate_start {
	-webkit-animation:0.8s ease 0s normal backwards 1 rfr;
	animation:0.8s ease 0s normal backwards 1 rfr;
	opacity:1
}
.d1.animate_start {
	-webkit-animation-delay:0.2s;
	animation-delay:0.2s
}
.d2.animate_start {
	-webkit-animation-delay:0.4s;
	animation-delay:0.4s
}
.d3.animate_start {
	-webkit-animation-delay:0.6s;
	animation-delay:0.6s
}
.d4.animate_start {
	-webkit-animation-delay:0.8s;
	animation-delay:0.8s
}
.d5.animate_start {
	-webkit-animation-delay:1s;
	animation-delay: 1s
}

.d6.animate_start {
	-webkit-animation-delay:1.2s;
	animation-delay: 1.2s
}