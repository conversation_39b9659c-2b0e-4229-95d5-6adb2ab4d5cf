.btn:focus,
.subscribe-btn,
.progress .progress-bar,
.accordionMod .panel-heading .current,
#current:after,
.tp-leftarrow.default:hover,
.tp-rightarrow.default:hover,
.nivo-prevNav:hover,
.nivo-nextNav:hover,
.contact-box:hover,
#options li a.selected,
.ch-info .ch-info-back { background: #0488cd; }
.btn:focus { background: #ff7400; }
.pagination > .active > a,
.pagination > .active > span,
.pagination > .active > a:hover,
.pagination > .active > span:hover,
.pagination > .active > a:focus,
.pagination > .active > span:focus,
.pager li > a:hover,
.pager li > a:focus,
.navbar-toggle i,
.cn-wrapper li a,
.cn-wrapper:after { border:1px solid #0488cd; color: #fff; }
#options li a.selected { border: 1px solid #0488cd; }
.ie8 .btn-color,
.ie9 .btn-color { background: #ff5f04 !important; }
.ie8 .btn-color:hover,
.ie9 .btn-color:hover { background: #ffb47b !important; }
.btn-color:hover,
.subscribe-btn:hover { background: -webkit-linear-gradient(#ff5f04, #ffb47b); background: -moz-linear-gradient(#ff5f04, #ffb47b); background: -o-linear-gradient(#ff5f04, #ffb47b); background: -ms-linear-gradient(#ff5f04, #ffb47b); background: linear-gradient(#ff5f04, #ffb47b); }
.btn-color,
.subscribe-btn { width: 126px; height: 51px; -webkit-border-radius: 5px; -moz-border-radius: 5px; border-radius: 5px; background-color: #fff; -webkit-box-shadow: 0 2px rgba(0,0,0,.2); -moz-box-shadow: 0 2px rgba(0,0,0,.2); box-shadow: 0 2px rgba(0,0,0,.2); border: solid 1px #f27109; background-image: -webkit-linear-gradient(bottom, #ff7400, #ffa443); background-image: -moz-linear-gradient(bottom, #ff7400, #ffa443); background-image: -o-linear-gradient(bottom, #ff7400, #ffa443); background-image: linear-gradient(to top, #ff7400, #ffa443); cursor: pointer; font-family: "微软雅黑 Light", "微软雅黑"; }
#current,
.menu ul li a:hover,
.menu ul>li:hover>a { border-bottom: 3px solid #0488cd; }
ul.why li:before { background: #0488cd; }
.menu ul ul { border-top: 3px solid #0488cd !important; }
#horizontal-tabs ul.tabs li.current { border-top: 2px solid #0488cd; }
#vertical-tabs ul.tabs li.current { border-left: 2px solid #0488cd; }
.author-content,
div.content-box.big,
#cn-tabs .service,
.comment-des,
.contact-box { border-bottom-color: #0488cd; }
a { color: #FFFFFF; font-size: 16px; }
.row a:hover,
.post-item h4 a:hover,
.cn-wrapper li a:hover,
.cn-wrapper li a.active { color: #FCF2D6; }
.b { color: #333333; font-size: 16px; }
.row .b:hover { color: #000; }
.btn-down { width: 204px; height: 68px;padding-top:17px; background-color: #fff; -webkit-box-shadow: 0 2px rgba(0,0,0,.2); -moz-box-shadow: 0 2px rgba(0,0,0,.2); box-shadow: 0 2px rgba(0,0,0,.2); border: solid 1px #ed8203; background-image: -webkit-linear-gradient(bottom, #ff7400, #ffa443); background-image: -moz-linear-gradient(bottom, #ff7400, #ffa443); background-image: -o-linear-gradient(bottom, #ff7400, #ffa443); background-image: linear-gradient(to top, #ff7400, #ffa443); cursor: pointer; color: #FFF; font: 24px Tahoma, "微软雅黑 Light", "微软雅黑";margin-right:10px; }
.btn-down:hover { background-image: -webkit-linear-gradient(top, #ff7400, #ffa443); background-image: -moz-linear-gradient(top, #ff7400, #ffa443); background-image: -o-linear-gradient(top, #ff7400, #ffa443); background-image: linear-gradient(to bottom, #ff7400, #ffa443); }
