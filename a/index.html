<!DOCTYPE html>
<html>

<head>
	<meta charset="utf-8">
	<meta name="viewport"
		content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
	<link href="mobiledownload.css" type="text/css" rel="stylesheet">
	<link href="mobile.css" type="text/css" rel="stylesheet">
	<script src="jquery.js" type="text/javascript" charset="utf-8"></script>
	<style type="text/css">
		#weixin-tip img {
			max-width: 90%;
			float: right;
		}
	</style>

	<!--[if IE]>
			<script src="css3-mediaqueries.js"></script>
			<script src="html5.js"></script>
		<![endif]-->

	<!--[if IEMobile]> 
			<style>
                header .title {font-size:24px; padding:0 5px;}
                header .intro {font-size:16px; padding:0 21px;}
                #logo {width:220px;}
                #iPhoneBtn {width: 250px; height: 57px;}
                #androidBtn {width: 250px; height: 57px;}
			</style>
		<![endif]-->

	<title>GeekOBD Download</title>
</head>

<body>
	<aside class="wrap">
		<header class="middle">
			<header class="middle"><img src="geekobdlogo.png" id="logo" title="GeekOBD" alt="GeekOBD"></header>
			<div class="title middle">Welcome, <b>GeekOBD</b>!</div>
			<footer class="intro middle">
			</footer>
		</header>
		<section class="content">
			<header class="hrline"></header>
			<figure class="mobile-icon middle"></figure>
			<footer class="middle">
				<a href="https://itunes.apple.com/us/app/geekobd/id909638336?mt=8"><img src="button-iphone-en.png"
						id="iPhoneBtn" title="iPhone" alt="iPhone" /></a><br>
				<a href="https://play.google.com/store/apps/details?id=rocket.vehiclemgr.android.obd2.geekobd"
					id="btnwx"><img src="button-android-en.png" id="androidBtn" title="Android" alt="Android" /></a>
				<br>
				<p class="note">For details visit our website at <a href="http://www.geekobd.com/"
						target="_blank">www.geekobd.com</a>.

			</footer>

		</section>
	</aside>
	<script type="text/javascript">
		function is_weixin() {
			var ua = navigator.userAgent.toLowerCase();
			if (ua.match(/MicroMessenger/i) == "micromessenger") {
				return true;
			} else {
				return false;
			}
		}
		var isWeixin = is_weixin();
		var winHeight = typeof window.innerHeight != 'undefined' ? window.innerHeight : document.documentElement.clientHeight;
		console.log(winHeight);
		function loadHtml() {
			var div = document.createElement('div');
			div.id = 'weixin-tip';
			div.innerHTML = '<p><img src="weixin_share.png" alt="WeChat"/></p>';
			document.body.appendChild(div);
		}

		function loadStyleText(cssText) {
			var style = document.createElement('style');
			style.rel = 'stylesheet';
			style.type = 'text/css';
			try {
				style.appendChild(document.createTextNode(cssText));
			} catch (e) {
				style.styleSheet.cssText = cssText; //ie9以下
			}
			var head = document.getElementsByTagName("head")[0]; //head标签之间加上style样式
			head.appendChild(style);
		}
		var cssText = "#weixin-tip{position: fixed; left:0; top:0; background: rgba(0,0,0,0.8); filter:alpha(opacity=80); width: 100%; height:100%; z-index: 100;} #weixin-tip p{margin-top: 10%; padding:0;}";
		if (isWeixin) {
			loadHtml();
			loadStyleText(cssText);
		}
	</script>

</body>

</html>