/* =Reset default browser CSS. Based on work by <PERSON>: http://meyerweb.com/eric/tools/css/reset/index.html
-------------------------------------------------------------- */
html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,a,abbr,acronym,address,big,cite,code,del,dfn,em,font,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td{border:0;font-family:inherit;font-size:100%;font-style:inherit;font-weight:inherit;outline:0;vertical-align:baseline;margin:0;padding:0;}
:focus{outline:0;}
p {margin:0; padding:0; display:inline;}
body{line-height:1;}
ol,ul{list-style:none;}
table{border-collapse:separate;border-spacing:0;}
caption,th,td{font-weight:400;text-align:left;}
blockquote:before,blockquote:after,q:before,q:after{content:"";}
blockquote,q{quotes:"" "";}
a img{border:0;}
article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section{display:block; margin:0; padding:0;}

/*------------------------------------------------------------------------------------------------------------*/

body {background: #FFF url(bg-gradient.png) repeat-x; font:100% Arial, Helvetica, sans-serif; color: #333333;  -webkit-text-size-adjust: 100%;}
.wrap {width:100%; margin:0 auto;}

header #logo {background:url(mobdlogo.png) no-repeat; margin-top: -20px;}
header .title {margin:30px 0 15px 0; font-size:35px; }
header .intro {font-size:20px;}

.content {margin:0 0 30px 0;}
.content .hrline {border:1px solid #eeeeee; margin:50px 0 -5px 0;}
.content .mobile-icon {background:url(icon-mobile.png) no-repeat; width:86px; height:86px; position:relative; top:-40px; }
.content img {margin:-10px 0 20px 0;}
.content .note {font-size:14px;}
.content .note a {color:#333333;}
.content .copy { display: block; font-size: 12px; margin-top: 20px;}

.middle {text-align:center; margin:0 auto;}
.bold {font-weight:700;}

#share { width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.7); position: fixed; top:0; left:0; display: none; z-index: 3333;}
.img-responsive { max-width: 100%; height: auto; border: solid 1px #FF0000;}



