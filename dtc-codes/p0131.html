<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<title>P0131 - O2 Sensor Low Voltage Bank 1 Sensor 1 | GeekOBD Diagnostic Guide</title>
<meta name="description" content="P0131 diagnostic trouble code: O2 Sensor Low Voltage Bank 1 Sensor 1. Learn about symptoms, causes, diagnosis steps, and repair solutions for P0131 with GeekOBD professional tools.">
<meta name="keywords" content="P0131, P0131 code, P0131 diagnostic, o2 sensor low voltage bank 1 sensor 1, automotive diagnostic code">
<meta name="author" content="Beijing MentalRoad Technology Co., Ltd.">
<meta name="robots" content="index, follow">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
<link rel="canonical" href="https://www.geekobd.com/dtc-codes/p0131.html">

<link rel="stylesheet" href="../css/bootstrap.css">
<link rel="stylesheet" href="../css/fonts/font-awesome/css/font-awesome.css">
<link rel="stylesheet" href="../css/animations.css" media="screen">
<link rel="stylesheet" href="../css/superfish.css" media="screen">
<link rel="stylesheet" href="../css/style.css">
<link rel="stylesheet" href="../css/colors/blue.css" id="colors">
<link rel="stylesheet" href="../css/theme-responsive.css">
<link rel="stylesheet" href="../css/seo-enhancements.css">
<link rel="shortcut icon" href="../img/ico/favicon.ico">

<style>
.dtc-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 60px 0 40px;
}
.dtc-code-badge {
    display: inline-block;
    background: rgba(255,255,255,0.2);
    padding: 10px 20px;
    border-radius: 50px;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
}
.severity-badge {
    display: inline-block;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
    margin-left: 15px;
}
.severity-high { background: #ff4757; color: white; }
.severity-medium { background: #ffa502; color: white; }
.severity-low { background: #2ed573; color: white; }
.content-section { padding: 50px 0; }
.info-box {
    background: #f8f9fa;
    border-left: 4px solid #667eea;
    padding: 20px;
    margin: 20px 0;
    border-radius: 5px;
}
.warning-box {
    background: #fff3cd;
    border-left: 4px solid #ffc107;
    padding: 20px;
    margin: 20px 0;
    border-radius: 5px;
}
.danger-box {
    background: #f8d7da;
    border-left: 4px solid #dc3545;
    padding: 20px;
    margin: 20px 0;
    border-radius: 5px;
}
</style>

<!-- Article Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": "P0131 - O2 Sensor Low Voltage Bank 1 Sensor 1 | Complete Diagnostic Guide",
  "description": "P0131 diagnostic trouble code: O2 Sensor Low Voltage Bank 1 Sensor 1. Complete guide with symptoms, causes, diagnosis steps, and repair solutions using GeekOBD professional tools.",
  "image": "https://www.geekobd.com/img/logo.png",
  "author": {
    "@type": "Organization",
    "name": "GeekOBD",
    "url": "https://www.geekobd.com"
  },
  "publisher": {
    "@type": "Organization",
    "name": "GeekOBD",
    "logo": {
      "@type": "ImageObject",
      "url": "https://www.geekobd.com/img/logo.png"
    }
  },
  "datePublished": "2025-01-31",
  "dateModified": "2025-01-31",
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "https://www.geekobd.com/dtc-codes/p0131.html"
  },
  "articleSection": "Automotive Diagnostics",
  "keywords": "P0131, oxygen sensor, O2 sensor low voltage, bank 1 sensor 1",
  "about": {
    "@type": "Thing",
    "name": "P0131 Diagnostic Trouble Code",
    "description": "Oxygen sensor low voltage on bank 1, sensor 1"
  }
}
</script>

<!-- FAQ Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "What does P0131 mean?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "P0131 indicates that the engine control module has detected low voltage from the oxygen sensor circuit for bank 1, sensor 1 (the upstream O2 sensor on the side with cylinder 1)."
      }
    },
    {
      "@type": "Question",
      "name": "What causes P0131 low voltage?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "P0131 is typically caused by a faulty oxygen sensor, damaged wiring, poor connections, exhaust leaks near the sensor, or a rich fuel mixture causing sensor contamination."
      }
    },
    {
      "@type": "Question",
      "name": "How much does it cost to fix P0131?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Repair costs typically range from $180-450, with oxygen sensor replacement being the most common fix. Wiring repairs may cost less, while multiple issues cost more."
      }
    },
    {
      "@type": "Question",
      "name": "Can I drive with P0131 code?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "You can drive short distances with P0131, but prolonged driving may cause poor fuel economy, failed emissions tests, and potential catalytic converter damage. Repair promptly."
      }
    }
  ]
}
</script>

<!-- HowTo Schema for P0131 Diagnostic Steps -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "HowTo",
  "name": "How to Diagnose P0131 O2 Sensor Low Voltage",
  "description": "Step-by-step guide to diagnose and fix P0131 oxygen sensor low voltage on Bank 1 Sensor 1",
  "image": "https://www.geekobd.com/img/logo.png",
  "totalTime": "PT75M",
  "estimatedCost": {
    "@type": "MonetaryAmount",
    "currency": "USD",
    "value": "315"
  },
  "tool": [
    {
      "@type": "HowToTool",
      "name": "GeekOBD APP with MOBD Adapter",
      "description": "Professional OBD2 diagnostic tool with live O2 sensor voltage monitoring for Bank 1 Sensor 1",
      "url": "https://www.geekobd.com/app.html"
    },
    {
      "@type": "HowToTool",
      "name": "Digital Multimeter",
      "description": "For testing O2 sensor voltage and circuit continuity"
    },
    {
      "@type": "HowToTool",
      "name": "O2 Sensor Socket",
      "description": "Special socket for removing oxygen sensors"
    }
  ],
  "supply": [
    {
      "@type": "HowToSupply",
      "name": "Bank 1 Sensor 1 Oxygen Sensor"
    },
    {
      "@type": "HowToSupply",
      "name": "Anti-Seize Compound"
    },
    {
      "@type": "HowToSupply",
      "name": "Electrical Contact Cleaner"
    }
  ],
  "step": [
    {
      "@type": "HowToStep",
      "position": 1,
      "name": "Scan and Confirm P0131 Code",
      "text": "Connect GeekOBD APP and scan for P0131 code. Monitor live data to check Bank 1 Sensor 1 voltage readings - should be consistently low (under 0.45V).",
      "image": "https://www.geekobd.com/img/geekobd-p0131-scan.jpg"
    },
    {
      "@type": "HowToStep",
      "position": 2,
      "name": "Inspect Bank 1 Sensor 1 Wiring",
      "text": "Locate Bank 1 Sensor 1 (upstream O2 sensor) and inspect wiring harness for damage, corrosion, or loose connections. Check for exhaust leaks near sensor.",
      "image": "https://www.geekobd.com/img/bank1-sensor1-wiring.jpg"
    },
    {
      "@type": "HowToStep",
      "position": 3,
      "name": "Test Sensor Voltage and Heater",
      "text": "Use multimeter to test sensor signal voltage (should switch 0.1-0.9V), heater circuit resistance (2-14 ohms), and ground circuit continuity.",
      "image": "https://www.geekobd.com/img/o2-sensor-voltage-test.jpg"
    },
    {
      "@type": "HowToStep",
      "position": 4,
      "name": "Replace O2 Sensor if Faulty",
      "text": "If sensor voltage stays low or heater circuit fails, replace Bank 1 Sensor 1 oxygen sensor. Apply anti-seize to threads and torque to specification.",
      "image": "https://www.geekobd.com/img/o2-sensor-replacement.jpg"
    },
    {
      "@type": "HowToStep",
      "position": 5,
      "name": "Clear Code and Verify Repair",
      "text": "Clear P0131 code with GeekOBD APP, drive for 15-20 minutes, and monitor Bank 1 Sensor 1 voltage to ensure proper switching between 0.1-0.9V.",
      "image": "https://www.geekobd.com/img/geekobd-o2-sensor-verified.jpg"
    }
  ]
}
</script>

<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-RD6767XBCL"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-RD6767XBCL');
</script>
</head>

<body>
<div class="wrap">
	<header id="header" role="banner">
	<div class="main-header">
	<div class="container">
	<div class="row">
	<div class="col-md-3">
	<div class="logo pull-left">
	<h1> <a href="../index.html"> <img src="../img/logo.png" alt="MOBD"> </a> </h1>
	</div>
	</div>
	<div class="col-md-9">
	<div class="mobile navbar-header"> <a class="navbar-toggle" data-toggle="collapse" data-target=".menu"> <i class="icon-reorder icon-2x"></i> </a> </div>
	<nav class="collapse navbar-collapse menu" role="navigation" aria-label="Main navigation">
	<ul class="nav navbar-nav sf-menu">
	<li><a href="../index.html" class="sf-with-ul">Home</a></li>
	<li><a href="../app.html" class="sf-with-ul">APP</a></li>
	<li><a href="javascript:;;" class="sf-with-ul">Adaptor <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
	<ul>
	<li><a href="../hardware2.html" class="sf-with-ul">MOBD GPS</a></li>
	<li><a href="../hardware.html" class="sf-with-ul">MOBD</a></li>
	</ul>
	</li>
	<li><a href="javascript:;;" class="sf-with-ul">Resources <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
	<ul>
	<li><a href="../dtc-codes.html" class="sf-with-ul" id="current">DTC Codes</a></li>
	<li><a href="../obd-diagnostic-guide.html" class="sf-with-ul">OBD Guide</a></li>
	<li><a href="../vehicle-compatibility.html" class="sf-with-ul">Compatibility</a></li>
	<li><a href="../fuel-efficiency-monitoring.html" class="sf-with-ul">Fuel Efficiency</a></li>
	<li><a href="../support.html" class="sf-with-ul">Support</a></li>
	<li><a href="../blog.html" class="sf-with-ul">Blog</a></li>
	</ul>
	</li>
	<li><a href="../about.html" class="sf-with-ul">About Us</a></li>
	<li><a href="//www.mobd.cn" class="sf-with-ul" target="_blank">中文版</a></li>
	</ul>
	</nav>
	</div>
	</div>
	</div>
	</div>
	</header>

	<section class="dtc-header">
		<div class="container">
			<div class="row">
				<div class="col-md-12">
					<div class="dtc-code-badge">P0131</div>
					<span class="severity-badge severity-medium">MEDIUM Priority</span>
					<h1 style="margin: 20px 0; font-size: 36px;">O2 Sensor Low Voltage Bank 1 Sensor 1</h1>
					<p style="font-size: 18px; opacity: 0.9; margin-bottom: 0;">The ECM has detected a malfunction in the o2 sensor low voltage bank 1 sensor 1.</p>
				</div>
			</div>
		</div>
	</section>

	<section class="content-section">
		<div class="container">
			<div class="row">
				<div class="col-md-8">
					<!-- Quick Answer Section for AI -->
					<div id="quick-answer" style="background: #e8f4fd; border: 2px solid #2196F3; border-radius: 10px; padding: 25px; margin-bottom: 30px;">
						<h2 style="color: #1976D2; margin-bottom: 15px;"><i class="fa fa-bolt"></i> Quick Answer</h2>
						<p style="font-size: 18px; font-weight: 500; margin-bottom: 15px; color: #333;">
							<strong>P0131 means:</strong> Bank 1 Sensor 1 (upstream O2 sensor) voltage is consistently LOW, indicating sensor or circuit failure.
						</p>
						<div style="display: flex; flex-wrap: wrap; gap: 15px; margin-bottom: 15px;">
							<span style="background: #4CAF50; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
								<i class="fa fa-wrench"></i> Fix: Replace Bank 1 Sensor 1 O2 sensor
							</span>
							<span style="background: #FF9800; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
								<i class="fa fa-dollar"></i> Cost: $180-$450
							</span>
							<span style="background: #2196F3; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
								<i class="fa fa-clock-o"></i> Time: 75 minutes
							</span>
						</div>
						<p style="margin: 0; color: #666; font-size: 14px;">
							<strong>Can I drive with P0131?</strong> YES - Safe to drive short distances, but repair soon to avoid poor fuel economy and emissions failure.
						</p>
					</div>

					<!-- AI-Friendly Q&A Section -->
					<div id="ai-qa" style="background: white; border-radius: 10px; padding: 25px; margin-bottom: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
						<h2><i class="fa fa-comments"></i> Common Questions</h2>

						<div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
							<h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">What does "low voltage" mean for P0131?</h3>
							<p style="color: #666; line-height: 1.6;">P0131 low voltage means Bank 1 Sensor 1 O2 sensor voltage stays below 0.45V constantly, instead of switching between 0.1-0.9V. This indicates the sensor is "stuck lean" and can't detect rich exhaust conditions properly.</p>
						</div>

						<div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
							<h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">What's the difference between P0131 and P0134?</h3>
							<p style="color: #666; line-height: 1.6;">P0131 = Low voltage (sensor stuck lean, reads under 0.45V), P0134 = No activity (sensor completely dead, no voltage switching). Both affect Bank 1 Sensor 1 but P0131 shows partial sensor function.</p>
						</div>

						<div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
							<h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">Can a rich fuel mixture cause P0131?</h3>
							<p style="color: #666; line-height: 1.6;">YES - Prolonged rich fuel mixture can contaminate the O2 sensor with carbon deposits, causing it to read low voltage constantly. This is why P0131 often appears with fuel system codes like P0172.</p>
						</div>

						<div class="qa-item" style="margin-bottom: 0;">
							<h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">How do I test Bank 1 Sensor 1 voltage with GeekOBD?</h3>
							<p style="color: #666; line-height: 1.6;">Use GeekOBD APP live data to monitor "O2S11" (Bank 1 Sensor 1). A healthy sensor switches rapidly between 0.1-0.9V. P0131 shows voltage stuck below 0.45V with little or no switching activity.</p>
						</div>
					</div>

					<div id="overview">
						<h2><i class="fa fa-info-circle"></i> Technical Overview</h2>
						<div class="info-box">
							<h4>P0131 Definition</h4>
							<p>P0131 indicates that the Engine Control Module (ECM) has detected consistently low voltage from the oxygen sensor circuit for Bank 1, Sensor 1. This upstream oxygen sensor (before the catalytic converter) should normally switch between 0.1V (lean) and 0.9V (rich) as it monitors exhaust gases. When voltage stays below 0.45V, the ECM triggers P0131.</p>
						</div>

						<h3>Technical Details</h3>
						<ul>
							<li><strong>Code Type:</strong> Generic Powertrain Code</li>
							<li><strong>System:</strong> Engine Management / Emissions Control</li>
							<li><strong>Severity:</strong> Medium - Affects fuel economy and emissions</li>
							<li><strong>Driving Safety:</strong> Safe to drive but should be repaired promptly</li>
						</ul>
					</div>

					<!-- Symptoms Section -->
					<div id="symptoms">
						<h2><i class="fa fa-exclamation-triangle"></i> Symptoms</h2>
						<p>When P0131 is triggered, you may experience:</p>
						<ul>
							<li>Check Engine Light illuminated</li>
							<li>Poor fuel economy (engine runs rich due to faulty sensor reading)</li>
							<li>Rough idle or unstable engine operation</li>
							<li>Engine hesitation during acceleration</li>
							<li>Failed emissions test (high CO and HC levels)</li>
							<li>Black exhaust smoke (rich fuel mixture)</li>
							<li>Strong fuel odor from exhaust</li>
							<li>Engine may run poorly when cold</li>
							<li>Reduced engine performance and power</li>
						</ul>

						<div class="warning-box">
							<strong><i class="fa fa-warning"></i> Important:</strong> P0131 causes the ECM to assume the engine is always running lean, leading to over-fueling and poor fuel economy. Address this issue promptly to restore proper air/fuel mixture control.
						</div>
					</div>

					<!-- Causes Section -->
					<div id="causes">
						<h2><i class="fa fa-search"></i> Possible Causes</h2>
						<p>P0131 can be triggered by several different issues:</p>
						<ol>
							<li><strong>Faulty oxygen sensor (Bank 1, Sensor 1)</strong> - Most common cause (80%)</li>
							<li><strong>Damaged or corroded oxygen sensor wiring</strong></li>
							<li><strong>Poor electrical connections at oxygen sensor connector</strong></li>
							<li><strong>Exhaust leaks near the oxygen sensor</strong></li>
							<li><strong>Contaminated oxygen sensor (carbon, oil, or coolant deposits)</strong></li>
							<li><strong>Faulty oxygen sensor heater circuit</strong></li>
							<li><strong>Rich fuel mixture contaminating the sensor</strong></li>
							<li><strong>High fuel pressure causing rich conditions</strong></li>
							<li><strong>Faulty fuel injectors (leaking or stuck open)</strong></li>
							<li><strong>ECM issues (rare)</strong></li>
						</ol>
					</div>

					<!-- Repair Cost Information -->
					<div id="cost-info" style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
						<h2><i class="fa fa-calculator"></i> P0131 Repair Costs</h2>

						<div style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin: 20px 0;">
							<h3 style="color: #2c3e50; margin-bottom: 20px;">Cost Breakdown by Repair Type</h3>

							<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 25px;">
								<div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #4CAF50;">
									<h4 style="color: #4CAF50; margin-bottom: 10px;"><i class="fa fa-wrench"></i> Wiring/Connector Repair</h4>
									<p style="margin-bottom: 15px; color: #666;">Simple fix - 20% of P0131 cases</p>
									<ul style="list-style: none; padding: 0;">
										<li style="margin-bottom: 8px;"><strong>Connector cleaning:</strong> $50-90</li>
										<li style="margin-bottom: 8px;"><strong>Wiring repair:</strong> $130-220</li>
										<li style="margin-bottom: 8px;"><strong>Harness replacement:</strong> $200-350</li>
										<li style="color: #666; font-size: 14px;">Success rate: ~85%</li>
									</ul>
								</div>

								<div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #FF9800;">
									<h4 style="color: #FF9800; margin-bottom: 10px;"><i class="fa fa-cog"></i> Bank 1 Sensor 1 Replacement</h4>
									<p style="margin-bottom: 15px; color: #666;">Most common P0131 fix - 80% success</p>
									<ul style="list-style: none; padding: 0;">
										<li style="margin-bottom: 8px;"><strong>Standard O2 sensor:</strong> $180-320</li>
										<li style="margin-bottom: 8px;"><strong>OEM O2 sensor:</strong> $250-450</li>
										<li style="margin-bottom: 8px;"><strong>Premium sensor:</strong> $290-520</li>
										<li style="color: #666; font-size: 14px;">Success rate: ~95%</li>
									</ul>
								</div>
							</div>

							<div style="background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #4CAF50; margin-bottom: 20px;">
								<h4 style="color: #2e7d32; margin-bottom: 15px;"><i class="fa fa-thumbs-up"></i> P0131 Low Voltage Advantages</h4>
								<p style="margin-bottom: 10px; color: #333;">P0131 repairs are often straightforward because:</p>
								<ul style="margin: 0; color: #333;">
									<li style="margin-bottom: 8px;"><strong>Clear diagnosis:</strong> Low voltage is easier to identify than intermittent issues</li>
									<li style="margin-bottom: 8px;"><strong>Upstream location:</strong> Bank 1 Sensor 1 is more accessible than downstream sensors</li>
									<li style="margin-bottom: 8px;"><strong>Common problem:</strong> Well-documented issue with proven solutions</li>
									<li><strong>Live data confirmation:</strong> Easy to verify with GeekOBD APP monitoring</li>
								</ul>
							</div>

							<div style="background: #e3f2fd; padding: 20px; border-radius: 8px; border-left: 4px solid #2196F3;">
								<h4 style="color: #1976D2; margin-bottom: 15px;"><i class="fa fa-lightbulb-o"></i> Money-Saving Tips for P0131</h4>
								<ul style="margin: 0; color: #333;">
									<li style="margin-bottom: 10px;">Check wiring and connections first - 20% of P0131 codes are electrical issues</li>
									<li style="margin-bottom: 10px;">Use GeekOBD APP to confirm low voltage before sensor replacement</li>
									<li style="margin-bottom: 10px;">Address rich fuel conditions (P0172) that may have contaminated the sensor</li>
									<li style="margin-bottom: 10px;">Fix P0131 quickly to prevent catalytic converter damage from rich mixture</li>
									<li>Consider replacing both upstream sensors if vehicle has high mileage (120k+)</li>
								</ul>
							</div>
						</div>
					</div>

					<!-- Case Study -->
					<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
						<h2><i class="fa fa-file-text"></i> Real Repair Case Studies</h2>

						<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
							<h4>Case 1: 2018 Honda Accord - Contaminated O2 Sensor</h4>
							<p><strong>Vehicle:</strong> 2018 Honda Accord 2.4L 4-cylinder, 75,000 miles</p>
							<p><strong>Problem:</strong> Customer reported poor fuel economy and check engine light. GeekOBD scan showed P0131 code with Bank 1 Sensor 1 voltage stuck at 0.2V. Also found P0172 (System Too Rich Bank 1).</p>
							<p><strong>Solution:</strong> Diagnosed rich fuel condition caused by faulty fuel pressure regulator, which contaminated the O2 sensor. Replaced fuel pressure regulator and Bank 1 Sensor 1 oxygen sensor. Cleared codes and verified proper voltage switching.</p>
							<p><strong>Cost:</strong> $385 (parts: $245, labor: $140)</p>
							<p><strong>Time:</strong> 2 hours</p>
						</div>

						<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
							<h4>Case 2: 2015 Toyota Camry - Simple Wiring Fix</h4>
							<p><strong>Vehicle:</strong> 2015 Toyota Camry 2.5L 4-cylinder, 92,000 miles</p>
							<p><strong>Problem:</strong> P0131 code with Bank 1 Sensor 1 showing 0V constantly. GeekOBD live data showed no voltage activity from the sensor circuit.</p>
							<p><strong>Solution:</strong> Found corroded ground wire at Bank 1 Sensor 1 connector. Cleaned corrosion, repaired ground connection, and applied dielectric grease. Sensor immediately began switching properly between 0.1-0.9V.</p>
							<p><strong>Cost:</strong> $75 (parts: $25, labor: $50)</p>
							<p><strong>Time:</strong> 45 minutes</p>
						</div>
					</div>

					<!-- Diagnostic Steps -->
					<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
						<h2><i class="fa fa-list-ol"></i> P0131 Diagnostic Steps</h2>
						<div class="diagnostic-steps">
							<div class="step" style="margin-bottom: 25px; padding: 20px; background: #f8f9fa; border-radius: 10px; border-left: 4px solid #667eea;">
								<h4 style="color: #667eea; margin-bottom: 15px;"><i class="fa fa-search"></i> Step 1: Scan and Confirm P0131</h4>
								<p>Connect GeekOBD APP and scan for P0131 code. Monitor live data to check Bank 1 Sensor 1 voltage readings - should be consistently low (under 0.45V) with little switching activity.</p>
								<ul style="margin-top: 10px;">
									<li>Check for additional codes (P0172, P0174, P0420)</li>
									<li>Monitor O2S11 voltage in live data</li>
									<li>Note if voltage is stuck below 0.45V</li>
								</ul>
							</div>

							<div class="step" style="margin-bottom: 25px; padding: 20px; background: #f8f9fa; border-radius: 10px; border-left: 4px solid #667eea;">
								<h4 style="color: #667eea; margin-bottom: 15px;"><i class="fa fa-eye"></i> Step 2: Visual Inspection</h4>
								<p>Locate Bank 1 Sensor 1 (upstream O2 sensor) and inspect wiring harness for damage, corrosion, or loose connections. Check for exhaust leaks near sensor.</p>
								<ul style="margin-top: 10px;">
									<li>Inspect sensor connector for corrosion</li>
									<li>Check wiring for damage or chafing</li>
									<li>Look for exhaust leaks near sensor</li>
									<li>Verify sensor is properly secured</li>
								</ul>
							</div>

							<div class="step" style="margin-bottom: 25px; padding: 20px; background: #f8f9fa; border-radius: 10px; border-left: 4px solid #667eea;">
								<h4 style="color: #667eea; margin-bottom: 15px;"><i class="fa fa-bolt"></i> Step 3: Test Sensor Circuits</h4>
								<p>Use multimeter to test sensor signal voltage, heater circuit resistance, and ground circuit continuity.</p>
								<ul style="margin-top: 10px;">
									<li>Signal voltage: Should switch 0.1-0.9V</li>
									<li>Heater resistance: 2-14 ohms typically</li>
									<li>Ground circuit: Less than 1 ohm resistance</li>
									<li>Power supply: Battery voltage to heater</li>
								</ul>
							</div>

							<div class="step" style="margin-bottom: 25px; padding: 20px; background: #f8f9fa; border-radius: 10px; border-left: 4px solid #667eea;">
								<h4 style="color: #667eea; margin-bottom: 15px;"><i class="fa fa-wrench"></i> Step 4: Replace Components</h4>
								<p>If sensor voltage stays low or heater circuit fails, replace Bank 1 Sensor 1 oxygen sensor. Address any rich fuel conditions first.</p>
								<ul style="margin-top: 10px;">
									<li>Apply anti-seize to sensor threads</li>
									<li>Torque to manufacturer specification</li>
									<li>Ensure proper connector seating</li>
									<li>Fix any rich fuel mixture issues</li>
								</ul>
							</div>

							<div class="step" style="margin-bottom: 0; padding: 20px; background: #e8f5e8; border-radius: 10px; border-left: 4px solid #4CAF50;">
								<h4 style="color: #2e7d32; margin-bottom: 15px;"><i class="fa fa-check"></i> Step 5: Verify Repair</h4>
								<p>Clear P0131 code with GeekOBD APP, drive for 15-20 minutes, and monitor Bank 1 Sensor 1 voltage to ensure proper switching between 0.1-0.9V.</p>
								<ul style="margin-top: 10px;">
									<li>Clear codes and reset monitors</li>
									<li>Drive vehicle through various conditions</li>
									<li>Monitor O2 sensor switching activity</li>
									<li>Verify no codes return</li>
								</ul>
							</div>
						</div>
					</div>

					<!-- Related Codes -->
					<div id="related" style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
						<h2><i class="fa fa-link"></i> Related Diagnostic Codes</h2>

						<div style="margin-bottom: 30px;">
							<h3 style="color: #333; margin-bottom: 20px;">Bank 1 Sensor 1 O2 Sensor Codes</h3>
							<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
								<div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #667eea;">
									<h4 style="margin-bottom: 10px;"><a href="p0130.html" style="color: #667eea; text-decoration: none;">P0130 - O2 Sensor Circuit Malfunction</a></h4>
									<p style="margin: 0; color: #666; font-size: 14px;">General circuit problem - could be sensor, wiring, or connections</p>
								</div>
								<div style="background: #fff3e0; padding: 15px; border-radius: 8px; border-left: 4px solid #FF9800;">
									<h4 style="margin-bottom: 10px; color: #FF9800;">P0131 - O2 Sensor Low Voltage (Current)</h4>
									<p style="margin: 0; color: #666; font-size: 14px;">Sensor stuck lean - voltage consistently below 0.45V</p>
								</div>
								<div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #667eea;">
									<h4 style="margin-bottom: 10px;"><a href="p0132.html" style="color: #667eea; text-decoration: none;">P0132 - O2 Sensor High Voltage</a></h4>
									<p style="margin: 0; color: #666; font-size: 14px;">Sensor stuck rich - voltage consistently above 0.45V</p>
								</div>
								<div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #667eea;">
									<h4 style="margin-bottom: 10px;"><a href="p0134.html" style="color: #667eea; text-decoration: none;">P0134 - O2 Sensor No Activity</a></h4>
									<p style="margin: 0; color: #666; font-size: 14px;">Sensor completely dead - no voltage switching detected</p>
								</div>
							</div>
						</div>

						<div style="margin-bottom: 30px;">
							<h3 style="color: #333; margin-bottom: 20px;">Commonly Associated Codes</h3>
							<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
								<div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #e91e63;">
									<h4 style="margin-bottom: 10px;"><a href="p0172.html" style="color: #e91e63; text-decoration: none;">P0172 - System Too Rich Bank 1</a></h4>
									<p style="margin: 0; color: #666; font-size: 14px;">Rich fuel mixture can contaminate O2 sensor causing P0131</p>
								</div>
								<div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #9c27b0;">
									<h4 style="margin-bottom: 10px;"><a href="p0420.html" style="color: #9c27b0; text-decoration: none;">P0420 - Catalyst System Efficiency</a></h4>
									<p style="margin: 0; color: #666; font-size: 14px;">Faulty upstream O2 sensor can trigger catalyst efficiency codes</p>
								</div>
							</div>
						</div>

						<div style="background: #e3f2fd; padding: 20px; border-radius: 10px; border-left: 4px solid #2196F3;">
							<h4 style="color: #1976D2; margin-bottom: 15px;"><i class="fa fa-lightbulb-o"></i> Diagnostic Tip</h4>
							<p style="margin: 0; color: #333;">P0131 specifically indicates LOW voltage, meaning the sensor is "stuck lean." This is different from P0134 (no activity) or P0130 (general malfunction). Always check for rich fuel conditions (P0172) that may have contaminated the sensor before replacement.</p>
						</div>
					</div>
				</div>

				<div class="col-md-4">
					<!-- GeekOBD APP Promotion -->
					<div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
						<h4 style="margin-bottom: 20px; color: white;"><i class="fa fa-mobile"></i> Monitor O2 Sensor Voltage</h4>
						<p style="margin-bottom: 20px; opacity: 0.9; font-size: 14px;">Track Bank 1 Sensor 1 voltage and validate low voltage diagnosis with our GeekOBD APP!</p>
						<ul style="margin-bottom: 20px; padding-left: 20px; font-size: 14px;">
							<li style="margin-bottom: 8px;">Real-time O2 sensor voltage readings</li>
							<li style="margin-bottom: 8px;">Bank 1 Sensor 1 monitoring</li>
							<li style="margin-bottom: 8px;">Low voltage detection</li>
							<li style="margin-bottom: 8px;">Sensor switching validation</li>
						</ul>
						<a href="../app.html" class="btn btn-block" style="background: white; color: #667eea; border: none; border-radius: 25px; font-weight: bold; margin-bottom: 10px;">
							<i class="fa fa-download"></i> Download APP
						</a>
						<a href="../hardware.html" class="btn btn-block" style="background: rgba(255,255,255,0.2); color: white; border: 2px solid white; border-radius: 25px; font-weight: bold;">
							<i class="fa fa-shopping-cart"></i> Get MOBD Adapter
						</a>
					</div>

					<!-- Code Information -->
					<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
						<h4 style="margin-bottom: 20px;"><i class="fa fa-info"></i> Code Information</h4>
						<table class="table table-borderless">
							<tr>
								<td><strong>Code:</strong></td>
								<td>P0131</td>
							</tr>
							<tr>
								<td><strong>System:</strong></td>
								<td>Emissions</td>
							</tr>
							<tr>
								<td><strong>Severity:</strong></td>
								<td><span class="severity-badge severity-medium">MEDIUM</span></td>
							</tr>
							<tr>
								<td><strong>Category:</strong></td>
								<td>O2 Sensor Codes</td>
							</tr>
						</table>
					</div>

					<!-- Popular Codes -->
					<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
						<h4 style="margin-bottom: 20px;"><i class="fa fa-star"></i> Related O2 Sensor Codes</h4>
						<ul style="list-style: none; padding: 0;">
							<li style="margin-bottom: 10px;"><a href="p0130.html" style="color: #667eea;">P0130 - O2 Sensor Circuit Malfunction</a></li>
							<li style="margin-bottom: 10px;"><a href="p0132.html" style="color: #667eea;">P0132 - O2 Sensor High Voltage</a></li>
							<li style="margin-bottom: 10px;"><a href="p0133.html" style="color: #667eea;">P0133 - O2 Sensor Slow Response</a></li>
							<li style="margin-bottom: 10px;"><a href="p0134.html" style="color: #667eea;">P0134 - O2 Sensor No Activity</a></li>
							<li><a href="../dtc-codes.html" style="color: #667eea;">View All Codes →</a></li>
						</ul>
					</div>

					<!-- Diagnostic Tools -->
					<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
						<h4 style="margin-bottom: 20px;"><i class="fa fa-wrench"></i> Diagnostic Resources</h4>
						<ul style="list-style: none; padding: 0;">
							<li style="margin-bottom: 10px;"><a href="../obd-diagnostic-guide.html" style="color: #667eea;"><i class="fa fa-book"></i> OBD Diagnostic Guide</a></li>
							<li style="margin-bottom: 10px;"><a href="../vehicle-compatibility.html" style="color: #667eea;"><i class="fa fa-car"></i> Vehicle Compatibility</a></li>
							<li style="margin-bottom: 10px;"><a href="../fuel-efficiency-monitoring.html" style="color: #667eea;"><i class="fa fa-tachometer"></i> Fuel Efficiency Tips</a></li>
							<li style="margin-bottom: 10px;"><a href="../support.html" style="color: #667eea;"><i class="fa fa-support"></i> Technical Support</a></li>
							<li><a href="../blog.html" style="color: #667eea;"><i class="fa fa-newspaper-o"></i> Latest Articles</a></li>
						</ul>
					</div>

					<!-- Quick Navigation -->
					<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
						<h4 style="margin-bottom: 20px;"><i class="fa fa-compass"></i> Quick Navigation</h4>
						<div style="display: flex; flex-direction: column; gap: 10px;">
							<a href="#quick-answer" style="color: #667eea; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
								<i class="fa fa-bolt"></i> Quick Answer
							</a>
							<a href="#ai-qa" style="color: #667eea; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
								<i class="fa fa-comments"></i> Common Questions
							</a>
							<a href="#symptoms" style="color: #667eea; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
								<i class="fa fa-exclamation-triangle"></i> Symptoms
							</a>
							<a href="#causes" style="color: #667eea; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
								<i class="fa fa-search"></i> Causes
							</a>
							<a href="#cost-info" style="color: #667eea; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
								<i class="fa fa-calculator"></i> Repair Costs
							</a>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<div class="footer">
		<div class="container">
			<div class="row">
				<div class="col-md-12 text-center">
					<p>&copy; 2025 Beijing MentalRoad Technology Co., Ltd. All rights reserved.</p>
				</div>
			</div>
		</div>
	</div>
</div>

<script src="../js/jquery.min.js"></script>
<script src="../js/bootstrap.js"></script>
<script src="../js/superfish.js"></script>
<script src="../js/custom.js"></script>

</body>
</html>