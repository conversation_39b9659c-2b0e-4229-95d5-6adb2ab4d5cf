<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>P0121 - TPS Range/Performance | GeekOBD</title>
    <meta name="description" content="The Engine Control Module has detected that the Throttle Position Sensor signal is outside the expected range or not performing within specifications.">
    <meta name="keywords" content="P0121, P0121, diagnostic trouble code, OBD2, car repair, engine problems, GeekOBD">
    <link rel="canonical" href="https://www.geekobd.com/dtc-codes/p0121.html">
    
    <!-- Open Graph -->
    <meta property="og:title" content="P0121 - TPS Range/Performance">
    <meta property="og:description" content="The Engine Control Module has detected that the Throttle Position Sensor signal is outside the expected range or not performing within specifications.">
    <meta property="og:url" content="https://www.geekobd.com/dtc-codes/p0121.html">
    <meta property="og:type" content="article">
    
    <!-- CSS -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../css/fonts/font-awesome/css/font-awesome.css">
    <link rel="stylesheet" href="../css/animations.css" media="screen">
    <link rel="stylesheet" href="../css/superfish.css" media="screen">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/colors/blue.css" id="colors">
    <link rel="stylesheet" href="../css/theme-responsive.css">
    <link rel="stylesheet" href="../css/seo-enhancements.css">
    <link rel="shortcut icon" href="../img/ico/favicon.ico">

    <style>
    .dtc-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 60px 0 40px;
    }
    .dtc-code-badge {
        display: inline-block;
        background: rgba(255,255,255,0.2);
        padding: 10px 20px;
        border-radius: 50px;
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 20px;
    }
    .severity-badge {
        display: inline-block;
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
        text-transform: uppercase;
        margin-left: 15px;
    }
    .severity-high { background: #ff4757; color: white; }
    .severity-medium { background: #ffa502; color: white; }
    .content-section { padding: 50px 0; }
    .info-box {
        background: #f8f9fa;
        border-left: 4px solid #667eea;
        padding: 20px;
        margin: 20px 0;
        border-radius: 5px;
    }
    .warning-box {
        background: #fff3cd;
        border-left: 4px solid #ffc107;
        padding: 20px;
        margin: 20px 0;
        border-radius: 5px;
    }
    .danger-box {
        background: #f8d7da;
        border-left: 4px solid #dc3545;
        padding: 20px;
        margin: 20px 0;
        border-radius: 5px;
    }
    .breadcrumb-custom {
        background: none;
        padding: 20px 0;
        margin: 0;
    }
    .breadcrumb-custom a {
        color: #667eea;
        text-decoration: none;
    }
    .breadcrumb-custom a:hover {
        text-decoration: underline;
    }
    </style>

    
<!-- Article Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": "P0121 - TPS Range/Performance",
  "description": "The Engine Control Module has detected that the Throttle Position Sensor signal is outside the expected range or not performing within specifications.",
  "author": {
    "@type": "Organization",
    "name": "GeekOBD",
    "url": "https://www.geekobd.com"
  },
  "publisher": {
    "@type": "Organization",
    "name": "GeekOBD",
    "logo": {
      "@type": "ImageObject",
      "url": "https://www.geekobd.com/images/logo.png"
    }
  },
  "datePublished": "2025-07-31",
  "dateModified": "2025-07-31",
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "https://www.geekobd.com/dtc-codes/p0121.html"
  }
}
</script>

<!-- FAQ Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "What's the difference between P0121 and P0120 TPS codes?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "P0121 indicates the TPS is working electrically but providing readings outside expected range, while P0120 indicates an electrical circuit problem. P0121 is often caused by dirty throttle body or worn sensor, while P0120 is typically wiring or connector issues."
      }
    },
    {
      "@type": "Question",
      "name": "Can a dirty throttle body cause P0121?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Yes, carbon buildup on the throttle body is one of the most common causes of P0121. Carbon deposits can prevent the throttle blade from closing completely or moving smoothly, causing TPS readings that don't match expected values for the throttle position."
      }
    },
    {
      "@type": "Question",
      "name": "How do I test TPS sensor for P0121?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Use GeekOBD APP to monitor TPS voltage while slowly moving throttle from closed to wide open. Voltage should increase smoothly from about 0.5V to 4.5V without jumps or dead spots. Erratic readings or values outside this range indicate TPS problems."
      }
    },
    {
      "@type": "Question",
      "name": "Why does P0121 affect transmission shifting?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "The transmission uses TPS data to determine shift points and shift firmness based on driver demand. When TPS readings are inaccurate, the transmission may shift too early, too late, or with incorrect firmness, leading to poor performance and potential transmission damage."
      }
    }
  ]
}
</script>

<!-- HowTo Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "HowTo",
  "name": "How to Diagnose P0121 TPS Range/Performance",
  "description": "Step-by-step guide to diagnose and fix P0121",
  "totalTime": "PT90M",
  "estimatedCost": {
    "@type": "MonetaryAmount",
    "currency": "USD",
    "value": "$120-$450 for most P0121 repairs"
  },
  "tool": [
    {
      "@type": "HowToTool",
      "name": "GeekOBD APP with MOBD Adapter",
      "description": "Professional OBD2 diagnostic tool",
      "url": "https://www.geekobd.com/app.html"
    }
  ],
  "step": [
    {
      "@type": "HowToStep",
      "position": 1,
      "name": "Monitor TPS Performance",
      "text": "Connect GeekOBD APP and monitor TPS voltage while slowly moving throttle from closed to wide open. Look for smooth voltage increase from 0.5V to 4.5V without jumps or dead spots."
    },
    {
      "@type": "HowToStep",
      "position": 2,
      "name": "Visual Inspection",
      "text": "Remove air intake and inspect throttle body for carbon buildup, damaged throttle blade, or worn throttle shaft. Check TPS mounting and alignment."
    },
    {
      "@type": "HowToStep",
      "position": 3,
      "name": "Throttle Body Cleaning",
      "text": "Clean throttle body thoroughly with appropriate cleaner, removing all carbon deposits from blade, bore, and TPS area. Allow to dry completely before reassembly."
    },
    {
      "@type": "HowToStep",
      "position": 4,
      "name": "TPS Calibration Test",
      "text": "Test TPS calibration by checking voltage at idle (should be 0.5-0.9V) and wide open throttle (should be 4.0-4.8V). Verify smooth transition between positions."
    },
    {
      "@type": "HowToStep",
      "position": 5,
      "name": "Component Replacement and Relearn",
      "text": "Replace TPS sensor or throttle body as needed. Clear codes and perform throttle relearn procedure if required by vehicle manufacturer."
    }
  ]
}
</script>
</head>
<body>
    <div class="wrap">
	<header id="header" role="banner">
	<div class="main-header">
	<div class="container">
	<div class="row">
	<div class="col-md-3">
	<div class="logo pull-left">
	<h1> <a href="../index.html"> <img src="../img/logo.png" alt="MOBD"> </a> </h1>
	</div>
	</div>
	<div class="col-md-9">
	<div class="mobile navbar-header"> <a class="navbar-toggle" data-toggle="collapse" data-target=".menu"> <i class="icon-reorder icon-2x"></i> </a> </div>
	<nav class="collapse navbar-collapse menu" role="navigation" aria-label="Main navigation">
	<ul class="nav navbar-nav sf-menu">
	<li><a href="../index.html" class="sf-with-ul">Home</a></li>
	<li><a href="../app.html" class="sf-with-ul">APP</a></li>
	<li><a href="javascript:;;" class="sf-with-ul">Adaptor <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
	<ul>
	<li><a href="../hardware2.html" class="sf-with-ul">MOBD GPS</a></li>
	<li><a href="../hardware.html" class="sf-with-ul">MOBD</a></li>
	</ul>
	</li>
	<li><a href="javascript:;;" class="sf-with-ul">Resources <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
	<ul>
	<li><a href="../dtc-codes.html" class="sf-with-ul">DTC Codes</a></li>
	<li><a href="../obd-diagnostic-guide.html" class="sf-with-ul">OBD Guide</a></li>
	<li><a href="../vehicle-compatibility.html" class="sf-with-ul">Compatibility</a></li>
	<li><a href="../fuel-efficiency-monitoring.html" class="sf-with-ul">Fuel Efficiency</a></li>
	<li><a href="../support.html" class="sf-with-ul">Support</a></li>
	<li><a href="../blog.html" class="sf-with-ul">Blog</a></li>
	</ul>
	</li>
	<li><a href="../about.html" class="sf-with-ul">About Us</a></li>
	<li><a href="//www.mobd.cn" class="sf-with-ul" target="_blank">中文版</a></li>
	</ul>
	</nav>
	</div>
	</div>
	</div>
	</div>
	</header>

    <!-- Breadcrumb -->
    <div class="container">
        <nav class="breadcrumb-custom">
            <a href="../index.html">Home</a> &raquo;
            <a href="../dtc-codes.html">DTC Codes</a> &raquo;
            <a href="../dtc-codes.html#engine">Engine Codes</a> &raquo;
            <span>P0121</span>
        </nav>
    </div>

	<section class="dtc-header">
		<div class="container">
			<div class="row">
				<div class="col-md-12">
					<div class="dtc-code-badge">P0121</div>
					<span class="severity-badge severity-medium">MEDIUM Priority</span>
					<h1 style="margin: 20px 0; font-size: 36px;">TPS Range/Performance</h1>
					<p style="font-size: 18px; opacity: 0.9; margin-bottom: 0;">The Engine Control Module has detected that the Throttle Position Sensor signal is outside the expected range or not performing within specifications.</p>
				</div>
			</div>
		</div>
	</section>

    <!-- Main Content -->
    <section class="content-section">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    
<!-- Quick Answer Section for AI -->
<div id="quick-answer" style="background: #e8f4fd; border: 2px solid #4a90e2; border-radius: 10px; padding: 25px; margin-bottom: 30px;">
    <h2 style="color: #2c5aa0; margin-bottom: 15px;"><i class="fa fa-tachometer"></i> Quick Answer</h2>
    <p style="font-size: 18px; font-weight: 500; margin-bottom: 15px; color: #333;">
        <strong>P0121 means:</strong> TPS sensor reading outside expected range - usually dirty sensor or throttle body carbon buildup.
    </p>
    <div style="display: flex; flex-wrap: wrap; gap: 15px; margin-bottom: 15px;">
        <span style="background: #4CAF50; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
            <i class="fa fa-wrench"></i> Fix: Clean throttle body, replace TPS sensor, check throttle linkage
        </span>
        <span style="background: #FF9800; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
            <i class="fa fa-dollar"></i> Cost: $120-$450
        </span>
        <span style="background: #2196F3; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
            <i class="fa fa-clock-o"></i> Time: 60-150 minutes
        </span>
    </div>
    <p style="margin: 0; color: #666; font-size: 14px;">
        <strong>Can I drive with P0121?</strong> Safe to drive but expect poor throttle response and performance. Clean throttle body first as simple fix.
    </p>
</div>
                    
<!-- AI-Friendly Q&A Section -->
<div id="ai-qa" style="background: white; border-radius: 10px; padding: 25px; margin-bottom: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
    <h2><i class="fa fa-comments"></i> Common Questions</h2>
    
    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">What's the difference between P0121 and P0120 TPS codes?</h3>
        <p style="color: #666; line-height: 1.6;">P0121 indicates the TPS is working electrically but providing readings outside expected range, while P0120 indicates an electrical circuit problem. P0121 is often caused by dirty throttle body or worn sensor, while P0120 is typically wiring or connector issues.</p>
    </div>
    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">Can a dirty throttle body cause P0121?</h3>
        <p style="color: #666; line-height: 1.6;">Yes, carbon buildup on the throttle body is one of the most common causes of P0121. Carbon deposits can prevent the throttle blade from closing completely or moving smoothly, causing TPS readings that don't match expected values for the throttle position.</p>
    </div>
    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">How do I test TPS sensor for P0121?</h3>
        <p style="color: #666; line-height: 1.6;">Use GeekOBD APP to monitor TPS voltage while slowly moving throttle from closed to wide open. Voltage should increase smoothly from about 0.5V to 4.5V without jumps or dead spots. Erratic readings or values outside this range indicate TPS problems.</p>
    </div>
    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; ">
        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">Why does P0121 affect transmission shifting?</h3>
        <p style="color: #666; line-height: 1.6;">The transmission uses TPS data to determine shift points and shift firmness based on driver demand. When TPS readings are inaccurate, the transmission may shift too early, too late, or with incorrect firmness, leading to poor performance and potential transmission damage.</p>
    </div>
</div>
                    
<!-- Main Content Section -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h2><i class="fa fa-info-circle"></i> What is P0121?</h2>
    <p style="font-size: 16px; line-height: 1.6; color: #333; margin-bottom: 20px;">The Engine Control Module has detected that the Throttle Position Sensor (TPS) signal is outside the expected range or not performing within specifications. The TPS measures throttle blade position to help the ECM determine driver intent and calculate proper fuel injection and ignition timing. When the TPS reading doesn't correlate with other engine parameters like MAP sensor, MAF sensor, or expected throttle response, P0121 is triggered.</p>

    <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; margin: 20px 0;">
        <strong><i class="fa fa-exclamation-triangle"></i> Performance Impact:</strong> P0121 causes poor throttle response, inconsistent engine performance, transmission shifting problems, and reduced fuel economy due to the ECM's inability to accurately determine driver intent and optimize engine operation.
    </div>
</div>

<!-- Symptoms Section -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h2><i class="fa fa-stethoscope"></i> Symptoms of P0121</h2>
    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
        <ul style="margin: 0; padding-left: 20px;">
            <li><strong>Check engine light illuminated - ECM detected TPS performance issue</strong></li>
								<li><strong>Poor throttle response - Delayed or inconsistent acceleration</strong></li>
								<li><strong>Engine hesitation during acceleration - TPS not accurately reporting throttle position</strong></li>
								<li><strong>Rough idle or stalling - Incorrect idle throttle position readings</strong></li>
								<li><strong>Engine surging at cruise speeds - Inconsistent TPS readings causing fuel fluctuations</strong></li>
								<li><strong>Transmission shifting problems - TPS data affects automatic transmission operation</strong></li>
								<li><strong>Reduced power output - ECM limiting performance due to TPS uncertainty</strong></li>
								<li><strong>Poor fuel economy - Non-optimized fuel delivery from inaccurate TPS data</strong></li>
								<li><strong>Cruise control malfunction - System requires accurate throttle position feedback</strong></li>
        </ul>
    </div>
</div>

<!-- Causes Section -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h2><i class="fa fa-search"></i> Common Causes of P0121</h2>
    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
        <ol style="margin: 0; padding-left: 20px;">
            <li>Dirty or contaminated TPS sensor - Carbon buildup affecting sensor operation</li>
									<li>Worn TPS sensor - Internal components degraded from age and use</li>
									<li>Throttle body carbon buildup - Restricting throttle blade movement</li>
									<li>Damaged throttle shaft or bushings - Causing erratic throttle blade position</li>
									<li>Loose TPS mounting - Sensor not properly aligned with throttle shaft</li>
									<li>Vacuum leaks affecting throttle operation - Unmetered air changing expected readings</li>
									<li>Faulty throttle cable or linkage - Mechanical problems affecting throttle response</li>
									<li>ECM calibration issues - Software not properly interpreting TPS signals</li>
        </ol>
    </div>
</div>
                    
<!-- Cost Analysis Section -->
<div id="cost-info" style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
    <h2><i class="fa fa-calculator"></i> P0121 Repair Costs</h2>

    <div style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin: 20px 0;">
        <h3 style="color: #2c3e50; margin-bottom: 20px;">Cost Breakdown by Repair Type</h3>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 25px;">
            
        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #4CAF50;">
            <h4 style="color: #4CAF50; margin-bottom: 10px;"><i class="fa fa-refresh"></i> Throttle Body Cleaning</h4>
            <p style="margin-bottom: 15px; color: #666;">Most common first step - Clean carbon buildup (40% success rate)</p>
            <ul style="list-style: none; padding: 0;">
                <li style="margin-bottom: 8px;"><strong>Throttle body cleaner:</strong> $12-$25</li>
                <li style="margin-bottom: 8px;"><strong>Labor (45-75 minutes):</strong> $60-$150</li>
                <li style="margin-bottom: 8px;"><strong>Total:</strong> <span style="color: #4CAF50; font-weight: bold;">$72-$175</span></li>
                <li style="color: #666; font-size: 14px;">Success rate: ~40% success rate%</li>
            </ul>
        </div>
        
        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #2196F3;">
            <h4 style="color: #2196F3; margin-bottom: 10px;"><i class="fa fa-tachometer"></i> TPS Sensor Replacement</h4>
            <p style="margin-bottom: 15px; color: #666;">Replace worn or damaged sensor (50% of cases)</p>
            <ul style="list-style: none; padding: 0;">
                <li style="margin-bottom: 8px;"><strong>TPS sensor:</strong> $45-$120</li>
                <li style="margin-bottom: 8px;"><strong>Labor (60-90 minutes):</strong> $80-$180</li>
                <li style="margin-bottom: 8px;"><strong>Total:</strong> <span style="color: #2196F3; font-weight: bold;">$125-$300</span></li>
                <li style="color: #666; font-size: 14px;">Success rate: ~95% success rate%</li>
            </ul>
        </div>
        
        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #FF9800;">
            <h4 style="color: #FF9800; margin-bottom: 10px;"><i class="fa fa-cog"></i> Throttle Body Replacement</h4>
            <p style="margin-bottom: 15px; color: #666;">Replace entire throttle body if shaft/bushings worn (10% of cases)</p>
            <ul style="list-style: none; padding: 0;">
                <li style="margin-bottom: 8px;"><strong>Throttle body assembly:</strong> $200-$450</li>
                <li style="margin-bottom: 8px;"><strong>Labor (1.5-2.5 hours):</strong> $150-$300</li>
                <li style="margin-bottom: 8px;"><strong>Total:</strong> <span style="color: #FF9800; font-weight: bold;">$350-$750</span></li>
                <li style="color: #666; font-size: 14px;">Success rate: ~98% success rate%</li>
            </ul>
        </div>
        </div>

        <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #4CAF50;">
            <h4 style="color: #2e7d32; margin-bottom: 15px;"><i class="fa fa-lightbulb-o"></i> Money-Saving Tips</h4>
            <ul style="margin: 0; color: #2e7d32;">
                <li style="margin-bottom: 8px;">Always try throttle body cleaning first - fixes 40% of P0121 cases for under $100</li>
                <li style="margin-bottom: 8px;">Use GeekOBD APP to test TPS response before and after cleaning</li>
                <li style="margin-bottom: 8px;">Check for vacuum leaks before replacing expensive components</li>
                <li style="margin-bottom: 8px;">TPS replacement is often DIY-friendly, saving $80-180 in labor</li>
                <li style="margin-bottom: 8px;">Consider throttle body service as preventive maintenance every 60k miles</li>
            </ul>
        </div>
    </div>
</div>
                    
<!-- Diagnostic Steps Section -->
<div id="diagnostic-steps" style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
    <h2><i class="fa fa-stethoscope"></i> Diagnostic Steps</h2>

    <div style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin: 20px 0;">
        <h3 style="color: #2c3e50; margin-bottom: 20px;">Professional P0121 Diagnosis Process</h3>
        <p style="margin-bottom: 20px; color: #666;">Follow these systematic steps to accurately diagnose P0121. Each step builds on the previous one to ensure accurate diagnosis.</p>

        
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-line-chart"></i> Step 1: Monitor TPS Performance</h4>
            <p style="margin-bottom: 15px; color: #333;">Connect GeekOBD APP and monitor TPS voltage while slowly moving throttle from closed to wide open. Look for smooth voltage increase from 0.5V to 4.5V without jumps or dead spots.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> GeekOBD APP can graph TPS voltage over time - look for erratic readings, stuck values, or voltage outside normal 0.5-4.5V range.
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-eye"></i> Step 2: Visual Inspection</h4>
            <p style="margin-bottom: 15px; color: #333;">Remove air intake and inspect throttle body for carbon buildup, damaged throttle blade, or worn throttle shaft. Check TPS mounting and alignment.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> Use GeekOBD APP to monitor TPS readings while manually moving throttle blade - readings should change smoothly with blade movement.
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-refresh"></i> Step 3: Throttle Body Cleaning</h4>
            <p style="margin-bottom: 15px; color: #333;">Clean throttle body thoroughly with appropriate cleaner, removing all carbon deposits from blade, bore, and TPS area. Allow to dry completely before reassembly.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> Monitor TPS readings with GeekOBD APP after cleaning - improved linearity and proper voltage range indicate successful cleaning.
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-cogs"></i> Step 4: TPS Calibration Test</h4>
            <p style="margin-bottom: 15px; color: #333;">Test TPS calibration by checking voltage at idle (should be 0.5-0.9V) and wide open throttle (should be 4.0-4.8V). Verify smooth transition between positions.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> GeekOBD APP can perform TPS sweep test - voltage should increase linearly without jumps or dead spots throughout throttle range.
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-check-circle"></i> Step 5: Component Replacement and Relearn</h4>
            <p style="margin-bottom: 15px; color: #333;">Replace TPS sensor or throttle body as needed. Clear codes and perform throttle relearn procedure if required by vehicle manufacturer.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> Use GeekOBD APP to verify TPS readings are now within specification and respond properly to throttle input after replacement.
            </div>
        </div>

        <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin-top: 20px; border-left: 4px solid #ffc107;">
            <h4 style="color: #856404; margin-bottom: 15px;"><i class="fa fa-exclamation-triangle"></i> Important Notes</h4>
            <ul style="margin: 0; color: #856404;">
                <li style="margin-bottom: 8px;">Clean throttle body first - resolves 40% of P0121 cases</li>
                <li style="margin-bottom: 8px;">TPS voltage should increase smoothly from 0.5V to 4.5V</li>
                <li style="margin-bottom: 8px;">Some vehicles require throttle relearn procedure after service</li>
            </ul>
        </div>
    </div>
</div>
                    
<!-- Real Repair Case Studies -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
    <h2><i class="fa fa-file-text"></i> Real Repair Case Studies</h2>
    
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2;"><i class="fa fa-car"></i> Case 1: Honda Accord Carbon Buildup</h4>
            <p><strong>Vehicle:</strong> 2016 Honda Accord 2.4L 4-cylinder, 95,000 miles</p>
            <p><strong>Problem:</strong> Customer reported poor acceleration, engine hesitation, and occasional stalling at idle. P0121 code was present with rough idle symptoms.</p>
            <p><strong>Diagnosis:</strong> GeekOBD APP showed TPS voltage was erratic and wouldn't drop below 1.2V at idle (should be 0.5-0.9V). Visual inspection revealed heavy carbon buildup preventing throttle blade from closing completely.</p>
            <p><strong>Solution:</strong> Thoroughly cleaned throttle body with throttle body cleaner, removing all carbon deposits. Performed throttle relearn procedure as specified by Honda.</p>
            <p><strong>Cost:</strong> Throttle body cleaner: $18, Labor: $95, Total: $113</p>
            <p><strong>Result:</strong> P0121 code cleared immediately. TPS now reads 0.7V at idle and 4.4V at WOT. Engine idles smoothly and acceleration response fully restored.</p>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 4px solid #28a745;">
            <h4 style="color: #28a745;"><i class="fa fa-car"></i> Case 2: Ford Escape Worn TPS Sensor</h4>
            <p><strong>Vehicle:</strong> 2015 Ford Escape 1.6L Turbo, 118,000 miles</p>
            <p><strong>Problem:</strong> Intermittent P0121 code with poor throttle response and transmission shifting harshly. Problem seemed to worsen with engine temperature.</p>
            <p><strong>Diagnosis:</strong> Throttle body was clean, but GeekOBD APP showed TPS voltage had dead spots between 2.0-2.5V where voltage wouldn't change despite throttle movement. Internal TPS wear was causing signal dropout.</p>
            <p><strong>Solution:</strong> Replaced TPS sensor with OEM part. Sensor was integrated into throttle body, requiring complete throttle body replacement on this model.</p>
            <p><strong>Cost:</strong> Throttle body assembly: $285, Labor: $150, Total: $435</p>
            <p><strong>Result:</strong> P0121 code cleared and has not returned. TPS voltage now increases smoothly throughout range and transmission shifting returned to normal.</p>
        </div>
</div>
                </div>

                <div class="col-md-4">
                    
<!-- GeekOBD APP Promotion -->
<div style="background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%); color: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: white;"><i class="fa fa-mobile"></i> Diagnose P0121</h4>
    <p style="margin-bottom: 20px; opacity: 0.9; font-size: 14px;">Use GeekOBD APP for comprehensive TPS performance testing!</p>
    <ul style="margin-bottom: 20px; padding-left: 20px; font-size: 14px;">
        <li style="margin-bottom: 8px;">Real-time TPS voltage monitoring</li>
        <li style="margin-bottom: 8px;">Throttle sweep testing</li>
        <li style="margin-bottom: 8px;">Performance verification</li>
        <li style="margin-bottom: 8px;">Calibration assistance</li>
    </ul>
    <a href="https://www.geekobd.com/app.html" style="display: inline-block; background: rgba(255,255,255,0.2); color: white; padding: 12px 25px; border-radius: 25px; text-decoration: none; font-weight: bold; transition: all 0.3s;">
        <i class="fa fa-download"></i> Download GeekOBD APP
    </a>
</div>

<!-- Related System Codes -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-link"></i> TPS Sensor Codes</h4>
    <p style="margin-bottom: 15px; color: #666; font-size: 14px;">Related throttle position sensor codes:</p>
    <div style="margin-bottom: 15px;">
        
                <a href="p0120.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #e74c3c;">
                    <strong style="color: #e74c3c;">P0120</strong> - TPS Circuit Malfunction - Electrical circuit problems
                </a>
                <a href="p0122.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #3498db;">
                    <strong style="color: #3498db;">P0122</strong> - TPS Low Input - Sensor reading too low voltage
                </a>
                <a href="p0123.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #f39c12;">
                    <strong style="color: #f39c12;">P0123</strong> - TPS High Input - Sensor reading too high voltage
                </a>
                <a href="p0124.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #9b59b6;">
                    <strong style="color: #9b59b6;">P0124</strong> - TPS Intermittent - Intermittent sensor readings
                </a>
                <a href="p0171.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #9b59b6;">
                    <strong style="color: #9b59b6;">P0171</strong> - System Too Lean Bank 1 - Can be caused by TPS problems
                </a>
                <a href="p0172.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #9b59b6;">
                    <strong style="color: #9b59b6;">P0172</strong> - System Too Rich Bank 1 - Can be caused by TPS problems
                </a>
                <a href="p0300.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #e67e22;">
                    <strong style="color: #e67e22;">P0300</strong> - Random Misfire - Poor performance from incorrect throttle data
                </a>
    </div>
</div>

<!-- Diagnostic Resources -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-tools"></i> Diagnostic Resources</h4>
    <div style="margin-bottom: 20px;">
        
        <a href="#diagnostic-steps" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-tachometer" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">TPS Performance Testing</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">Professional procedures for testing TPS sensor operation</span>
        </a>
        <a href="../resources/throttle-body-service.html" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-refresh" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">Throttle Body Service</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">Complete throttle body cleaning and maintenance guide</span>
        </a>
        <a href="../resources/tps-calibration.html" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-cogs" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">TPS Calibration</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">Throttle position sensor calibration and relearn procedures</span>
        </a>
        <a href="../resources/throttle-response-issues.html" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-dashboard" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">Throttle Response Issues</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">Diagnosing and fixing poor throttle response problems</span>
        </a>
    </div>
</div>

<!-- Quick Navigation -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-compass"></i> Quick Navigation</h4>
    <div style="display: flex; flex-direction: column; gap: 8px;">
        <a href="#quick-answer" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-bolt"></i> Quick Answer
        </a>
        <a href="#ai-qa" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-comments"></i> Common Questions
        </a>
        <a href="#cost-info" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-calculator"></i> Repair Costs
        </a>
        <a href="#diagnostic-steps" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-stethoscope"></i> Diagnostic Steps
        </a>
    </div>
</div>

<!-- Code Information -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-info-circle"></i> Code Information</h4>
    <table style="width: 100%; font-size: 14px;">
        <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Code:</strong></td>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;">P0121</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>System:</strong></td>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;">Engine Management</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Severity:</strong></td>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><span class="severity-badge severity-medium">MEDIUM</span></td>
        </tr>
        <tr>
            <td style="padding: 8px 0;"><strong>Category:</strong></td>
            <td style="padding: 8px 0;">Sensor Performance</td>
        </tr>
    </table>
</div>
                </div>
            </div>
        </div>
    </section>

    </div>

<script src="../js/jquery.min.js"></script>
<script src="../js/bootstrap.js"></script>
<script src="../js/superfish.js"></script>
<script src="../js/custom.js"></script>
</body>
</html>