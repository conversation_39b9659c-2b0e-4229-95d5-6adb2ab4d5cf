<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>P0402 - EGR Flow Excessive | GeekOBD</title>
    <meta name="description" content="The Engine Control Module has detected excessive exhaust gas recirculation flow.">
    <meta name="keywords" content="P0402, P0402, diagnostic trouble code, OBD2, car repair, engine problems, GeekOBD">
    <link rel="canonical" href="https://www.geekobd.com/dtc-codes/p0402.html">
    
    <!-- Open Graph -->
    <meta property="og:title" content="P0402 - EGR Flow Excessive">
    <meta property="og:description" content="The Engine Control Module has detected excessive exhaust gas recirculation flow.">
    <meta property="og:url" content="https://www.geekobd.com/dtc-codes/p0402.html">
    <meta property="og:type" content="article">
    
    <!-- CSS -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../css/fonts/font-awesome/css/font-awesome.css">
    <link rel="stylesheet" href="../css/animations.css" media="screen">
    <link rel="stylesheet" href="../css/superfish.css" media="screen">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/colors/blue.css" id="colors">
    <link rel="stylesheet" href="../css/theme-responsive.css">
    <link rel="stylesheet" href="../css/seo-enhancements.css">
    <link rel="shortcut icon" href="../img/ico/favicon.ico">

    <style>
    .dtc-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 60px 0 40px;
    }
    .dtc-code-badge {
        display: inline-block;
        background: rgba(255,255,255,0.2);
        padding: 10px 20px;
        border-radius: 50px;
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 20px;
    }
    .severity-badge {
        display: inline-block;
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
        text-transform: uppercase;
        margin-left: 15px;
    }
    .severity-high { background: #ff4757; color: white; }
    .severity-medium { background: #ffa502; color: white; }
    .content-section { padding: 50px 0; }
    .info-box {
        background: #f8f9fa;
        border-left: 4px solid #667eea;
        padding: 20px;
        margin: 20px 0;
        border-radius: 5px;
    }
    .warning-box {
        background: #fff3cd;
        border-left: 4px solid #ffc107;
        padding: 20px;
        margin: 20px 0;
        border-radius: 5px;
    }
    .danger-box {
        background: #f8d7da;
        border-left: 4px solid #dc3545;
        padding: 20px;
        margin: 20px 0;
        border-radius: 5px;
    }
    .breadcrumb-custom {
        background: none;
        padding: 20px 0;
        margin: 0;
    }
    .breadcrumb-custom a {
        color: #667eea;
        text-decoration: none;
    }
    .breadcrumb-custom a:hover {
        text-decoration: underline;
    }
    </style>

    
<!-- Article Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": "P0402 - EGR Flow Excessive",
  "description": "The Engine Control Module has detected excessive exhaust gas recirculation flow.",
  "author": {
    "@type": "Organization",
    "name": "GeekOBD",
    "url": "https://www.geekobd.com"
  },
  "publisher": {
    "@type": "Organization",
    "name": "GeekOBD",
    "logo": {
      "@type": "ImageObject",
      "url": "https://www.geekobd.com/images/logo.png"
    }
  },
  "datePublished": "2025-07-31",
  "dateModified": "2025-07-31",
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "https://www.geekobd.com/dtc-codes/p0402.html"
  }
}
</script>

<!-- FAQ Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "What's the difference between P0401 and P0402?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "P0401 indicates insufficient EGR flow (too little), while P0402 indicates excessive EGR flow (too much). P0402 typically causes more noticeable drivability problems like rough idle and stalling because too much EGR severely affects combustion."
      }
    },
    {
      "@type": "Question",
      "name": "Can excessive EGR flow damage my engine?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Excessive EGR flow typically won't cause immediate engine damage, but it can cause poor combustion, carbon buildup, and increased wear over time. The main concerns are poor performance and potential stalling while driving."
      }
    },
    {
      "@type": "Question",
      "name": "Why does my engine stall with P0402?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Excessive EGR flow dilutes the air/fuel mixture with inert exhaust gases, reducing the oxygen available for combustion. This can make the mixture too lean to sustain combustion, especially at idle, causing stalling."
      }
    },
    {
      "@type": "Question",
      "name": "How do I test if my EGR valve is stuck open?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Use GeekOBD APP to command EGR valve closed while monitoring engine RPM at idle. If RPM doesn't increase when valve is commanded closed, the valve is likely stuck open. You can also manually check if the valve closes properly."
      }
    }
  ]
}
</script>

<!-- HowTo Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "HowTo",
  "name": "How to Diagnose P0402 EGR Flow Excessive",
  "description": "Step-by-step guide to diagnose and fix P0402",
  "totalTime": "PT90M",
  "estimatedCost": {
    "@type": "MonetaryAmount",
    "currency": "USD",
    "value": "$180-$650 for most P0402 repairs"
  },
  "tool": [
    {
      "@type": "HowToTool",
      "name": "GeekOBD APP with MOBD Adapter",
      "description": "Professional OBD2 diagnostic tool",
      "url": "https://www.geekobd.com/app.html"
    }
  ],
  "step": [
    {
      "@type": "HowToStep",
      "position": 1,
      "name": "Test EGR Valve Closure",
      "text": "Connect GeekOBD APP and command EGR valve closed while monitoring engine RPM at idle. RPM should increase if valve closes properly."
    },
    {
      "@type": "HowToStep",
      "position": 2,
      "name": "Check EGR Position Sensor",
      "text": "Monitor EGR valve position sensor readings while commanding valve operation. Position should change when valve is commanded open/closed."
    },
    {
      "@type": "HowToStep",
      "position": 3,
      "name": "Test Vacuum System Operation",
      "text": "Check EGR vacuum solenoid operation and vacuum lines for proper function. Verify solenoid can control vacuum to EGR valve."
    },
    {
      "@type": "HowToStep",
      "position": 4,
      "name": "Visual Inspection",
      "text": "Remove EGR valve and inspect for carbon buildup preventing closure, damaged diaphragm, or mechanical problems with valve operation."
    },
    {
      "@type": "HowToStep",
      "position": 5,
      "name": "Component Replacement and Verification",
      "text": "Replace faulty EGR valve or repair vacuum system as diagnosed. Clear codes and verify EGR system operates within normal parameters."
    }
  ]
}
</script>
</head>
<body>
    <div class="wrap">
	<header id="header" role="banner">
	<div class="main-header">
	<div class="container">
	<div class="row">
	<div class="col-md-3">
	<div class="logo pull-left">
	<h1> <a href="../index.html"> <img src="../img/logo.png" alt="MOBD"> </a> </h1>
	</div>
	</div>
	<div class="col-md-9">
	<div class="mobile navbar-header"> <a class="navbar-toggle" data-toggle="collapse" data-target=".menu"> <i class="icon-reorder icon-2x"></i> </a> </div>
	<nav class="collapse navbar-collapse menu" role="navigation" aria-label="Main navigation">
	<ul class="nav navbar-nav sf-menu">
	<li><a href="../index.html" class="sf-with-ul">Home</a></li>
	<li><a href="../app.html" class="sf-with-ul">APP</a></li>
	<li><a href="javascript:;;" class="sf-with-ul">Adaptor <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
	<ul>
	<li><a href="../hardware2.html" class="sf-with-ul">MOBD GPS</a></li>
	<li><a href="../hardware.html" class="sf-with-ul">MOBD</a></li>
	</ul>
	</li>
	<li><a href="javascript:;;" class="sf-with-ul">Resources <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
	<ul>
	<li><a href="../dtc-codes.html" class="sf-with-ul">DTC Codes</a></li>
	<li><a href="../obd-diagnostic-guide.html" class="sf-with-ul">OBD Guide</a></li>
	<li><a href="../vehicle-compatibility.html" class="sf-with-ul">Compatibility</a></li>
	<li><a href="../fuel-efficiency-monitoring.html" class="sf-with-ul">Fuel Efficiency</a></li>
	<li><a href="../support.html" class="sf-with-ul">Support</a></li>
	<li><a href="../blog.html" class="sf-with-ul">Blog</a></li>
	</ul>
	</li>
	<li><a href="../about.html" class="sf-with-ul">About Us</a></li>
	<li><a href="//www.mobd.cn" class="sf-with-ul" target="_blank">中文版</a></li>
	</ul>
	</nav>
	</div>
	</div>
	</div>
	</div>
	</header>

    <!-- Breadcrumb -->
    <div class="container">
        <nav class="breadcrumb-custom">
            <a href="../index.html">Home</a> &raquo;
            <a href="../dtc-codes.html">DTC Codes</a> &raquo;
            <a href="../dtc-codes.html#engine">Engine Codes</a> &raquo;
            <span>P0402</span>
        </nav>
    </div>

	<section class="dtc-header">
		<div class="container">
			<div class="row">
				<div class="col-md-12">
					<div class="dtc-code-badge">P0402</div>
					<span class="severity-badge severity-medium">MEDIUM Priority</span>
					<h1 style="margin: 20px 0; font-size: 36px;">EGR Flow Excessive</h1>
					<p style="font-size: 18px; opacity: 0.9; margin-bottom: 0;">The Engine Control Module has detected excessive exhaust gas recirculation flow.</p>
				</div>
			</div>
		</div>
	</section>

    <!-- Main Content -->
    <section class="content-section">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    
<!-- Quick Answer Section for AI -->
<div id="quick-answer" style="background: #e8f4fd; border: 2px solid #4a90e2; border-radius: 10px; padding: 25px; margin-bottom: 30px;">
    <h2 style="color: #2c5aa0; margin-bottom: 15px;"><i class="fa fa-arrow-up"></i> Quick Answer</h2>
    <p style="font-size: 18px; font-weight: 500; margin-bottom: 15px; color: #333;">
        <strong>P0402 means:</strong> Too much exhaust gas flowing through EGR system - usually stuck-open valve or vacuum system problem.
    </p>
    <div style="display: flex; flex-wrap: wrap; gap: 15px; margin-bottom: 15px;">
        <span style="background: #4CAF50; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
            <i class="fa fa-wrench"></i> Fix: Check EGR valve operation, test vacuum system, replace stuck EGR valve
        </span>
        <span style="background: #FF9800; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
            <i class="fa fa-dollar"></i> Cost: $180-$650
        </span>
        <span style="background: #2196F3; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
            <i class="fa fa-clock-o"></i> Time: 90-180 minutes
        </span>
    </div>
    <p style="margin: 0; color: #666; font-size: 14px;">
        <strong>Can I drive with P0402?</strong> Safe to drive but expect poor performance, rough idle, and possible stalling. Repair promptly to restore normal operation.
    </p>
</div>
                    
<!-- AI-Friendly Q&A Section -->
<div id="ai-qa" style="background: white; border-radius: 10px; padding: 25px; margin-bottom: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
    <h2><i class="fa fa-comments"></i> Common Questions</h2>
    
    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">What's the difference between P0401 and P0402?</h3>
        <p style="color: #666; line-height: 1.6;">P0401 indicates insufficient EGR flow (too little), while P0402 indicates excessive EGR flow (too much). P0402 typically causes more noticeable drivability problems like rough idle and stalling because too much EGR severely affects combustion.</p>
    </div>
    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">Can excessive EGR flow damage my engine?</h3>
        <p style="color: #666; line-height: 1.6;">Excessive EGR flow typically won't cause immediate engine damage, but it can cause poor combustion, carbon buildup, and increased wear over time. The main concerns are poor performance and potential stalling while driving.</p>
    </div>
    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">Why does my engine stall with P0402?</h3>
        <p style="color: #666; line-height: 1.6;">Excessive EGR flow dilutes the air/fuel mixture with inert exhaust gases, reducing the oxygen available for combustion. This can make the mixture too lean to sustain combustion, especially at idle, causing stalling.</p>
    </div>
    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; ">
        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">How do I test if my EGR valve is stuck open?</h3>
        <p style="color: #666; line-height: 1.6;">Use GeekOBD APP to command EGR valve closed while monitoring engine RPM at idle. If RPM doesn't increase when valve is commanded closed, the valve is likely stuck open. You can also manually check if the valve closes properly.</p>
    </div>
</div>
                    
<!-- Main Content Section -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h2><i class="fa fa-info-circle"></i> What is P0402?</h2>
    <p style="font-size: 16px; line-height: 1.6; color: #333; margin-bottom: 20px;">The Engine Control Module has detected excessive exhaust gas recirculation (EGR) flow. The EGR system is designed to recirculate a controlled amount of exhaust gases back into the intake manifold to reduce combustion temperatures and NOx emissions. When the ECM detects more EGR flow than commanded or expected, P0402 is triggered. This typically indicates a stuck-open EGR valve, vacuum system problems, or control system malfunctions.</p>

    <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; margin: 20px 0;">
        <strong><i class="fa fa-exclamation-triangle"></i> Performance Impact:</strong> P0402 causes poor engine performance, rough idle, stalling, and poor fuel economy due to excessive exhaust gas diluting the air/fuel mixture and reducing combustion efficiency.
    </div>
</div>

<!-- Symptoms Section -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h2><i class="fa fa-stethoscope"></i> Symptoms of P0402</h2>
    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
        <ul style="margin: 0; padding-left: 20px;">
            <li><strong>Check engine light illuminated - ECM detected excessive EGR flow</strong></li>
								<li><strong>Rough idle - Too much EGR flow diluting air/fuel mixture at idle</strong></li>
								<li><strong>Engine stalling - Excessive exhaust gas reducing combustion efficiency</strong></li>
								<li><strong>Poor acceleration - EGR flow reducing available oxygen for combustion</strong></li>
								<li><strong>Engine hesitation - Inconsistent power delivery due to excessive EGR</strong></li>
								<li><strong>Black smoke from exhaust - Rich mixture from excessive EGR dilution</strong></li>
								<li><strong>Poor fuel economy - Engine compensating for excessive EGR with more fuel</strong></li>
								<li><strong>Engine surging - Unstable combustion from too much exhaust gas recirculation</strong></li>
								<li><strong>Hard starting - Excessive EGR affecting combustion during startup</strong></li>
        </ul>
    </div>
</div>

<!-- Causes Section -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h2><i class="fa fa-search"></i> Common Causes of P0402</h2>
    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
        <ol style="margin: 0; padding-left: 20px;">
            <li>Stuck open EGR valve - Valve unable to close properly allowing continuous flow</li>
									<li>Faulty EGR vacuum solenoid - Solenoid stuck open providing constant vacuum</li>
									<li>Vacuum leak after EGR solenoid - Causing EGR valve to stay open</li>
									<li>Damaged EGR valve diaphragm - Preventing proper valve closure</li>
									<li>Faulty EGR position sensor - Providing incorrect feedback to ECM</li>
									<li>ECM software issues - Control module commanding excessive EGR flow</li>
									<li>Carbon buildup preventing valve closure - Deposits keeping valve partially open</li>
									<li>Faulty EGR control module - Electronic control system malfunction</li>
        </ol>
    </div>
</div>
                    
<!-- Cost Analysis Section -->
<div id="cost-info" style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
    <h2><i class="fa fa-calculator"></i> P0402 Repair Costs</h2>

    <div style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin: 20px 0;">
        <h3 style="color: #2c3e50; margin-bottom: 20px;">Cost Breakdown by Repair Type</h3>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 25px;">
            
        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #4CAF50;">
            <h4 style="color: #4CAF50; margin-bottom: 10px;"><i class="fa fa-arrow-up"></i> EGR Valve Replacement</h4>
            <p style="margin-bottom: 15px; color: #666;">Replace stuck-open EGR valve (60% of cases)</p>
            <ul style="list-style: none; padding: 0;">
                <li style="margin-bottom: 8px;"><strong>EGR valve:</strong> $120-$400</li>
                <li style="margin-bottom: 8px;"><strong>Labor (1-2 hours):</strong> $100-$240</li>
                <li style="margin-bottom: 8px;"><strong>Total:</strong> <span style="color: #4CAF50; font-weight: bold;">$220-$640</span></li>
                <li style="color: #666; font-size: 14px;">Success rate: ~90% success rate%</li>
            </ul>
        </div>
        
        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #2196F3;">
            <h4 style="color: #2196F3; margin-bottom: 10px;"><i class="fa fa-compress"></i> Vacuum System Repair</h4>
            <p style="margin-bottom: 15px; color: #666;">Fix faulty vacuum solenoid or leaks (30% of cases)</p>
            <ul style="list-style: none; padding: 0;">
                <li style="margin-bottom: 8px;"><strong>Vacuum solenoid/lines:</strong> $40-$150</li>
                <li style="margin-bottom: 8px;"><strong>Labor (1-1.5 hours):</strong> $100-$180</li>
                <li style="margin-bottom: 8px;"><strong>Total:</strong> <span style="color: #2196F3; font-weight: bold;">$140-$330</span></li>
                <li style="color: #666; font-size: 14px;">Success rate: ~85% success rate%</li>
            </ul>
        </div>
        
        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #FF9800;">
            <h4 style="color: #FF9800; margin-bottom: 10px;"><i class="fa fa-refresh"></i> EGR System Cleaning</h4>
            <p style="margin-bottom: 15px; color: #666;">Clean carbon buildup preventing valve closure (10% of cases)</p>
            <ul style="list-style: none; padding: 0;">
                <li style="margin-bottom: 8px;"><strong>EGR cleaner and gaskets:</strong> $25-$50</li>
                <li style="margin-bottom: 8px;"><strong>Labor (1.5-2.5 hours):</strong> $150-$300</li>
                <li style="margin-bottom: 8px;"><strong>Total:</strong> <span style="color: #FF9800; font-weight: bold;">$175-$350</span></li>
                <li style="color: #666; font-size: 14px;">Success rate: ~60% success rate%</li>
            </ul>
        </div>
        </div>

        <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #4CAF50;">
            <h4 style="color: #2e7d32; margin-bottom: 15px;"><i class="fa fa-lightbulb-o"></i> Money-Saving Tips</h4>
            <ul style="margin: 0; color: #2e7d32;">
                <li style="margin-bottom: 8px;">Test EGR valve closure with GeekOBD APP before replacement</li>
                <li style="margin-bottom: 8px;">Check vacuum system first - may be simple solenoid or line repair</li>
                <li style="margin-bottom: 8px;">Stuck-open valves usually require replacement rather than cleaning</li>
                <li style="margin-bottom: 8px;">Address P0402 promptly to prevent poor performance and stalling</li>
                <li style="margin-bottom: 8px;">Consider EGR valve cleaning if carbon buildup is preventing closure</li>
            </ul>
        </div>
    </div>
</div>
                    
<!-- Diagnostic Steps Section -->
<div id="diagnostic-steps" style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
    <h2><i class="fa fa-stethoscope"></i> Diagnostic Steps</h2>

    <div style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin: 20px 0;">
        <h3 style="color: #2c3e50; margin-bottom: 20px;">Professional P0402 Diagnosis Process</h3>
        <p style="margin-bottom: 20px; color: #666;">Follow these systematic steps to accurately diagnose P0402. Each step builds on the previous one to ensure accurate diagnosis.</p>

        
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-stop"></i> Step 1: Test EGR Valve Closure</h4>
            <p style="margin-bottom: 15px; color: #333;">Connect GeekOBD APP and command EGR valve closed while monitoring engine RPM at idle. RPM should increase if valve closes properly.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> GeekOBD APP can command EGR valve closed - significant RPM increase indicates valve is closing properly, no change suggests stuck-open valve.
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-tachometer"></i> Step 2: Check EGR Position Sensor</h4>
            <p style="margin-bottom: 15px; color: #333;">Monitor EGR valve position sensor readings while commanding valve operation. Position should change when valve is commanded open/closed.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> Use GeekOBD APP to monitor EGR position - readings should match commanded position, discrepancies indicate sensor or valve problems.
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-compress"></i> Step 3: Test Vacuum System Operation</h4>
            <p style="margin-bottom: 15px; color: #333;">Check EGR vacuum solenoid operation and vacuum lines for proper function. Verify solenoid can control vacuum to EGR valve.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> GeekOBD APP can command vacuum solenoid operation - monitor vacuum at EGR valve to verify proper solenoid control.
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-eye"></i> Step 4: Visual Inspection</h4>
            <p style="margin-bottom: 15px; color: #333;">Remove EGR valve and inspect for carbon buildup preventing closure, damaged diaphragm, or mechanical problems with valve operation.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> Monitor EGR position with GeekOBD APP while manually operating valve - should show position changes if sensor and valve mechanism work properly.
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-check-circle"></i> Step 5: Component Replacement and Verification</h4>
            <p style="margin-bottom: 15px; color: #333;">Replace faulty EGR valve or repair vacuum system as diagnosed. Clear codes and verify EGR system operates within normal parameters.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> Use GeekOBD APP to verify EGR valve now closes properly and engine RPM responds appropriately to EGR commands.
            </div>
        </div>

        <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin-top: 20px; border-left: 4px solid #ffc107;">
            <h4 style="color: #856404; margin-bottom: 15px;"><i class="fa fa-exclamation-triangle"></i> Important Notes</h4>
            <ul style="margin: 0; color: #856404;">
                <li style="margin-bottom: 8px;">P0402 usually indicates stuck-open EGR valve requiring replacement</li>
                <li style="margin-bottom: 8px;">Test valve closure response before assuming valve failure</li>
                <li style="margin-bottom: 8px;">Check vacuum system operation - may be control problem rather than valve</li>
            </ul>
        </div>
    </div>
</div>
                    
<!-- Real Repair Case Studies -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
    <h2><i class="fa fa-file-text"></i> Real Repair Case Studies</h2>
    
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2;"><i class="fa fa-car"></i> Case 1: Ford Focus Stuck EGR Valve</h4>
            <p><strong>Vehicle:</strong> 2016 Ford Focus 2.0L 4-cylinder, 118,000 miles</p>
            <p><strong>Problem:</strong> Customer reported rough idle, engine stalling at traffic lights, and P0402 code. Engine would occasionally stall when coming to a stop.</p>
            <p><strong>Diagnosis:</strong> GeekOBD APP showed EGR valve position sensor indicated valve was partially open even when commanded closed. EGR valve was mechanically stuck open due to carbon buildup.</p>
            <p><strong>Solution:</strong> Replaced EGR valve assembly. Attempted cleaning was unsuccessful as carbon deposits had warped the valve seat preventing proper closure.</p>
            <p><strong>Cost:</strong> EGR valve: $165, Labor: $135, Total: $300</p>
            <p><strong>Result:</strong> P0402 code cleared immediately. Engine now idles smoothly and no more stalling problems. EGR valve closes properly when commanded.</p>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 4px solid #28a745;">
            <h4 style="color: #28a745;"><i class="fa fa-car"></i> Case 2: Honda Civic Vacuum Solenoid Failure</h4>
            <p><strong>Vehicle:</strong> 2017 Honda Civic 1.5L Turbo, 85,000 miles</p>
            <p><strong>Problem:</strong> Intermittent P0402 code with occasional rough idle. Problem seemed worse during city driving with frequent stops.</p>
            <p><strong>Diagnosis:</strong> EGR valve tested mechanically good, but GeekOBD APP showed vacuum solenoid was not responding to close commands. Solenoid was stuck in open position.</p>
            <p><strong>Solution:</strong> Replaced faulty EGR vacuum solenoid. Solenoid was providing constant vacuum to EGR valve, keeping it partially open.</p>
            <p><strong>Cost:</strong> EGR vacuum solenoid: $75, Labor: $95, Total: $170</p>
            <p><strong>Result:</strong> P0402 code has not returned after 6 months. EGR valve now operates properly and rough idle eliminated during city driving.</p>
        </div>
</div>
                </div>

                <div class="col-md-4">
                    
<!-- GeekOBD APP Promotion -->
<div style="background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%); color: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: white;"><i class="fa fa-mobile"></i> Diagnose P0402</h4>
    <p style="margin-bottom: 20px; opacity: 0.9; font-size: 14px;">Use GeekOBD APP for EGR valve closure testing!</p>
    <ul style="margin-bottom: 20px; padding-left: 20px; font-size: 14px;">
        <li style="margin-bottom: 8px;">EGR valve command testing</li>
        <li style="margin-bottom: 8px;">Position sensor monitoring</li>
        <li style="margin-bottom: 8px;">Vacuum system verification</li>
        <li style="margin-bottom: 8px;">RPM response analysis</li>
    </ul>
    <a href="https://www.geekobd.com/app.html" style="display: inline-block; background: rgba(255,255,255,0.2); color: white; padding: 12px 25px; border-radius: 25px; text-decoration: none; font-weight: bold; transition: all 0.3s;">
        <i class="fa fa-download"></i> Download GeekOBD APP
    </a>
</div>

<!-- Related System Codes -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-link"></i> EGR System Codes</h4>
    <p style="margin-bottom: 15px; color: #666; font-size: 14px;">Related exhaust gas recirculation codes:</p>
    <div style="margin-bottom: 15px;">
        
                <a href="p0400.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #e74c3c;">
                    <strong style="color: #e74c3c;">P0400</strong> - EGR Flow Malfunction - General EGR flow problem
                </a>
                <a href="p0401.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #3498db;">
                    <strong style="color: #3498db;">P0401</strong> - EGR Flow Insufficient - Not enough EGR flow detected
                </a>
                <a href="p0403.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #f39c12;">
                    <strong style="color: #f39c12;">P0403</strong> - EGR Circuit Malfunction - Electrical control problems
                </a>
                <a href="p0404.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #9b59b6;">
                    <strong style="color: #9b59b6;">P0404</strong> - EGR Position Sensor Range/Performance - Position sensor issues
                </a>
                <a href="p0405.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #4a90e2;">
                    <strong style="color: #4a90e2;">P0405</strong> - EGR Position Sensor Low - Position sensor voltage too low
                </a>
                <a href="p0406.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #e67e22;">
                    <strong style="color: #e67e22;">P0406</strong> - EGR Position Sensor High - Position sensor voltage too high
                </a>
                <a href="p0172.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #27ae60;">
                    <strong style="color: #27ae60;">P0172</strong> - System Too Rich - Can be caused by excessive EGR flow
                </a>
    </div>
</div>

<!-- Diagnostic Resources -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-tools"></i> Diagnostic Resources</h4>
    <div style="margin-bottom: 20px;">
        
        <a href="#diagnostic-steps" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-arrow-up" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">EGR Valve Testing</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">Professional procedures for testing EGR valve operation</span>
        </a>
        <a href="../resources/vacuum-system-diagnosis.html" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-compress" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">Vacuum System Diagnosis</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">Testing and repairing automotive vacuum systems</span>
        </a>
        <a href="../resources/idle-quality-issues.html" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-cog" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">Idle Quality Issues</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">Diagnosing and fixing rough idle problems</span>
        </a>
        <a href="../resources/stalling-diagnosis.html" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-stop" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">Stalling Diagnosis</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">Understanding and fixing engine stalling problems</span>
        </a>
    </div>
</div>

<!-- Quick Navigation -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-compass"></i> Quick Navigation</h4>
    <div style="display: flex; flex-direction: column; gap: 8px;">
        <a href="#quick-answer" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-bolt"></i> Quick Answer
        </a>
        <a href="#ai-qa" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-comments"></i> Common Questions
        </a>
        <a href="#cost-info" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-calculator"></i> Repair Costs
        </a>
        <a href="#diagnostic-steps" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-stethoscope"></i> Diagnostic Steps
        </a>
    </div>
</div>

<!-- Code Information -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-info-circle"></i> Code Information</h4>
    <table style="width: 100%; font-size: 14px;">
        <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Code:</strong></td>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;">P0402</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>System:</strong></td>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;">Emissions Control</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Severity:</strong></td>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><span class="severity-badge severity-medium">MEDIUM</span></td>
        </tr>
        <tr>
            <td style="padding: 8px 0;"><strong>Category:</strong></td>
            <td style="padding: 8px 0;">EGR System</td>
        </tr>
    </table>
</div>
                </div>
            </div>
        </div>
    </section>

    </div>

<script src="../js/jquery.min.js"></script>
<script src="../js/bootstrap.js"></script>
<script src="../js/superfish.js"></script>
<script src="../js/custom.js"></script>
</body>
</html>