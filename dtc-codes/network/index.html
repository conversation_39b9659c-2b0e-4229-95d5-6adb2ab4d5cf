<!DOCTYPE html>
<!--[if IE 8]><html class="ie ie8"> <![endif]-->
<!--[if IE 9]><html class="ie ie9"> <![endif]-->
<!--[if gt IE 9]><!-->
<html>
<!--<![endif]-->

<head>
<meta charset="utf-8">
<title>Network Communication Codes (U0XXX) | GeekOBD DTC Database</title>
<meta name="description" content="Complete list of network communication diagnostic trouble codes (U0XXX). Browse codes for CAN bus and module communication issues with detailed explanations and repair solutions.">
<meta name="keywords" content="U0XXX codes, network diagnostic codes, CAN bus codes, communication codes, module codes, network trouble codes, OBD network codes">
<meta name="author" content="Beijing MentalRoad Technology Co., Ltd.">
<meta name="robots" content="index, follow">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
<link rel="canonical" href="https://www.geekobd.com/dtc-codes/network/">

<link rel="stylesheet" href="../../css/bootstrap.css">
<link rel="stylesheet" href="../../css/fonts/font-awesome/css/font-awesome.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<link rel="stylesheet" href="../../css/animations.css" media="screen">
<link rel="stylesheet" href="../../css/superfish.css" media="screen">
<link rel="stylesheet" href="../../css/style.css">
<link rel="stylesheet" href="../../css/colors/blue.css" id="colors">
<link rel="stylesheet" href="../../css/theme-responsive.css">
<link rel="stylesheet" href="../../css/seo-enhancements.css">
<link rel="shortcut icon" href="../../img/ico/favicon.ico">
</head>

<body>
<div class="wrap">
<header id="header" role="banner">
<div class="main-header">
<div class="container">
<div class="row">
<div class="col-md-3">
<div class="logo pull-left">
<h1> <a href="../../index.html"> <img src="../../img/logo.png" alt="MOBD"> </a> </h1>
</div>
</div>
<div class="col-md-9">
<div class="mobile navbar-header"> <a class="navbar-toggle" data-toggle="collapse" data-target=".menu"> <i class="icon-reorder icon-2x"></i> </a> </div>
<nav class="collapse navbar-collapse menu" role="navigation" aria-label="Main navigation">
<ul class="nav navbar-nav sf-menu">
<li><a href="../../index.html" class="sf-with-ul">Home</a></li>
<li><a href="../../app.html" class="sf-with-ul">APP</a></li>
<li><a href="javascript:;;" class="sf-with-ul">Adaptor <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
<ul>
<li><a href="../../hardware2.html" class="sf-with-ul">MOBD GPS</a></li>
<li><a href="../../hardware.html" class="sf-with-ul">MOBD</a></li>
</ul>
</li>
<li><a href="javascript:;;" class="sf-with-ul">Resources <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
<ul>
<li><a href="../../dtc-codes.html" class="sf-with-ul" id="current">DTC Codes</a></li>
<li><a href="../../obd-diagnostic-guide.html" class="sf-with-ul">OBD Guide</a></li>
<li><a href="../../vehicle-compatibility.html" class="sf-with-ul">Compatibility</a></li>
<li><a href="../../fuel-efficiency-monitoring.html" class="sf-with-ul">Fuel Efficiency</a></li>
<li><a href="../../support.html" class="sf-with-ul">Support</a></li>
<li><a href="../../blog.html" class="sf-with-ul">Blog</a></li>
</ul>
</li>
<li><a href="../../about.html" class="sf-with-ul">About Us</a></li>
<li><a href="//www.mobd.cn" class="sf-with-ul" target="_blank">中文版</a></li>
</ul>
</nav>
</div>
</div>
</div>
</div>
</header>

<!-- Content Start -->
<main id="main" role="main">
<!-- Title, Breadcrumb Start-->
<section class="breadcrumb-wrapper">
<div class="container" style="min-height:86px">
<div class="row">
<div class="col-lg-12 col-md-12 col-xs-12 col-sm-12">
<h1 class="title">Network Communication Codes (U0XXX)</h1>
<nav aria-label="breadcrumb">
<ol class="breadcrumb" style="background: transparent; padding: 0; margin: 10px 0;">
<li class="breadcrumb-item" style="color: #666;"><a href="../../index.html" style="color: #007bff; text-decoration: none;">Home</a></li>
<li class="breadcrumb-item" style="color: #666;"><a href="../../dtc-codes.html" style="color: #007bff; text-decoration: none;">DTC Codes</a></li>
<li class="breadcrumb-item active" aria-current="page" style="color: #333;">Network Codes</li>
</ol>
</nav>
</div>
</div>
</div>
</section>
<!-- Title, Breadcrumb End-->

<!-- Main Content start-->
<section class="content">
<div class="container">
<div class="row">
<div class="posts-block col-md-8 col-sm-6 col-xs-12">
<article>
<div class="post-content">
<div style="background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 30px;">
<div style="display: flex; align-items: center; margin-bottom: 20px;">
<div style="width: 60px; height: 60px; background: #ffc107; border-radius: 50%; margin-right: 20px; display: flex; align-items: center; justify-content: center; flex-shrink: 0; box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3); transition: all 0.3s ease;" onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 6px 20px rgba(255, 193, 7, 0.4)'" onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 12px rgba(255, 193, 7, 0.3)'">
<i class="fa fa-sitemap" style="font-size: 1.5em; color: white;"></i>
</div>
<div>
<h2 style="color: #333; margin: 0;">Network Communication Codes (U0XXX)</h2>
<p style="color: #666; margin: 5px 0 0 0;">Complete list of network communication diagnostic trouble codes (U0XXX). Browse codes for CAN bus and module communication issues.</p>
</div>
</div>
</div>

<h3 style="color: #333; margin-bottom: 20px;">Popular Network Communication Codes</h3>
<div class="codes-list">

<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 20px; margin-bottom: 15px; transition: all 0.3s ease;" onmouseover="this.style.borderColor='#ffc107'; this.style.boxShadow='0 2px 10px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'">
<div style="display: flex; align-items: center;">
<div style="background: #ffc107; color: white; padding: 8px 12px; border-radius: 4px; font-weight: bold; margin-right: 15px; min-width: 70px; text-align: center; font-size: 14px;">U0100</div>
<div style="flex: 1;">
<h4 style="color: #333; margin: 0 0 5px 0; font-size: 16px;">Lost Communication with ECM</h4>
<p style="color: #666; margin: 0; font-size: 14px;">Lost Communication with Engine Control Module</p>
</div>
<div><a href="../u0100.html" class="btn btn-warning btn-sm">View Details</a></div>
</div>
</div>

<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 20px; margin-bottom: 15px; transition: all 0.3s ease;" onmouseover="this.style.borderColor='#ffc107'; this.style.boxShadow='0 2px 10px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'">
<div style="display: flex; align-items: center;">
<div style="background: #ffc107; color: white; padding: 8px 12px; border-radius: 4px; font-weight: bold; margin-right: 15px; min-width: 70px; text-align: center; font-size: 14px;">U0101</div>
<div style="flex: 1;">
<h4 style="color: #333; margin: 0 0 5px 0; font-size: 16px;">Lost Communication with TCM</h4>
<p style="color: #666; margin: 0; font-size: 14px;">Lost Communication with Transmission Control Module</p>
</div>
<div><a href="../u0101.html" class="btn btn-warning btn-sm">View Details</a></div>
</div>
</div>

<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 20px; margin-bottom: 15px; transition: all 0.3s ease;" onmouseover="this.style.borderColor='#ffc107'; this.style.boxShadow='0 2px 10px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'">
<div style="display: flex; align-items: center;">
<div style="background: #ffc107; color: white; padding: 8px 12px; border-radius: 4px; font-weight: bold; margin-right: 15px; min-width: 70px; text-align: center; font-size: 14px;">U0121</div>
<div style="flex: 1;">
<h4 style="color: #333; margin: 0 0 5px 0; font-size: 16px;">Lost Communication with ABS</h4>
<p style="color: #666; margin: 0; font-size: 14px;">Lost Communication with Anti-Lock Brake System Control Module</p>
</div>
<div><a href="../u0121.html" class="btn btn-warning btn-sm">View Details</a></div>
</div>
</div>

<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 20px; margin-bottom: 15px; transition: all 0.3s ease;" onmouseover="this.style.borderColor='#ffc107'; this.style.boxShadow='0 2px 10px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'">
<div style="display: flex; align-items: center;">
<div style="background: #ffc107; color: white; padding: 8px 12px; border-radius: 4px; font-weight: bold; margin-right: 15px; min-width: 70px; text-align: center; font-size: 14px;">U0202</div>
<div style="flex: 1;">
<h4 style="color: #333; margin: 0 0 5px 0; font-size: 16px;">Lost Communication with Instrument Panel</h4>
<p style="color: #666; margin: 0; font-size: 14px;">Lost Communication with Instrument Panel Control Module</p>
</div>
<div><a href="../u0202.html" class="btn btn-warning btn-sm">View Details</a></div>
</div>
</div>

<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 20px; margin-bottom: 15px; transition: all 0.3s ease;" onmouseover="this.style.borderColor='#ffc107'; this.style.boxShadow='0 2px 10px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'">
<div style="display: flex; align-items: center;">
<div style="background: #ffc107; color: white; padding: 8px 12px; border-radius: 4px; font-weight: bold; margin-right: 15px; min-width: 70px; text-align: center; font-size: 14px;">U0300</div>
<div style="flex: 1;">
<h4 style="color: #333; margin: 0 0 5px 0; font-size: 16px;">Lost Communication with Infotainment</h4>
<p style="color: #666; margin: 0; font-size: 14px;">Lost Communication with Infotainment Control Module</p>
</div>
<div><a href="../u0300.html" class="btn btn-warning btn-sm">View Details</a></div>
</div>
</div>

</div>

<h3 style="color: #333; margin: 40px 0 20px 0;">Complete Network Codes Index</h3>
<p style="color: #666; margin-bottom: 20px;">Browse all available network communication diagnostic trouble codes (U0XXX) organized by series:</p>

<!-- U0100-U0199 - Engine & Powertrain Communication -->
<div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
<h4 style="color: #333; margin-bottom: 15px; display: flex; align-items: center;">
<span style="background: #17a2b8; color: white; padding: 5px 10px; border-radius: 4px; font-size: 14px; margin-right: 10px;">U0100-U0199</span>
Engine & Powertrain Communication
</h4>
<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(220px, 1fr)); gap: 12px;">
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../p0600.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">P0600</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Serial Communication Li...</div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../p0601.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">P0601</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Internal Control Module...</div>
  </div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0100.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0100</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with E...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0101.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0101</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with T...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0102.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0102</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with T...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0103.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0103</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with G...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0104.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0104</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with C...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0105.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0105</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with F...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0106.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0106</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with G...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0107.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0107</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with T...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0108.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0108</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with A...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0109.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0109</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with F...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0110.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0110</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with D...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0111.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0111</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with B...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0112.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0112</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with B...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0113.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0113</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with E...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0114.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0114</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with F...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0115.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0115</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with E...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0116.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0116</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with E...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0117.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0117</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with H...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0118.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0118</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with M...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0119.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0119</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with B...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0120.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0120</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with S...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0121.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0121</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with A...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0122.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0122</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with V...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0123.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0123</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with E...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0124.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0124</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with H...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0125.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0125</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with M...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0126.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0126</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with S...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0127.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0127</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with T...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0128.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0128</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with P...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0129.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0129</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with B...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0130.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0130</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with W...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0131.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0131</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with P...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0132.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0132</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with V...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0133.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0133</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with G...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0134.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0134</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with C...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0135.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0135</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with B...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0136.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0136</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with T...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0137.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0137</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with I...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0138.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0138</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with E...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0139.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0139</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with P...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0140.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0140</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with B...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0155.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0155</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with I...</div>
  </div>
</div>
</div>

<!-- U0200-U0299 - Chassis & Body Communication -->
<div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
<h4 style="color: #333; margin-bottom: 15px; display: flex; align-items: center;">
<span style="background: #17a2b8; color: white; padding: 5px 10px; border-radius: 4px; font-size: 14px; margin-right: 10px;">U0200-U0299</span>
Chassis & Body Communication
</h4>
<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(220px, 1fr)); gap: 12px;">
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0200.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0200</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with S...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0201.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0201</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with S...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0202.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0202</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with I...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0203.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0203</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with I...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0204.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0204</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with I...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0205.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0205</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with I...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0206.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0206</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with B...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0207.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0207</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with A...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0208.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0208</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with F...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0209.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0209</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with F...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0210.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0210</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with P...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0211.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0211</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with A...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0212.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0212</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with F...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0213.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0213</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with F...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0214.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0214</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with E...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0215.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0215</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with E...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0216.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0216</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with T...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0217.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0217</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with T...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0218.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0218</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with E...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0219.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0219</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with E...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0220.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0220</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with E...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0221.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0221</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with E...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0222.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0222</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with E...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0223.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0223</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with E...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0224.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0224</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with E...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0225.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0225</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with E...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0226.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0226</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with E...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0227.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0227</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with E...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0228.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0228</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with E...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0229.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0229</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with E...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0230.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0230</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with E...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0231.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0231</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with E...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0232.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0232</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with T...</div>
  </div>
</div>
</div>

<!-- U0300-U0399 - Advanced Driver Assistance Communication -->
<div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
<h4 style="color: #333; margin-bottom: 15px; display: flex; align-items: center;">
<span style="background: #17a2b8; color: white; padding: 5px 10px; border-radius: 4px; font-size: 14px; margin-right: 10px;">U0300-U0399</span>
Advanced Driver Assistance Communication
</h4>
<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(220px, 1fr)); gap: 12px;">
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0300.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0300</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Internal Control Module S...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0301.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0301</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with S...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0302.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0302</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with I...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0303.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0303</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with R...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0304.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0304</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with N...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0305.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0305</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with A...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0306.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0306</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with B...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0307.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0307</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with W...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0308.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0308</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with C...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0309.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0309</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with T...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0310.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0310</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with V...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0311.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0311</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with D...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0312.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0312</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with R...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0313.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0313</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with D...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0314.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0314</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with D...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0315.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0315</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with S...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0316.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0316</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with U...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0317.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0317</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with A...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0318.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0318</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with A...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0319.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0319</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with S...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0320.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0320</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with A...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0321.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0321</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with L...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0322.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0322</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with B...</div>
  </div>
</div>
</div>

<!-- U0400-U0499 - Telematics & Connectivity -->
<div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
<h4 style="color: #333; margin-bottom: 15px; display: flex; align-items: center;">
<span style="background: #17a2b8; color: white; padding: 5px 10px; border-radius: 4px; font-size: 14px; margin-right: 10px;">U0400-U0499</span>
Telematics & Connectivity
</h4>
<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(220px, 1fr)); gap: 12px;">
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0400.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0400</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with T...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0401.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0401</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with M...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0402.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0402</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with L...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0403.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0403</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with D...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0404.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0404</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with P...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0405.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0405</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with L...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0406.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0406</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with F...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0407.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0407</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with E...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0408.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0408</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with M...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0409.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0409</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with A...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0410.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0410</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with O...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0411.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0411</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with S...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0412.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0412</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with S...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0413.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0413</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with R...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0414.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0414</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with P...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0415.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0415</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with A...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0416.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0416</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with E...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0417.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0417</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with C...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0418.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0418</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with I...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0419.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0419</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with S...</div>
  </div>
</div>
</div>

<!-- U0500-U0599 - Autonomous Driving Communication -->
<div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
<h4 style="color: #333; margin-bottom: 15px; display: flex; align-items: center;">
<span style="background: #17a2b8; color: white; padding: 5px 10px; border-radius: 4px; font-size: 14px; margin-right: 10px;">U0500-U0599</span>
Autonomous Driving Communication
</h4>
<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(220px, 1fr)); gap: 12px;">
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0500.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0500</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with A...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0501.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0501</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with A...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0502.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0502</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">CAN Bus Voltage High</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0503.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0503</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">CAN Bus Voltage Low</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0504.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0504</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">CAN Bus Short to Ground</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0505.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0505</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">CAN Bus Short to Power</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0506.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0506</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">CAN Bus Open Circuit</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0507.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0507</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">CAN Bus Termination Resis...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0508.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0508</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Gateway Module Communicat...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0509.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0509</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Network Configuration Err...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0510.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0510</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Body Control Module Lost ...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0511.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0511</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Instrument Cluster Lost C...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0512.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0512</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">HVAC Control Module Lost ...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0513.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0513</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Audio System Lost Communi...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0514.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0514</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Navigation System Lost Co...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0515.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0515</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Telematics Module Lost Co...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0516.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0516</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Security System Lost Comm...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0517.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0517</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Parking Assist Module Los...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0518.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0518</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Camera Module Lost Commun...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0519.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0519</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Radar Module Lost Communi...</div>
  </div>
</div>
</div>

<!-- U0600-U0699 - Vehicle-to-Everything (V2X) -->
<div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
<h4 style="color: #333; margin-bottom: 15px; display: flex; align-items: center;">
<span style="background: #17a2b8; color: white; padding: 5px 10px; border-radius: 4px; font-size: 14px; margin-right: 10px;">U0600-U0699</span>
Vehicle-to-Everything (V2X)
</h4>
<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(220px, 1fr)); gap: 12px;">
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0600.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0600</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with V...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0601.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0601</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">V2X Security Certificate ...</div>
  </div>
</div>
</div>

<!-- U0700-U0799 - Advanced Control Modules -->
<div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
<h4 style="color: #333; margin-bottom: 15px; display: flex; align-items: center;">
<span style="background: #17a2b8; color: white; padding: 5px 10px; border-radius: 4px; font-size: 14px; margin-right: 10px;">U0700-U0799</span>
Advanced Control Modules
</h4>
<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(220px, 1fr)); gap: 12px;">
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0700.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0700</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with A...</div>
  </div>
</div>
</div>

<!-- U0800-U0899 - Transmission Communication -->
<div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
<h4 style="color: #333; margin-bottom: 15px; display: flex; align-items: center;">
<span style="background: #17a2b8; color: white; padding: 5px 10px; border-radius: 4px; font-size: 14px; margin-right: 10px;">U0800-U0899</span>
Transmission Communication
</h4>
<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(220px, 1fr)); gap: 12px;">
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../u0800.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">U0800</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Lost Communication with T...</div>
  </div>
</div>
</div>


</article>
</div>

<!-- Sidebar -->
<div class="sidebar col-md-4">
<aside>
<div class="widget">
<h3 class="widget-title">Other Categories</h3>
<ul style="list-style: none; padding: 0;">
<li style="margin-bottom: 10px;"><a href="../engine/index.html" style="color: #007bff;">Engine Codes (P0XXX)</a></li>
<li style="margin-bottom: 10px;"><a href="../body/index.html" style="color: #007bff;">Body Codes (B0XXX)</a></li>
<li style="margin-bottom: 10px;"><a href="../chassis/index.html" style="color: #007bff;">Chassis Codes (C0XXX)</a></li>
</ul>
</div>

<div class="widget">
<h3 class="widget-title">Quick Search</h3>
<form onsubmit="searchCode(event)">
<input type="text" id="quickSearch" placeholder="Enter DTC code (e.g., U0100)" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; margin-bottom: 10px;">
<button type="submit" style="width: 100%; padding: 10px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">Search</button>
</form>
</div>

<div class="widget">
<h3 class="widget-title">Popular Network Codes</h3>
<ul style="list-style: none; padding: 0;">
<li style="margin-bottom: 8px;"><a href="../u0100.html" style="color: #666;">U0100 - Lost Communication ECM</a></li>
<li style="margin-bottom: 8px;"><a href="../u0101.html" style="color: #666;">U0101 - Lost Communication TCM</a></li>
<li style="margin-bottom: 8px;"><a href="../u0155.html" style="color: #666;">U0155 - Lost Communication IPC</a></li>
<li style="margin-bottom: 8px;"><a href="../u0200.html" style="color: #666;">U0200 - Internal Control Module</a></li>
<li style="margin-bottom: 8px;"><a href="../u0300.html" style="color: #666;">U0300 - Internal Control Module</a></li>
</ul>
</div>
</aside>
</div>
</section>

<script src="../../js/jquery.min.js"></script>
<script src="../../js/bootstrap.js"></script>
<script src="../../js/superfish.js"></script>
<script src="../../js/custom.js"></script>

<script>
function searchCode(event) {
    event.preventDefault();
    var searchTerm = document.getElementById('quickSearch').value.trim().toUpperCase();
    if (searchTerm) {
        if (searchTerm.match(/^[PBCU]\d{4}$/)) {
            window.location.href = '../' + searchTerm.toLowerCase() + '.html';
        } else {
            alert('Please enter a valid DTC code (e.g., P0300, B1000, C0101, U0100)');
        }
    }
}
</script>

</body>
</html>
