<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>P0117 - ECT Sensor Low Input | GeekOBD</title>
    <meta name="description" content="The Engine Control Module has detected that the Engine Coolant Temperature sensor is reading extremely hot temperatures.">
    <meta name="keywords" content="P0117, P0117, diagnostic trouble code, OBD2, car repair, engine problems, GeekOBD">
    <link rel="canonical" href="https://www.geekobd.com/dtc-codes/p0117.html">
    
    <!-- Open Graph -->
    <meta property="og:title" content="P0117 - ECT Sensor Low Input">
    <meta property="og:description" content="The Engine Control Module has detected that the Engine Coolant Temperature sensor is reading extremely hot temperatures.">
    <meta property="og:url" content="https://www.geekobd.com/dtc-codes/p0117.html">
    <meta property="og:type" content="article">
    
    <!-- CSS -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../css/fonts/font-awesome/css/font-awesome.css">
    <link rel="stylesheet" href="../css/animations.css" media="screen">
    <link rel="stylesheet" href="../css/superfish.css" media="screen">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/colors/blue.css" id="colors">
    <link rel="stylesheet" href="../css/theme-responsive.css">
    <link rel="stylesheet" href="../css/seo-enhancements.css">
    <link rel="shortcut icon" href="../img/ico/favicon.ico">

    <style>
    .dtc-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 60px 0 40px;
    }
    .dtc-code-badge {
        display: inline-block;
        background: rgba(255,255,255,0.2);
        padding: 10px 20px;
        border-radius: 50px;
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 20px;
    }
    .severity-badge {
        display: inline-block;
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
        text-transform: uppercase;
        margin-left: 15px;
    }
    .severity-high { background: #ff4757; color: white; }
    .severity-medium { background: #ffa502; color: white; }
    .content-section { padding: 50px 0; }
    .info-box {
        background: #f8f9fa;
        border-left: 4px solid #667eea;
        padding: 20px;
        margin: 20px 0;
        border-radius: 5px;
    }
    .warning-box {
        background: #fff3cd;
        border-left: 4px solid #ffc107;
        padding: 20px;
        margin: 20px 0;
        border-radius: 5px;
    }
    .danger-box {
        background: #f8d7da;
        border-left: 4px solid #dc3545;
        padding: 20px;
        margin: 20px 0;
        border-radius: 5px;
    }
    .breadcrumb-custom {
        background: none;
        padding: 20px 0;
        margin: 0;
    }
    .breadcrumb-custom a {
        color: #667eea;
        text-decoration: none;
    }
    .breadcrumb-custom a:hover {
        text-decoration: underline;
    }
    </style>

    
<!-- Article Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": "P0117 - ECT Sensor Low Input",
  "description": "The Engine Control Module has detected that the Engine Coolant Temperature sensor is reading extremely hot temperatures.",
  "author": {
    "@type": "Organization",
    "name": "GeekOBD",
    "url": "https://www.geekobd.com"
  },
  "publisher": {
    "@type": "Organization",
    "name": "GeekOBD",
    "logo": {
      "@type": "ImageObject",
      "url": "https://www.geekobd.com/images/logo.png"
    }
  },
  "datePublished": "2025-07-31",
  "dateModified": "2025-07-31",
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "https://www.geekobd.com/dtc-codes/p0117.html"
  }
}
</script>

<!-- FAQ Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "What's the difference between P0117 and P0118 ECT codes?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "P0117 indicates the ECT sensor is reading too hot (low input voltage), while P0118 indicates too cold readings (high input voltage). P0117 typically means a short circuit or sensor reading actual high temperatures, while P0118 usually indicates an open circuit or failed sensor defaulting to -40°F."
      }
    },
    {
      "@type": "Question",
      "name": "Why does P0117 cause rich fuel mixture?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "When the ECM thinks the coolant is extremely hot, it assumes the engine is overheating and enriches the fuel mixture to help cool combustion temperatures. This protective strategy results in poor fuel economy and potential engine flooding, especially during cold starts."
      }
    },
    {
      "@type": "Question",
      "name": "Can actual engine overheating cause P0117?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "While severe overheating can affect ECT readings, P0117 typically indicates sensor readings far beyond normal operating temperatures (300°F+). True P0117 is usually caused by electrical faults rather than actual overheating, unless there's a catastrophic cooling system failure."
      }
    },
    {
      "@type": "Question",
      "name": "How do I test an ECT sensor for P0117?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Measure resistance across sensor terminals - it should decrease as temperature increases. At room temperature (68°F), expect around 2,500 ohms. If you get very low resistance (under 100 ohms) regardless of temperature, the sensor has failed. GeekOBD APP can monitor live ECT readings to confirm the fault."
      }
    }
  ]
}
</script>

<!-- HowTo Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "HowTo",
  "name": "How to Diagnose P0117 ECT Sensor Low Input",
  "description": "Step-by-step guide to diagnose and fix P0117",
  "totalTime": "PT45M",
  "estimatedCost": {
    "@type": "MonetaryAmount",
    "currency": "USD",
    "value": "$85-$320 for most P0117 repairs"
  },
  "tool": [
    {
      "@type": "HowToTool",
      "name": "GeekOBD APP with MOBD Adapter",
      "description": "Professional OBD2 diagnostic tool",
      "url": "https://www.geekobd.com/app.html"
    }
  ],
  "step": [
    {
      "@type": "HowToStep",
      "position": 1,
      "name": "Verify P0117 Code and Symptoms",
      "text": "Connect scan tool and confirm P0117 is present. Check current ECT reading - should show extremely high temperature (300°F+) even when engine is cold. Note if cooling fans are running constantly."
    },
    {
      "@type": "HowToStep",
      "position": 2,
      "name": "Visual Inspection of ECT Sensor",
      "text": "Locate ECT sensor (usually in cylinder head, intake manifold, or radiator). Check for physical damage, coolant leaks around sensor, or signs of overheating. Inspect connector for corrosion."
    },
    {
      "@type": "HowToStep",
      "position": 3,
      "name": "Test ECT Sensor Resistance",
      "text": "Disconnect sensor and measure resistance across terminals with multimeter. At 68°F, expect ~2,500 ohms. If resistance is very low (under 100 ohms), sensor has internal short circuit."
    },
    {
      "@type": "HowToStep",
      "position": 4,
      "name": "Check Wiring and Connector",
      "text": "Inspect wiring harness for damage, shorts to ground, or pinched wires. Clean connector terminals and check for proper connection. Test continuity from sensor to ECM."
    },
    {
      "@type": "HowToStep",
      "position": 5,
      "name": "Verify Repair and Test Drive",
      "text": "After replacing sensor or repairing wiring, clear codes and test drive. Monitor ECT readings to ensure they respond normally to engine temperature changes."
    }
  ]
}
</script>
</head>
<body>
    <div class="wrap">
	<header id="header" role="banner">
	<div class="main-header">
	<div class="container">
	<div class="row">
	<div class="col-md-3">
	<div class="logo pull-left">
	<h1> <a href="../index.html"> <img src="../img/logo.png" alt="MOBD"> </a> </h1>
	</div>
	</div>
	<div class="col-md-9">
	<div class="mobile navbar-header"> <a class="navbar-toggle" data-toggle="collapse" data-target=".menu"> <i class="icon-reorder icon-2x"></i> </a> </div>
	<nav class="collapse navbar-collapse menu" role="navigation" aria-label="Main navigation">
	<ul class="nav navbar-nav sf-menu">
	<li><a href="../index.html" class="sf-with-ul">Home</a></li>
	<li><a href="../app.html" class="sf-with-ul">APP</a></li>
	<li><a href="javascript:;;" class="sf-with-ul">Adaptor <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
	<ul>
	<li><a href="../hardware2.html" class="sf-with-ul">MOBD GPS</a></li>
	<li><a href="../hardware.html" class="sf-with-ul">MOBD</a></li>
	</ul>
	</li>
	<li><a href="javascript:;;" class="sf-with-ul">Resources <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
	<ul>
	<li><a href="../dtc-codes.html" class="sf-with-ul">DTC Codes</a></li>
	<li><a href="../obd-diagnostic-guide.html" class="sf-with-ul">OBD Guide</a></li>
	<li><a href="../vehicle-compatibility.html" class="sf-with-ul">Compatibility</a></li>
	<li><a href="../fuel-efficiency-monitoring.html" class="sf-with-ul">Fuel Efficiency</a></li>
	<li><a href="../support.html" class="sf-with-ul">Support</a></li>
	<li><a href="../blog.html" class="sf-with-ul">Blog</a></li>
	</ul>
	</li>
	<li><a href="../about.html" class="sf-with-ul">About Us</a></li>
	<li><a href="//www.mobd.cn" class="sf-with-ul" target="_blank">中文版</a></li>
	</ul>
	</nav>
	</div>
	</div>
	</div>
	</div>
	</header>

    <!-- Breadcrumb -->
    <div class="container">
        <nav class="breadcrumb-custom">
            <a href="../index.html">Home</a> &raquo;
            <a href="../dtc-codes.html">DTC Codes</a> &raquo;
            <a href="../dtc-codes.html#engine">Engine Codes</a> &raquo;
            <span>P0117</span>
        </nav>
    </div>

	<section class="dtc-header">
		<div class="container">
			<div class="row">
				<div class="col-md-12">
					<div class="dtc-code-badge">P0117</div>
					<span class="severity-badge severity-medium">MEDIUM Priority</span>
					<h1 style="margin: 20px 0; font-size: 36px;">ECT Sensor Low Input</h1>
					<p style="font-size: 18px; opacity: 0.9; margin-bottom: 0;">The Engine Control Module has detected that the Engine Coolant Temperature sensor is reading extremely hot temperatures.</p>
				</div>
			</div>
		</div>
	</section>

    <!-- Main Content -->
    <section class="content-section">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    
<!-- Quick Answer Section for AI -->
<div id="quick-answer" style="background: #e8f4fd; border: 2px solid #4a90e2; border-radius: 10px; padding: 25px; margin-bottom: 30px;">
    <h2 style="color: #2c5aa0; margin-bottom: 15px;"><i class="fa fa-thermometer-full"></i> Quick Answer</h2>
    <p style="font-size: 18px; font-weight: 500; margin-bottom: 15px; color: #333;">
        <strong>P0117 means:</strong> ECT sensor reading extremely hot temperatures (300°F+) when coolant is actually cooler - usually short circuit or sensor failure.
    </p>
    <div style="display: flex; flex-wrap: wrap; gap: 15px; margin-bottom: 15px;">
        <span style="background: #4CAF50; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
            <i class="fa fa-wrench"></i> Fix: Replace ECT sensor or repair short circuit
        </span>
        <span style="background: #FF9800; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
            <i class="fa fa-dollar"></i> Cost: $85-$320
        </span>
        <span style="background: #2196F3; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
            <i class="fa fa-clock-o"></i> Time: 30-60 minutes
        </span>
    </div>
    <p style="margin: 0; color: #666; font-size: 14px;">
        <strong>Can I drive with P0117?</strong> Safe to drive short distances, but expect poor fuel economy and constantly running fans. Repair soon to prevent engine flooding.
    </p>
</div>
                    
<!-- AI-Friendly Q&A Section -->
<div id="ai-qa" style="background: white; border-radius: 10px; padding: 25px; margin-bottom: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
    <h2><i class="fa fa-comments"></i> Common Questions</h2>
    
    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">What's the difference between P0117 and P0118 ECT codes?</h3>
        <p style="color: #666; line-height: 1.6;">P0117 indicates the ECT sensor is reading too hot (low input voltage), while P0118 indicates too cold readings (high input voltage). P0117 typically means a short circuit or sensor reading actual high temperatures, while P0118 usually indicates an open circuit or failed sensor defaulting to -40°F.</p>
    </div>
    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">Why does P0117 cause rich fuel mixture?</h3>
        <p style="color: #666; line-height: 1.6;">When the ECM thinks the coolant is extremely hot, it assumes the engine is overheating and enriches the fuel mixture to help cool combustion temperatures. This protective strategy results in poor fuel economy and potential engine flooding, especially during cold starts.</p>
    </div>
    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">Can actual engine overheating cause P0117?</h3>
        <p style="color: #666; line-height: 1.6;">While severe overheating can affect ECT readings, P0117 typically indicates sensor readings far beyond normal operating temperatures (300°F+). True P0117 is usually caused by electrical faults rather than actual overheating, unless there's a catastrophic cooling system failure.</p>
    </div>
    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; ">
        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">How do I test an ECT sensor for P0117?</h3>
        <p style="color: #666; line-height: 1.6;">Measure resistance across sensor terminals - it should decrease as temperature increases. At room temperature (68°F), expect around 2,500 ohms. If you get very low resistance (under 100 ohms) regardless of temperature, the sensor has failed. GeekOBD APP can monitor live ECT readings to confirm the fault.</p>
    </div>
</div>
                    
<!-- Main Content Section -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h2><i class="fa fa-info-circle"></i> What is P0117?</h2>
    <p style="font-size: 16px; line-height: 1.6; color: #333; margin-bottom: 20px;">The Engine Control Module has detected that the Engine Coolant Temperature (ECT) sensor is producing readings that indicate extremely hot coolant temperatures (typically 300°F or higher) when the actual coolant temperature should be much lower. This sensor monitors coolant temperature to help the ECM control fuel injection, ignition timing, cooling fan operation, and transmission shift points. When the sensor reads too hot, it can cause rich fuel mixture, overheating protection mode, and poor engine performance.</p>

    <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; margin: 20px 0;">
        <strong><i class="fa fa-exclamation-triangle"></i> Performance Impact:</strong> P0117 causes the ECM to receive incorrect coolant temperature data, leading to overly rich fuel mixture, constantly running cooling fans, poor fuel economy, potential engine flooding, and incorrect transmission shift points.
    </div>
</div>

<!-- Symptoms Section -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h2><i class="fa fa-stethoscope"></i> Symptoms of P0117</h2>
    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
        <ul style="margin: 0; padding-left: 20px;">
            <li><strong>Check engine light illuminated - ECT sensor fault detected</strong></li>
								<li><strong>Engine running rich - ECM thinks coolant is overheating</strong></li>
								<li><strong>Cooling fans running constantly - ECM trying to cool "hot" engine</strong></li>
								<li><strong>Poor fuel economy - Rich fuel mixture from hot temperature reading</strong></li>
								<li><strong>Engine hesitation or rough idle - Incorrect fuel/timing calculations</strong></li>
								<li><strong>Hard starting when engine is cold - ECM thinks engine is already hot</strong></li>
								<li><strong>Transmission shifting problems - Incorrect temperature data affects shift points</strong></li>
								<li><strong>Engine may enter "limp mode" - Protection against perceived overheating</strong></li>
								<li><strong>Temperature gauge reading incorrectly high - If connected to same sensor</strong></li>
        </ul>
    </div>
</div>

<!-- Causes Section -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h2><i class="fa fa-search"></i> Common Causes of P0117</h2>
    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
        <ol style="margin: 0; padding-left: 20px;">
            <li>Faulty ECT sensor - Internal component failure causing low resistance</li>
									<li>Short circuit in ECT sensor wiring - Wire touching ground causing low resistance</li>
									<li>Corroded ECT sensor connector - Poor electrical contact causing resistance drop</li>
									<li>ECT sensor contamination - Conductive debris causing short circuit</li>
									<li>Damaged ECT sensor threads - Physical damage affecting sensor operation</li>
									<li>ECM internal fault - Module misreading sensor signal</li>
									<li>Wiring harness damage - Short to ground in sensor circuit</li>
									<li>Incorrect ECT sensor installation - Wrong sensor type or poor thermal contact</li>
        </ol>
    </div>
</div>
                    
<!-- Cost Analysis Section -->
<div id="cost-info" style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
    <h2><i class="fa fa-calculator"></i> P0117 Repair Costs</h2>

    <div style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin: 20px 0;">
        <h3 style="color: #2c3e50; margin-bottom: 20px;">Cost Breakdown by Repair Type</h3>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 25px;">
            
        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #4CAF50;">
            <h4 style="color: #4CAF50; margin-bottom: 10px;"><i class="fa fa-thermometer-full"></i> ECT Sensor Replacement</h4>
            <p style="margin-bottom: 15px; color: #666;">Most common fix - Replace faulty sensor (80% of cases)</p>
            <ul style="list-style: none; padding: 0;">
                <li style="margin-bottom: 8px;"><strong>ECT Sensor:</strong> $35-$95</li>
                <li style="margin-bottom: 8px;"><strong>Coolant (if drained):</strong> $15-$35</li>
                <li style="margin-bottom: 8px;"><strong>Labor (0.5-1 hour):</strong> $50-$120</li>
                <li style="margin-bottom: 8px;"><strong>Total:</strong> <span style="color: #4CAF50; font-weight: bold;">$100-$250</span></li>
                <li style="color: #666; font-size: 14px;">Success rate: ~95% success rate%</li>
            </ul>
        </div>
        
        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #FF9800;">
            <h4 style="color: #FF9800; margin-bottom: 10px;"><i class="fa fa-plug"></i> Wiring Repair</h4>
            <p style="margin-bottom: 15px; color: #666;">Fix short circuit in sensor wiring (15% of cases)</p>
            <ul style="list-style: none; padding: 0;">
                <li style="margin-bottom: 8px;"><strong>Wire repair/splice:</strong> $20-$40</li>
                <li style="margin-bottom: 8px;"><strong>Diagnostic time:</strong> $80-$150</li>
                <li style="margin-bottom: 8px;"><strong>Labor (1-2 hours):</strong> $100-$240</li>
                <li style="margin-bottom: 8px;"><strong>Total:</strong> <span style="color: #FF9800; font-weight: bold;">$200-$430</span></li>
                <li style="color: #666; font-size: 14px;">Success rate: ~90% success rate%</li>
            </ul>
        </div>
        
        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #2196F3;">
            <h4 style="color: #2196F3; margin-bottom: 10px;"><i class="fa fa-wrench"></i> Connector Cleaning/Replacement</h4>
            <p style="margin-bottom: 15px; color: #666;">Clean corroded connector or replace if damaged (5% of cases)</p>
            <ul style="list-style: none; padding: 0;">
                <li style="margin-bottom: 8px;"><strong>Connector cleaning:</strong> $25-$45</li>
                <li style="margin-bottom: 8px;"><strong>New connector (if needed):</strong> $30-$70</li>
                <li style="margin-bottom: 8px;"><strong>Labor (0.5-1 hour):</strong> $50-$120</li>
                <li style="margin-bottom: 8px;"><strong>Total:</strong> <span style="color: #2196F3; font-weight: bold;">$105-$235</span></li>
                <li style="color: #666; font-size: 14px;">Success rate: ~85% success rate%</li>
            </ul>
        </div>
        </div>

        <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #4CAF50;">
            <h4 style="color: #2e7d32; margin-bottom: 15px;"><i class="fa fa-lightbulb-o"></i> Money-Saving Tips</h4>
            <ul style="margin: 0; color: #2e7d32;">
                <li style="margin-bottom: 8px;">Check connector first - 10% of P0117 cases are just corroded connections</li>
                <li style="margin-bottom: 8px;">ECT sensors are usually accessible - consider DIY replacement to save $50-120 in labor</li>
                <li style="margin-bottom: 8px;">Test sensor resistance before buying parts - confirm failure first</li>
                <li style="margin-bottom: 8px;">Some aftermarket sensors cost 40% less than OEM with same reliability</li>
                <li style="margin-bottom: 8px;">If coolant needs draining, combine with scheduled coolant service to save money</li>
            </ul>
        </div>
    </div>
</div>
                    
<!-- Diagnostic Steps Section -->
<div id="diagnostic-steps" style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
    <h2><i class="fa fa-stethoscope"></i> Diagnostic Steps</h2>

    <div style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin: 20px 0;">
        <h3 style="color: #2c3e50; margin-bottom: 20px;">Professional P0117 Diagnosis Process</h3>
        <p style="margin-bottom: 20px; color: #666;">Follow these systematic steps to accurately diagnose P0117. Each step builds on the previous one to ensure accurate diagnosis.</p>

        
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-search"></i> Step 1: Verify P0117 Code and Symptoms</h4>
            <p style="margin-bottom: 15px; color: #333;">Connect scan tool and confirm P0117 is present. Check current ECT reading - should show extremely high temperature (300°F+) even when engine is cold. Note if cooling fans are running constantly.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> Use GeekOBD APP to monitor live ECT sensor data. Look for readings above 300°F when engine is cold - this confirms P0117 fault.
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-eye"></i> Step 2: Visual Inspection of ECT Sensor</h4>
            <p style="margin-bottom: 15px; color: #333;">Locate ECT sensor (usually in cylinder head, intake manifold, or radiator). Check for physical damage, coolant leaks around sensor, or signs of overheating. Inspect connector for corrosion.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> GeekOBD APP can help identify which ECT sensor is faulty if vehicle has multiple temperature sensors.
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-thermometer-full"></i> Step 3: Test ECT Sensor Resistance</h4>
            <p style="margin-bottom: 15px; color: #333;">Disconnect sensor and measure resistance across terminals with multimeter. At 68°F, expect ~2,500 ohms. If resistance is very low (under 100 ohms), sensor has internal short circuit.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> Compare resistance readings with GeekOBD APP temperature charts to verify sensor is within specification for current coolant temperature.
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-plug"></i> Step 4: Check Wiring and Connector</h4>
            <p style="margin-bottom: 15px; color: #333;">Inspect wiring harness for damage, shorts to ground, or pinched wires. Clean connector terminals and check for proper connection. Test continuity from sensor to ECM.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> Use GeekOBD APP to monitor sensor voltage while wiggling wires - voltage should remain stable if wiring is good.
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-check-circle"></i> Step 5: Verify Repair and Test Drive</h4>
            <p style="margin-bottom: 15px; color: #333;">After replacing sensor or repairing wiring, clear codes and test drive. Monitor ECT readings to ensure they respond normally to engine temperature changes.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> GeekOBD APP provides real-time verification - ECT should read close to ambient temperature when cold, gradually increase to 180-220°F during normal operation.
            </div>
        </div>

        <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin-top: 20px; border-left: 4px solid #ffc107;">
            <h4 style="color: #856404; margin-bottom: 15px;"><i class="fa fa-exclamation-triangle"></i> Important Notes</h4>
            <ul style="margin: 0; color: #856404;">
                <li style="margin-bottom: 8px;">P0117 indicates sensor reading too hot - do not confuse with P0118 (too cold)</li>
                <li style="margin-bottom: 8px;">Always test sensor resistance before replacement - connector issues can mimic sensor failure</li>
                <li style="margin-bottom: 8px;">ECT sensor affects fuel mixture and cooling fans - driving with P0117 causes poor fuel economy</li>
            </ul>
        </div>
    </div>
</div>
                    
<!-- Real Repair Case Studies -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
    <h2><i class="fa fa-file-text"></i> Real Repair Case Studies</h2>
    
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2;"><i class="fa fa-car"></i> Case 1: Chevrolet Silverado ECT Sensor Short Circuit</h4>
            <p><strong>Vehicle:</strong> 2019 Chevrolet Silverado 5.3L V8, 95,000 miles</p>
            <p><strong>Problem:</strong> Customer complained of poor fuel economy (dropped from 18 to 12 MPG), constantly running cooling fans, and check engine light. Truck was running rough and seemed to be "flooding" during cold starts.</p>
            <p><strong>Diagnosis:</strong> GeekOBD APP showed P0117 code with ECT reading constant 325°F even when engine was stone cold. Resistance test revealed ECT sensor had only 35 ohms resistance (should be ~2,500 ohms at room temperature), indicating internal short circuit.</p>
            <p><strong>Solution:</strong> Replaced ECT sensor located in cylinder head near thermostat housing. Sensor required partial coolant drain and took 45 minutes to replace due to tight access.</p>
            <p><strong>Cost:</strong> ECT sensor: $68, Coolant: $25, Labor: $90, Total: $183</p>
            <p><strong>Result:</strong> P0117 code cleared immediately. ECT now reads correctly (75°F cold, 195°F operating). Fuel economy returned to normal 18 MPG, cooling fans operate normally, smooth cold starts restored.</p>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 4px solid #28a745;">
            <h4 style="color: #28a745;"><i class="fa fa-car"></i> Case 2: Honda Accord Wiring Harness Damage</h4>
            <p><strong>Vehicle:</strong> 2016 Honda Accord 2.4L 4-cylinder, 88,000 miles</p>
            <p><strong>Problem:</strong> Intermittent P0117 code with occasional poor idle and rich exhaust smell. Problem seemed to occur more often during hot weather or after driving over rough roads.</p>
            <p><strong>Diagnosis:</strong> Initial ECT sensor test showed normal resistance, but GeekOBD APP revealed intermittent spikes to 350°F+ during driving. Wire wiggle test found damaged section of harness near exhaust manifold where wires had been damaged by heat.</p>
            <p><strong>Solution:</strong> Repaired damaged section of ECT sensor wiring harness. Cut out heat-damaged portion and spliced in new high-temperature wire with proper heat shielding.</p>
            <p><strong>Cost:</strong> High-temp wire kit: $35, Heat shielding: $20, Diagnostic time: $120, Labor: $160, Total: $335</p>
            <p><strong>Result:</strong> P0117 code has not returned after 4 months. ECT readings remain stable during all driving conditions. Customer reports normal fuel economy and no more rich exhaust smell.</p>
        </div>
</div>
                </div>

                <div class="col-md-4">
                    
<!-- GeekOBD APP Promotion -->
<div style="background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%); color: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: white;"><i class="fa fa-mobile"></i> Diagnose P0117</h4>
    <p style="margin-bottom: 20px; opacity: 0.9; font-size: 14px;">Use GeekOBD APP for professional ECT sensor diagnosis!</p>
    <ul style="margin-bottom: 20px; padding-left: 20px; font-size: 14px;">
        <li style="margin-bottom: 8px;">Live coolant temperature monitoring</li>
        <li style="margin-bottom: 8px;">Resistance testing guidance</li>
        <li style="margin-bottom: 8px;">Cooling system analysis</li>
        <li style="margin-bottom: 8px;">Repair verification tools</li>
    </ul>
    <a href="https://www.geekobd.com/app.html" style="display: inline-block; background: rgba(255,255,255,0.2); color: white; padding: 12px 25px; border-radius: 25px; text-decoration: none; font-weight: bold; transition: all 0.3s;">
        <i class="fa fa-download"></i> Download GeekOBD APP
    </a>
</div>

<!-- Related System Codes -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-link"></i> ECT Sensor Codes</h4>
    <p style="margin-bottom: 15px; color: #666; font-size: 14px;">Related Engine Coolant Temperature sensor codes:</p>
    <div style="margin-bottom: 15px;">
        
                <a href="p0118.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #3498db;">
                    <strong style="color: #3498db;">P0118</strong> - ECT Sensor High Input - Opposite condition (too cold readings)
                </a>
                <a href="p0125.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #e74c3c;">
                    <strong style="color: #e74c3c;">P0125</strong> - Insufficient Coolant Temperature - Related coolant temperature issue
                </a>
                <a href="p0128.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #f39c12;">
                    <strong style="color: #f39c12;">P0128</strong> - Coolant Thermostat Rationality - Related cooling system problem
                </a>
                <a href="p0115.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #4a90e2;">
                    <strong style="color: #4a90e2;">P0115</strong> - ECT Sensor Circuit Malfunction - General ECT circuit problem
                </a>
                <a href="p0172.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #9b59b6;">
                    <strong style="color: #9b59b6;">P0172</strong> - System Too Rich Bank 1 - Can be caused by incorrect ECT readings
                </a>
                <a href="p0175.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #9b59b6;">
                    <strong style="color: #9b59b6;">P0175</strong> - System Too Rich Bank 2 - Can be caused by incorrect ECT readings
                </a>
                <a href="p0300.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #e67e22;">
                    <strong style="color: #e67e22;">P0300</strong> - Random Misfire - Can be caused by rich mixture from P0117
                </a>
    </div>
</div>

<!-- Diagnostic Resources -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-tools"></i> Diagnostic Resources</h4>
    <div style="margin-bottom: 20px;">
        
        <a href="#diagnostic-steps" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-thermometer-full" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">ECT Sensor Testing Guide</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">Professional resistance and voltage testing procedures</span>
        </a>
        <a href="../resources/ect-wiring-diagrams.html" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-sitemap" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">ECT Wiring Diagrams</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">Sensor circuit diagrams and pin configurations</span>
        </a>
        <a href="../resources/ect-temperature-charts.html" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-line-chart" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">Temperature Charts</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">ECT sensor resistance vs temperature specifications</span>
        </a>
        <a href="../resources/cooling-system-guide.html" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-tint" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">Cooling System Guide</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">Complete cooling system diagnostic procedures</span>
        </a>
    </div>
</div>

<!-- Quick Navigation -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-compass"></i> Quick Navigation</h4>
    <div style="display: flex; flex-direction: column; gap: 8px;">
        <a href="#quick-answer" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-bolt"></i> Quick Answer
        </a>
        <a href="#ai-qa" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-comments"></i> Common Questions
        </a>
        <a href="#cost-info" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-calculator"></i> Repair Costs
        </a>
        <a href="#diagnostic-steps" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-stethoscope"></i> Diagnostic Steps
        </a>
    </div>
</div>

<!-- Code Information -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-info-circle"></i> Code Information</h4>
    <table style="width: 100%; font-size: 14px;">
        <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Code:</strong></td>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;">P0117</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>System:</strong></td>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;">Engine Management</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Severity:</strong></td>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><span class="severity-badge severity-medium">MEDIUM</span></td>
        </tr>
        <tr>
            <td style="padding: 8px 0;"><strong>Category:</strong></td>
            <td style="padding: 8px 0;">Engine Coolant Temperature</td>
        </tr>
    </table>
</div>
                </div>
            </div>
        </div>
    </section>

    </div>

<script src="../js/jquery.min.js"></script>
<script src="../js/bootstrap.js"></script>
<script src="../js/superfish.js"></script>
<script src="../js/custom.js"></script>
</body>
</html>