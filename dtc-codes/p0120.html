<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>P0120 - TPS Circuit Malfunction | GeekOBD</title>
    <meta name="description" content="The Engine Control Module has detected an electrical malfunction in the Throttle Position Sensor circuit.">
    <meta name="keywords" content="P0120, P0120, diagnostic trouble code, OBD2, car repair, engine problems, GeekOBD">
    <link rel="canonical" href="https://www.geekobd.com/dtc-codes/p0120.html">
    
    <!-- Open Graph -->
    <meta property="og:title" content="P0120 - TPS Circuit Malfunction">
    <meta property="og:description" content="The Engine Control Module has detected an electrical malfunction in the Throttle Position Sensor circuit.">
    <meta property="og:url" content="https://www.geekobd.com/dtc-codes/p0120.html">
    <meta property="og:type" content="article">
    
    <!-- CSS -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../css/fonts/font-awesome/css/font-awesome.css">
    <link rel="stylesheet" href="../css/animations.css" media="screen">
    <link rel="stylesheet" href="../css/superfish.css" media="screen">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/colors/blue.css" id="colors">
    <link rel="stylesheet" href="../css/theme-responsive.css">
    <link rel="stylesheet" href="../css/seo-enhancements.css">
    <link rel="shortcut icon" href="../img/ico/favicon.ico">

    <style>
    .dtc-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 60px 0 40px;
    }
    .dtc-code-badge {
        display: inline-block;
        background: rgba(255,255,255,0.2);
        padding: 10px 20px;
        border-radius: 50px;
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 20px;
    }
    .severity-badge {
        display: inline-block;
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
        text-transform: uppercase;
        margin-left: 15px;
    }
    .severity-high { background: #ff4757; color: white; }
    .severity-medium { background: #ffa502; color: white; }
    .content-section { padding: 50px 0; }
    .info-box {
        background: #f8f9fa;
        border-left: 4px solid #667eea;
        padding: 20px;
        margin: 20px 0;
        border-radius: 5px;
    }
    .warning-box {
        background: #fff3cd;
        border-left: 4px solid #ffc107;
        padding: 20px;
        margin: 20px 0;
        border-radius: 5px;
    }
    .danger-box {
        background: #f8d7da;
        border-left: 4px solid #dc3545;
        padding: 20px;
        margin: 20px 0;
        border-radius: 5px;
    }
    .breadcrumb-custom {
        background: none;
        padding: 20px 0;
        margin: 0;
    }
    .breadcrumb-custom a {
        color: #667eea;
        text-decoration: none;
    }
    .breadcrumb-custom a:hover {
        text-decoration: underline;
    }
    </style>

    
<!-- Article Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": "P0120 - TPS Circuit Malfunction",
  "description": "The Engine Control Module has detected an electrical malfunction in the Throttle Position Sensor circuit.",
  "author": {
    "@type": "Organization",
    "name": "GeekOBD",
    "url": "https://www.geekobd.com"
  },
  "publisher": {
    "@type": "Organization",
    "name": "GeekOBD",
    "logo": {
      "@type": "ImageObject",
      "url": "https://www.geekobd.com/images/logo.png"
    }
  },
  "datePublished": "2025-07-31",
  "dateModified": "2025-07-31",
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "https://www.geekobd.com/dtc-codes/p0120.html"
  }
}
</script>

<!-- FAQ Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "What's the difference between P0120 and P0121/P0122 TPS codes?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "P0120 indicates an electrical circuit problem (wiring, connector, or sensor electrical failure), while P0121/P0122 indicate the sensor is working electrically but providing readings outside expected range. P0120 is typically easier to diagnose with electrical testing."
      }
    },
    {
      "@type": "Question",
      "name": "Can a dirty throttle body cause P0120?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "A dirty throttle body typically causes P0121 (range/performance) rather than P0120. However, severe carbon buildup can sometimes interfere with TPS operation or cause connector problems, potentially leading to P0120. Clean throttle body first, then test TPS circuit."
      }
    },
    {
      "@type": "Question",
      "name": "How do I test TPS circuit for P0120?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Use a multimeter to check for 5V reference voltage, good ground, and signal wire continuity. GeekOBD APP can show if the ECM is receiving any signal from the TPS - no data or fixed values indicate circuit problems rather than sensor range issues."
      }
    },
    {
      "@type": "Question",
      "name": "Why does P0120 affect transmission shifting?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Automatic transmissions use TPS data to determine when to shift gears and how firm the shifts should be. Without accurate throttle position information, the transmission cannot properly interpret driver intent, leading to harsh shifts, delayed shifts, or staying in lower gears."
      }
    }
  ]
}
</script>

<!-- HowTo Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "HowTo",
  "name": "How to Diagnose P0120 TPS Circuit Malfunction",
  "description": "Step-by-step guide to diagnose and fix P0120",
  "totalTime": "PT90M",
  "estimatedCost": {
    "@type": "MonetaryAmount",
    "currency": "USD",
    "value": "$95-$380 for most P0120 repairs"
  },
  "tool": [
    {
      "@type": "HowToTool",
      "name": "GeekOBD APP with MOBD Adapter",
      "description": "Professional OBD2 diagnostic tool",
      "url": "https://www.geekobd.com/app.html"
    }
  ],
  "step": [
    {
      "@type": "HowToStep",
      "position": 1,
      "name": "Check for TPS Data",
      "text": "Connect GeekOBD APP and check if TPS data is available. With P0120, you may see no data, fixed values, or complete absence of throttle position readings."
    },
    {
      "@type": "HowToStep",
      "position": 2,
      "name": "Visual Inspection",
      "text": "Inspect TPS sensor, throttle body, and wiring for damage. Look for corroded pins, damaged wires, carbon buildup, or signs of wear on throttle shaft."
    },
    {
      "@type": "HowToStep",
      "position": 3,
      "name": "Electrical Circuit Testing",
      "text": "Test TPS power supply (5V reference), ground circuit, and signal wire continuity with multimeter. Check for proper voltage at sensor connector."
    },
    {
      "@type": "HowToStep",
      "position": 4,
      "name": "Throttle Body Cleaning",
      "text": "Clean throttle body and TPS area with appropriate cleaner. Carbon buildup can interfere with TPS operation and electrical connections."
    },
    {
      "@type": "HowToStep",
      "position": 5,
      "name": "Component Replacement and Calibration",
      "text": "Replace faulty wiring, connector, or TPS as diagnosed. Clear codes and perform TPS relearn procedure if required by vehicle."
    }
  ]
}
</script>
</head>
<body>
    <div class="wrap">
	<header id="header" role="banner">
	<div class="main-header">
	<div class="container">
	<div class="row">
	<div class="col-md-3">
	<div class="logo pull-left">
	<h1> <a href="../index.html"> <img src="../img/logo.png" alt="MOBD"> </a> </h1>
	</div>
	</div>
	<div class="col-md-9">
	<div class="mobile navbar-header"> <a class="navbar-toggle" data-toggle="collapse" data-target=".menu"> <i class="icon-reorder icon-2x"></i> </a> </div>
	<nav class="collapse navbar-collapse menu" role="navigation" aria-label="Main navigation">
	<ul class="nav navbar-nav sf-menu">
	<li><a href="../index.html" class="sf-with-ul">Home</a></li>
	<li><a href="../app.html" class="sf-with-ul">APP</a></li>
	<li><a href="javascript:;;" class="sf-with-ul">Adaptor <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
	<ul>
	<li><a href="../hardware2.html" class="sf-with-ul">MOBD GPS</a></li>
	<li><a href="../hardware.html" class="sf-with-ul">MOBD</a></li>
	</ul>
	</li>
	<li><a href="javascript:;;" class="sf-with-ul">Resources <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
	<ul>
	<li><a href="../dtc-codes.html" class="sf-with-ul">DTC Codes</a></li>
	<li><a href="../obd-diagnostic-guide.html" class="sf-with-ul">OBD Guide</a></li>
	<li><a href="../vehicle-compatibility.html" class="sf-with-ul">Compatibility</a></li>
	<li><a href="../fuel-efficiency-monitoring.html" class="sf-with-ul">Fuel Efficiency</a></li>
	<li><a href="../support.html" class="sf-with-ul">Support</a></li>
	<li><a href="../blog.html" class="sf-with-ul">Blog</a></li>
	</ul>
	</li>
	<li><a href="../about.html" class="sf-with-ul">About Us</a></li>
	<li><a href="//www.mobd.cn" class="sf-with-ul" target="_blank">中文版</a></li>
	</ul>
	</nav>
	</div>
	</div>
	</div>
	</div>
	</header>

    <!-- Breadcrumb -->
    <div class="container">
        <nav class="breadcrumb-custom">
            <a href="../index.html">Home</a> &raquo;
            <a href="../dtc-codes.html">DTC Codes</a> &raquo;
            <a href="../dtc-codes.html#engine">Engine Codes</a> &raquo;
            <span>P0120</span>
        </nav>
    </div>

	<section class="dtc-header">
		<div class="container">
			<div class="row">
				<div class="col-md-12">
					<div class="dtc-code-badge">P0120</div>
					<span class="severity-badge severity-medium">MEDIUM Priority</span>
					<h1 style="margin: 20px 0; font-size: 36px;">TPS Circuit Malfunction</h1>
					<p style="font-size: 18px; opacity: 0.9; margin-bottom: 0;">The Engine Control Module has detected an electrical malfunction in the Throttle Position Sensor circuit.</p>
				</div>
			</div>
		</div>
	</section>

    <!-- Main Content -->
    <section class="content-section">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    
<!-- Quick Answer Section for AI -->
<div id="quick-answer" style="background: #e8f4fd; border: 2px solid #4a90e2; border-radius: 10px; padding: 25px; margin-bottom: 30px;">
    <h2 style="color: #2c5aa0; margin-bottom: 15px;"><i class="fa fa-flash"></i> Quick Answer</h2>
    <p style="font-size: 18px; font-weight: 500; margin-bottom: 15px; color: #333;">
        <strong>P0120 means:</strong> Electrical problem in TPS circuit - wiring, connector, or sensor electrical failure.
    </p>
    <div style="display: flex; flex-wrap: wrap; gap: 15px; margin-bottom: 15px;">
        <span style="background: #4CAF50; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
            <i class="fa fa-wrench"></i> Fix: Check wiring, test connections, replace TPS if needed
        </span>
        <span style="background: #FF9800; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
            <i class="fa fa-dollar"></i> Cost: $95-$380
        </span>
        <span style="background: #2196F3; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
            <i class="fa fa-clock-o"></i> Time: 60-120 minutes
        </span>
    </div>
    <p style="margin: 0; color: #666; font-size: 14px;">
        <strong>Can I drive with P0120?</strong> Safe to drive but expect poor performance and possible stalling. Repair soon for normal operation.
    </p>
</div>
                    
<!-- AI-Friendly Q&A Section -->
<div id="ai-qa" style="background: white; border-radius: 10px; padding: 25px; margin-bottom: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
    <h2><i class="fa fa-comments"></i> Common Questions</h2>
    
    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">What's the difference between P0120 and P0121/P0122 TPS codes?</h3>
        <p style="color: #666; line-height: 1.6;">P0120 indicates an electrical circuit problem (wiring, connector, or sensor electrical failure), while P0121/P0122 indicate the sensor is working electrically but providing readings outside expected range. P0120 is typically easier to diagnose with electrical testing.</p>
    </div>
    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">Can a dirty throttle body cause P0120?</h3>
        <p style="color: #666; line-height: 1.6;">A dirty throttle body typically causes P0121 (range/performance) rather than P0120. However, severe carbon buildup can sometimes interfere with TPS operation or cause connector problems, potentially leading to P0120. Clean throttle body first, then test TPS circuit.</p>
    </div>
    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">How do I test TPS circuit for P0120?</h3>
        <p style="color: #666; line-height: 1.6;">Use a multimeter to check for 5V reference voltage, good ground, and signal wire continuity. GeekOBD APP can show if the ECM is receiving any signal from the TPS - no data or fixed values indicate circuit problems rather than sensor range issues.</p>
    </div>
    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; ">
        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">Why does P0120 affect transmission shifting?</h3>
        <p style="color: #666; line-height: 1.6;">Automatic transmissions use TPS data to determine when to shift gears and how firm the shifts should be. Without accurate throttle position information, the transmission cannot properly interpret driver intent, leading to harsh shifts, delayed shifts, or staying in lower gears.</p>
    </div>
</div>
                    
<!-- Main Content Section -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h2><i class="fa fa-info-circle"></i> What is P0120?</h2>
    <p style="font-size: 16px; line-height: 1.6; color: #333; margin-bottom: 20px;">The Engine Control Module has detected an electrical malfunction in the Throttle Position Sensor (TPS) circuit. This indicates a problem with the electrical components of the TPS system rather than the sensor readings themselves. The TPS circuit includes the sensor, wiring harness, connector, and ECM connections. P0120 is triggered when there are voltage irregularities, open circuits, short circuits, or other electrical faults that prevent proper communication between the TPS and ECM.</p>

    <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; margin: 20px 0;">
        <strong><i class="fa fa-exclamation-triangle"></i> Performance Impact:</strong> P0120 forces the ECM to operate without accurate throttle position data, using default throttle assumptions that result in poor acceleration, rough idle, transmission shifting problems, and significantly reduced engine performance.
    </div>
</div>

<!-- Symptoms Section -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h2><i class="fa fa-stethoscope"></i> Symptoms of P0120</h2>
    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
        <ul style="margin: 0; padding-left: 20px;">
            <li><strong>Check engine light illuminated - ECM detected TPS circuit electrical fault</strong></li>
								<li><strong>Engine running in limp mode - ECM using backup throttle position calculations</strong></li>
								<li><strong>Poor acceleration response - Incorrect throttle position data affects fuel delivery</strong></li>
								<li><strong>Engine stalling at idle - ECM cannot determine proper idle throttle position</strong></li>
								<li><strong>Rough idle or surging - Inconsistent fuel mixture from missing TPS data</strong></li>
								<li><strong>Hard starting - ECM unable to calculate proper fuel delivery for throttle position</strong></li>
								<li><strong>Transmission shifting problems - TPS data affects automatic transmission shift points</strong></li>
								<li><strong>Cruise control not working - System requires accurate throttle position data</strong></li>
								<li><strong>Poor fuel economy - Non-optimized fuel delivery without TPS feedback</strong></li>
        </ul>
    </div>
</div>

<!-- Causes Section -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h2><i class="fa fa-search"></i> Common Causes of P0120</h2>
    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
        <ol style="margin: 0; padding-left: 20px;">
            <li>Open circuit in TPS wiring - Broken wire preventing signal transmission</li>
									<li>Short circuit in TPS harness - Wire touching ground or power</li>
									<li>Faulty TPS connector - Corroded, damaged, or loose connection</li>
									<li>Failed TPS sensor - Internal electrical failure in sensor components</li>
									<li>ECM internal fault - Control module unable to process TPS signals</li>
									<li>Damaged wiring harness - Physical damage from heat, vibration, or wear</li>
									<li>Poor ground connection - Inadequate ground circuit for TPS sensor</li>
									<li>Throttle body problems - Carbon buildup affecting TPS operation</li>
        </ol>
    </div>
</div>
                    
<!-- Cost Analysis Section -->
<div id="cost-info" style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
    <h2><i class="fa fa-calculator"></i> P0120 Repair Costs</h2>

    <div style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin: 20px 0;">
        <h3 style="color: #2c3e50; margin-bottom: 20px;">Cost Breakdown by Repair Type</h3>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 25px;">
            
        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #4CAF50;">
            <h4 style="color: #4CAF50; margin-bottom: 10px;"><i class="fa fa-tachometer"></i> TPS Sensor Replacement</h4>
            <p style="margin-bottom: 15px; color: #666;">Most common fix - Replace sensor with internal electrical failure (60% of cases)</p>
            <ul style="list-style: none; padding: 0;">
                <li style="margin-bottom: 8px;"><strong>TPS sensor:</strong> $45-$120</li>
                <li style="margin-bottom: 8px;"><strong>Labor (45-90 minutes):</strong> $60-$180</li>
                <li style="margin-bottom: 8px;"><strong>Total:</strong> <span style="color: #4CAF50; font-weight: bold;">$105-$300</span></li>
                <li style="color: #666; font-size: 14px;">Success rate: ~95% success rate%</li>
            </ul>
        </div>
        
        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #FF9800;">
            <h4 style="color: #FF9800; margin-bottom: 10px;"><i class="fa fa-flash"></i> Wiring Harness Repair</h4>
            <p style="margin-bottom: 15px; color: #666;">Fix damaged TPS sensor wiring (30% of cases)</p>
            <ul style="list-style: none; padding: 0;">
                <li style="margin-bottom: 8px;"><strong>Wiring repair materials:</strong> $25-$60</li>
                <li style="margin-bottom: 8px;"><strong>Diagnostic time:</strong> $100-$160</li>
                <li style="margin-bottom: 8px;"><strong>Labor (1-2 hours):</strong> $100-$240</li>
                <li style="margin-bottom: 8px;"><strong>Total:</strong> <span style="color: #FF9800; font-weight: bold;">$225-$460</span></li>
                <li style="color: #666; font-size: 14px;">Success rate: ~90% success rate%</li>
            </ul>
        </div>
        
        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #2196F3;">
            <h4 style="color: #2196F3; margin-bottom: 10px;"><i class="fa fa-plug"></i> Connector Service</h4>
            <p style="margin-bottom: 15px; color: #666;">Clean or replace corroded TPS connector (10% of cases)</p>
            <ul style="list-style: none; padding: 0;">
                <li style="margin-bottom: 8px;"><strong>Connector cleaning/replacement:</strong> $20-$55</li>
                <li style="margin-bottom: 8px;"><strong>Dielectric grease:</strong> $8-$15</li>
                <li style="margin-bottom: 8px;"><strong>Labor (30-60 minutes):</strong> $50-$120</li>
                <li style="margin-bottom: 8px;"><strong>Total:</strong> <span style="color: #2196F3; font-weight: bold;">$78-$190</span></li>
                <li style="color: #666; font-size: 14px;">Success rate: ~85% success rate%</li>
            </ul>
        </div>
        </div>

        <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #4CAF50;">
            <h4 style="color: #2e7d32; margin-bottom: 15px;"><i class="fa fa-lightbulb-o"></i> Money-Saving Tips</h4>
            <ul style="margin: 0; color: #2e7d32;">
                <li style="margin-bottom: 8px;">Check connector first - 15% of P0120 cases are just corroded connections</li>
                <li style="margin-bottom: 8px;">Clean throttle body before TPS replacement - may resolve connection issues</li>
                <li style="margin-bottom: 8px;">Use multimeter to test circuits before replacing expensive components</li>
                <li style="margin-bottom: 8px;">GeekOBD APP can help identify if problem is sensor or wiring related</li>
                <li style="margin-bottom: 8px;">TPS replacement often requires throttle body removal - factor in labor time</li>
            </ul>
        </div>
    </div>
</div>
                    
<!-- Diagnostic Steps Section -->
<div id="diagnostic-steps" style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
    <h2><i class="fa fa-stethoscope"></i> Diagnostic Steps</h2>

    <div style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin: 20px 0;">
        <h3 style="color: #2c3e50; margin-bottom: 20px;">Professional P0120 Diagnosis Process</h3>
        <p style="margin-bottom: 20px; color: #666;">Follow these systematic steps to accurately diagnose P0120. Each step builds on the previous one to ensure accurate diagnosis.</p>

        
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-search"></i> Step 1: Check for TPS Data</h4>
            <p style="margin-bottom: 15px; color: #333;">Connect GeekOBD APP and check if TPS data is available. With P0120, you may see no data, fixed values, or complete absence of throttle position readings.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> GeekOBD APP will show if ECM is receiving TPS signals - complete absence of data indicates circuit failure rather than sensor range issues.
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-eye"></i> Step 2: Visual Inspection</h4>
            <p style="margin-bottom: 15px; color: #333;">Inspect TPS sensor, throttle body, and wiring for damage. Look for corroded pins, damaged wires, carbon buildup, or signs of wear on throttle shaft.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> Use GeekOBD APP to monitor for any signal while moving throttle - intermittent data indicates connection problems.
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-bolt"></i> Step 3: Electrical Circuit Testing</h4>
            <p style="margin-bottom: 15px; color: #333;">Test TPS power supply (5V reference), ground circuit, and signal wire continuity with multimeter. Check for proper voltage at sensor connector.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> GeekOBD APP should show stable readings when circuits are good - use multimeter to verify actual voltages match expected values.
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-refresh"></i> Step 4: Throttle Body Cleaning</h4>
            <p style="margin-bottom: 15px; color: #333;">Clean throttle body and TPS area with appropriate cleaner. Carbon buildup can interfere with TPS operation and electrical connections.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> Monitor TPS readings with GeekOBD APP during cleaning - improved signal stability indicates cleaning helped connection issues.
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-check-circle"></i> Step 5: Component Replacement and Calibration</h4>
            <p style="margin-bottom: 15px; color: #333;">Replace faulty wiring, connector, or TPS as diagnosed. Clear codes and perform TPS relearn procedure if required by vehicle.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> GeekOBD APP should now show stable TPS readings that respond smoothly to throttle movement, confirming successful circuit repair.
            </div>
        </div>

        <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin-top: 20px; border-left: 4px solid #ffc107;">
            <h4 style="color: #856404; margin-bottom: 15px;"><i class="fa fa-exclamation-triangle"></i> Important Notes</h4>
            <ul style="margin: 0; color: #856404;">
                <li style="margin-bottom: 8px;">P0120 is electrical circuit problem, not sensor range issue</li>
                <li style="margin-bottom: 8px;">Clean throttle body before replacing TPS - may resolve connection problems</li>
                <li style="margin-bottom: 8px;">Some vehicles require TPS relearn procedure after replacement</li>
            </ul>
        </div>
    </div>
</div>
                    
<!-- Real Repair Case Studies -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
    <h2><i class="fa fa-file-text"></i> Real Repair Case Studies</h2>
    
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2;"><i class="fa fa-car"></i> Case 1: Toyota Camry Carbon Buildup Issue</h4>
            <p><strong>Vehicle:</strong> 2015 Toyota Camry 2.5L 4-cylinder, 125,000 miles</p>
            <p><strong>Problem:</strong> Customer reported poor acceleration, engine stalling at idle, and P0120 code. Throttle response was very poor and engine would surge.</p>
            <p><strong>Diagnosis:</strong> GeekOBD APP showed no TPS data available. Visual inspection revealed severe carbon buildup around TPS sensor area, causing connector corrosion and poor electrical contact.</p>
            <p><strong>Solution:</strong> Thoroughly cleaned throttle body and TPS area, cleaned corroded connector pins, and applied dielectric grease. Carbon buildup had created electrical path to ground.</p>
            <p><strong>Cost:</strong> Throttle body cleaner: $15, Connector cleaning kit: $18, Labor: $120, Total: $153</p>
            <p><strong>Result:</strong> P0120 code cleared immediately. TPS data now available and throttle response fully restored. Engine idles smoothly and accelerates normally.</p>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 4px solid #28a745;">
            <h4 style="color: #28a745;"><i class="fa fa-car"></i> Case 2: Ford F-150 Wiring Harness Damage</h4>
            <p><strong>Vehicle:</strong> 2017 Ford F-150 3.5L V6, 89,000 miles</p>
            <p><strong>Problem:</strong> Intermittent P0120 code with occasional limp mode activation. Problem seemed worse when engine bay was hot or during rough driving conditions.</p>
            <p><strong>Diagnosis:</strong> Found TPS wiring harness had been damaged by heat from nearby exhaust component, creating intermittent open circuit. GeekOBD APP showed TPS data would disappear randomly.</p>
            <p><strong>Solution:</strong> Repaired heat-damaged section of TPS wiring harness and rerouted wiring away from heat source. Added heat shielding to prevent recurrence.</p>
            <p><strong>Cost:</strong> Wiring repair kit: $35, Heat shielding: $25, Labor: $180, Total: $240</p>
            <p><strong>Result:</strong> P0120 code has not returned after 8 months. TPS data remains stable even under high-temperature conditions and no more limp mode activation.</p>
        </div>
</div>
                </div>

                <div class="col-md-4">
                    
<!-- GeekOBD APP Promotion -->
<div style="background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%); color: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: white;"><i class="fa fa-mobile"></i> Diagnose P0120</h4>
    <p style="margin-bottom: 20px; opacity: 0.9; font-size: 14px;">Use GeekOBD APP for TPS circuit diagnosis!</p>
    <ul style="margin-bottom: 20px; padding-left: 20px; font-size: 14px;">
        <li style="margin-bottom: 8px;">Circuit connectivity testing</li>
        <li style="margin-bottom: 8px;">Real-time throttle monitoring</li>
        <li style="margin-bottom: 8px;">Wiring problem identification</li>
        <li style="margin-bottom: 8px;">Performance verification</li>
    </ul>
    <a href="https://www.geekobd.com/app.html" style="display: inline-block; background: rgba(255,255,255,0.2); color: white; padding: 12px 25px; border-radius: 25px; text-decoration: none; font-weight: bold; transition: all 0.3s;">
        <i class="fa fa-download"></i> Download GeekOBD APP
    </a>
</div>

<!-- Related System Codes -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-link"></i> TPS Sensor Codes</h4>
    <p style="margin-bottom: 15px; color: #666; font-size: 14px;">Related throttle position sensor codes:</p>
    <div style="margin-bottom: 15px;">
        
                <a href="p0121.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #4a90e2;">
                    <strong style="color: #4a90e2;">P0121</strong> - TPS Range/Performance - Sensor working but readings out of range
                </a>
                <a href="p0122.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #3498db;">
                    <strong style="color: #3498db;">P0122</strong> - TPS Low Input - Sensor reading too low voltage
                </a>
                <a href="p0123.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #e74c3c;">
                    <strong style="color: #e74c3c;">P0123</strong> - TPS High Input - Sensor reading too high voltage
                </a>
                <a href="p0124.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #f39c12;">
                    <strong style="color: #f39c12;">P0124</strong> - TPS Intermittent - Intermittent sensor readings
                </a>
                <a href="p0171.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #9b59b6;">
                    <strong style="color: #9b59b6;">P0171</strong> - System Too Lean Bank 1 - Can result from missing TPS data
                </a>
                <a href="p0172.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #9b59b6;">
                    <strong style="color: #9b59b6;">P0172</strong> - System Too Rich Bank 1 - Can result from default fuel maps
                </a>
                <a href="p0300.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #e67e22;">
                    <strong style="color: #e67e22;">P0300</strong> - Random Misfire - Poor performance from non-optimal fuel delivery
                </a>
    </div>
</div>

<!-- Diagnostic Resources -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-tools"></i> Diagnostic Resources</h4>
    <div style="margin-bottom: 20px;">
        
        <a href="#diagnostic-steps" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-flash" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">Electrical Circuit Testing</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">Professional procedures for testing TPS circuits</span>
        </a>
        <a href="../resources/throttle-body-service.html" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-refresh" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">Throttle Body Service</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">Proper throttle body cleaning and maintenance</span>
        </a>
        <a href="../resources/tps-calibration.html" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-cogs" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">TPS Calibration</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">TPS relearn and calibration procedures</span>
        </a>
        <a href="../resources/limp-mode-recovery.html" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-exclamation-triangle" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">Limp Mode Recovery</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">Understanding and clearing limp mode conditions</span>
        </a>
    </div>
</div>

<!-- Quick Navigation -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-compass"></i> Quick Navigation</h4>
    <div style="display: flex; flex-direction: column; gap: 8px;">
        <a href="#quick-answer" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-bolt"></i> Quick Answer
        </a>
        <a href="#ai-qa" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-comments"></i> Common Questions
        </a>
        <a href="#cost-info" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-calculator"></i> Repair Costs
        </a>
        <a href="#diagnostic-steps" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-stethoscope"></i> Diagnostic Steps
        </a>
    </div>
</div>

<!-- Code Information -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-info-circle"></i> Code Information</h4>
    <table style="width: 100%; font-size: 14px;">
        <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Code:</strong></td>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;">P0120</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>System:</strong></td>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;">Engine Management</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Severity:</strong></td>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><span class="severity-badge severity-medium">MEDIUM</span></td>
        </tr>
        <tr>
            <td style="padding: 8px 0;"><strong>Category:</strong></td>
            <td style="padding: 8px 0;">Electrical Circuit</td>
        </tr>
    </table>
</div>
                </div>
            </div>
        </div>
    </section>

    </div>

<script src="../js/jquery.min.js"></script>
<script src="../js/bootstrap.js"></script>
<script src="../js/superfish.js"></script>
<script src="../js/custom.js"></script>
</body>
</html>