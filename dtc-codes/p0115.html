<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>P0115 - ECT Sensor Circuit Malfunction | GeekOBD</title>
    <meta name="description" content="The Engine Control Module has detected an electrical malfunction in the Engine Coolant Temperature sensor circuit.">
    <meta name="keywords" content="P0115, P0115, diagnostic trouble code, OBD2, car repair, engine problems, GeekOBD">
    <link rel="canonical" href="https://www.geekobd.com/dtc-codes/p0115.html">
    
    <!-- Open Graph -->
    <meta property="og:title" content="P0115 - ECT Sensor Circuit Malfunction">
    <meta property="og:description" content="The Engine Control Module has detected an electrical malfunction in the Engine Coolant Temperature sensor circuit.">
    <meta property="og:url" content="https://www.geekobd.com/dtc-codes/p0115.html">
    <meta property="og:type" content="article">
    
    <!-- CSS -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../css/fonts/font-awesome/css/font-awesome.css">
    <link rel="stylesheet" href="../css/animations.css" media="screen">
    <link rel="stylesheet" href="../css/superfish.css" media="screen">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/colors/blue.css" id="colors">
    <link rel="stylesheet" href="../css/theme-responsive.css">
    <link rel="stylesheet" href="../css/seo-enhancements.css">
    <link rel="shortcut icon" href="../img/ico/favicon.ico">

    <style>
    .dtc-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 60px 0 40px;
    }
    .dtc-code-badge {
        display: inline-block;
        background: rgba(255,255,255,0.2);
        padding: 10px 20px;
        border-radius: 50px;
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 20px;
    }
    .severity-badge {
        display: inline-block;
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
        text-transform: uppercase;
        margin-left: 15px;
    }
    .severity-high { background: #ff4757; color: white; }
    .severity-medium { background: #ffa502; color: white; }
    .content-section { padding: 50px 0; }
    .info-box {
        background: #f8f9fa;
        border-left: 4px solid #667eea;
        padding: 20px;
        margin: 20px 0;
        border-radius: 5px;
    }
    .warning-box {
        background: #fff3cd;
        border-left: 4px solid #ffc107;
        padding: 20px;
        margin: 20px 0;
        border-radius: 5px;
    }
    .danger-box {
        background: #f8d7da;
        border-left: 4px solid #dc3545;
        padding: 20px;
        margin: 20px 0;
        border-radius: 5px;
    }
    .breadcrumb-custom {
        background: none;
        padding: 20px 0;
        margin: 0;
    }
    .breadcrumb-custom a {
        color: #667eea;
        text-decoration: none;
    }
    .breadcrumb-custom a:hover {
        text-decoration: underline;
    }
    </style>

    
<!-- Article Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": "P0115 - ECT Sensor Circuit Malfunction",
  "description": "The Engine Control Module has detected an electrical malfunction in the Engine Coolant Temperature sensor circuit.",
  "author": {
    "@type": "Organization",
    "name": "GeekOBD",
    "url": "https://www.geekobd.com"
  },
  "publisher": {
    "@type": "Organization",
    "name": "GeekOBD",
    "logo": {
      "@type": "ImageObject",
      "url": "https://www.geekobd.com/images/logo.png"
    }
  },
  "datePublished": "2025-07-31",
  "dateModified": "2025-07-31",
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "https://www.geekobd.com/dtc-codes/p0115.html"
  }
}
</script>

<!-- FAQ Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "What's the difference between P0115 and P0117/P0118 ECT codes?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "P0115 indicates an electrical circuit problem (wiring, connector, or sensor electrical failure), while P0117/P0118 indicate the sensor is working electrically but providing readings outside expected range. P0115 is typically easier to diagnose with electrical testing."
      }
    },
    {
      "@type": "Question",
      "name": "Can P0115 cause engine overheating?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Yes, P0115 can cause overheating because the ECM cannot monitor actual coolant temperature and may not activate cooling fans when needed. Without ECT data, the cooling system cannot respond properly to temperature changes, potentially leading to engine damage."
      }
    },
    {
      "@type": "Question",
      "name": "How do I test ECT sensor circuit for P0115?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Use a multimeter to check for 5V reference voltage, good ground, and signal wire continuity. GeekOBD APP can show if the ECM is receiving any signal from the ECT sensor - no data or fixed values indicate circuit problems rather than sensor range issues."
      }
    },
    {
      "@type": "Question",
      "name": "Why does P0115 affect transmission shifting?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Many transmissions use ECT sensor data to determine shift points and torque converter lockup. Without this data, the transmission may shift harshly, stay in lower gears longer, or not engage lockup properly, affecting performance and fuel economy."
      }
    }
  ]
}
</script>

<!-- HowTo Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "HowTo",
  "name": "How to Diagnose P0115 ECT Sensor Circuit Malfunction",
  "description": "Step-by-step guide to diagnose and fix P0115",
  "totalTime": "PT75M",
  "estimatedCost": {
    "@type": "MonetaryAmount",
    "currency": "USD",
    "value": "$85-$320 for most P0115 repairs"
  },
  "tool": [
    {
      "@type": "HowToTool",
      "name": "GeekOBD APP with MOBD Adapter",
      "description": "Professional OBD2 diagnostic tool",
      "url": "https://www.geekobd.com/app.html"
    }
  ],
  "step": [
    {
      "@type": "HowToStep",
      "position": 1,
      "name": "Check for ECT Sensor Data",
      "text": "Connect GeekOBD APP and check if ECT sensor data is available. With P0115, you may see no data, fixed values, or complete absence of temperature readings."
    },
    {
      "@type": "HowToStep",
      "position": 2,
      "name": "Monitor Cooling System Operation",
      "text": "Check if cooling fans are operating properly and monitor actual engine temperature with infrared thermometer. P0115 may prevent proper cooling system control."
    },
    {
      "@type": "HowToStep",
      "position": 3,
      "name": "Visual Inspection",
      "text": "Inspect ECT sensor, wiring harness, and connector for obvious damage. Look for corroded pins, damaged wires, coolant leaks, or signs of overheating damage."
    },
    {
      "@type": "HowToStep",
      "position": 4,
      "name": "Electrical Circuit Testing",
      "text": "Test ECT sensor power supply (5V reference), ground circuit, and signal wire continuity with multimeter. Check for proper voltage at sensor connector."
    },
    {
      "@type": "HowToStep",
      "position": 5,
      "name": "Component Replacement and Verification",
      "text": "Replace faulty wiring, connector, or ECT sensor as diagnosed. Clear codes and verify ECT sensor data is available and cooling system operates properly."
    }
  ]
}
</script>
</head>
<body>
    <div class="wrap">
	<header id="header" role="banner">
	<div class="main-header">
	<div class="container">
	<div class="row">
	<div class="col-md-3">
	<div class="logo pull-left">
	<h1> <a href="../index.html"> <img src="../img/logo.png" alt="MOBD"> </a> </h1>
	</div>
	</div>
	<div class="col-md-9">
	<div class="mobile navbar-header"> <a class="navbar-toggle" data-toggle="collapse" data-target=".menu"> <i class="icon-reorder icon-2x"></i> </a> </div>
	<nav class="collapse navbar-collapse menu" role="navigation" aria-label="Main navigation">
	<ul class="nav navbar-nav sf-menu">
	<li><a href="../index.html" class="sf-with-ul">Home</a></li>
	<li><a href="../app.html" class="sf-with-ul">APP</a></li>
	<li><a href="javascript:;;" class="sf-with-ul">Adaptor <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
	<ul>
	<li><a href="../hardware2.html" class="sf-with-ul">MOBD GPS</a></li>
	<li><a href="../hardware.html" class="sf-with-ul">MOBD</a></li>
	</ul>
	</li>
	<li><a href="javascript:;;" class="sf-with-ul">Resources <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
	<ul>
	<li><a href="../dtc-codes.html" class="sf-with-ul">DTC Codes</a></li>
	<li><a href="../obd-diagnostic-guide.html" class="sf-with-ul">OBD Guide</a></li>
	<li><a href="../vehicle-compatibility.html" class="sf-with-ul">Compatibility</a></li>
	<li><a href="../fuel-efficiency-monitoring.html" class="sf-with-ul">Fuel Efficiency</a></li>
	<li><a href="../support.html" class="sf-with-ul">Support</a></li>
	<li><a href="../blog.html" class="sf-with-ul">Blog</a></li>
	</ul>
	</li>
	<li><a href="../about.html" class="sf-with-ul">About Us</a></li>
	<li><a href="//www.mobd.cn" class="sf-with-ul" target="_blank">中文版</a></li>
	</ul>
	</nav>
	</div>
	</div>
	</div>
	</div>
	</header>

    <!-- Breadcrumb -->
    <div class="container">
        <nav class="breadcrumb-custom">
            <a href="../index.html">Home</a> &raquo;
            <a href="../dtc-codes.html">DTC Codes</a> &raquo;
            <a href="../dtc-codes.html#engine">Engine Codes</a> &raquo;
            <span>P0115</span>
        </nav>
    </div>

	<section class="dtc-header">
		<div class="container">
			<div class="row">
				<div class="col-md-12">
					<div class="dtc-code-badge">P0115</div>
					<span class="severity-badge severity-high">HIGH Priority</span>
					<h1 style="margin: 20px 0; font-size: 36px;">ECT Sensor Circuit Malfunction</h1>
					<p style="font-size: 18px; opacity: 0.9; margin-bottom: 0;">The Engine Control Module has detected an electrical malfunction in the Engine Coolant Temperature sensor circuit.</p>
				</div>
			</div>
		</div>
	</section>

    <!-- Main Content -->
    <section class="content-section">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    
<!-- Quick Answer Section for AI -->
<div id="quick-answer" style="background: #e8f4fd; border: 2px solid #4a90e2; border-radius: 10px; padding: 25px; margin-bottom: 30px;">
    <h2 style="color: #2c5aa0; margin-bottom: 15px;"><i class="fa fa-flash"></i> Quick Answer</h2>
    <p style="font-size: 18px; font-weight: 500; margin-bottom: 15px; color: #333;">
        <strong>P0115 means:</strong> Electrical problem in ECT sensor circuit - wiring, connector, or sensor electrical failure.
    </p>
    <div style="display: flex; flex-wrap: wrap; gap: 15px; margin-bottom: 15px;">
        <span style="background: #4CAF50; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
            <i class="fa fa-wrench"></i> Fix: Check wiring, test connections, replace ECT sensor if needed
        </span>
        <span style="background: #FF9800; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
            <i class="fa fa-dollar"></i> Cost: $85-$320
        </span>
        <span style="background: #2196F3; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
            <i class="fa fa-clock-o"></i> Time: 60-120 minutes
        </span>
    </div>
    <p style="margin: 0; color: #666; font-size: 14px;">
        <strong>Can I drive with P0115?</strong> Risky to drive - cooling fans may not work properly. Monitor temperature closely and repair immediately.
    </p>
</div>
                    
<!-- AI-Friendly Q&A Section -->
<div id="ai-qa" style="background: white; border-radius: 10px; padding: 25px; margin-bottom: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
    <h2><i class="fa fa-comments"></i> Common Questions</h2>
    
    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">What's the difference between P0115 and P0117/P0118 ECT codes?</h3>
        <p style="color: #666; line-height: 1.6;">P0115 indicates an electrical circuit problem (wiring, connector, or sensor electrical failure), while P0117/P0118 indicate the sensor is working electrically but providing readings outside expected range. P0115 is typically easier to diagnose with electrical testing.</p>
    </div>
    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">Can P0115 cause engine overheating?</h3>
        <p style="color: #666; line-height: 1.6;">Yes, P0115 can cause overheating because the ECM cannot monitor actual coolant temperature and may not activate cooling fans when needed. Without ECT data, the cooling system cannot respond properly to temperature changes, potentially leading to engine damage.</p>
    </div>
    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">How do I test ECT sensor circuit for P0115?</h3>
        <p style="color: #666; line-height: 1.6;">Use a multimeter to check for 5V reference voltage, good ground, and signal wire continuity. GeekOBD APP can show if the ECM is receiving any signal from the ECT sensor - no data or fixed values indicate circuit problems rather than sensor range issues.</p>
    </div>
    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; ">
        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">Why does P0115 affect transmission shifting?</h3>
        <p style="color: #666; line-height: 1.6;">Many transmissions use ECT sensor data to determine shift points and torque converter lockup. Without this data, the transmission may shift harshly, stay in lower gears longer, or not engage lockup properly, affecting performance and fuel economy.</p>
    </div>
</div>
                    
<!-- Main Content Section -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h2><i class="fa fa-info-circle"></i> What is P0115?</h2>
    <p style="font-size: 16px; line-height: 1.6; color: #333; margin-bottom: 20px;">The Engine Control Module has detected an electrical malfunction in the Engine Coolant Temperature (ECT) sensor circuit. This indicates a problem with the electrical components of the ECT sensor system rather than the sensor readings themselves. The ECT sensor circuit includes the sensor, wiring harness, connector, and ECM connections. P0115 is triggered when there are voltage irregularities, open circuits, short circuits, or other electrical faults that prevent proper communication between the ECT sensor and ECM.</p>

    <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; margin: 20px 0;">
        <strong><i class="fa fa-exclamation-triangle"></i> Performance Impact:</strong> P0115 forces the ECM to operate without ECT sensor data, using default temperature assumptions that may cause overheating, poor cold-weather performance, incorrect fuel delivery, and potential engine damage from inadequate cooling system control.
    </div>
</div>

<!-- Symptoms Section -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h2><i class="fa fa-stethoscope"></i> Symptoms of P0115</h2>
    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
        <ul style="margin: 0; padding-left: 20px;">
            <li><strong>Check engine light illuminated - ECM detected ECT sensor circuit electrical fault</strong></li>
								<li><strong>Engine running in default mode - ECM using backup coolant temperature calculations</strong></li>
								<li><strong>Cooling fans running constantly - ECM assumes engine is overheating without data</strong></li>
								<li><strong>Poor cold weather performance - Incorrect fuel mixture during cold starts</strong></li>
								<li><strong>Hard starting when cold - Improper cold-start fuel enrichment</strong></li>
								<li><strong>Engine overheating - Cooling fans may not activate without temperature data</strong></li>
								<li><strong>Poor fuel economy - Non-optimized fuel delivery without ECT data</strong></li>
								<li><strong>Transmission shifting problems - Incorrect temperature data affects shift points</strong></li>
								<li><strong>Temperature gauge not working - If connected to same sensor circuit</strong></li>
        </ul>
    </div>
</div>

<!-- Causes Section -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h2><i class="fa fa-search"></i> Common Causes of P0115</h2>
    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
        <ol style="margin: 0; padding-left: 20px;">
            <li>Open circuit in ECT sensor wiring - Broken wire preventing signal transmission</li>
									<li>Short circuit in ECT sensor harness - Wire touching ground or power</li>
									<li>Faulty ECT sensor connector - Corroded, damaged, or loose connection</li>
									<li>Failed ECT sensor - Internal electrical failure in sensor components</li>
									<li>ECM internal fault - Control module unable to process ECT signals</li>
									<li>Damaged wiring harness - Physical damage from heat, vibration, or corrosion</li>
									<li>Poor ground connection - Inadequate ground circuit for ECT sensor</li>
									<li>Coolant contamination - Conductive coolant causing electrical shorts</li>
        </ol>
    </div>
</div>
                    
<!-- Cost Analysis Section -->
<div id="cost-info" style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
    <h2><i class="fa fa-calculator"></i> P0115 Repair Costs</h2>

    <div style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin: 20px 0;">
        <h3 style="color: #2c3e50; margin-bottom: 20px;">Cost Breakdown by Repair Type</h3>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 25px;">
            
        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #4CAF50;">
            <h4 style="color: #4CAF50; margin-bottom: 10px;"><i class="fa fa-thermometer-full"></i> ECT Sensor Replacement</h4>
            <p style="margin-bottom: 15px; color: #666;">Most common fix - Replace sensor with internal electrical failure (70% of cases)</p>
            <ul style="list-style: none; padding: 0;">
                <li style="margin-bottom: 8px;"><strong>ECT sensor:</strong> $35-$95</li>
                <li style="margin-bottom: 8px;"><strong>Coolant (if drained):</strong> $15-$35</li>
                <li style="margin-bottom: 8px;"><strong>Labor (45-90 minutes):</strong> $60-$150</li>
                <li style="margin-bottom: 8px;"><strong>Total:</strong> <span style="color: #4CAF50; font-weight: bold;">$110-$280</span></li>
                <li style="color: #666; font-size: 14px;">Success rate: ~95% success rate%</li>
            </ul>
        </div>
        
        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #FF9800;">
            <h4 style="color: #FF9800; margin-bottom: 10px;"><i class="fa fa-flash"></i> Wiring Harness Repair</h4>
            <p style="margin-bottom: 15px; color: #666;">Fix damaged ECT sensor wiring (20% of cases)</p>
            <ul style="list-style: none; padding: 0;">
                <li style="margin-bottom: 8px;"><strong>Wiring repair materials:</strong> $25-$60</li>
                <li style="margin-bottom: 8px;"><strong>Diagnostic time:</strong> $100-$160</li>
                <li style="margin-bottom: 8px;"><strong>Labor (1-2 hours):</strong> $100-$240</li>
                <li style="margin-bottom: 8px;"><strong>Total:</strong> <span style="color: #FF9800; font-weight: bold;">$225-$460</span></li>
                <li style="color: #666; font-size: 14px;">Success rate: ~90% success rate%</li>
            </ul>
        </div>
        
        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #2196F3;">
            <h4 style="color: #2196F3; margin-bottom: 10px;"><i class="fa fa-plug"></i> Connector Service</h4>
            <p style="margin-bottom: 15px; color: #666;">Clean or replace corroded ECT sensor connector (10% of cases)</p>
            <ul style="list-style: none; padding: 0;">
                <li style="margin-bottom: 8px;"><strong>Connector cleaning/replacement:</strong> $20-$50</li>
                <li style="margin-bottom: 8px;"><strong>Dielectric grease:</strong> $8-$15</li>
                <li style="margin-bottom: 8px;"><strong>Labor (30-60 minutes):</strong> $50-$120</li>
                <li style="margin-bottom: 8px;"><strong>Total:</strong> <span style="color: #2196F3; font-weight: bold;">$78-$185</span></li>
                <li style="color: #666; font-size: 14px;">Success rate: ~85% success rate%</li>
            </ul>
        </div>
        </div>

        <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #4CAF50;">
            <h4 style="color: #2e7d32; margin-bottom: 15px;"><i class="fa fa-lightbulb-o"></i> Money-Saving Tips</h4>
            <ul style="margin: 0; color: #2e7d32;">
                <li style="margin-bottom: 8px;">Check connector first - 15% of P0115 cases are just corroded connections</li>
                <li style="margin-bottom: 8px;">ECT sensors are usually accessible - consider DIY replacement to save labor</li>
                <li style="margin-bottom: 8px;">Use multimeter to test circuits before replacing expensive components</li>
                <li style="margin-bottom: 8px;">GeekOBD APP can help identify if problem is sensor or wiring related</li>
                <li style="margin-bottom: 8px;">Monitor coolant temperature closely until repair to prevent overheating</li>
            </ul>
        </div>
    </div>
</div>
                    
<!-- Diagnostic Steps Section -->
<div id="diagnostic-steps" style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
    <h2><i class="fa fa-stethoscope"></i> Diagnostic Steps</h2>

    <div style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin: 20px 0;">
        <h3 style="color: #2c3e50; margin-bottom: 20px;">Professional P0115 Diagnosis Process</h3>
        <p style="margin-bottom: 20px; color: #666;">Follow these systematic steps to accurately diagnose P0115. Each step builds on the previous one to ensure accurate diagnosis.</p>

        
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-search"></i> Step 1: Check for ECT Sensor Data</h4>
            <p style="margin-bottom: 15px; color: #333;">Connect GeekOBD APP and check if ECT sensor data is available. With P0115, you may see no data, fixed values, or complete absence of temperature readings.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> GeekOBD APP will show if ECM is receiving ECT sensor signals - complete absence of data indicates circuit failure rather than sensor range issues.
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-tint"></i> Step 2: Monitor Cooling System Operation</h4>
            <p style="margin-bottom: 15px; color: #333;">Check if cooling fans are operating properly and monitor actual engine temperature with infrared thermometer. P0115 may prevent proper cooling system control.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> Use GeekOBD APP to command cooling fan operation if available - this helps verify ECM can control cooling system despite missing ECT data.
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-eye"></i> Step 3: Visual Inspection</h4>
            <p style="margin-bottom: 15px; color: #333;">Inspect ECT sensor, wiring harness, and connector for obvious damage. Look for corroded pins, damaged wires, coolant leaks, or signs of overheating damage.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> Use GeekOBD APP to monitor for any signal while wiggling wires - intermittent data indicates wiring problems.
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-bolt"></i> Step 4: Electrical Circuit Testing</h4>
            <p style="margin-bottom: 15px; color: #333;">Test ECT sensor power supply (5V reference), ground circuit, and signal wire continuity with multimeter. Check for proper voltage at sensor connector.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> GeekOBD APP should show stable readings when circuits are good - use multimeter to verify actual voltages match expected values.
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-check-circle"></i> Step 5: Component Replacement and Verification</h4>
            <p style="margin-bottom: 15px; color: #333;">Replace faulty wiring, connector, or ECT sensor as diagnosed. Clear codes and verify ECT sensor data is available and cooling system operates properly.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> GeekOBD APP should now show stable ECT sensor readings and cooling fans should activate at proper temperature, confirming circuit repair.
            </div>
        </div>

        <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin-top: 20px; border-left: 4px solid #ffc107;">
            <h4 style="color: #856404; margin-bottom: 15px;"><i class="fa fa-exclamation-triangle"></i> Important Notes</h4>
            <ul style="margin: 0; color: #856404;">
                <li style="margin-bottom: 8px;">P0115 can cause overheating - monitor temperature closely during diagnosis</li>
                <li style="margin-bottom: 8px;">Test circuits with multimeter before replacing components</li>
                <li style="margin-bottom: 8px;">Cooling fans may not work properly with P0115 - drive carefully</li>
            </ul>
        </div>
    </div>
</div>
                    
<!-- Real Repair Case Studies -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
    <h2><i class="fa fa-file-text"></i> Real Repair Case Studies</h2>
    
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2;"><i class="fa fa-car"></i> Case 1: Chevrolet Silverado Overheating Issue</h4>
            <p><strong>Vehicle:</strong> 2018 Chevrolet Silverado 5.3L V8, 95,000 miles</p>
            <p><strong>Problem:</strong> Customer experienced engine overheating with cooling fans not turning on. P0115 code was present and temperature gauge showed no reading.</p>
            <p><strong>Diagnosis:</strong> GeekOBD APP showed no ECT sensor data available. Visual inspection revealed ECT sensor connector had melted due to previous overheating, creating open circuit.</p>
            <p><strong>Solution:</strong> Replaced damaged ECT sensor connector and sensor. Also repaired heat-damaged wiring section and added heat shielding to prevent recurrence.</p>
            <p><strong>Cost:</strong> ECT sensor: $58, Connector: $35, Wiring: $25, Labor: $150, Total: $268</p>
            <p><strong>Result:</strong> P0115 code cleared immediately. Cooling fans now operate properly and temperature gauge works normally. No overheating issues after 6 months.</p>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 4px solid #28a745;">
            <h4 style="color: #28a745;"><i class="fa fa-car"></i> Case 2: Honda Accord Transmission Problems</h4>
            <p><strong>Vehicle:</strong> 2016 Honda Accord 2.4L 4-cylinder, 108,000 miles</p>
            <p><strong>Problem:</strong> Customer reported harsh shifting and poor fuel economy. P0115 code was present along with transmission-related symptoms.</p>
            <p><strong>Diagnosis:</strong> Found ECT sensor wiring had been damaged during previous radiator replacement, creating intermittent open circuit. GeekOBD APP showed ECT data would disappear randomly.</p>
            <p><strong>Solution:</strong> Repaired damaged ECT sensor wiring with proper automotive wire and connectors. Ensured proper routing away from heat sources.</p>
            <p><strong>Cost:</strong> Wiring repair kit: $30, Labor: $120, Total: $150</p>
            <p><strong>Result:</strong> P0115 code cleared and transmission shifting returned to normal. Fuel economy improved by 3 MPG with proper ECT data available.</p>
        </div>
</div>
                </div>

                <div class="col-md-4">
                    
<!-- GeekOBD APP Promotion -->
<div style="background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%); color: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: white;"><i class="fa fa-mobile"></i> Diagnose P0115</h4>
    <p style="margin-bottom: 20px; opacity: 0.9; font-size: 14px;">Use GeekOBD APP for ECT sensor circuit diagnosis!</p>
    <ul style="margin-bottom: 20px; padding-left: 20px; font-size: 14px;">
        <li style="margin-bottom: 8px;">Circuit connectivity testing</li>
        <li style="margin-bottom: 8px;">Cooling system monitoring</li>
        <li style="margin-bottom: 8px;">Temperature data verification</li>
        <li style="margin-bottom: 8px;">Overheating prevention alerts</li>
    </ul>
    <a href="https://www.geekobd.com/app.html" style="display: inline-block; background: rgba(255,255,255,0.2); color: white; padding: 12px 25px; border-radius: 25px; text-decoration: none; font-weight: bold; transition: all 0.3s;">
        <i class="fa fa-download"></i> Download GeekOBD APP
    </a>
</div>

<!-- Related System Codes -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-link"></i> ECT Sensor Codes</h4>
    <p style="margin-bottom: 15px; color: #666; font-size: 14px;">Related engine coolant temperature sensor codes:</p>
    <div style="margin-bottom: 15px;">
        
                <a href="p0117.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #e74c3c;">
                    <strong style="color: #e74c3c;">P0117</strong> - ECT Sensor Low Input - Sensor reading too hot temperatures
                </a>
                <a href="p0118.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #3498db;">
                    <strong style="color: #3498db;">P0118</strong> - ECT Sensor High Input - Sensor reading too cold temperatures
                </a>
                <a href="p0125.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #f39c12;">
                    <strong style="color: #f39c12;">P0125</strong> - Insufficient Coolant Temperature - Related coolant temperature issue
                </a>
                <a href="p0128.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #9b59b6;">
                    <strong style="color: #9b59b6;">P0128</strong> - Coolant Thermostat Rationality - Related cooling system problem
                </a>
                <a href="p0172.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #9b59b6;">
                    <strong style="color: #9b59b6;">P0172</strong> - System Too Rich Bank 1 - Can result from default fuel maps
                </a>
                <a href="p0175.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #9b59b6;">
                    <strong style="color: #9b59b6;">P0175</strong> - System Too Rich Bank 2 - Can result from default fuel maps
                </a>
                <a href="p0300.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #e67e22;">
                    <strong style="color: #e67e22;">P0300</strong> - Random Misfire - Poor performance from non-optimal fuel delivery
                </a>
    </div>
</div>

<!-- Diagnostic Resources -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-tools"></i> Diagnostic Resources</h4>
    <div style="margin-bottom: 20px;">
        
        <a href="#diagnostic-steps" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-flash" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">Electrical Circuit Testing</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">Professional procedures for testing ECT sensor circuits</span>
        </a>
        <a href="../resources/cooling-system-safety.html" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-fire-extinguisher" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">Cooling System Safety</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">Preventing overheating during ECT sensor diagnosis</span>
        </a>
        <a href="../resources/temperature-sensor-guide.html" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-thermometer-full" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">Temperature Sensor Guide</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">Complete guide to automotive temperature sensors</span>
        </a>
        <a href="../resources/overheating-prevention.html" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-shield" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">Overheating Prevention</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">Protecting your engine from overheating damage</span>
        </a>
    </div>
</div>

<!-- Quick Navigation -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-compass"></i> Quick Navigation</h4>
    <div style="display: flex; flex-direction: column; gap: 8px;">
        <a href="#quick-answer" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-bolt"></i> Quick Answer
        </a>
        <a href="#ai-qa" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-comments"></i> Common Questions
        </a>
        <a href="#cost-info" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-calculator"></i> Repair Costs
        </a>
        <a href="#diagnostic-steps" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-stethoscope"></i> Diagnostic Steps
        </a>
    </div>
</div>

<!-- Code Information -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-info-circle"></i> Code Information</h4>
    <table style="width: 100%; font-size: 14px;">
        <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Code:</strong></td>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;">P0115</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>System:</strong></td>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;">Engine Management</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Severity:</strong></td>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><span class="severity-badge severity-high">HIGH</span></td>
        </tr>
        <tr>
            <td style="padding: 8px 0;"><strong>Category:</strong></td>
            <td style="padding: 8px 0;">Electrical Circuit</td>
        </tr>
    </table>
</div>
                </div>
            </div>
        </div>
    </section>

    </div>

<script src="../js/jquery.min.js"></script>
<script src="../js/bootstrap.js"></script>
<script src="../js/superfish.js"></script>
<script src="../js/custom.js"></script>
</body>
</html>