<!DOCTYPE html>
<!--[if IE 8]><html class="ie ie8"> <![endif]-->
<!--[if IE 9]><html class="ie ie9"> <![endif]-->
<!--[if gt IE 9]><!-->
<html>
<!--<![endif]-->

<head>
<meta charset="utf-8">
<title>P0171 - System Too Lean (Bank 1) | GeekOBD Diagnostic Guide</title>
<meta name="description" content="P0171 diagnostic trouble code: System Too Lean (Bank 1). Learn about symptoms, causes, diagnosis steps, and repair solutions for P0171 with GeekOBD professional tools.">
<meta name="keywords" content="P0171, P0171 code, P0171 diagnostic, system too lean, lean fuel mixture, oxygen sensor, MAF sensor, vacuum leak, OBD diagnostic code">
<meta name="author" content="Beijing MentalRoad Technology Co., Ltd.">
<meta name="robots" content="index, follow">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
<link rel="canonical" href="https://www.geekobd.com/dtc-codes/p0171.html">

<!-- Open Graph / Facebook -->
<meta property="og:type" content="article">
<meta property="og:url" content="https://www.geekobd.com/dtc-codes/p0171.html">
<meta property="og:title" content="P0171 - System Too Lean (Bank 1) | Diagnostic Code Guide">
<meta property="og:description" content="P0171 diagnostic trouble code: System Too Lean (Bank 1). Complete guide with symptoms, causes, and repair solutions.">
<meta property="og:image" content="https://www.geekobd.com/img/logo.png">
<meta property="og:site_name" content="GeekOBD">

<!-- Twitter -->
<meta property="twitter:card" content="summary_large_image">
<meta property="twitter:url" content="https://www.geekobd.com/dtc-codes/p0171.html">
<meta property="twitter:title" content="P0171 - System Too Lean (Bank 1) | Diagnostic Code Guide">
<meta property="twitter:description" content="P0171 diagnostic trouble code: System Too Lean (Bank 1). Complete guide with symptoms, causes, and repair solutions.">
<meta property="twitter:image" content="https://www.geekobd.com/img/logo.png">

<link rel="stylesheet" href="../css/bootstrap.css">
<link rel="stylesheet" href="../css/fonts/font-awesome/css/font-awesome.css">
<link rel="stylesheet" href="../css/animations.css" media="screen">
<link rel="stylesheet" href="../css/superfish.css" media="screen">
<link rel="stylesheet" href="../css/style.css">
<link rel="stylesheet" href="../css/colors/blue.css" id="colors">
<link rel="stylesheet" href="../css/theme-responsive.css">
<link rel="stylesheet" href="../css/seo-enhancements.css">
<link rel="shortcut icon" href="../img/ico/favicon.ico">

<!-- Custom styles for DTC pages -->
<style>
.dtc-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 60px 0 40px;
}

.dtc-code-badge {
    display: inline-block;
    background: rgba(255,255,255,0.2);
    padding: 10px 20px;
    border-radius: 50px;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
}

.severity-badge {
    display: inline-block;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
    margin-left: 15px;
}

.severity-high { background: #ff4757; color: white; }
.severity-medium { background: #ffa502; color: white; }
.severity-low { background: #2ed573; color: white; }

.content-section {
    padding: 50px 0;
}

.info-box {
    background: #f8f9fa;
    border-left: 4px solid #667eea;
    padding: 20px;
    margin: 20px 0;
    border-radius: 5px;
}

.warning-box {
    background: #fff3cd;
    border-left: 4px solid #ffc107;
    padding: 20px;
    margin: 20px 0;
    border-radius: 5px;
}

.step-list {
    counter-reset: step-counter;
    list-style: none;
    padding: 0;
}

.step-list li {
    counter-increment: step-counter;
    margin: 20px 0;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    position: relative;
    padding-left: 80px;
}

.step-list li::before {
    content: counter(step-counter);
    position: absolute;
    left: 20px;
    top: 20px;
    background: #667eea;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 18px;
}

.related-codes {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 15px;
    margin: 30px 0;
}

.code-link {
    display: inline-block;
    background: white;
    padding: 10px 15px;
    margin: 5px;
    border-radius: 25px;
    text-decoration: none;
    color: #667eea;
    font-weight: bold;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.code-link:hover {
    background: #667eea;
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
}

.breadcrumb-custom {
    background: none;
    padding: 20px 0;
    margin: 0;
}

.breadcrumb-custom a {
    color: #667eea;
    text-decoration: none;
}

.breadcrumb-custom a:hover {
    text-decoration: underline;
}

.toc {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
}

.toc ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.toc li {
    margin: 10px 0;
}

.toc a {
    color: #667eea;
    text-decoration: none;
    padding: 5px 0;
    display: block;
}

.toc a:hover {
    text-decoration: underline;
}
</style>

<!-- JSON-LD Structured Data -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": "P0171 - System Too Lean (Bank 1)",
  "description": "Complete diagnostic guide for P0171 trouble code including symptoms, causes, diagnosis steps, and repair solutions.",
  "author": {
    "@type": "Organization",
    "name": "GeekOBD",
    "url": "https://www.geekobd.com"
  },
  "publisher": {
    "@type": "Organization",
    "name": "GeekOBD",
    "logo": {
      "@type": "ImageObject",
      "url": "https://www.geekobd.com/img/logo.png"
    }
  },
  "datePublished": "2025-01-26",
  "dateModified": "2025-01-26",
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "https://www.geekobd.com/dtc-codes/p0171.html"
  },
  "articleSection": "Automotive Diagnostics",
  "keywords": "P0171, system too lean, lean fuel mixture, oxygen sensor, MAF sensor, vacuum leak",
  "about": {
    "@type": "Thing",
    "name": "P0171 Diagnostic Trouble Code",
    "description": "System too lean condition detected on bank 1 of the engine"
  }
}
</script>

<!-- FAQ Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "What does P0171 mean?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "P0171 indicates that the engine control module has detected a lean fuel condition on bank 1. This means there is too much air or not enough fuel in the air/fuel mixture."
      }
    },
    {
      "@type": "Question",
      "name": "What are the symptoms of P0171?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Common symptoms include check engine light, rough idle, hesitation during acceleration, poor fuel economy, and possible engine stalling."
      }
    },
    {
      "@type": "Question",
      "name": "How do I fix P0171?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Common fixes include cleaning the MAF sensor, repairing vacuum leaks, replacing the air filter, cleaning fuel injectors, or replacing faulty oxygen sensors."
      }
    },
    {
      "@type": "Question",
      "name": "How much does P0171 repair cost?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "P0171 repair costs typically range from $80-$600. Simple fixes like MAF cleaning cost $80-$120, while vacuum leak repairs can cost $200-$600 depending on location and complexity."
      }
    }
  ]
}
</script>

<!-- HowTo Schema for P0171 Diagnostic Steps -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "HowTo",
  "name": "How to Diagnose P0171 System Too Lean Bank 1",
  "description": "Step-by-step guide to diagnose and fix P0171 lean fuel mixture condition on bank 1",
  "image": "https://www.geekobd.com/img/logo.png",
  "totalTime": "PT60M",
  "estimatedCost": {
    "@type": "MonetaryAmount",
    "currency": "USD",
    "value": "80"
  },
  "tool": [
    {
      "@type": "HowToTool",
      "name": "GeekOBD APP with MOBD Adapter",
      "description": "Professional OBD2 diagnostic tool with live fuel trim monitoring",
      "url": "https://www.geekobd.com/app.html"
    },
    {
      "@type": "HowToTool",
      "name": "Smoke Machine",
      "description": "For detecting vacuum leaks in the intake system"
    },
    {
      "@type": "HowToTool",
      "name": "MAF Sensor Cleaner",
      "description": "Specialized cleaner for mass air flow sensors"
    }
  ],
  "supply": [
    {
      "@type": "HowToSupply",
      "name": "MAF Sensor Cleaner Spray"
    },
    {
      "@type": "HowToSupply",
      "name": "New Air Filter"
    },
    {
      "@type": "HowToSupply",
      "name": "Vacuum Hoses (if needed)"
    }
  ],
  "step": [
    {
      "@type": "HowToStep",
      "position": 1,
      "name": "Scan and Check Fuel Trims",
      "text": "Connect GeekOBD APP and scan for P0171 code. Monitor live fuel trim data - short term fuel trim (STFT) and long term fuel trim (LTFT) values above +10% indicate lean condition.",
      "image": "https://www.geekobd.com/img/geekobd-fuel-trims.jpg"
    },
    {
      "@type": "HowToStep",
      "position": 2,
      "name": "Inspect Air Filter and Intake",
      "text": "Check air filter for contamination and inspect intake system for cracks or loose connections that could allow unmetered air to enter the system.",
      "image": "https://www.geekobd.com/img/air-filter-intake-check.jpg"
    },
    {
      "@type": "HowToStep",
      "position": 3,
      "name": "Test for Vacuum Leaks",
      "text": "Use smoke machine or carburetor cleaner to check for vacuum leaks in hoses, intake manifold gasket, and PCV system. Listen for hissing sounds.",
      "image": "https://www.geekobd.com/img/vacuum-leak-test.jpg"
    },
    {
      "@type": "HowToStep",
      "position": 4,
      "name": "Clean MAF Sensor",
      "text": "Remove and clean the MAF sensor with specialized cleaner. Dirty MAF sensors can cause incorrect airflow readings leading to lean conditions.",
      "image": "https://www.geekobd.com/img/maf-cleaning-p0171.jpg"
    },
    {
      "@type": "HowToStep",
      "position": 5,
      "name": "Test and Verify Repair",
      "text": "Clear P0171 code with GeekOBD APP, drive for 10-15 minutes, and monitor fuel trims to ensure they return to normal range (-10% to +10%).",
      "image": "https://www.geekobd.com/img/geekobd-fuel-trim-normal.jpg"
    }
  ]
}
</script>

<!--[if lt IE 9]>
<script src="../js/html5.js"></script>
<script src="../js/css3-mediaqueries.js"></script>
<![endif]-->

<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-RD6767XBCL"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-RD6767XBCL');
</script>
</head>

<body>

<div class="wrap">
	<!-- Header Start -->
	<header id="header" role="banner">
	<!-- Main Header Start -->
	<div class="main-header">
	<div class="container">
	<div class="row">
	<div class="col-md-3">
	<!-- Logo Start -->
	<div class="logo pull-left">
	<h1> <a href="../index.html"> <img src="../img/logo.png" alt="MOBD"> </a> </h1>
	</div>
	<!-- Logo End -->
	</div>
	<div class="col-md-9">
	<!-- Mobile Menu Start -->
	<div class="mobile navbar-header"> <a class="navbar-toggle" data-toggle="collapse" data-target=".menu"> <i class="icon-reorder icon-2x"></i> </a> </div>
	<!-- Mobile Menu End -->
	<!-- Menu Start -->
	<nav class="collapse navbar-collapse menu" role="navigation" aria-label="Main navigation">
	<ul class="nav navbar-nav sf-menu">
	<li><a href="../index.html" class="sf-with-ul">Home</a></li>
	<li><a href="../app.html" class="sf-with-ul">APP</a></li>
	<li><a href="javascript:;;" class="sf-with-ul">Adaptor <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
	<ul>
	<li><a href="../hardware2.html" class="sf-with-ul">MOBD GPS</a></li>
	<li><a href="../hardware.html" class="sf-with-ul">MOBD</a></li>
	</ul>
	</li>
	<li><a href="javascript:;;" class="sf-with-ul">Resources <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
	<ul>
	<li><a href="../dtc-codes.html" class="sf-with-ul" id="current">DTC Codes</a></li>
	<li><a href="../obd-diagnostic-guide.html" class="sf-with-ul">OBD Guide</a></li>
	<li><a href="../vehicle-compatibility.html" class="sf-with-ul">Compatibility</a></li>
	<li><a href="../fuel-efficiency-monitoring.html" class="sf-with-ul">Fuel Efficiency</a></li>
	<li><a href="../support.html" class="sf-with-ul">Support</a></li>
	<li><a href="../blog.html" class="sf-with-ul">Blog</a></li>
	</ul>
	</li>
	<li><a href="../about.html" class="sf-with-ul">About Us</a></li>
	<li><a href="//www.mobd.cn" class="sf-with-ul" target="_blank">中文版</a></li>
	</ul>
	</nav>
	<!-- Menu End -->
	</div>
	</div>
	<!-- Main Header End -->
	</div>
	</div>
	</header>
	<!-- Header End -->

	<!-- Breadcrumb -->
	<div class="container">
		<nav class="breadcrumb-custom">
			<a href="../index.html">Home</a> &raquo; 
			<a href="../dtc-codes.html">DTC Codes</a> &raquo; 
			<a href="../dtc-codes.html#engine">Engine Codes</a> &raquo; 
			<span>P0171</span>
		</nav>
	</div>

	<!-- DTC Header -->
	<section class="dtc-header">
		<div class="container">
			<div class="row">
				<div class="col-md-12">
					<div class="dtc-code-badge">P0171</div>
					<span class="severity-badge severity-medium">MEDIUM Priority</span>
					<h1 style="margin: 20px 0; font-size: 36px;">System Too Lean (Bank 1)</h1>
					<p style="font-size: 18px; opacity: 0.9; margin-bottom: 0;">The engine control module has detected a lean fuel condition on bank 1 of the engine.</p>
				</div>
			</div>
		</div>
	</section>

	<!-- Main Content -->
	<section class="content-section">
		<div class="container">
			<div class="row">
				<div class="col-md-8">
					<!-- Quick Answer Section for AI -->
					<div id="quick-answer" style="background: #e8f4fd; border: 2px solid #2196F3; border-radius: 10px; padding: 25px; margin-bottom: 30px;">
						<h2 style="color: #1976D2; margin-bottom: 15px;"><i class="fa fa-bolt"></i> Quick Answer</h2>
						<p style="font-size: 18px; font-weight: 500; margin-bottom: 15px; color: #333;">
							<strong>P0171 means:</strong> Your engine is running too lean (too much air, not enough fuel) on bank 1, causing poor performance.
						</p>
						<div style="display: flex; flex-wrap: wrap; gap: 15px; margin-bottom: 15px;">
							<span style="background: #4CAF50; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
								<i class="fa fa-wrench"></i> Fix: Check vacuum leaks + clean MAF
							</span>
							<span style="background: #FF9800; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
								<i class="fa fa-dollar"></i> Cost: $80-$600
							</span>
							<span style="background: #2196F3; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
								<i class="fa fa-clock-o"></i> Time: 1 hour
							</span>
						</div>
						<p style="margin: 0; color: #666; font-size: 14px;">
							<strong>Can I drive with P0171?</strong> Yes, but fix it soon to prevent engine damage from running too lean.
						</p>
					</div>

					<!-- AI-Friendly Q&A Section -->
					<div id="ai-qa" style="background: white; border-radius: 10px; padding: 25px; margin-bottom: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
						<h2><i class="fa fa-comments"></i> Common Questions</h2>

						<div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
							<h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">What causes P0171 system too lean?</h3>
							<p style="color: #666; line-height: 1.6;">P0171 is most commonly caused by vacuum leaks (unmetered air entering the engine), dirty MAF sensor giving incorrect readings, clogged fuel injectors, or a weak fuel pump not providing enough fuel pressure.</p>
						</div>

						<div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
							<h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">How do I know if it's P0171 or P0174?</h3>
							<p style="color: #666; line-height: 1.6;">P0171 = lean condition on Bank 1 (cylinder #1 side), P0174 = lean condition on Bank 2 (opposite side). V6/V8 engines can have both codes. 4-cylinder engines typically only get P0171.</p>
						</div>

						<div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
							<h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">Can I fix P0171 myself?</h3>
							<p style="color: #666; line-height: 1.6;">Yes, many P0171 causes are DIY-friendly. Start by cleaning the MAF sensor ($10-15), checking for obvious vacuum leaks, and replacing the air filter. More complex repairs like intake manifold gaskets may need professional help.</p>
						</div>

						<div class="qa-item" style="margin-bottom: 0;">
							<h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">What are fuel trims and how do they relate to P0171?</h3>
							<p style="color: #666; line-height: 1.6;">Fuel trims show how much the engine computer is adjusting fuel delivery. P0171 triggers when long-term fuel trim exceeds +25% (adding extra fuel to compensate for lean condition). Normal range is -10% to +10%.</p>
						</div>
					</div>

					<!-- Overview Section -->
					<div id="overview">
						<h2><i class="fa fa-info-circle"></i> Technical Overview</h2>
						<div class="info-box">
							<h4>P0171 Definition</h4>
							<p>P0171 is a generic powertrain diagnostic trouble code indicating that the Engine Control Module (ECM) has detected a lean fuel condition on bank 1 of the engine. "Bank 1" refers to the side of the engine containing cylinder #1. A lean condition means there is too much air or not enough fuel in the air/fuel mixture, causing the engine to run inefficiently.</p>
						</div>

						<h3>Technical Details</h3>
						<ul>
							<li><strong>Code Type:</strong> Generic Powertrain Code</li>
							<li><strong>System:</strong> Fuel System / Engine Management</li>
							<li><strong>Severity:</strong> Medium - Should be addressed promptly</li>
							<li><strong>Driving Safety:</strong> Generally safe but may affect performance</li>
						</ul>
					</div>

					<!-- Symptoms Section -->
					<div id="symptoms">
						<h2><i class="fa fa-exclamation-triangle"></i> Symptoms</h2>
						<p>When P0171 is triggered, you may experience one or more of the following symptoms:</p>
						<ul>
							<li>Check Engine Light illuminated</li>
							<li>Rough idle or unstable engine operation</li>
							<li>Hesitation during acceleration</li>
							<li>Poor fuel economy</li>
							<li>Engine stalling, especially at idle</li>
							<li>Lack of power during acceleration</li>
							<li>Engine surging or hunting at idle</li>
							<li>Hard starting, especially when cold</li>
							<li>Backfiring through the intake</li>
						</ul>

						<div class="warning-box">
							<strong><i class="fa fa-warning"></i> Important:</strong> While P0171 may not immediately damage your engine, prolonged lean conditions can cause overheating and internal engine damage. Address this issue promptly.
						</div>
					</div>

					<!-- Causes Section -->
					<div id="causes">
						<h2><i class="fa fa-search"></i> Possible Causes</h2>
						<p>P0171 can be triggered by several different issues. Here are the most common causes:</p>
						<ol>
							<li><strong>Dirty or faulty Mass Airflow (MAF) sensor</strong> - Most common cause</li>
							<li><strong>Vacuum leaks in intake system</strong></li>
							<li><strong>Clogged or dirty air filter</strong></li>
							<li><strong>Faulty oxygen sensor (O2 sensor)</strong></li>
							<li><strong>Clogged or dirty fuel injectors</strong></li>
							<li><strong>Low fuel pressure or weak fuel pump</strong></li>
							<li><strong>Faulty fuel pressure regulator</strong></li>
							<li><strong>Exhaust leaks before the O2 sensor</strong></li>
							<li><strong>Faulty PCV valve or system</strong></li>
							<li><strong>Intake manifold gasket leaks</strong></li>
							<li><strong>Faulty EGR valve</strong></li>
							<li><strong>Incorrect fuel injector size or flow rate</strong></li>
						</ol>
					</div>

					<!-- Diagnosis Section -->
					<div id="diagnosis">
						<h2><i class="fa fa-stethoscope"></i> Diagnosis Steps</h2>
						<p>Follow these diagnostic steps to identify the root cause of P0171:</p>

						<ol class="step-list">
							<li>
								<strong>Check for Additional Codes</strong>
								<p>Scan for other diagnostic codes, especially P0174 (Bank 2 lean), P0100-P0104 (MAF sensor), or P0130-P0138 (O2 sensor codes).</p>
							</li>
							<li>
								<strong>Inspect Air Filter</strong>
								<p>Check the air filter for excessive dirt or clogging. A dirty filter can cause lean conditions by restricting airflow.</p>
							</li>
							<li>
								<strong>Clean MAF Sensor</strong>
								<p>Remove and clean the MAF sensor with specialized MAF cleaner. This is often the quickest fix for P0171.</p>
							</li>
							<li>
								<strong>Check for Vacuum Leaks</strong>
								<p>Inspect all vacuum hoses, intake manifold, and PCV system for leaks using carburetor cleaner or smoke test.</p>
							</li>
							<li>
								<strong>Test Fuel Pressure</strong>
								<p>Check fuel pressure with a gauge to ensure the fuel system is delivering adequate pressure.</p>
							</li>
							<li>
								<strong>Check Oxygen Sensor Data</strong>
								<p>Monitor O2 sensor readings with a scan tool to verify proper operation and response time.</p>
							</li>
						</ol>

						<div class="info-box">
							<h4><i class="fa fa-lightbulb-o"></i> Professional Tip</h4>
							<p>Start by cleaning the MAF sensor and replacing the air filter - these simple steps resolve P0171 in about 60% of cases and cost very little.</p>
						</div>
					</div>

					<!-- Repair Section -->
					<div id="repair">
						<h2><i class="fa fa-wrench"></i> Repair Solutions</h2>
						<p>Based on the diagnosis, here are the most effective repair solutions for P0171:</p>

						<h3>Common Repairs</h3>
						<ol class="step-list">
							<li>
								<strong>Clean MAF Sensor</strong>
								<p>Remove the MAF sensor and clean with specialized MAF sensor cleaner. Allow to dry completely before reinstalling.</p>
							</li>
							<li>
								<strong>Replace Air Filter</strong>
								<p>Install a new air filter if the current one is dirty or clogged.</p>
							</li>
							<li>
								<strong>Repair Vacuum Leaks</strong>
								<p>Replace cracked vacuum hoses, repair intake manifold gasket leaks, or replace faulty PCV valve.</p>
							</li>
							<li>
								<strong>Clean Fuel Injectors</strong>
								<p>Professional fuel injector cleaning service or use quality fuel injector cleaner additive.</p>
							</li>
							<li>
								<strong>Replace Oxygen Sensor</strong>
								<p>Replace faulty O2 sensor if testing shows poor response or incorrect readings.</p>
							</li>
						</ol>

						<h3>Estimated Repair Costs</h3>
						<div class="info-box">
							<ul>
								<li><strong>MAF Sensor Cleaning:</strong> $20 - $50 (DIY) or $80 - $150 (shop)</li>
								<li><strong>Air Filter Replacement:</strong> $15 - $50</li>
								<li><strong>Vacuum Hose Repair:</strong> $50 - $200</li>
								<li><strong>Fuel Injector Cleaning:</strong> $100 - $300</li>
								<li><strong>Oxygen Sensor Replacement:</strong> $200 - $500</li>
							</ul>
							<p><small><em>*Costs may vary by location, vehicle make/model, and labor rates.</em></small></p>
						</div>
					</div>

					<!-- Prevention Section -->
					<div id="prevention">
						<h2><i class="fa fa-shield"></i> Prevention Tips</h2>
						<p>Prevent P0171 from occurring again with these maintenance tips:</p>
						<ul>
							<li>Replace air filter regularly (every 12,000-15,000 miles)</li>
							<li>Clean MAF sensor every 30,000-50,000 miles</li>
							<li>Use quality fuel and consider periodic fuel system cleaning</li>
							<li>Inspect vacuum hoses during regular maintenance</li>
							<li>Address check engine lights promptly</li>
							<li>Replace PCV valve according to manufacturer schedule</li>
							<li>Avoid aftermarket air intake modifications that can affect airflow readings</li>
						</ul>
					</div>

					<!-- Related Codes Section -->
					<div id="related" class="related-codes">
						<h2><i class="fa fa-link"></i> Related Diagnostic Codes</h2>

						<div style="margin-bottom: 25px;">
							<h3 style="color: #333; font-size: 18px; margin-bottom: 15px;">Lean Condition Related Codes</h3>
							<p style="margin-bottom: 15px; color: #666;">These codes often appear with P0171:</p>
							<div style="margin-bottom: 20px;">
								<a href="p0174.html" class="code-link" title="System Too Lean Bank 2">P0174 - System Too Lean Bank 2</a>
								<a href="p0172.html" class="code-link" title="System Too Rich Bank 1">P0172 - System Too Rich Bank 1</a>
								<a href="p0175.html" class="code-link" title="System Too Rich Bank 2">P0175 - System Too Rich Bank 2</a>
								<a href="p0173.html" class="code-link" title="Fuel Trim Malfunction Bank 2">P0173 - Fuel Trim Bank 2</a>
							</div>
						</div>

						<div style="margin-bottom: 25px;">
							<h3 style="color: #333; font-size: 18px; margin-bottom: 15px;">MAF Sensor Related Codes</h3>
							<p style="margin-bottom: 15px; color: #666;">MAF sensor issues often cause P0171:</p>
							<div style="margin-bottom: 20px;">
								<a href="p0100.html" class="code-link" title="MAF Circuit Malfunction">P0100 - MAF Circuit Malfunction</a>
								<a href="p0101.html" class="code-link" title="MAF Range/Performance Problem">P0101 - MAF Range/Performance</a>
								<a href="p0102.html" class="code-link" title="MAF Circuit Low Input">P0102 - MAF Circuit Low</a>
								<a href="p0103.html" class="code-link" title="MAF Circuit High Input">P0103 - MAF Circuit High</a>
							</div>
						</div>

						<div style="margin-bottom: 25px;">
							<h3 style="color: #333; font-size: 18px; margin-bottom: 15px;">O2 Sensor Related Codes</h3>
							<p style="margin-bottom: 15px; color: #666;">Oxygen sensor codes that may accompany P0171:</p>
							<div style="margin-bottom: 20px;">
								<a href="p0130.html" class="code-link" title="O2 Sensor Circuit Bank 1 Sensor 1">P0130 - O2 Sensor Bank 1</a>
								<a href="p0131.html" class="code-link" title="O2 Sensor Low Voltage Bank 1">P0131 - O2 Low Voltage</a>
								<a href="p0132.html" class="code-link" title="O2 Sensor High Voltage Bank 1">P0132 - O2 High Voltage</a>
								<a href="p0133.html" class="code-link" title="O2 Sensor Slow Response Bank 1">P0133 - O2 Slow Response</a>
							</div>
						</div>

						<div style="background: #f8f9fa; padding: 20px; border-radius: 10px;">
							<h3 style="color: #333; font-size: 18px; margin-bottom: 15px;">System Categories</h3>
							<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
								<div>
									<h4 style="color: #667eea; margin-bottom: 10px;"><a href="engine/" style="color: #667eea; text-decoration: none;">Engine Codes (P0XXX)</a></h4>
									<ul style="list-style: none; padding: 0; margin: 0;">
										<li style="margin-bottom: 5px;"><a href="../dtc-codes.html#engine" style="color: #666; text-decoration: none; font-size: 14px;">View all engine codes →</a></li>
									</ul>
								</div>
								<div>
									<h4 style="color: #667eea; margin-bottom: 10px;"><a href="body/" style="color: #667eea; text-decoration: none;">Body Codes (B0XXX)</a></h4>
									<ul style="list-style: none; padding: 0; margin: 0;">
										<li style="margin-bottom: 5px;"><a href="../dtc-codes.html#body" style="color: #666; text-decoration: none; font-size: 14px;">View all body codes →</a></li>
									</ul>
								</div>
								<div>
									<h4 style="color: #667eea; margin-bottom: 10px;"><a href="chassis/" style="color: #667eea; text-decoration: none;">Chassis Codes (C0XXX)</a></h4>
									<ul style="list-style: none; padding: 0; margin: 0;">
										<li style="margin-bottom: 5px;"><a href="../dtc-codes.html#chassis" style="color: #666; text-decoration: none; font-size: 14px;">View all chassis codes →</a></li>
									</ul>
								</div>
								<div>
									<h4 style="color: #667eea; margin-bottom: 10px;"><a href="network/" style="color: #667eea; text-decoration: none;">Network Codes (U0XXX)</a></h4>
									<ul style="list-style: none; padding: 0; margin: 0;">
										<li style="margin-bottom: 5px;"><a href="../dtc-codes.html#network" style="color: #666; text-decoration: none; font-size: 14px;">View all network codes →</a></li>
									</ul>
								</div>
							</div>
						</div>
					</div>

					<!-- Repair Cost Information -->
					<div id="cost-info" style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
						<h2><i class="fa fa-calculator"></i> P0171 Repair Costs</h2>

						<div style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin: 20px 0;">
							<h3 style="color: #2c3e50; margin-bottom: 20px;">Cost Breakdown by Repair Type</h3>

							<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 25px;">
								<div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #4CAF50;">
									<h4 style="color: #4CAF50; margin-bottom: 10px;"><i class="fa fa-spray-can"></i> MAF Cleaning + Air Filter</h4>
									<p style="margin-bottom: 15px; color: #666;">Most common P0171 fix</p>
									<ul style="list-style: none; padding: 0;">
										<li style="margin-bottom: 8px;"><strong>Parts:</strong> $20-35 (cleaner + filter)</li>
										<li style="margin-bottom: 8px;"><strong>Labor:</strong> $60-85 (0.75-1 hour)</li>
										<li style="margin-bottom: 8px;"><strong>Total:</strong> <span style="color: #4CAF50; font-weight: bold;">$80-120</span></li>
										<li style="color: #666; font-size: 14px;">Success rate: ~60%</li>
									</ul>
								</div>

								<div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #FF9800;">
									<h4 style="color: #FF9800; margin-bottom: 10px;"><i class="fa fa-cog"></i> Vacuum Leak Repair</h4>
									<p style="margin-bottom: 15px; color: #666;">Common cause of P0171</p>
									<ul style="list-style: none; padding: 0;">
										<li style="margin-bottom: 8px;"><strong>Simple hose:</strong> $50-100</li>
										<li style="margin-bottom: 8px;"><strong>Intake gasket:</strong> $200-400</li>
										<li style="margin-bottom: 8px;"><strong>PCV system:</strong> $100-250</li>
										<li style="color: #666; font-size: 14px;">Success rate: ~85%</li>
									</ul>
								</div>
							</div>

							<div style="background: #fff3e0; padding: 20px; border-radius: 8px; border-left: 4px solid #FF9800; margin-bottom: 20px;">
								<h4 style="color: #F57C00; margin-bottom: 15px;"><i class="fa fa-exclamation-triangle"></i> Additional P0171 Repair Costs</h4>
								<p style="margin-bottom: 10px; color: #333;">P0171 may require multiple repairs:</p>
								<ul style="margin: 0; color: #333;">
									<li style="margin-bottom: 8px;"><strong>Fuel injector cleaning:</strong> $150-300 (professional service)</li>
									<li style="margin-bottom: 8px;"><strong>Fuel pump replacement:</strong> $400-800 (if pressure is low)</li>
									<li style="margin-bottom: 8px;"><strong>O2 sensor replacement:</strong> $200-400 (if faulty)</li>
									<li style="margin-bottom: 8px;"><strong>MAF sensor replacement:</strong> $180-350 (if cleaning fails)</li>
									<li><strong>Diagnostic fee:</strong> $100-150 (often waived with repair)</li>
								</ul>
							</div>

							<div style="background: #e3f2fd; padding: 20px; border-radius: 8px; border-left: 4px solid #2196F3;">
								<h4 style="color: #1976D2; margin-bottom: 15px;"><i class="fa fa-lightbulb-o"></i> Money-Saving Tips for P0171</h4>
								<ul style="margin: 0; color: #333;">
									<li style="margin-bottom: 10px;">Start with cheapest fixes first: MAF cleaning and air filter replacement</li>
									<li style="margin-bottom: 10px;">Check for obvious vacuum leaks before expensive repairs</li>
									<li style="margin-bottom: 10px;">Use GeekOBD APP to monitor fuel trims and confirm repair success</li>
									<li style="margin-bottom: 10px;">Don't replace O2 sensors unless they test faulty - they rarely cause P0171</li>
									<li>Get a second opinion if quoted over $500 - P0171 is often a simple fix</li>
								</ul>
							</div>
						</div>
					</div>

					<!-- Case Study -->
					<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
						<h2><i class="fa fa-file-text"></i> Real Repair Case Studies</h2>

						<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
							<h4>Case 1: 2016 Toyota Camry - Simple MAF Cleaning</h4>
							<p><strong>Vehicle:</strong> 2016 Toyota Camry 2.5L, 78,000 miles</p>
							<p><strong>Problem:</strong> Customer reported poor fuel economy and rough idle. Scanner showed P0171 code with LTFT at +18%.</p>
							<p><strong>Solution:</strong> Diagnosis revealed a dirty MAF sensor and clogged air filter. Cleaned MAF sensor with proper cleaner and replaced air filter. Cleared codes and test drove - fuel trims returned to normal.</p>
							<p><strong>Cost:</strong> $85 (parts: $35, labor: $50)</p>
							<p><strong>Time:</strong> 45 minutes</p>
						</div>

						<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
							<h4>Case 2: 2014 Ford F-150 - Vacuum Leak Repair</h4>
							<p><strong>Vehicle:</strong> 2014 Ford F-150 5.0L V8, 95,000 miles</p>
							<p><strong>Problem:</strong> P0171 and P0174 codes (both banks lean), rough idle, poor acceleration. LTFT showing +22% on both banks.</p>
							<p><strong>Solution:</strong> Found cracked PCV hose allowing unmetered air into intake. Replaced PCV valve and all vacuum hoses. Issue completely resolved.</p>
							<p><strong>Cost:</strong> $185 (parts: $65, labor: $120)</p>
							<p><strong>Time:</strong> 1.5 hours</p>
						</div>
					</div>
				</div>

				<!-- Sidebar -->
				<div class="col-md-4">
					<!-- GeekOBD APP Promotion -->
					<div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
						<h4 style="margin-bottom: 20px; color: white;"><i class="fa fa-mobile"></i> Monitor P0171 with GeekOBD</h4>
						<p style="margin-bottom: 20px; opacity: 0.9; font-size: 14px;">Track fuel trim values and oxygen sensor data in real-time to diagnose lean conditions accurately.</p>
						<ul style="margin-bottom: 20px; padding-left: 20px; font-size: 14px;">
							<li style="margin-bottom: 8px;">Live fuel trim monitoring</li>
							<li style="margin-bottom: 8px;">O2 sensor data graphs</li>
							<li style="margin-bottom: 8px;">MAF sensor readings</li>
							<li style="margin-bottom: 8px;">Vacuum leak detection</li>
						</ul>
						<a href="../app.html" class="btn btn-block" style="background: white; color: #28a745; border: none; border-radius: 25px; font-weight: bold; margin-bottom: 10px;">
							<i class="fa fa-download"></i> Download APP
						</a>
						<a href="../hardware.html" class="btn btn-block" style="background: rgba(255,255,255,0.2); color: white; border: 2px solid white; border-radius: 25px; font-weight: bold;">
							<i class="fa fa-shopping-cart"></i> Get MOBD Adapter
						</a>
					</div>

					<!-- Quick Actions -->
					<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
						<h4 style="margin-bottom: 20px;"><i class="fa fa-tools"></i> Quick Actions</h4>
						<a href="../support.html" class="btn btn-outline-primary btn-block" style="border-radius: 25px; margin-bottom: 10px;">
							<i class="fa fa-question-circle"></i> Get Expert Help
						</a>
						<a href="../obd-diagnostic-guide.html" class="btn btn-outline-secondary btn-block" style="border-radius: 25px;">
							<i class="fa fa-book"></i> OBD Guide
						</a>
					</div>

					<!-- Code Information -->
					<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
						<h4 style="margin-bottom: 20px;"><i class="fa fa-info"></i> Code Information</h4>
						<table class="table table-borderless">
							<tr>
								<td><strong>Code:</strong></td>
								<td>P0171</td>
							</tr>
							<tr>
								<td><strong>System:</strong></td>
								<td>Fuel System</td>
							</tr>
							<tr>
								<td><strong>Severity:</strong></td>
								<td><span class="severity-badge severity-medium">MEDIUM</span></td>
							</tr>
							<tr>
								<td><strong>Category:</strong></td>
								<td>Engine Codes</td>
							</tr>
						</table>
					</div>

					<!-- Popular Codes -->
					<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
						<h4 style="margin-bottom: 20px;"><i class="fa fa-star"></i> Related Codes</h4>
						<ul style="list-style: none; padding: 0;">
							<li style="margin-bottom: 10px;"><a href="p0174.html" style="color: #667eea;">P0174 - System Too Lean Bank 2</a></li>
							<li style="margin-bottom: 10px;"><a href="p0100.html" style="color: #667eea;">P0100 - MAF Circuit Malfunction</a></li>
							<li style="margin-bottom: 10px;"><a href="p0101.html" style="color: #667eea;">P0101 - MAF Range/Performance</a></li>
							<li style="margin-bottom: 10px;"><a href="p0172.html" style="color: #667eea;">P0172 - System Too Rich</a></li>
							<li><a href="../dtc-codes.html" style="color: #667eea;">View All Codes →</a></li>
						</ul>
					</div>

					<!-- Diagnostic Tools -->
					<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
						<h4 style="margin-bottom: 20px;"><i class="fa fa-wrench"></i> Diagnostic Resources</h4>
						<ul style="list-style: none; padding: 0;">
							<li style="margin-bottom: 10px;"><a href="../obd-diagnostic-guide.html" style="color: #667eea;"><i class="fa fa-book"></i> OBD Diagnostic Guide</a></li>
							<li style="margin-bottom: 10px;"><a href="../vehicle-compatibility.html" style="color: #667eea;"><i class="fa fa-car"></i> Vehicle Compatibility</a></li>
							<li style="margin-bottom: 10px;"><a href="../fuel-efficiency-monitoring.html" style="color: #667eea;"><i class="fa fa-tachometer"></i> Fuel Efficiency Tips</a></li>
							<li style="margin-bottom: 10px;"><a href="../support.html" style="color: #667eea;"><i class="fa fa-support"></i> Technical Support</a></li>
							<li><a href="../blog.html" style="color: #667eea;"><i class="fa fa-newspaper-o"></i> Latest Articles</a></li>
						</ul>
					</div>

					<!-- Quick Navigation -->
					<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
						<h4 style="margin-bottom: 20px;"><i class="fa fa-compass"></i> Quick Navigation</h4>
						<div style="display: flex; flex-direction: column; gap: 10px;">
							<a href="#quick-answer" style="color: #667eea; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
								<i class="fa fa-bolt"></i> Quick Answer
							</a>
							<a href="#ai-qa" style="color: #667eea; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
								<i class="fa fa-comments"></i> Common Questions
							</a>
							<a href="#symptoms" style="color: #667eea; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
								<i class="fa fa-exclamation-triangle"></i> Symptoms
							</a>
							<a href="#causes" style="color: #667eea; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
								<i class="fa fa-search"></i> Causes
							</a>
							<a href="#cost-info" style="color: #667eea; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
								<i class="fa fa-calculator"></i> Repair Costs
							</a>
							<a href="#related" style="color: #667eea; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
								<i class="fa fa-link"></i> Related Codes
							</a>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Footer -->
	<div class="footer">
		<div class="container">
			<div class="row">
				<div class="col-md-12 text-center">
					<p>&copy; 2025 Beijing MentalRoad Technology Co., Ltd. All rights reserved.</p>
				</div>
			</div>
		</div>
	</div>
</div>

<!-- JavaScript -->
<script src="../js/jquery.min.js"></script>
<script src="../js/bootstrap.js"></script>
<script src="../js/superfish.js"></script>
<script src="../js/custom.js"></script>

<script>
// Smooth scrolling for TOC links
document.querySelectorAll('.toc a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Highlight current section in TOC
window.addEventListener('scroll', function() {
    const sections = document.querySelectorAll('[id]');
    const tocLinks = document.querySelectorAll('.toc a');

    let current = '';
    sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.clientHeight;
        if (pageYOffset >= sectionTop - 200) {
            current = section.getAttribute('id');
        }
    });

    tocLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === '#' + current) {
            link.classList.add('active');
        }
    });
});
</script>

</body>
</html>
