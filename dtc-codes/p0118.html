<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>P0118 - ECT Sensor High Input | GeekOBD</title>
    <meta name="description" content="The Engine Control Module has detected that the Engine Coolant Temperature sensor is reading extremely cold temperatures.">
    <meta name="keywords" content="P0118, P0118, diagnostic trouble code, OBD2, car repair, engine problems, GeekOBD">
    <link rel="canonical" href="https://www.geekobd.com/dtc-codes/p0118.html">
    
    <!-- Open Graph -->
    <meta property="og:title" content="P0118 - ECT Sensor High Input">
    <meta property="og:description" content="The Engine Control Module has detected that the Engine Coolant Temperature sensor is reading extremely cold temperatures.">
    <meta property="og:url" content="https://www.geekobd.com/dtc-codes/p0118.html">
    <meta property="og:type" content="article">
    
    <!-- CSS -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../css/fonts/font-awesome/css/font-awesome.css">
    <link rel="stylesheet" href="../css/animations.css" media="screen">
    <link rel="stylesheet" href="../css/superfish.css" media="screen">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/colors/blue.css" id="colors">
    <link rel="stylesheet" href="../css/theme-responsive.css">
    <link rel="stylesheet" href="../css/seo-enhancements.css">
    <link rel="shortcut icon" href="../img/ico/favicon.ico">

    <style>
    .dtc-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 60px 0 40px;
    }
    .dtc-code-badge {
        display: inline-block;
        background: rgba(255,255,255,0.2);
        padding: 10px 20px;
        border-radius: 50px;
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 20px;
    }
    .severity-badge {
        display: inline-block;
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
        text-transform: uppercase;
        margin-left: 15px;
    }
    .severity-high { background: #ff4757; color: white; }
    .severity-medium { background: #ffa502; color: white; }
    .content-section { padding: 50px 0; }
    .info-box {
        background: #f8f9fa;
        border-left: 4px solid #667eea;
        padding: 20px;
        margin: 20px 0;
        border-radius: 5px;
    }
    .warning-box {
        background: #fff3cd;
        border-left: 4px solid #ffc107;
        padding: 20px;
        margin: 20px 0;
        border-radius: 5px;
    }
    .danger-box {
        background: #f8d7da;
        border-left: 4px solid #dc3545;
        padding: 20px;
        margin: 20px 0;
        border-radius: 5px;
    }
    .breadcrumb-custom {
        background: none;
        padding: 20px 0;
        margin: 0;
    }
    .breadcrumb-custom a {
        color: #667eea;
        text-decoration: none;
    }
    .breadcrumb-custom a:hover {
        text-decoration: underline;
    }
    </style>

    
<!-- Article Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": "P0118 - ECT Sensor High Input",
  "description": "The Engine Control Module has detected that the Engine Coolant Temperature sensor is reading extremely cold temperatures.",
  "author": {
    "@type": "Organization",
    "name": "GeekOBD",
    "url": "https://www.geekobd.com"
  },
  "publisher": {
    "@type": "Organization",
    "name": "GeekOBD",
    "logo": {
      "@type": "ImageObject",
      "url": "https://www.geekobd.com/images/logo.png"
    }
  },
  "datePublished": "2025-07-31",
  "dateModified": "2025-07-31",
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "https://www.geekobd.com/dtc-codes/p0118.html"
  }
}
</script>

<!-- FAQ Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "What's the difference between P0118 and P0117 ECT codes?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "P0118 indicates the ECT sensor is reading too cold (high input voltage/open circuit), while P0117 indicates too hot readings (low input voltage/short circuit). P0118 is more dangerous because cooling fans won't turn on, potentially causing overheating."
      }
    },
    {
      "@type": "Question",
      "name": "Why is P0118 more serious than other ECT codes?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "P0118 prevents cooling fans from operating because the ECM thinks the engine is cold. This can lead to actual engine overheating, especially in stop-and-go traffic or hot weather. The lean fuel mixture and advanced timing can also cause engine knock and damage."
      }
    },
    {
      "@type": "Question",
      "name": "Can P0118 cause engine overheating?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Yes, P0118 is one of the few diagnostic codes that can directly cause engine overheating. When the ECM reads -40°F from the ECT sensor, it never turns on the cooling fans, even when the engine reaches dangerous temperatures. This makes P0118 a high-priority repair."
      }
    },
    {
      "@type": "Question",
      "name": "How do I test for P0118 open circuit?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Disconnect the ECT sensor and measure resistance - if it reads infinite ohms (OL on multimeter), the sensor has failed open. Check wiring continuity from sensor to ECM. GeekOBD APP will show -40°F reading when circuit is open, confirming the diagnosis."
      }
    }
  ]
}
</script>

<!-- HowTo Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "HowTo",
  "name": "How to Diagnose P0118 ECT Sensor High Input",
  "description": "Step-by-step guide to diagnose and fix P0118",
  "totalTime": "PT60M",
  "estimatedCost": {
    "@type": "MonetaryAmount",
    "currency": "USD",
    "value": "$90-$380 for most P0118 repairs"
  },
  "tool": [
    {
      "@type": "HowToTool",
      "name": "GeekOBD APP with MOBD Adapter",
      "description": "Professional OBD2 diagnostic tool",
      "url": "https://www.geekobd.com/app.html"
    }
  ],
  "step": [
    {
      "@type": "HowToStep",
      "position": 1,
      "name": "Verify P0118 Code and Check Cooling System",
      "text": "Connect scan tool and confirm P0118 is present. Check current ECT reading - should show -40°F even when engine is warm. IMMEDIATELY check if cooling fans are working and monitor engine temperature to prevent overheating."
    },
    {
      "@type": "HowToStep",
      "position": 2,
      "name": "Visual Inspection and Safety Check",
      "text": "Locate ECT sensor and inspect for physical damage, loose connections, or coolant leaks. Check that cooling fans can be manually activated. Ensure engine is not overheating before proceeding."
    },
    {
      "@type": "HowToStep",
      "position": 3,
      "name": "Test ECT Sensor Resistance",
      "text": "Disconnect sensor and measure resistance across terminals. If multimeter reads \"OL\" (infinite resistance), sensor has failed open. Normal resistance at operating temperature should be 200-1000 ohms."
    },
    {
      "@type": "HowToStep",
      "position": 4,
      "name": "Check Wiring Continuity",
      "text": "Test continuity from ECT sensor connector to ECM. Check for broken wires, corroded connections, or damaged harness. Pay special attention to areas near heat sources."
    },
    {
      "@type": "HowToStep",
      "position": 5,
      "name": "Verify Repair and Monitor Temperature",
      "text": "After replacing sensor or repairing wiring, clear codes and monitor ECT readings. Verify cooling fans activate when temperature reaches normal operating range (195-220°F)."
    }
  ]
}
</script>
</head>
<body>
    <div class="wrap">
	<header id="header" role="banner">
	<div class="main-header">
	<div class="container">
	<div class="row">
	<div class="col-md-3">
	<div class="logo pull-left">
	<h1> <a href="../index.html"> <img src="../img/logo.png" alt="MOBD"> </a> </h1>
	</div>
	</div>
	<div class="col-md-9">
	<div class="mobile navbar-header"> <a class="navbar-toggle" data-toggle="collapse" data-target=".menu"> <i class="icon-reorder icon-2x"></i> </a> </div>
	<nav class="collapse navbar-collapse menu" role="navigation" aria-label="Main navigation">
	<ul class="nav navbar-nav sf-menu">
	<li><a href="../index.html" class="sf-with-ul">Home</a></li>
	<li><a href="../app.html" class="sf-with-ul">APP</a></li>
	<li><a href="javascript:;;" class="sf-with-ul">Adaptor <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
	<ul>
	<li><a href="../hardware2.html" class="sf-with-ul">MOBD GPS</a></li>
	<li><a href="../hardware.html" class="sf-with-ul">MOBD</a></li>
	</ul>
	</li>
	<li><a href="javascript:;;" class="sf-with-ul">Resources <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
	<ul>
	<li><a href="../dtc-codes.html" class="sf-with-ul">DTC Codes</a></li>
	<li><a href="../obd-diagnostic-guide.html" class="sf-with-ul">OBD Guide</a></li>
	<li><a href="../vehicle-compatibility.html" class="sf-with-ul">Compatibility</a></li>
	<li><a href="../fuel-efficiency-monitoring.html" class="sf-with-ul">Fuel Efficiency</a></li>
	<li><a href="../support.html" class="sf-with-ul">Support</a></li>
	<li><a href="../blog.html" class="sf-with-ul">Blog</a></li>
	</ul>
	</li>
	<li><a href="../about.html" class="sf-with-ul">About Us</a></li>
	<li><a href="//www.mobd.cn" class="sf-with-ul" target="_blank">中文版</a></li>
	</ul>
	</nav>
	</div>
	</div>
	</div>
	</div>
	</header>

    <!-- Breadcrumb -->
    <div class="container">
        <nav class="breadcrumb-custom">
            <a href="../index.html">Home</a> &raquo;
            <a href="../dtc-codes.html">DTC Codes</a> &raquo;
            <a href="../dtc-codes.html#engine">Engine Codes</a> &raquo;
            <span>P0118</span>
        </nav>
    </div>

	<section class="dtc-header">
		<div class="container">
			<div class="row">
				<div class="col-md-12">
					<div class="dtc-code-badge">P0118</div>
					<span class="severity-badge severity-high">HIGH Priority</span>
					<h1 style="margin: 20px 0; font-size: 36px;">ECT Sensor High Input</h1>
					<p style="font-size: 18px; opacity: 0.9; margin-bottom: 0;">The Engine Control Module has detected that the Engine Coolant Temperature sensor is reading extremely cold temperatures.</p>
				</div>
			</div>
		</div>
	</section>

    <!-- Main Content -->
    <section class="content-section">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    
<!-- Quick Answer Section for AI -->
<div id="quick-answer" style="background: #e8f4fd; border: 2px solid #4a90e2; border-radius: 10px; padding: 25px; margin-bottom: 30px;">
    <h2 style="color: #2c5aa0; margin-bottom: 15px;"><i class="fa fa-snowflake-o"></i> Quick Answer</h2>
    <p style="font-size: 18px; font-weight: 500; margin-bottom: 15px; color: #333;">
        <strong>P0118 means:</strong> ECT sensor reading extremely cold temperatures (-40°F) when coolant is actually warm - usually open circuit or sensor failure.
    </p>
    <div style="display: flex; flex-wrap: wrap; gap: 15px; margin-bottom: 15px;">
        <span style="background: #4CAF50; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
            <i class="fa fa-wrench"></i> Fix: Replace ECT sensor or repair open circuit
        </span>
        <span style="background: #FF9800; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
            <i class="fa fa-dollar"></i> Cost: $90-$380
        </span>
        <span style="background: #2196F3; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
            <i class="fa fa-clock-o"></i> Time: 45-75 minutes
        </span>
    </div>
    <p style="margin: 0; color: #666; font-size: 14px;">
        <strong>Can I drive with P0118?</strong> Risky to drive - engine may overheat due to non-functioning cooling fans. Repair immediately to prevent engine damage.
    </p>
</div>
                    
<!-- AI-Friendly Q&A Section -->
<div id="ai-qa" style="background: white; border-radius: 10px; padding: 25px; margin-bottom: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
    <h2><i class="fa fa-comments"></i> Common Questions</h2>
    
    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">What's the difference between P0118 and P0117 ECT codes?</h3>
        <p style="color: #666; line-height: 1.6;">P0118 indicates the ECT sensor is reading too cold (high input voltage/open circuit), while P0117 indicates too hot readings (low input voltage/short circuit). P0118 is more dangerous because cooling fans won't turn on, potentially causing overheating.</p>
    </div>
    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">Why is P0118 more serious than other ECT codes?</h3>
        <p style="color: #666; line-height: 1.6;">P0118 prevents cooling fans from operating because the ECM thinks the engine is cold. This can lead to actual engine overheating, especially in stop-and-go traffic or hot weather. The lean fuel mixture and advanced timing can also cause engine knock and damage.</p>
    </div>
    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">Can P0118 cause engine overheating?</h3>
        <p style="color: #666; line-height: 1.6;">Yes, P0118 is one of the few diagnostic codes that can directly cause engine overheating. When the ECM reads -40°F from the ECT sensor, it never turns on the cooling fans, even when the engine reaches dangerous temperatures. This makes P0118 a high-priority repair.</p>
    </div>
    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; ">
        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">How do I test for P0118 open circuit?</h3>
        <p style="color: #666; line-height: 1.6;">Disconnect the ECT sensor and measure resistance - if it reads infinite ohms (OL on multimeter), the sensor has failed open. Check wiring continuity from sensor to ECM. GeekOBD APP will show -40°F reading when circuit is open, confirming the diagnosis.</p>
    </div>
</div>
                    
<!-- Main Content Section -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h2><i class="fa fa-info-circle"></i> What is P0118?</h2>
    <p style="font-size: 16px; line-height: 1.6; color: #333; margin-bottom: 20px;">The Engine Control Module has detected that the Engine Coolant Temperature (ECT) sensor is producing readings that indicate extremely cold coolant temperatures (typically -40°F or lower) when the actual coolant temperature should be much higher. This occurs when the sensor circuit has high resistance or is open, causing the ECM to default to minimum temperature readings. The ECT sensor is critical for fuel injection timing, ignition advance, cooling fan control, and transmission operation.</p>

    <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; margin: 20px 0;">
        <strong><i class="fa fa-exclamation-triangle"></i> Performance Impact:</strong> P0118 causes the ECM to receive incorrect coolant temperature data showing extremely cold readings, leading to lean fuel mixture, advanced ignition timing, engine knock, potential overheating due to non-functioning cooling fans, and poor transmission shift quality.
    </div>
</div>

<!-- Symptoms Section -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h2><i class="fa fa-stethoscope"></i> Symptoms of P0118</h2>
    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
        <ul style="margin: 0; padding-left: 20px;">
            <li><strong>Check engine light illuminated - ECT sensor circuit fault detected</strong></li>
								<li><strong>Engine running lean - ECM thinks coolant is extremely cold</strong></li>
								<li><strong>Cooling fans never turning on - ECM believes engine is cold</strong></li>
								<li><strong>Poor fuel economy - Lean fuel mixture from cold temperature reading</strong></li>
								<li><strong>Engine knocking or pinging - Advanced timing due to "cold" readings</strong></li>
								<li><strong>Hard starting when engine is warm - ECM provides cold start enrichment</strong></li>
								<li><strong>Engine overheating - Cooling fans not activated due to false cold readings</strong></li>
								<li><strong>Transmission not shifting properly - Incorrect temperature data affects shift points</strong></li>
								<li><strong>Temperature gauge reading incorrectly low - If connected to same sensor</strong></li>
        </ul>
    </div>
</div>

<!-- Causes Section -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h2><i class="fa fa-search"></i> Common Causes of P0118</h2>
    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
        <ol style="margin: 0; padding-left: 20px;">
            <li>Open circuit in ECT sensor wiring - Broken wire causing high resistance</li>
									<li>Faulty ECT sensor - Internal component failure causing open circuit</li>
									<li>Corroded or loose ECT sensor connector - Poor electrical contact</li>
									<li>ECT sensor contamination - Non-conductive debris blocking electrical contact</li>
									<li>Damaged ECT sensor threads - Poor thermal or electrical contact</li>
									<li>ECM internal fault - Module not receiving sensor signal properly</li>
									<li>Wiring harness damage - Cut or damaged wires in sensor circuit</li>
									<li>Incorrect ECT sensor - Wrong resistance range for vehicle application</li>
        </ol>
    </div>
</div>
                    
<!-- Cost Analysis Section -->
<div id="cost-info" style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
    <h2><i class="fa fa-calculator"></i> P0118 Repair Costs</h2>

    <div style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin: 20px 0;">
        <h3 style="color: #2c3e50; margin-bottom: 20px;">Cost Breakdown by Repair Type</h3>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 25px;">
            
        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #4CAF50;">
            <h4 style="color: #4CAF50; margin-bottom: 10px;"><i class="fa fa-thermometer-empty"></i> ECT Sensor Replacement</h4>
            <p style="margin-bottom: 15px; color: #666;">Most common fix - Replace failed open sensor (75% of cases)</p>
            <ul style="list-style: none; padding: 0;">
                <li style="margin-bottom: 8px;"><strong>ECT Sensor:</strong> $40-$110</li>
                <li style="margin-bottom: 8px;"><strong>Coolant (if drained):</strong> $15-$35</li>
                <li style="margin-bottom: 8px;"><strong>Labor (0.5-1.5 hours):</strong> $60-$180</li>
                <li style="margin-bottom: 8px;"><strong>Total:</strong> <span style="color: #4CAF50; font-weight: bold;">$115-$325</span></li>
                <li style="color: #666; font-size: 14px;">Success rate: ~95% success rate%</li>
            </ul>
        </div>
        
        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #FF9800;">
            <h4 style="color: #FF9800; margin-bottom: 10px;"><i class="fa fa-plug"></i> Wiring Harness Repair</h4>
            <p style="margin-bottom: 15px; color: #666;">Fix open circuit in sensor wiring (20% of cases)</p>
            <ul style="list-style: none; padding: 0;">
                <li style="margin-bottom: 8px;"><strong>Wire repair/replacement:</strong> $25-$60</li>
                <li style="margin-bottom: 8px;"><strong>Diagnostic time:</strong> $100-$180</li>
                <li style="margin-bottom: 8px;"><strong>Labor (1.5-3 hours):</strong> $150-$360</li>
                <li style="margin-bottom: 8px;"><strong>Total:</strong> <span style="color: #FF9800; font-weight: bold;">$275-$600</span></li>
                <li style="color: #666; font-size: 14px;">Success rate: ~90% success rate%</li>
            </ul>
        </div>
        
        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #2196F3;">
            <h4 style="color: #2196F3; margin-bottom: 10px;"><i class="fa fa-wrench"></i> Connector Replacement</h4>
            <p style="margin-bottom: 15px; color: #666;">Replace corroded or damaged connector (5% of cases)</p>
            <ul style="list-style: none; padding: 0;">
                <li style="margin-bottom: 8px;"><strong>ECT connector kit:</strong> $35-$85</li>
                <li style="margin-bottom: 8px;"><strong>Splice materials:</strong> $15-$25</li>
                <li style="margin-bottom: 8px;"><strong>Labor (1-2 hours):</strong> $100-$240</li>
                <li style="margin-bottom: 8px;"><strong>Total:</strong> <span style="color: #2196F3; font-weight: bold;">$150-$350</span></li>
                <li style="color: #666; font-size: 14px;">Success rate: ~85% success rate%</li>
            </ul>
        </div>
        </div>

        <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #4CAF50;">
            <h4 style="color: #2e7d32; margin-bottom: 15px;"><i class="fa fa-lightbulb-o"></i> Money-Saving Tips</h4>
            <ul style="margin: 0; color: #2e7d32;">
                <li style="margin-bottom: 8px;">P0118 is urgent - don't delay repair as overheating can cause expensive engine damage</li>
                <li style="margin-bottom: 8px;">Test sensor resistance first - infinite resistance confirms open circuit failure</li>
                <li style="margin-bottom: 8px;">Check connector before replacing sensor - 15% of cases are connector issues</li>
                <li style="margin-bottom: 8px;">ECT sensors are usually accessible - DIY replacement can save $60-180 in labor</li>
                <li style="margin-bottom: 8px;">Monitor coolant temperature closely until repair - watch for overheating signs</li>
            </ul>
        </div>
    </div>
</div>
                    
<!-- Diagnostic Steps Section -->
<div id="diagnostic-steps" style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
    <h2><i class="fa fa-stethoscope"></i> Diagnostic Steps</h2>

    <div style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin: 20px 0;">
        <h3 style="color: #2c3e50; margin-bottom: 20px;">Professional P0118 Diagnosis Process</h3>
        <p style="margin-bottom: 20px; color: #666;">Follow these systematic steps to accurately diagnose P0118. Each step builds on the previous one to ensure accurate diagnosis.</p>

        
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-exclamation-triangle"></i> Step 1: Verify P0118 Code and Check Cooling System</h4>
            <p style="margin-bottom: 15px; color: #333;">Connect scan tool and confirm P0118 is present. Check current ECT reading - should show -40°F even when engine is warm. IMMEDIATELY check if cooling fans are working and monitor engine temperature to prevent overheating.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> Use GeekOBD APP to monitor live ECT sensor data. Reading of -40°F when engine is warm confirms P0118 fault. Watch coolant temperature closely!
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-eye"></i> Step 2: Visual Inspection and Safety Check</h4>
            <p style="margin-bottom: 15px; color: #333;">Locate ECT sensor and inspect for physical damage, loose connections, or coolant leaks. Check that cooling fans can be manually activated. Ensure engine is not overheating before proceeding.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> GeekOBD APP can command cooling fan operation on many vehicles - use this to verify fans work while diagnosing ECT sensor.
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-thermometer-empty"></i> Step 3: Test ECT Sensor Resistance</h4>
            <p style="margin-bottom: 15px; color: #333;">Disconnect sensor and measure resistance across terminals. If multimeter reads "OL" (infinite resistance), sensor has failed open. Normal resistance at operating temperature should be 200-1000 ohms.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> Compare resistance readings with GeekOBD APP temperature specifications. Open circuit (infinite resistance) confirms P0118 diagnosis.
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-plug"></i> Step 4: Check Wiring Continuity</h4>
            <p style="margin-bottom: 15px; color: #333;">Test continuity from ECT sensor connector to ECM. Check for broken wires, corroded connections, or damaged harness. Pay special attention to areas near heat sources.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> Use GeekOBD APP to monitor sensor voltage while checking connections - should see voltage change when circuit is completed.
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-check-circle"></i> Step 5: Verify Repair and Monitor Temperature</h4>
            <p style="margin-bottom: 15px; color: #333;">After replacing sensor or repairing wiring, clear codes and monitor ECT readings. Verify cooling fans activate when temperature reaches normal operating range (195-220°F).</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> GeekOBD APP provides continuous monitoring - ECT should read actual coolant temperature and cooling fans should activate automatically.
            </div>
        </div>

        <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin-top: 20px; border-left: 4px solid #ffc107;">
            <h4 style="color: #856404; margin-bottom: 15px;"><i class="fa fa-exclamation-triangle"></i> Important Notes</h4>
            <ul style="margin: 0; color: #856404;">
                <li style="margin-bottom: 8px;">P0118 can cause engine overheating - monitor temperature closely during diagnosis</li>
                <li style="margin-bottom: 8px;">Cooling fans may not work with P0118 - be prepared to shut off engine if overheating occurs</li>
                <li style="margin-bottom: 8px;">Open circuit diagnosis is usually straightforward - infinite resistance confirms sensor failure</li>
            </ul>
        </div>
    </div>
</div>
                    
<!-- Real Repair Case Studies -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
    <h2><i class="fa fa-file-text"></i> Real Repair Case Studies</h2>
    
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2;"><i class="fa fa-car"></i> Case 1: Ford Explorer Overheating Emergency</h4>
            <p><strong>Vehicle:</strong> 2018 Ford Explorer 3.5L V6, 72,000 miles</p>
            <p><strong>Problem:</strong> Customer called roadside assistance for overheating. Engine temperature gauge was pegged at maximum, but cooling fans never turned on. Check engine light was on with P0118 code stored.</p>
            <p><strong>Diagnosis:</strong> GeekOBD APP showed ECT reading constant -40°F even with engine at dangerous temperatures. Resistance test of ECT sensor showed infinite resistance (open circuit). Sensor had failed internally, preventing cooling fan activation.</p>
            <p><strong>Solution:</strong> Immediately shut off engine to prevent damage. Replaced ECT sensor located in cylinder head. Sensor required partial coolant drain and careful installation to avoid cross-threading.</p>
            <p><strong>Cost:</strong> ECT sensor: $78, Coolant: $28, Emergency service: $150, Labor: $120, Total: $376</p>
            <p><strong>Result:</strong> P0118 code cleared immediately. ECT now reads correctly and cooling fans activate at 210°F as designed. No engine damage occurred due to quick diagnosis and repair.</p>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 4px solid #28a745;">
            <h4 style="color: #28a745;"><i class="fa fa-car"></i> Case 2: Nissan Altima Wiring Harness Failure</h4>
            <p><strong>Vehicle:</strong> 2017 Nissan Altima 2.5L 4-cylinder, 95,000 miles</p>
            <p><strong>Problem:</strong> Intermittent P0118 code with occasional overheating in traffic. Customer noticed cooling fans would sometimes not turn on, and engine would knock during acceleration.</p>
            <p><strong>Diagnosis:</strong> ECT sensor tested normal resistance, but GeekOBD APP showed intermittent -40°F readings. Found broken wire in ECT harness near alternator where vibration had caused wire fatigue and eventual open circuit.</p>
            <p><strong>Solution:</strong> Repaired broken wire in ECT sensor harness. Cut out damaged section and spliced in new automotive-grade wire with proper insulation and strain relief.</p>
            <p><strong>Cost:</strong> Wire repair kit: $45, Diagnostic time: $160, Labor: $180, Total: $385</p>
            <p><strong>Result:</strong> P0118 code has not returned after 8 months. ECT readings remain stable and cooling fans operate normally. Customer reports no more overheating or engine knock.</p>
        </div>
</div>
                </div>

                <div class="col-md-4">
                    
<!-- GeekOBD APP Promotion -->
<div style="background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%); color: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: white;"><i class="fa fa-mobile"></i> Diagnose P0118</h4>
    <p style="margin-bottom: 20px; opacity: 0.9; font-size: 14px;">Use GeekOBD APP for critical ECT sensor diagnosis!</p>
    <ul style="margin-bottom: 20px; padding-left: 20px; font-size: 14px;">
        <li style="margin-bottom: 8px;">Emergency temperature monitoring</li>
        <li style="margin-bottom: 8px;">Cooling fan control testing</li>
        <li style="margin-bottom: 8px;">Open circuit detection</li>
        <li style="margin-bottom: 8px;">Overheating prevention alerts</li>
    </ul>
    <a href="https://www.geekobd.com/app.html" style="display: inline-block; background: rgba(255,255,255,0.2); color: white; padding: 12px 25px; border-radius: 25px; text-decoration: none; font-weight: bold; transition: all 0.3s;">
        <i class="fa fa-download"></i> Download GeekOBD APP
    </a>
</div>

<!-- Related System Codes -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-link"></i> ECT Sensor Codes</h4>
    <p style="margin-bottom: 15px; color: #666; font-size: 14px;">Related Engine Coolant Temperature sensor codes:</p>
    <div style="margin-bottom: 15px;">
        
                <a href="p0117.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #e74c3c;">
                    <strong style="color: #e74c3c;">P0117</strong> - ECT Sensor Low Input - Opposite condition (too hot readings)
                </a>
                <a href="p0125.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #f39c12;">
                    <strong style="color: #f39c12;">P0125</strong> - Insufficient Coolant Temperature - Related coolant temperature issue
                </a>
                <a href="p0128.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #3498db;">
                    <strong style="color: #3498db;">P0128</strong> - Coolant Thermostat Rationality - Related cooling system problem
                </a>
                <a href="p0115.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #4a90e2;">
                    <strong style="color: #4a90e2;">P0115</strong> - ECT Sensor Circuit Malfunction - General ECT circuit problem
                </a>
                <a href="p0171.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #9b59b6;">
                    <strong style="color: #9b59b6;">P0171</strong> - System Too Lean Bank 1 - Can be caused by incorrect ECT readings
                </a>
                <a href="p0174.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #9b59b6;">
                    <strong style="color: #9b59b6;">P0174</strong> - System Too Lean Bank 2 - Can be caused by incorrect ECT readings
                </a>
                <a href="p0325.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #e67e22;">
                    <strong style="color: #e67e22;">P0325</strong> - Knock Sensor Circuit - Engine knock from advanced timing due to P0118
                </a>
    </div>
</div>

<!-- Diagnostic Resources -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-tools"></i> Diagnostic Resources</h4>
    <div style="margin-bottom: 20px;">
        
        <a href="#diagnostic-steps" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-exclamation-triangle" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">Emergency P0118 Guide</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">Critical procedures to prevent engine overheating</span>
        </a>
        <a href="../resources/open-circuit-testing.html" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-plug" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">Open Circuit Testing</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">Professional procedures for testing open circuits</span>
        </a>
        <a href="../resources/cooling-system-safety.html" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-fire-extinguisher" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">Cooling System Safety</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">Overheating prevention and emergency procedures</span>
        </a>
        <a href="../resources/ect-specifications.html" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-line-chart" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">ECT Specifications</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">Resistance values and temperature correlations</span>
        </a>
    </div>
</div>

<!-- Quick Navigation -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-compass"></i> Quick Navigation</h4>
    <div style="display: flex; flex-direction: column; gap: 8px;">
        <a href="#quick-answer" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-bolt"></i> Quick Answer
        </a>
        <a href="#ai-qa" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-comments"></i> Common Questions
        </a>
        <a href="#cost-info" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-calculator"></i> Repair Costs
        </a>
        <a href="#diagnostic-steps" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-stethoscope"></i> Diagnostic Steps
        </a>
    </div>
</div>

<!-- Code Information -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-info-circle"></i> Code Information</h4>
    <table style="width: 100%; font-size: 14px;">
        <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Code:</strong></td>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;">P0118</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>System:</strong></td>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;">Engine Management</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Severity:</strong></td>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><span class="severity-badge severity-high">HIGH</span></td>
        </tr>
        <tr>
            <td style="padding: 8px 0;"><strong>Category:</strong></td>
            <td style="padding: 8px 0;">Engine Coolant Temperature</td>
        </tr>
    </table>
</div>
                </div>
            </div>
        </div>
    </section>

    </div>

<script src="../js/jquery.min.js"></script>
<script src="../js/bootstrap.js"></script>
<script src="../js/superfish.js"></script>
<script src="../js/custom.js"></script>
</body>
</html>