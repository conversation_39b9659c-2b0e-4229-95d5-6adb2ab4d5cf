<!DOCTYPE html>
<!--[if IE 8]><html class="ie ie8"> <![endif]-->
<!--[if IE 9]><html class="ie ie9"> <![endif]-->
<!--[if gt IE 9]><!-->
<html>
<!--<![endif]-->

<head>
<meta charset="utf-8">
<title>P0130 - O2 Sensor Circuit Malfunction (Bank 1, Sensor 1) | GeekOBD Diagnostic Guide</title>
<meta name="description" content="P0130 diagnostic trouble code: O2 Sensor Circuit Malfunction (Bank 1, Sensor 1). Learn about symptoms, causes, diagnosis steps, and repair solutions for P0130 with GeekOBD professional tools.">
<meta name="keywords" content="P0130, P0130 code, P0130 diagnostic, oxygen sensor, O2 sensor bank 1, sensor circuit malfunction, OBD diagnostic code">
<meta name="author" content="Beijing MentalRoad Technology Co., Ltd.">
<meta name="robots" content="index, follow">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
<link rel="canonical" href="https://www.geekobd.com/dtc-codes/p0130.html">

<!-- Open Graph / Facebook -->
<meta property="og:type" content="article">
<meta property="og:url" content="https://www.geekobd.com/dtc-codes/p0130.html">
<meta property="og:title" content="P0130 - O2 Sensor Circuit Malfunction (Bank 1, Sensor 1) | Diagnostic Code Guide">
<meta property="og:description" content="P0130 diagnostic trouble code: O2 Sensor Circuit Malfunction (Bank 1, Sensor 1). Complete guide with symptoms, causes, and repair solutions.">
<meta property="og:image" content="https://www.geekobd.com/img/logo.png">
<meta property="og:site_name" content="GeekOBD">

<!-- Twitter -->
<meta property="twitter:card" content="summary_large_image">
<meta property="twitter:url" content="https://www.geekobd.com/dtc-codes/p0130.html">
<meta property="twitter:title" content="P0130 - O2 Sensor Circuit Malfunction (Bank 1, Sensor 1) | Diagnostic Code Guide">
<meta property="twitter:description" content="P0130 diagnostic trouble code: O2 Sensor Circuit Malfunction (Bank 1, Sensor 1). Complete guide with symptoms, causes, and repair solutions.">
<meta property="twitter:image" content="https://www.geekobd.com/img/logo.png">

<link rel="stylesheet" href="../css/bootstrap.css">
<link rel="stylesheet" href="../css/fonts/font-awesome/css/font-awesome.css">
<link rel="stylesheet" href="../css/animations.css" media="screen">
<link rel="stylesheet" href="../css/superfish.css" media="screen">
<link rel="stylesheet" href="../css/style.css">
<link rel="stylesheet" href="../css/colors/blue.css" id="colors">
<link rel="stylesheet" href="../css/theme-responsive.css">
<link rel="stylesheet" href="../css/seo-enhancements.css">
<link rel="shortcut icon" href="../img/ico/favicon.ico">

<!-- Custom styles for DTC pages -->
<style>
.dtc-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 60px 0 40px;
}

.dtc-code-badge {
    display: inline-block;
    background: rgba(255,255,255,0.2);
    padding: 10px 20px;
    border-radius: 50px;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
}

.severity-badge {
    display: inline-block;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
    margin-left: 15px;
}

.severity-high { background: #ff4757; color: white; }
.severity-medium { background: #ffa502; color: white; }
.severity-low { background: #2ed573; color: white; }

.content-section {
    padding: 50px 0;
}

.info-box {
    background: #f8f9fa;
    border-left: 4px solid #667eea;
    padding: 20px;
    margin: 20px 0;
    border-radius: 5px;
}

.warning-box {
    background: #fff3cd;
    border-left: 4px solid #ffc107;
    padding: 20px;
    margin: 20px 0;
    border-radius: 5px;
}

.related-codes {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 15px;
    margin: 30px 0;
}

.code-link {
    display: inline-block;
    background: white;
    padding: 10px 15px;
    margin: 5px;
    border-radius: 25px;
    text-decoration: none;
    color: #667eea;
    font-weight: bold;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.code-link:hover {
    background: #667eea;
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
}

.breadcrumb-custom {
    background: none;
    padding: 20px 0;
    margin: 0;
}

.breadcrumb-custom a {
    color: #667eea;
    text-decoration: none;
}

.breadcrumb-custom a:hover {
    text-decoration: underline;
}
</style>

<!-- JSON-LD Structured Data -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": "P0130 - O2 Sensor Circuit Malfunction (Bank 1, Sensor 1)",
  "description": "Complete diagnostic guide for P0130 trouble code including oxygen sensor circuit issues, symptoms, causes, and repair solutions.",
  "author": {
    "@type": "Organization",
    "name": "GeekOBD",
    "url": "https://www.geekobd.com"
  },
  "publisher": {
    "@type": "Organization",
    "name": "GeekOBD",
    "logo": {
      "@type": "ImageObject",
      "url": "https://www.geekobd.com/img/logo.png"
    }
  },
  "datePublished": "2025-01-26",
  "dateModified": "2025-01-26",
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "https://www.geekobd.com/dtc-codes/p0130.html"
  },
  "articleSection": "Automotive Diagnostics",
  "keywords": "P0130, oxygen sensor, O2 sensor bank 1, sensor circuit malfunction",
  "about": {
    "@type": "Thing",
    "name": "P0130 Diagnostic Trouble Code",
    "description": "Oxygen sensor circuit malfunction on bank 1, sensor 1"
  }
}
</script>

<!-- FAQ Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "What does P0130 mean?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "P0130 indicates that the engine control module has detected a malfunction in the oxygen sensor circuit for bank 1, sensor 1 (the upstream O2 sensor on the side with cylinder 1)."
      }
    },
    {
      "@type": "Question", 
      "name": "Where is Bank 1 Sensor 1 located?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Bank 1 Sensor 1 is the upstream oxygen sensor located before the catalytic converter on the side of the engine that contains cylinder 1."
      }
    },
    {
      "@type": "Question",
      "name": "How much does it cost to fix P0130?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Repair costs typically range from $200-500, with oxygen sensor replacement being the most common fix. Wiring repairs may cost less, while multiple sensor replacement costs more."
      }
    },
    {
      "@type": "Question",
      "name": "Can I drive with P0130 code?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "You can drive short distances with P0130, but prolonged driving may cause poor fuel economy, failed emissions tests, and potential catalytic converter damage. Repair as soon as possible."
      }
    }
  ]
}
</script>

<!-- HowTo Schema for P0130 Diagnostic Steps -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "HowTo",
  "name": "How to Diagnose P0130 O2 Sensor Circuit Malfunction",
  "description": "Step-by-step guide to diagnose and fix P0130 oxygen sensor circuit malfunction on Bank 1 Sensor 1",
  "image": "https://www.geekobd.com/img/logo.png",
  "totalTime": "PT90M",
  "estimatedCost": {
    "@type": "MonetaryAmount",
    "currency": "USD",
    "value": "280"
  },
  "tool": [
    {
      "@type": "HowToTool",
      "name": "GeekOBD APP with MOBD Adapter",
      "description": "Professional OBD2 diagnostic tool with live O2 sensor data monitoring for Bank 1 Sensor 1",
      "url": "https://www.geekobd.com/app.html"
    },
    {
      "@type": "HowToTool",
      "name": "Digital Multimeter",
      "description": "For testing O2 sensor voltage and circuit continuity"
    },
    {
      "@type": "HowToTool",
      "name": "O2 Sensor Socket",
      "description": "Special socket for removing oxygen sensors"
    }
  ],
  "supply": [
    {
      "@type": "HowToSupply",
      "name": "Bank 1 Sensor 1 Oxygen Sensor"
    },
    {
      "@type": "HowToSupply",
      "name": "Anti-Seize Compound"
    },
    {
      "@type": "HowToSupply",
      "name": "Electrical Contact Cleaner"
    }
  ],
  "step": [
    {
      "@type": "HowToStep",
      "position": 1,
      "name": "Scan and Confirm P0130 Code",
      "text": "Connect GeekOBD APP and scan for P0130 code. Check live data to monitor Bank 1 Sensor 1 voltage readings and confirm sensor circuit malfunction.",
      "image": "https://www.geekobd.com/img/geekobd-p0130-scan.jpg"
    },
    {
      "@type": "HowToStep",
      "position": 2,
      "name": "Locate Bank 1 Sensor 1",
      "text": "Locate the upstream oxygen sensor on Bank 1 (cylinder 1 side) before the catalytic converter. Check sensor connector and wiring for damage or corrosion.",
      "image": "https://www.geekobd.com/img/bank1-sensor1-location.jpg"
    },
    {
      "@type": "HowToStep",
      "position": 3,
      "name": "Test Sensor Circuit",
      "text": "Use multimeter to test sensor voltage (should switch between 0.1-0.9V), heater circuit resistance (2-14 ohms), and wiring continuity to ECM.",
      "image": "https://www.geekobd.com/img/o2-sensor-circuit-test.jpg"
    },
    {
      "@type": "HowToStep",
      "position": 4,
      "name": "Replace O2 Sensor if Faulty",
      "text": "If sensor or circuit tests fail, replace Bank 1 Sensor 1 oxygen sensor. Apply anti-seize to threads and torque to specification (30-33 ft-lbs).",
      "image": "https://www.geekobd.com/img/o2-sensor-replacement.jpg"
    },
    {
      "@type": "HowToStep",
      "position": 5,
      "name": "Clear Code and Verify Repair",
      "text": "Clear P0130 code with GeekOBD APP, drive for 15-20 minutes, and monitor Bank 1 Sensor 1 live data to ensure proper voltage switching and no code return.",
      "image": "https://www.geekobd.com/img/geekobd-o2-sensor-verified.jpg"
    }
  ]
}
</script>

<!--[if lt IE 9]>
<script src="../js/html5.js"></script>
<script src="../js/css3-mediaqueries.js"></script>
<![endif]-->

<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-RD6767XBCL"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-RD6767XBCL');
</script>
</head>

<body>

<div class="wrap">
	<!-- Header Start -->
	<header id="header" role="banner">
	<!-- Main Header Start -->
	<div class="main-header">
	<div class="container">
	<div class="row">
	<div class="col-md-3">
	<!-- Logo Start -->
	<div class="logo pull-left">
	<h1> <a href="../index.html"> <img src="../img/logo.png" alt="MOBD"> </a> </h1>
	</div>
	<!-- Logo End --> 
	</div>
	<div class="col-md-9">
	<!-- Mobile Menu Start -->
	<div class="mobile navbar-header"> <a class="navbar-toggle" data-toggle="collapse" data-target=".menu"> <i class="icon-reorder icon-2x"></i> </a> </div>
	<!-- Mobile Menu End --> 
	<!-- Menu Start -->
	<nav class="collapse navbar-collapse menu" role="navigation" aria-label="Main navigation">
	<ul class="nav navbar-nav sf-menu">
	<li><a href="../index.html" class="sf-with-ul">Home</a></li>
	<li><a href="../app.html" class="sf-with-ul">APP</a></li>
	<li><a href="javascript:;;" class="sf-with-ul">Adaptor <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
	<ul>
	<li><a href="../hardware2.html" class="sf-with-ul">MOBD GPS</a></li>
	<li><a href="../hardware.html" class="sf-with-ul">MOBD</a></li>
	</ul>
	</li>
	<li><a href="javascript:;;" class="sf-with-ul">Resources <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
	<ul>
	<li><a href="../dtc-codes.html" class="sf-with-ul" id="current">DTC Codes</a></li>
	<li><a href="../obd-diagnostic-guide.html" class="sf-with-ul">OBD Guide</a></li>
	<li><a href="../vehicle-compatibility.html" class="sf-with-ul">Compatibility</a></li>
	<li><a href="../fuel-efficiency-monitoring.html" class="sf-with-ul">Fuel Efficiency</a></li>
	<li><a href="../support.html" class="sf-with-ul">Support</a></li>
	<li><a href="../blog.html" class="sf-with-ul">Blog</a></li>
	</ul>
	</li>
	<li><a href="../about.html" class="sf-with-ul">About Us</a></li>
	<li><a href="//www.mobd.cn" class="sf-with-ul" target="_blank">中文版</a></li>
	</ul>
	</nav>
	<!-- Menu End --> 
	</div>
	</div>
	<!-- Main Header End --> 
	</div>
	</div>
	</header>
	<!-- Header End -->

	<!-- Breadcrumb -->
	<div class="container">
		<nav class="breadcrumb-custom">
			<a href="../index.html">Home</a> &raquo; 
			<a href="../dtc-codes.html">DTC Codes</a> &raquo; 
			<a href="../dtc-codes.html#engine">Engine Codes</a> &raquo; 
			<span>P0130</span>
		</nav>
	</div>

	<!-- DTC Header -->
	<section class="dtc-header">
		<div class="container">
			<div class="row">
				<div class="col-md-12">
					<div class="dtc-code-badge">P0130</div>
					<span class="severity-badge severity-medium">MEDIUM Priority</span>
					<h1 style="margin: 20px 0; font-size: 36px;">O2 Sensor Circuit Malfunction (Bank 1, Sensor 1)</h1>
					<p style="font-size: 18px; opacity: 0.9; margin-bottom: 0;">The upstream oxygen sensor circuit on bank 1 has a malfunction affecting fuel trim and emissions.</p>
				</div>
			</div>
		</div>
	</section>

	<!-- Main Content -->
	<section class="content-section">
		<div class="container">
			<div class="row">
				<div class="col-md-8">
					<!-- Quick Answer Section for AI -->
					<div id="quick-answer" style="background: #e8f4fd; border: 2px solid #2196F3; border-radius: 10px; padding: 25px; margin-bottom: 30px;">
						<h2 style="color: #1976D2; margin-bottom: 15px;"><i class="fa fa-bolt"></i> Quick Answer</h2>
						<p style="font-size: 18px; font-weight: 500; margin-bottom: 15px; color: #333;">
							<strong>P0130 means:</strong> Bank 1 Sensor 1 (upstream O2 sensor) circuit malfunction causing poor fuel economy and emissions issues.
						</p>
						<div style="display: flex; flex-wrap: wrap; gap: 15px; margin-bottom: 15px;">
							<span style="background: #4CAF50; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
								<i class="fa fa-wrench"></i> Fix: Replace Bank 1 Sensor 1 O2 sensor
							</span>
							<span style="background: #FF9800; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
								<i class="fa fa-dollar"></i> Cost: $200-$500
							</span>
							<span style="background: #2196F3; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
								<i class="fa fa-clock-o"></i> Time: 90 minutes
							</span>
						</div>
						<p style="margin: 0; color: #666; font-size: 14px;">
							<strong>Can I drive with P0130?</strong> YES - Safe to drive short distances, but repair soon to avoid poor fuel economy and emissions test failure.
						</p>
					</div>

					<!-- AI-Friendly Q&A Section -->
					<div id="ai-qa" style="background: white; border-radius: 10px; padding: 25px; margin-bottom: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
						<h2><i class="fa fa-comments"></i> Common Questions</h2>

						<div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
							<h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">Where is Bank 1 Sensor 1 located?</h3>
							<p style="color: #666; line-height: 1.6;">Bank 1 Sensor 1 is the upstream oxygen sensor located BEFORE the catalytic converter on the side of the engine with cylinder #1. In V6/V8 engines, Bank 1 is typically the side with cylinders 1-3-5. In 4-cylinder engines, there's only one bank.</p>
						</div>

						<div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
							<h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">What's the difference between P0130 and P0150?</h3>
							<p style="color: #666; line-height: 1.6;">P0130 = Bank 1 Sensor 1 (upstream O2 sensor), P0150 = Bank 2 Sensor 1 (upstream O2 sensor on opposite side). Both are upstream sensors but on different engine banks. Fix the specific bank showing the code.</p>
						</div>

						<div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
							<h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">Will P0130 cause my car to fail emissions test?</h3>
							<p style="color: #666; line-height: 1.6;">YES - P0130 will likely cause emissions test failure because the faulty upstream O2 sensor can't properly monitor exhaust gases, leading to incorrect air/fuel mixture and increased emissions. Repair before emissions testing.</p>
						</div>

						<div class="qa-item" style="margin-bottom: 0;">
							<h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">How do I test if it's the O2 sensor or wiring?</h3>
							<p style="color: #666; line-height: 1.6;">Use GeekOBD APP to monitor Bank 1 Sensor 1 live data. A good sensor switches between 0.1-0.9V rapidly. If voltage is stuck or slow, replace sensor. If no voltage, check wiring and connections first.</p>
						</div>
					</div>

					<!-- Overview Section -->
					<div id="overview">
						<h2><i class="fa fa-info-circle"></i> Technical Overview</h2>
						<div class="info-box">
							<h4>P0130 Definition</h4>
							<p>P0130 indicates that the Engine Control Module (ECM) has detected a malfunction in the oxygen sensor circuit for Bank 1, Sensor 1. This is the upstream oxygen sensor (before the catalytic converter) on the side of the engine that contains cylinder #1. The oxygen sensor monitors exhaust gases to help the ECM maintain the proper air/fuel mixture for optimal performance and emissions control.</p>
						</div>

						<h3>Technical Details</h3>
						<ul>
							<li><strong>Code Type:</strong> Generic Powertrain Code</li>
							<li><strong>System:</strong> Engine Management / Emissions Control</li>
							<li><strong>Severity:</strong> Medium - Affects fuel economy and emissions</li>
							<li><strong>Driving Safety:</strong> Safe to drive but should be repaired promptly</li>
						</ul>
					</div>

					<!-- Symptoms Section -->
					<div id="symptoms">
						<h2><i class="fa fa-exclamation-triangle"></i> Symptoms</h2>
						<p>When P0130 is triggered, you may experience:</p>
						<ul>
							<li>Check Engine Light illuminated</li>
							<li>Poor fuel economy</li>
							<li>Rough idle or unstable engine operation</li>
							<li>Engine hesitation during acceleration</li>
							<li>Failed emissions test</li>
							<li>Black exhaust smoke (rich condition)</li>
							<li>Strong fuel odor from exhaust</li>
							<li>Engine may run poorly when cold</li>
							<li>Reduced engine performance</li>
						</ul>
						
						<div class="warning-box">
							<strong><i class="fa fa-warning"></i> Important:</strong> While P0130 doesn't typically cause immediate safety issues, it affects the engine's ability to maintain proper air/fuel ratios, leading to poor fuel economy and increased emissions. Address this issue to restore optimal engine performance.
						</div>
					</div>

					<!-- Causes Section -->
					<div id="causes">
						<h2><i class="fa fa-search"></i> Possible Causes</h2>
						<p>P0130 can be triggered by several different issues:</p>
						<ol>
							<li><strong>Faulty oxygen sensor (Bank 1, Sensor 1)</strong> - Most common cause</li>
							<li><strong>Damaged or corroded oxygen sensor wiring</strong></li>
							<li><strong>Poor electrical connections at oxygen sensor</strong></li>
							<li><strong>Exhaust leaks near the oxygen sensor</strong></li>
							<li><strong>Contaminated oxygen sensor (oil, coolant, or fuel)</strong></li>
							<li><strong>Faulty oxygen sensor heater circuit</strong></li>
							<li><strong>Engine running too rich or too lean</strong></li>
							<li><strong>Vacuum leaks affecting air/fuel mixture</strong></li>
							<li><strong>Faulty fuel injectors</strong></li>
							<li><strong>ECM issues (rare)</strong></li>
						</ol>
					</div>

					<!-- Repair Cost Information -->
					<div id="cost-info" style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
						<h2><i class="fa fa-calculator"></i> P0130 Repair Costs</h2>

						<div style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin: 20px 0;">
							<h3 style="color: #2c3e50; margin-bottom: 20px;">Cost Breakdown by Repair Type</h3>

							<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 25px;">
								<div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #4CAF50;">
									<h4 style="color: #4CAF50; margin-bottom: 10px;"><i class="fa fa-wrench"></i> Wiring/Connector Repair</h4>
									<p style="margin-bottom: 15px; color: #666;">Simple fix - 25% of P0130 cases</p>
									<ul style="list-style: none; padding: 0;">
										<li style="margin-bottom: 8px;"><strong>Connector cleaning:</strong> $45-85</li>
										<li style="margin-bottom: 8px;"><strong>Wiring repair:</strong> $120-200</li>
										<li style="margin-bottom: 8px;"><strong>Harness replacement:</strong> $180-320</li>
										<li style="color: #666; font-size: 14px;">Success rate: ~90%</li>
									</ul>
								</div>

								<div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #FF9800;">
									<h4 style="color: #FF9800; margin-bottom: 10px;"><i class="fa fa-cog"></i> Bank 1 Sensor 1 Replacement</h4>
									<p style="margin-bottom: 15px; color: #666;">Most common P0130 fix - 70% success</p>
									<ul style="list-style: none; padding: 0;">
										<li style="margin-bottom: 8px;"><strong>Standard O2 sensor:</strong> $200-350</li>
										<li style="margin-bottom: 8px;"><strong>OEM O2 sensor:</strong> $280-500</li>
										<li style="margin-bottom: 8px;"><strong>Premium sensor:</strong> $320-580</li>
										<li style="color: #666; font-size: 14px;">Success rate: ~95%</li>
									</ul>
								</div>
							</div>

							<div style="background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #4CAF50; margin-bottom: 20px;">
								<h4 style="color: #2e7d32; margin-bottom: 15px;"><i class="fa fa-thumbs-up"></i> Bank 1 Sensor 1 Advantages</h4>
								<p style="margin-bottom: 10px; color: #333;">Bank 1 Sensor 1 repairs are often easier because:</p>
								<ul style="margin: 0; color: #333;">
									<li style="margin-bottom: 8px;"><strong>Upstream location:</strong> More accessible than downstream sensors</li>
									<li style="margin-bottom: 8px;"><strong>Standard tools:</strong> Most O2 sensor sockets fit Bank 1 Sensor 1</li>
									<li style="margin-bottom: 8px;"><strong>Clear diagnosis:</strong> Easy to test with live data monitoring</li>
									<li><strong>Common part:</strong> Bank 1 Sensor 1 parts widely available</li>
								</ul>
							</div>

							<div style="background: #e3f2fd; padding: 20px; border-radius: 8px; border-left: 4px solid #2196F3;">
								<h4 style="color: #1976D2; margin-bottom: 15px;"><i class="fa fa-lightbulb-o"></i> Money-Saving Tips for P0130</h4>
								<ul style="margin: 0; color: #333;">
									<li style="margin-bottom: 10px;">Check connector and wiring first - 25% of P0130 codes are wiring issues</li>
									<li style="margin-bottom: 10px;">Use GeekOBD APP to verify sensor operation before replacement</li>
									<li style="margin-bottom: 10px;">Bank 1 Sensor 1 is easier to access than other O2 sensors</li>
									<li style="margin-bottom: 10px;">Address P0130 quickly to prevent catalytic converter damage</li>
									<li>Consider replacing both upstream sensors if vehicle has high mileage (150k+)</li>
								</ul>
							</div>
						</div>
					</div>

					<!-- Related Codes Section -->
					<div id="related" class="related-codes">
						<h2><i class="fa fa-link"></i> Related Diagnostic Codes</h2>

						<div style="margin-bottom: 25px;">
							<h3 style="color: #333; font-size: 18px; margin-bottom: 15px;">Bank 1 O2 Sensor Codes</h3>
							<p style="margin-bottom: 15px; color: #666;">These codes affect the same Bank 1 Sensor 1 oxygen sensor:</p>
							<div style="margin-bottom: 20px;">
								<a href="p0131.html" class="code-link" title="O2 Sensor Circuit Low Voltage">P0131 - Low Voltage</a>
								<a href="p0132.html" class="code-link" title="O2 Sensor Circuit High Voltage">P0132 - High Voltage</a>
								<a href="p0133.html" class="code-link" title="O2 Sensor Circuit Slow Response">P0133 - Slow Response</a>
								<a href="p0134.html" class="code-link" title="O2 Sensor Circuit No Activity">P0134 - No Activity</a>
								<a href="p0135.html" class="code-link" title="O2 Sensor Heater Circuit">P0135 - Heater Circuit</a>
							</div>
						</div>

						<div style="margin-bottom: 25px;">
							<h3 style="color: #333; font-size: 18px; margin-bottom: 15px;">Related System Codes</h3>
							<p style="margin-bottom: 15px; color: #666;">Codes that can cause or result from P0130:</p>
							<div style="margin-bottom: 20px;">
								<a href="p0150.html" class="code-link" title="Bank 2 Sensor 1 Circuit">P0150 - Bank 2 Sensor 1</a>
								<a href="p0171.html" class="code-link" title="System Too Lean Bank 1">P0171 - System Lean B1</a>
								<a href="p0172.html" class="code-link" title="System Too Rich Bank 1">P0172 - System Rich B1</a>
								<a href="p0420.html" class="code-link" title="Catalyst System Efficiency">P0420 - Catalyst B1</a>
							</div>
						</div>

						<div style="background: #f8f9fa; padding: 20px; border-radius: 10px;">
							<h3 style="color: #333; font-size: 18px; margin-bottom: 15px;">Complete O2 Sensor Diagnostic Network</h3>
							<p style="margin-bottom: 15px; color: #666;">Navigate our complete oxygen sensor diagnostic system:</p>
							<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
								<div>
									<h4 style="color: #667eea; margin-bottom: 10px;"><a href="../dtc-codes.html#o2-sensors" style="color: #667eea; text-decoration: none;">Bank 1 Sensors</a></h4>
									<ul style="list-style: none; padding: 0; margin: 0;">
										<li style="margin-bottom: 5px;"><strong>P0130 - Circuit Malfunction</strong></li>
										<li style="margin-bottom: 5px;"><a href="p0131.html" style="color: #666; text-decoration: none; font-size: 14px;">P0131 - Low Voltage</a></li>
										<li style="margin-bottom: 5px;"><a href="p0132.html" style="color: #666; text-decoration: none; font-size: 14px;">P0132 - High Voltage</a></li>
										<li style="margin-bottom: 5px;"><a href="p0133.html" style="color: #666; text-decoration: none; font-size: 14px;">P0133 - Slow Response</a></li>
										<li style="margin-bottom: 5px;"><a href="p0134.html" style="color: #666; text-decoration: none; font-size: 14px;">P0134 - No Activity</a></li>
									</ul>
								</div>
								<div>
									<h4 style="color: #667eea; margin-bottom: 10px;"><a href="../dtc-codes.html#o2-sensors" style="color: #667eea; text-decoration: none;">Bank 2 Sensors</a></h4>
									<ul style="list-style: none; padding: 0; margin: 0;">
										<li style="margin-bottom: 5px;"><a href="p0150.html" style="color: #666; text-decoration: none; font-size: 14px;">P0150 - Circuit Malfunction</a></li>
										<li style="margin-bottom: 5px;"><a href="p0151.html" style="color: #666; text-decoration: none; font-size: 14px;">P0151 - Low Voltage</a></li>
										<li style="margin-bottom: 5px;"><a href="p0152.html" style="color: #666; text-decoration: none; font-size: 14px;">P0152 - High Voltage</a></li>
										<li style="margin-bottom: 5px;"><a href="p0153.html" style="color: #666; text-decoration: none; font-size: 14px;">P0153 - Slow Response</a></li>
									</ul>
								</div>
								<div>
									<h4 style="color: #667eea; margin-bottom: 10px;"><a href="../dtc-codes.html#fuel" style="color: #667eea; text-decoration: none;">Fuel System</a></h4>
									<ul style="list-style: none; padding: 0; margin: 0;">
										<li style="margin-bottom: 5px;"><a href="p0171.html" style="color: #666; text-decoration: none; font-size: 14px;">P0171 - System Lean</a></li>
										<li style="margin-bottom: 5px;"><a href="p0172.html" style="color: #666; text-decoration: none; font-size: 14px;">P0172 - System Rich</a></li>
										<li style="margin-bottom: 5px;"><a href="p0174.html" style="color: #666; text-decoration: none; font-size: 14px;">P0174 - System Lean B2</a></li>
										<li style="margin-bottom: 5px;"><a href="p0175.html" style="color: #666; text-decoration: none; font-size: 14px;">P0175 - System Rich B2</a></li>
									</ul>
								</div>
								<div>
									<h4 style="color: #667eea; margin-bottom: 10px;"><a href="../dtc-codes.html#emissions" style="color: #667eea; text-decoration: none;">Emissions</a></h4>
									<ul style="list-style: none; padding: 0; margin: 0;">
										<li style="margin-bottom: 5px;"><a href="p0420.html" style="color: #666; text-decoration: none; font-size: 14px;">P0420 - Catalyst B1</a></li>
										<li style="margin-bottom: 5px;"><a href="p0430.html" style="color: #666; text-decoration: none; font-size: 14px;">P0430 - Catalyst B2</a></li>
										<li style="margin-bottom: 5px;"><a href="../dtc-codes.html#emissions" style="color: #666; text-decoration: none; font-size: 14px;">View all emissions →</a></li>
									</ul>
								</div>
							</div>
						</div>
					</div>

					<!-- Case Study -->
					<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
						<h2><i class="fa fa-file-text"></i> Real Repair Case Studies</h2>

						<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
							<h4>Case 1: 2013 Honda Accord - Simple Connector Fix</h4>
							<p><strong>Vehicle:</strong> 2013 Honda Accord 2.4L 4-cylinder, 112,000 miles</p>
							<p><strong>Problem:</strong> Customer reported poor fuel economy and check engine light. GeekOBD scan showed P0130 code with no O2 sensor activity on Bank 1 Sensor 1. Live data showed 0V constant reading.</p>
							<p><strong>Solution:</strong> Inspection revealed a corroded connector at the oxygen sensor. Cleaned the connector with electrical contact cleaner, applied dielectric grease, and tested the sensor - it was functioning properly. Cleared codes and monitored live data - sensor now switching properly.</p>
							<p><strong>Cost:</strong> $65 (parts: $25, labor: $40)</p>
							<p><strong>Time:</strong> 45 minutes</p>
						</div>

						<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
							<h4>Case 2: 2016 Toyota Camry - Failed O2 Sensor</h4>
							<p><strong>Vehicle:</strong> 2016 Toyota Camry 2.5L 4-cylinder, 89,000 miles</p>
							<p><strong>Problem:</strong> P0130 code with poor fuel economy and rough idle. GeekOBD showed Bank 1 Sensor 1 voltage stuck at 0.45V with no switching activity. Connector and wiring tested good.</p>
							<p><strong>Solution:</strong> Replaced Bank 1 Sensor 1 oxygen sensor with OEM part. Applied anti-seize compound to threads and torqued to specification. Cleared codes and test drove - sensor now switching properly between 0.1-0.9V, fuel economy restored.</p>
							<p><strong>Cost:</strong> $340 (parts: $195, labor: $145)</p>
							<p><strong>Time:</strong> 1.5 hours</p>
						</div>
					</div>
				</div>

				<!-- Sidebar -->
				<div class="col-md-4">
					<!-- GeekOBD APP Promotion -->
					<div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
						<h4 style="margin-bottom: 20px; color: white;"><i class="fa fa-mobile"></i> Monitor O2 Sensor Data</h4>
						<p style="margin-bottom: 20px; opacity: 0.9; font-size: 14px;">Track oxygen sensor performance and validate circuit operation with our GeekOBD APP!</p>
						<ul style="margin-bottom: 20px; padding-left: 20px; font-size: 14px;">
							<li style="margin-bottom: 8px;">Real-time O2 sensor readings</li>
							<li style="margin-bottom: 8px;">Bank 1 sensor 1 monitoring</li>
							<li style="margin-bottom: 8px;">Fuel trim correlation</li>
							<li style="margin-bottom: 8px;">Sensor response time testing</li>
						</ul>
						<a href="../app.html" class="btn btn-block" style="background: white; color: #667eea; border: none; border-radius: 25px; font-weight: bold; margin-bottom: 10px;">
							<i class="fa fa-download"></i> Download APP
						</a>
						<a href="../hardware.html" class="btn btn-block" style="background: rgba(255,255,255,0.2); color: white; border: 2px solid white; border-radius: 25px; font-weight: bold;">
							<i class="fa fa-shopping-cart"></i> Get MOBD Adapter
						</a>
					</div>

					<!-- Code Information -->
					<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
						<h4 style="margin-bottom: 20px;"><i class="fa fa-info"></i> Code Information</h4>
						<table class="table table-borderless">
							<tr>
								<td><strong>Code:</strong></td>
								<td>P0130</td>
							</tr>
							<tr>
								<td><strong>System:</strong></td>
								<td>Emissions</td>
							</tr>
							<tr>
								<td><strong>Severity:</strong></td>
								<td><span class="severity-badge severity-medium">MEDIUM</span></td>
							</tr>
							<tr>
								<td><strong>Category:</strong></td>
								<td>Sensor Codes</td>
							</tr>
						</table>
					</div>

					<!-- Popular Codes -->
					<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
						<h4 style="margin-bottom: 20px;"><i class="fa fa-star"></i> Related O2 Sensor Codes</h4>
						<ul style="list-style: none; padding: 0;">
							<li style="margin-bottom: 10px;"><a href="p0131.html" style="color: #667eea;">P0131 - O2 Sensor Low Voltage</a></li>
							<li style="margin-bottom: 10px;"><a href="p0132.html" style="color: #667eea;">P0132 - O2 Sensor High Voltage</a></li>
							<li style="margin-bottom: 10px;"><a href="p0133.html" style="color: #667eea;">P0133 - O2 Sensor Slow Response</a></li>
							<li style="margin-bottom: 10px;"><a href="p0134.html" style="color: #667eea;">P0134 - O2 Sensor No Activity</a></li>
							<li><a href="../dtc-codes.html" style="color: #667eea;">View All Codes →</a></li>
						</ul>
					</div>

					<!-- Diagnostic Tools -->
					<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
						<h4 style="margin-bottom: 20px;"><i class="fa fa-wrench"></i> Diagnostic Resources</h4>
						<ul style="list-style: none; padding: 0;">
							<li style="margin-bottom: 10px;"><a href="../obd-diagnostic-guide.html" style="color: #667eea;"><i class="fa fa-book"></i> OBD Diagnostic Guide</a></li>
							<li style="margin-bottom: 10px;"><a href="../vehicle-compatibility.html" style="color: #667eea;"><i class="fa fa-car"></i> Vehicle Compatibility</a></li>
							<li style="margin-bottom: 10px;"><a href="../fuel-efficiency-monitoring.html" style="color: #667eea;"><i class="fa fa-tachometer"></i> Fuel Efficiency Tips</a></li>
							<li style="margin-bottom: 10px;"><a href="../support.html" style="color: #667eea;"><i class="fa fa-support"></i> Technical Support</a></li>
							<li><a href="../blog.html" style="color: #667eea;"><i class="fa fa-newspaper-o"></i> Latest Articles</a></li>
						</ul>
					</div>

					<!-- Quick Navigation -->
					<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
						<h4 style="margin-bottom: 20px;"><i class="fa fa-compass"></i> Quick Navigation</h4>
						<div style="display: flex; flex-direction: column; gap: 10px;">
							<a href="#quick-answer" style="color: #667eea; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
								<i class="fa fa-bolt"></i> Quick Answer
							</a>
							<a href="#ai-qa" style="color: #667eea; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
								<i class="fa fa-comments"></i> Common Questions
							</a>
							<a href="#symptoms" style="color: #667eea; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
								<i class="fa fa-exclamation-triangle"></i> Symptoms
							</a>
							<a href="#causes" style="color: #667eea; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
								<i class="fa fa-search"></i> Causes
							</a>
							<a href="#cost-info" style="color: #667eea; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
								<i class="fa fa-calculator"></i> Repair Costs
							</a>
							<a href="#related" style="color: #667eea; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
								<i class="fa fa-link"></i> Related Codes
							</a>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Footer -->
	<div class="footer">
		<div class="container">
			<div class="row">
				<div class="col-md-12 text-center">
					<p>&copy; 2025 Beijing MentalRoad Technology Co., Ltd. All rights reserved.</p>
				</div>
			</div>
		</div>
	</div>
</div>

<!-- JavaScript -->
<script src="../js/jquery.min.js"></script>
<script src="../js/bootstrap.js"></script>
<script src="../js/superfish.js"></script>
<script src="../js/custom.js"></script>

</body>
</html>
