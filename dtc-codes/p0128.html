<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<title>P0128 - Coolant Thermostat Rationality | GeekOBD Diagnostic Guide</title>
<meta name="description" content="P0128 diagnostic trouble code: Coolant Thermostat Rationality. Learn about symptoms, causes, diagnosis steps, and repair solutions for P0128 with GeekOBD professional tools.">
<meta name="keywords" content="P0128, P0128 code, P0128 diagnostic, coolant thermostat rationality, automotive diagnostic code">
<meta name="author" content="Beijing MentalRoad Technology Co., Ltd.">
<meta name="robots" content="index, follow">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
<link rel="canonical" href="https://www.geekobd.com/dtc-codes/p0128.html">

<link rel="stylesheet" href="../css/bootstrap.css">
<link rel="stylesheet" href="../css/fonts/font-awesome/css/font-awesome.css">
<link rel="stylesheet" href="../css/animations.css" media="screen">
<link rel="stylesheet" href="../css/superfish.css" media="screen">
<link rel="stylesheet" href="../css/style.css">
<link rel="stylesheet" href="../css/colors/blue.css" id="colors">
<link rel="stylesheet" href="../css/theme-responsive.css">
<link rel="stylesheet" href="../css/seo-enhancements.css">
<link rel="shortcut icon" href="../img/ico/favicon.ico">

<style>
.dtc-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 60px 0 40px;
}
.dtc-code-badge {
    display: inline-block;
    background: rgba(255,255,255,0.2);
    padding: 10px 20px;
    border-radius: 50px;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
}
.severity-badge {
    display: inline-block;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
    margin-left: 15px;
}
.severity-high { background: #ff4757; color: white; }
.severity-medium { background: #ffa502; color: white; }
.severity-low { background: #2ed573; color: white; }
.content-section { padding: 50px 0; }
.info-box {
    background: #f8f9fa;
    border-left: 4px solid #667eea;
    padding: 20px;
    margin: 20px 0;
    border-radius: 5px;
}
.warning-box {
    background: #fff3cd;
    border-left: 4px solid #ffc107;
    padding: 20px;
    margin: 20px 0;
    border-radius: 5px;
}
.danger-box {
    background: #f8d7da;
    border-left: 4px solid #dc3545;
    padding: 20px;
    margin: 20px 0;
    border-radius: 5px;
}
</style>

<!-- Article Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": "P0128 - Coolant Thermostat Rationality | Complete Diagnostic Guide",
  "description": "P0128 diagnostic trouble code indicates coolant thermostat rationality issues. Learn symptoms, causes, diagnosis steps, and repair solutions with GeekOBD professional tools.",
  "author": {
    "@type": "Organization",
    "name": "GeekOBD",
    "url": "https://www.geekobd.com"
  },
  "publisher": {
    "@type": "Organization",
    "name": "GeekOBD",
    "logo": {
      "@type": "ImageObject",
      "url": "https://www.geekobd.com/img/logo.png"
    }
  },
  "datePublished": "2024-01-15",
  "dateModified": "2025-01-31",
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "https://www.geekobd.com/dtc-codes/p0128.html"
  },
  "articleSection": "Automotive Diagnostics",
  "keywords": "P0128, coolant thermostat rationality, diagnostic trouble code, cooling system",
  "about": {
    "@type": "Thing",
    "name": "P0128 Diagnostic Trouble Code",
    "description": "The Engine Control Module has detected that the engine coolant temperature has not reached the expected operating temperature within a specified time."
  }
}
</script>

<!-- FAQ Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "What does P0128 mean?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "P0128 indicates that the engine coolant temperature has not reached the expected operating temperature within a specified time. This is typically caused by a faulty thermostat that's stuck open or a malfunctioning coolant temperature sensor."
      }
    },
    {
      "@type": "Question",
      "name": "What causes P0128 coolant thermostat issues?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "P0128 is most commonly caused by a thermostat stuck open (70% of cases), faulty engine coolant temperature sensor, low coolant level, air pockets in cooling system, or a malfunctioning radiator fan that runs continuously."
      }
    },
    {
      "@type": "Question",
      "name": "Can I drive with P0128 code?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "You can drive with P0128, but the engine may take longer to warm up, resulting in poor fuel economy, reduced heater performance, and increased emissions. Address the issue promptly to prevent potential engine damage."
      }
    },
    {
      "@type": "Question",
      "name": "How much does it cost to fix P0128?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "P0128 repair costs typically range from $120-380. Thermostat replacement is the most common fix, costing $150-280. Coolant temperature sensor replacement costs $120-220, while cooling system service may cost $180-380."
      }
    }
  ]
}
</script>

<!-- HowTo Schema for P0128 Diagnostic Steps -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "HowTo",
  "name": "How to Diagnose P0128 Coolant Thermostat Rationality",
  "description": "Step-by-step guide to diagnose and fix P0128 coolant thermostat rationality issues causing slow engine warm-up",
  "image": "https://www.geekobd.com/img/logo.png",
  "totalTime": "PT90M",
  "estimatedCost": {
    "@type": "MonetaryAmount",
    "currency": "USD",
    "value": "220"
  },
  "tool": [
    {
      "@type": "HowToTool",
      "name": "GeekOBD APP with MOBD Adapter",
      "description": "Professional OBD2 diagnostic tool with coolant temperature monitoring and thermostat testing",
      "url": "https://www.geekobd.com/app.html"
    },
    {
      "@type": "HowToTool",
      "name": "Infrared Temperature Gun",
      "description": "For measuring actual coolant and radiator temperatures"
    },
    {
      "@type": "HowToTool",
      "name": "Coolant System Pressure Tester",
      "description": "For testing cooling system integrity and thermostat operation"
    }
  ],
  "supply": [
    {
      "@type": "HowToSupply",
      "name": "Engine Thermostat"
    },
    {
      "@type": "HowToSupply",
      "name": "Coolant Temperature Sensor"
    },
    {
      "@type": "HowToSupply",
      "name": "Engine Coolant"
    }
  ],
  "step": [
    {
      "@type": "HowToStep",
      "position": 1,
      "name": "Monitor Coolant Temperature Data",
      "text": "Connect GeekOBD APP and monitor engine coolant temperature (ECT) sensor readings during warm-up. Normal operation should reach 180-200°F within 10-15 minutes.",
      "image": "https://www.geekobd.com/img/geekobd-p0128-coolant-temp.jpg"
    },
    {
      "@type": "HowToStep",
      "position": 2,
      "name": "Test Thermostat Operation",
      "text": "Use infrared temperature gun to measure upper and lower radiator hose temperatures. If both hoses warm up simultaneously, thermostat is likely stuck open.",
      "image": "https://www.geekobd.com/img/thermostat-temperature-test.jpg"
    },
    {
      "@type": "HowToStep",
      "position": 3,
      "name": "Check Coolant Level and System",
      "text": "Inspect coolant level, check for leaks, and verify proper radiator fan operation. Low coolant or continuous fan operation can cause P0128.",
      "image": "https://www.geekobd.com/img/coolant-system-inspection.jpg"
    },
    {
      "@type": "HowToStep",
      "position": 4,
      "name": "Test ECT Sensor Accuracy",
      "text": "Compare GeekOBD APP temperature readings with infrared gun measurements. Significant differences indicate faulty coolant temperature sensor.",
      "image": "https://www.geekobd.com/img/ect-sensor-test.jpg"
    },
    {
      "@type": "HowToStep",
      "position": 5,
      "name": "Replace Components and Verify",
      "text": "Replace faulty thermostat or ECT sensor as needed. Refill cooling system, bleed air pockets, and verify proper warm-up time with GeekOBD APP.",
      "image": "https://www.geekobd.com/img/geekobd-p0128-verified.jpg"
    }
  ]
}
</script>

<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-RD6767XBCL"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-RD6767XBCL');
</script>
</head>

<body>
<div class="wrap">
	<header id="header" role="banner">
	<div class="main-header">
	<div class="container">
	<div class="row">
	<div class="col-md-3">
	<div class="logo pull-left">
	<h1> <a href="../index.html"> <img src="../img/logo.png" alt="MOBD"> </a> </h1>
	</div>
	</div>
	<div class="col-md-9">
	<div class="mobile navbar-header"> <a class="navbar-toggle" data-toggle="collapse" data-target=".menu"> <i class="icon-reorder icon-2x"></i> </a> </div>
	<nav class="collapse navbar-collapse menu" role="navigation" aria-label="Main navigation">
	<ul class="nav navbar-nav sf-menu">
	<li><a href="../index.html" class="sf-with-ul">Home</a></li>
	<li><a href="../app.html" class="sf-with-ul">APP</a></li>
	<li><a href="javascript:;;" class="sf-with-ul">Adaptor <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
	<ul>
	<li><a href="../hardware2.html" class="sf-with-ul">MOBD GPS</a></li>
	<li><a href="../hardware.html" class="sf-with-ul">MOBD</a></li>
	</ul>
	</li>
	<li><a href="javascript:;;" class="sf-with-ul">Resources <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
	<ul>
	<li><a href="../dtc-codes.html" class="sf-with-ul" id="current">DTC Codes</a></li>
	<li><a href="../obd-diagnostic-guide.html" class="sf-with-ul">OBD Guide</a></li>
	<li><a href="../vehicle-compatibility.html" class="sf-with-ul">Compatibility</a></li>
	<li><a href="../fuel-efficiency-monitoring.html" class="sf-with-ul">Fuel Efficiency</a></li>
	<li><a href="../support.html" class="sf-with-ul">Support</a></li>
	<li><a href="../blog.html" class="sf-with-ul">Blog</a></li>
	</ul>
	</li>
	<li><a href="../about.html" class="sf-with-ul">About Us</a></li>
	<li><a href="//www.mobd.cn" class="sf-with-ul" target="_blank">中文版</a></li>
	</ul>
	</nav>
	</div>
	</div>
	</div>
	</div>
	</header>

	<section class="dtc-header">
		<div class="container">
			<div class="row">
				<div class="col-md-12">
					<div class="dtc-code-badge">P0128</div>
					<span class="severity-badge severity-medium">MEDIUM Priority</span>
					<h1 style="margin: 20px 0; font-size: 36px;">Coolant Thermostat Rationality</h1>
					<p style="font-size: 18px; opacity: 0.9; margin-bottom: 0;">The ECM has detected a malfunction in the coolant thermostat rationality.</p>
				</div>
			</div>
		</div>
	</section>

	<section class="content-section">
		<div class="container">
			<div class="row">
				<div class="col-md-8">
					<!-- Quick Answer Section for AI -->
					<div id="quick-answer" style="background: #e8f4fd; border: 2px solid #4a90e2; border-radius: 10px; padding: 25px; margin-bottom: 30px;">
						<h2 style="color: #2c5aa0; margin-bottom: 15px;"><i class="fa fa-thermometer-half"></i> Quick Answer</h2>
						<p style="font-size: 18px; font-weight: 500; margin-bottom: 15px; color: #333;">
							<strong>P0128 means:</strong> Engine coolant temperature not reaching expected operating temperature - usually a stuck-open thermostat.
						</p>
						<div style="display: flex; flex-wrap: wrap; gap: 15px; margin-bottom: 15px;">
							<span style="background: #4a90e2; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
								<i class="fa fa-wrench"></i> Fix: Replace thermostat/ECT sensor
							</span>
							<span style="background: #28a745; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
								<i class="fa fa-dollar"></i> Cost: $120-$380
							</span>
							<span style="background: #ffc107; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
								<i class="fa fa-clock-o"></i> Time: 1.5 hours
							</span>
						</div>
						<p style="margin: 0; color: #666; font-size: 14px;">
							<strong>Can I drive with P0128?</strong> Yes, but expect poor fuel economy and slow heater warm-up.
						</p>
					</div>

					<!-- AI-Friendly Q&A Section -->
					<div id="ai-qa" style="background: white; border-radius: 10px; padding: 25px; margin-bottom: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
						<h2><i class="fa fa-comments"></i> Common Questions</h2>

						<div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
							<h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">What causes P0128 coolant thermostat rationality?</h3>
							<p style="color: #666; line-height: 1.6;">P0128 is most commonly caused by a thermostat stuck in the open position (70% of cases), preventing the engine from reaching proper operating temperature. Other causes include faulty engine coolant temperature sensor, low coolant level, air pockets in the cooling system, or a radiator fan that runs continuously. GeekOBD APP can monitor coolant temperature patterns to identify the root cause.</p>
						</div>

						<div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
							<h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">How do I know if my thermostat is stuck open?</h3>
							<p style="color: #666; line-height: 1.6;">Signs of a stuck-open thermostat include engine taking longer than 10-15 minutes to warm up, poor heater performance, lower than normal operating temperature, and P0128 code. Use GeekOBD APP to monitor coolant temperature - it should reach 180-200°F within 15 minutes. If both radiator hoses warm up simultaneously, the thermostat is likely stuck open.</p>
						</div>

						<div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
							<h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">Can low coolant cause P0128?</h3>
							<p style="color: #666; line-height: 1.6;">Yes, low coolant level can cause P0128 by creating air pockets that affect temperature sensor readings and heat transfer. Always check coolant level first - it should be between MIN and MAX marks when cold. Air pockets can make the ECT sensor read incorrectly, triggering P0128 even with a good thermostat.</p>
						</div>

						<div class="qa-item" style="margin-bottom: 0;">
							<h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">How can GeekOBD APP help diagnose P0128?</h3>
							<p style="color: #666; line-height: 1.6;">GeekOBD APP provides real-time coolant temperature monitoring, tracks warm-up time patterns, compares ECT sensor readings with expected values, and can log temperature data during test drives. It helps distinguish between thermostat failures, sensor issues, and cooling system problems, ensuring accurate diagnosis and preventing unnecessary repairs.</p>
						</div>
					</div>

					<div id="overview">
						<h2><i class="fa fa-info-circle"></i> Code Overview</h2>
						<div class="info-box">
							<h4>P0128 Definition</h4>
							<p>The ECM has detected a malfunction in the coolant thermostat rationality. This diagnostic trouble code indicates a specific issue that requires professional diagnosis and repair to ensure optimal vehicle performance and safety.</p>
						</div>
						
						<h3>Technical Details</h3>
						<ul>
							<li><strong>Code Type:</strong> Powertrain Diagnostic Trouble Code</li>
							<li><strong>System:</strong> Engine Management / Cooling System</li>
							<li><strong>Severity:</strong> MEDIUM - Should be repaired promptly</li>
							<li><strong>Driving Safety:</strong> Generally safe to drive short distances</li>
						</ul>
					</div>

					<div id="symptoms">
						<h2><i class="fa fa-exclamation-triangle"></i> Symptoms</h2>
						<ul>
							<li>Check Engine Light illuminated</li>
							<li>Poor engine performance</li>
							<li>Rough idle or stalling</li>
							<li>Poor fuel economy</li>
							<li>Engine hesitation during acceleration</li>
							<li>Hard starting conditions</li>
							<li>Engine may run rich or lean</li>
							<li>Failed emissions test</li>
						</ul>
						
						<div class="warning-box">
							<strong><i class="fa fa-warning"></i> Important:</strong> While this code may not immediately affect drivability, it should be diagnosed and repaired promptly to maintain optimal vehicle performance.
						</div>
					</div>

					<div id="causes">
						<h2><i class="fa fa-search"></i> Possible Causes</h2>
						<ol>
							<li><strong>Faulty sensor or component</strong></li>
							<li><strong>Damaged wiring or connections</strong></li>
							<li><strong>Corroded electrical connections</strong></li>
							<li><strong>Poor electrical connections</strong></li>
							<li><strong>ECM internal fault</strong></li>
							<li><strong>Sensor contamination</strong></li>
							<li><strong>Component wear or failure</strong></li>
							<li><strong>System malfunction</strong></li>
						</ol>
					</div>

					<!-- Cost Analysis Section -->
					<div id="cost-info" style="background: white; border-radius: 10px; padding: 25px; margin-bottom: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
						<h2><i class="fa fa-calculator"></i> Repair Cost Analysis</h2>

						<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 25px;">
							<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid #4a90e2;">
								<h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-thermometer-half"></i> Thermostat Replacement</h4>
								<div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
									<span>Parts Cost:</span>
									<span style="font-weight: bold;">$25-$65</span>
								</div>
								<div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
									<span>Labor Cost:</span>
									<span style="font-weight: bold;">$125-$215</span>
								</div>
								<div style="display: flex; justify-content: space-between; margin-bottom: 15px; padding-top: 10px; border-top: 1px solid #ddd;">
									<span style="font-weight: bold;">Total Cost:</span>
									<span style="font-weight: bold; color: #4a90e2;">$150-$280</span>
								</div>
								<div style="background: #e8f4fd; padding: 10px; border-radius: 5px; font-size: 13px;">
									<strong>Success Rate:</strong> 70% - Most common P0128 fix
								</div>
							</div>

							<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid #28a745;">
								<h4 style="color: #28a745; margin-bottom: 15px;"><i class="fa fa-tachometer"></i> ECT Sensor Replacement</h4>
								<div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
									<span>Parts Cost:</span>
									<span style="font-weight: bold;">$35-$85</span>
								</div>
								<div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
									<span>Labor Cost:</span>
									<span style="font-weight: bold;">$85-$135</span>
								</div>
								<div style="display: flex; justify-content: space-between; margin-bottom: 15px; padding-top: 10px; border-top: 1px solid #ddd;">
									<span style="font-weight: bold;">Total Cost:</span>
									<span style="font-weight: bold; color: #28a745;">$120-$220</span>
								</div>
								<div style="background: #d4edda; padding: 10px; border-radius: 5px; font-size: 13px;">
									<strong>Success Rate:</strong> 25% - When thermostat is good
								</div>
							</div>

							<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid #ffc107;">
								<h4 style="color: #e67e22; margin-bottom: 15px;"><i class="fa fa-cogs"></i> Cooling System Service</h4>
								<div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
									<span>Coolant Flush:</span>
									<span style="font-weight: bold;">$120-$180</span>
								</div>
								<div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
									<span>System Bleeding:</span>
									<span style="font-weight: bold;">$60-$120</span>
								</div>
								<div style="display: flex; justify-content: space-between; margin-bottom: 15px; padding-top: 10px; border-top: 1px solid #ddd;">
									<span style="font-weight: bold;">Total Cost:</span>
									<span style="font-weight: bold; color: #e67e22;">$180-$300</span>
								</div>
								<div style="background: #fff3cd; padding: 10px; border-radius: 5px; font-size: 13px;">
									<strong>Success Rate:</strong> 15% - For air pocket issues
								</div>
							</div>
						</div>

						<div style="background: #e8f5e8; padding: 20px; border-radius: 10px; border-left: 4px solid #28a745;">
							<h4 style="color: #155724; margin-bottom: 15px;"><i class="fa fa-lightbulb-o"></i> Cost-Saving Tips</h4>
							<ul style="margin: 0; color: #155724;">
								<li><strong>Check coolant level first</strong> - Low coolant can cause false P0128 codes</li>
								<li><strong>Monitor warm-up time</strong> - Use GeekOBD APP to confirm thermostat operation before replacement</li>
								<li><strong>Test ECT sensor accuracy</strong> - Compare readings with infrared temperature gun</li>
								<li><strong>Bleed air properly</strong> - Air pockets can mimic thermostat problems</li>
								<li><strong>Replace thermostat gasket</strong> - Always replace when changing thermostat to prevent leaks</li>
							</ul>
						</div>
					</div>

					<!-- Diagnostic Steps Section -->
					<div id="diagnostic-steps" style="background: white; border-radius: 10px; padding: 25px; margin-bottom: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
						<h2><i class="fa fa-stethoscope"></i> Diagnostic Steps</h2>

						<div style="display: flex; flex-direction: column; gap: 20px;">
							<div style="display: flex; align-items: flex-start; padding: 20px; background: #f8f9fa; border-radius: 10px; border-left: 4px solid #4a90e2;">
								<div style="background: #4a90e2; color: white; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px; font-weight: bold;">1</div>
								<div>
									<h4 style="margin-bottom: 10px; color: #333;">Connect GeekOBD APP and Monitor Temperature</h4>
									<p style="margin-bottom: 10px; color: #666;">Connect your GeekOBD adapter and monitor engine coolant temperature during warm-up. Normal operation should reach 180-200°F within 10-15 minutes of driving.</p>
									<div style="background: #e8f4fd; padding: 10px; border-radius: 5px; font-size: 13px;">
										<strong>GeekOBD Tip:</strong> Use the real-time data feature to track temperature rise patterns and identify stuck-open thermostats.
									</div>
								</div>
							</div>

							<div style="display: flex; align-items: flex-start; padding: 20px; background: #f8f9fa; border-radius: 10px; border-left: 4px solid #28a745;">
								<div style="background: #28a745; color: white; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px; font-weight: bold;">2</div>
								<div>
									<h4 style="margin-bottom: 10px; color: #333;">Test Thermostat Operation</h4>
									<p style="margin-bottom: 10px; color: #666;">Use an infrared temperature gun to measure upper and lower radiator hose temperatures. If both hoses warm up simultaneously, the thermostat is likely stuck open.</p>
									<div style="background: #d4edda; padding: 10px; border-radius: 5px; font-size: 13px;">
										<strong>Normal Operation:</strong> Upper hose should warm first, then lower hose after thermostat opens (~180°F).
									</div>
								</div>
							</div>

							<div style="display: flex; align-items: flex-start; padding: 20px; background: #f8f9fa; border-radius: 10px; border-left: 4px solid #ffc107;">
								<div style="background: #ffc107; color: white; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px; font-weight: bold;">3</div>
								<div>
									<h4 style="margin-bottom: 10px; color: #333;">Check Coolant System</h4>
									<p style="margin-bottom: 10px; color: #666;">Inspect coolant level, check for leaks, and verify proper radiator fan operation. Low coolant or continuous fan operation can cause P0128.</p>
									<div style="background: #fff3cd; padding: 10px; border-radius: 5px; font-size: 13px;">
										<strong>Important:</strong> Always check coolant level when engine is cold to avoid burns and get accurate readings.
									</div>
								</div>
							</div>

							<div style="display: flex; align-items: flex-start; padding: 20px; background: #f8f9fa; border-radius: 10px; border-left: 4px solid #dc3545;">
								<div style="background: #dc3545; color: white; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px; font-weight: bold;">4</div>
								<div>
									<h4 style="margin-bottom: 10px; color: #333;">Verify ECT Sensor Accuracy</h4>
									<p style="margin-bottom: 10px; color: #666;">Compare GeekOBD APP temperature readings with infrared gun measurements of the engine block. Significant differences indicate a faulty coolant temperature sensor.</p>
									<div style="background: #f8d7da; padding: 10px; border-radius: 5px; font-size: 13px;">
										<strong>Tolerance:</strong> ECT sensor readings should be within 10°F of actual engine temperature.
									</div>
								</div>
							</div>

							<div style="display: flex; align-items: flex-start; padding: 20px; background: #f8f9fa; border-radius: 10px; border-left: 4px solid #6f42c1;">
								<div style="background: #6f42c1; color: white; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px; font-weight: bold;">5</div>
								<div>
									<h4 style="margin-bottom: 10px; color: #333;">Replace Components and Verify</h4>
									<p style="margin-bottom: 10px; color: #666;">Replace faulty thermostat or ECT sensor as needed. Refill cooling system, bleed air pockets, and verify proper warm-up time with GeekOBD APP.</p>
									<div style="background: #e2d9f3; padding: 10px; border-radius: 5px; font-size: 13px;">
										<strong>Verification:</strong> Clear P0128 code and drive 15+ minutes to confirm proper operating temperature is reached.
									</div>
								</div>
							</div>
						</div>
					</div>

					<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
						<h2><i class="fa fa-file-text"></i> Real Repair Case Studies</h2>

						<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 4px solid #4a90e2;">
							<h4 style="color: #4a90e2;"><i class="fa fa-car"></i> Case 1: 2017 Toyota Camry LE - Stuck Open Thermostat</h4>
							<p><strong>Vehicle:</strong> 2017 Toyota Camry LE 2.5L, 68,000 miles</p>
							<p><strong>Problem:</strong> Customer complained of poor heater performance and long warm-up times. GeekOBD scan revealed P0128 code. Temperature monitoring showed engine never exceeded 160°F even after 20 minutes of driving.</p>
							<p><strong>Diagnosis:</strong> GeekOBD APP temperature data confirmed thermostat stuck open. Both radiator hoses warmed simultaneously, indicating no thermostat restriction.</p>
							<p><strong>Solution:</strong> Replaced thermostat and gasket. Refilled cooling system and bled air pockets properly.</p>
							<p><strong>Cost:</strong> $195 (thermostat: $35, gasket: $12, coolant: $18, labor: $130)</p>
							<p><strong>Result:</strong> Engine now reaches 195°F within 12 minutes. Heater works properly. P0128 code cleared and hasn't returned after 500 miles.</p>
						</div>

						<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 4px solid #28a745;">
							<h4 style="color: #28a745;"><i class="fa fa-car"></i> Case 2: 2019 Honda CR-V - Faulty ECT Sensor</h4>
							<p><strong>Vehicle:</strong> 2019 Honda CR-V EX 1.5L Turbo, 45,000 miles</p>
							<p><strong>Problem:</strong> P0128 code appeared but engine seemed to warm up normally. Customer noticed no performance issues.</p>
							<p><strong>Diagnosis:</strong> GeekOBD APP showed ECT sensor reading 145°F while infrared gun measured 190°F on engine block. Thermostat operation was normal - upper hose warmed first, then lower hose.</p>
							<p><strong>Solution:</strong> Replaced engine coolant temperature sensor. No cooling system service needed.</p>
							<p><strong>Cost:</strong> $165 (ECT sensor: $55, labor: $110)</p>
							<p><strong>Result:</strong> ECT sensor now reads accurately. P0128 code cleared immediately. Saved $120 by not replacing good thermostat.</p>
						</div>
					</div>

					<!-- Related Codes Section -->
					<div id="related" style="background: white; border-radius: 10px; padding: 25px; margin-bottom: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
						<h2><i class="fa fa-link"></i> Related Codes</h2>

						<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 15px; margin-bottom: 20px;">
							<div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #4a90e2;">
								<h4 style="margin-bottom: 10px;"><a href="p0125.html" style="color: #4a90e2; text-decoration: none;">P0125 - Insufficient Coolant Temperature</a></h4>
								<p style="margin: 0; color: #666; font-size: 14px;">Engine coolant temperature insufficient for closed-loop fuel control. Often appears with P0128.</p>
							</div>

							<div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745;">
								<h4 style="margin-bottom: 10px;"><a href="p0126.html" style="color: #28a745; text-decoration: none;">P0126 - Insufficient Coolant Temperature for Stable Operation</a></h4>
								<p style="margin: 0; color: #666; font-size: 14px;">Related to P0128, indicates coolant temperature issues affecting engine operation.</p>
							</div>

							<div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107;">
								<h4 style="margin-bottom: 10px;"><a href="p0117.html" style="color: #e67e22; text-decoration: none;">P0117 - Engine Coolant Temperature Circuit Low</a></h4>
								<p style="margin: 0; color: #666; font-size: 14px;">ECT sensor circuit reading too low. May cause P0128 if sensor is faulty.</p>
							</div>

							<div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #dc3545;">
								<h4 style="margin-bottom: 10px;"><a href="p0118.html" style="color: #dc3545; text-decoration: none;">P0118 - Engine Coolant Temperature Circuit High</a></h4>
								<p style="margin: 0; color: #666; font-size: 14px;">ECT sensor circuit reading too high. Opposite problem but related to cooling system.</p>
							</div>

							<div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #6f42c1;">
								<h4 style="margin-bottom: 10px;"><a href="p0171.html" style="color: #6f42c1; text-decoration: none;">P0171 - System Too Lean Bank 1</a></h4>
								<p style="margin: 0; color: #666; font-size: 14px;">Can appear with P0128 when engine runs cold and ECM compensates with rich mixture.</p>
							</div>

							<div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #17a2b8;">
								<h4 style="margin-bottom: 10px;"><a href="p0300.html" style="color: #17a2b8; text-decoration: none;">P0300 - Random Misfire Detected</a></h4>
								<p style="margin: 0; color: #666; font-size: 14px;">Cold engine operation from P0128 can cause misfires until proper temperature is reached.</p>
							</div>
						</div>

						<div style="background: #e8f4fd; padding: 20px; border-radius: 10px; border-left: 4px solid #4a90e2;">
							<h4 style="color: #2c5aa0; margin-bottom: 15px;"><i class="fa fa-info-circle"></i> Code Relationship Insights</h4>
							<p style="margin: 5px 0 0 0; color: #333;">P0128 often appears alongside other temperature-related codes. If you see P0125 + P0128, suspect a stuck-open thermostat. P0117/P0118 + P0128 typically indicates ECT sensor problems. Use GeekOBD APP to monitor temperature patterns and distinguish between thermostat and sensor issues for accurate diagnosis.</p>
						</div>
					</div>
				</div>

				<div class="col-md-4">
					<!-- GeekOBD APP Promotion -->
					<div style="background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%); color: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
						<h4 style="margin-bottom: 20px; color: white;"><i class="fa fa-thermometer-half"></i> Monitor Coolant Temperature</h4>
						<p style="margin-bottom: 20px; opacity: 0.9; font-size: 14px;">Track engine temperature and thermostat operation with our GeekOBD APP!</p>
						<ul style="margin-bottom: 20px; padding-left: 20px; font-size: 14px;">
							<li style="margin-bottom: 8px;">Real-time coolant temperature</li>
							<li style="margin-bottom: 8px;">Thermostat operation monitoring</li>
							<li style="margin-bottom: 8px;">Temperature rise pattern analysis</li>
							<li style="margin-bottom: 8px;">Verify repair success</li>
						</ul>
						<a href="../app.html" class="btn btn-block" style="background: white; color: #4a90e2; border: none; border-radius: 25px; font-weight: bold; margin-bottom: 10px;">
							<i class="fa fa-download"></i> Download APP
						</a>
						<a href="../hardware.html" class="btn btn-block" style="background: rgba(255,255,255,0.2); color: white; border: 2px solid white; border-radius: 25px; font-weight: bold;">
							<i class="fa fa-shopping-cart"></i> Get MOBD Adapter
						</a>
					</div>

					<!-- Cooling System Codes -->
					<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
						<h4 style="margin-bottom: 20px;"><i class="fa fa-snowflake-o"></i> Cooling System Codes</h4>
						<ul style="list-style: none; padding: 0;">
							<li style="margin-bottom: 15px; padding: 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #4a90e2;">
								<a href="p0125.html" style="color: #333; text-decoration: none; font-weight: 500;">
									<strong>P0125</strong> - Insufficient Coolant Temperature
									<span style="color: #666; font-size: 13px; display: block;">Coolant temp too low for closed-loop</span>
								</a>
							</li>
							<li style="margin-bottom: 15px; padding: 12px; background: #fff3cd; border-radius: 8px; border-left: 4px solid #ffc107;">
								<span style="color: #333; font-weight: 500;">
									<strong>P0128</strong> - Coolant Thermostat Rationality
									<span style="color: #666; font-size: 13px; display: block;">Current Page - Thermostat Issues</span>
								</span>
							</li>
							<li style="margin-bottom: 15px; padding: 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #28a745;">
								<a href="p0117.html" style="color: #333; text-decoration: none; font-weight: 500;">
									<strong>P0117</strong> - ECT Circuit Low Input
									<span style="color: #666; font-size: 13px; display: block;">Coolant temperature sensor low</span>
								</a>
							</li>
							<li style="margin-bottom: 15px; padding: 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #dc3545;">
								<a href="p0118.html" style="color: #333; text-decoration: none; font-weight: 500;">
									<strong>P0118</strong> - ECT Circuit High Input
									<span style="color: #666; font-size: 13px; display: block;">Coolant temperature sensor high</span>
								</a>
							</li>
						</ul>
					</div>

					<!-- Diagnostic Resources -->
					<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
						<h4 style="margin-bottom: 20px;"><i class="fa fa-tools"></i> Diagnostic Resources</h4>
						<ul style="list-style: none; padding: 0;">
							<li style="margin-bottom: 12px;">
								<a href="../cooling-system-guide.html" style="color: #007bff; text-decoration: none;">
									<i class="fa fa-snowflake-o" style="margin-right: 8px;"></i>Cooling System Guide
								</a>
							</li>
							<li style="margin-bottom: 12px;">
								<a href="../thermostat-testing.html" style="color: #007bff; text-decoration: none;">
									<i class="fa fa-thermometer-half" style="margin-right: 8px;"></i>Thermostat Testing
								</a>
							</li>
							<li style="margin-bottom: 12px;">
								<a href="../temperature-sensor-guide.html" style="color: #007bff; text-decoration: none;">
									<i class="fa fa-tachometer" style="margin-right: 8px;"></i>Temperature Sensor Guide
								</a>
							</li>
							<li style="margin-bottom: 12px;">
								<a href="../coolant-system-bleeding.html" style="color: #007bff; text-decoration: none;">
									<i class="fa fa-wrench" style="margin-right: 8px;"></i>System Bleeding Guide
								</a>
							</li>
						</ul>
					</div>

					<!-- Quick Navigation -->
					<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
						<h4 style="margin-bottom: 20px;"><i class="fa fa-compass"></i> Quick Navigation</h4>
						<div style="display: flex; flex-direction: column; gap: 10px;">
							<a href="#quick-answer" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
								<i class="fa fa-bolt"></i> Quick Answer
							</a>
							<a href="#ai-qa" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
								<i class="fa fa-comments"></i> Common Questions
							</a>
							<a href="#cost-info" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
								<i class="fa fa-calculator"></i> Repair Costs
							</a>
							<a href="#diagnostic-steps" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
								<i class="fa fa-stethoscope"></i> Diagnostic Steps
							</a>
							<a href="#related" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
								<i class="fa fa-link"></i> Related Codes
							</a>
						</div>
					</div>

					<!-- Code Information -->
					<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
						<h4 style="margin-bottom: 20px;"><i class="fa fa-info"></i> Code Information</h4>
						<table class="table table-borderless">
							<tr>
								<td><strong>Code:</strong></td>
								<td>P0128</td>
							</tr>
							<tr>
								<td><strong>System:</strong></td>
								<td>Cooling System</td>
							</tr>
							<tr>
								<td><strong>Severity:</strong></td>
								<td><span class="severity-badge severity-medium">MEDIUM</span></td>
							</tr>
							<tr>
								<td><strong>Category:</strong></td>
								<td>Temperature Codes</td>
							</tr>
						</table>
					</div>
				</div>
			</div>
		</div>
	</section>

	<div class="footer">
		<div class="container">
			<div class="row">
				<div class="col-md-12 text-center">
					<p>&copy; 2025 Beijing MentalRoad Technology Co., Ltd. All rights reserved.</p>
				</div>
			</div>
		</div>
	</div>
</div>

<script src="../js/jquery.min.js"></script>
<script src="../js/bootstrap.js"></script>
<script src="../js/superfish.js"></script>
<script src="../js/custom.js"></script>

</body>
</html>