# DTC Code Generator

自动化生成DTC诊断代码页面的工具，专为车况检测大师网站设计。

## 📁 文件说明

### 核心文件
- **`fixed_dtc_generator.py`** - 主要的DTC页面生成器
- **`dtc_codes.txt`** - 代码状态跟踪文件（记录已完成的代码和时间戳）
- **`requirements.txt`** - Python依赖包列表
- **`data/`** - 存储生成的DTC数据JSON文件

## 🚀 使用方法

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 添加新的DTC代码
在 `dtc_codes.txt` 文件中添加新的代码（每行一个）：
```
B0016 [2025-08-01 22:13:37]
B001A [2025-08-01 22:13:37]
B001B [2025-08-01 22:23:22]
B001C [2025-08-01 22:13:37]
P0761 [2025-08-01 22:28:29]
P0762
```

### 3. 运行生成器
```bash
# 最简单的方式（推荐）
python fixed_dtc_generator.py

# 或指定并发数
python fixed_dtc_generator.py --workers 3

# 或指定自定义API密钥
python fixed_dtc_generator.py --api-key "your-custom-api-key"
```

### 4. 参数说明
- `--api-key`: ChatGPT API密钥（已设置默认值）
- `--workers`: 并发线程数（默认1，推荐1-3）
- `--base-url`: API基础URL（默认：https://one.close-api.com/v1）
- `--model`: 使用的模型（默认：gpt-4o-mini）

## ✨ 功能特点

### 页面结构完全匹配P0341.html模板
- ✅ **4个专业Common Questions**（符合优化指南要求）
- ✅ **专业的Diagnostic Steps卡片样式**（带GeekOBD APP提示）
- ✅ **正确的Repair Costs样式**（彩色卡片，无CSS类依赖）
- ✅ **HowTo结构化数据**（动态生成，基于实际诊断步骤）
- ✅ **GeekOBD APP深度集成**（每个部分都有相关提示）

### 技术特性
- ✅ **多线程并发生成**（提高生成效率）
- ✅ **状态跟踪和断点续传**（避免重复生成）
- ✅ **API错误处理和重试机制**
- ✅ **完整的日志记录**

## 📄 生成的页面包含

### 主要内容区域
1. **Quick Answer** - AI友好的快速答案（紧急程度、成本估算、驾驶建议）
2. **Common Questions** - 4个专业技术问答
   - 代码差异比较
   - 常见故障原因
   - 严重程度评估
   - GeekOBD APP诊断技巧
3. **Technical Overview** - 代码详细说明和系统信息
4. **Symptoms** - 故障症状列表
5. **Causes** - 可能原因分析
6. **Repair Costs** - 详细成本分析（多种维修方案）
7. **Diagnostic Steps** - 专业诊断流程（5步卡片式）
8. **Case Studies** - 真实维修案例（2个不同场景）

### 侧边栏
- **页面导航** - 快速跳转链接
- **Related Codes** - 相关代码推荐（按类别分组）

## 🎯 优化特点

### SEO优化
- **结构化数据**：Article、FAQ、HowTo三种Schema
- **元标签优化**：title、description、keywords
- **语义化HTML**：正确的标题层级和内容结构

### 用户体验
- **响应式设计**：移动端友好
- **专业视觉设计**：卡片式布局，彩色主题
- **快速导航**：侧边栏导航，页内锚点
- **GeekOBD APP集成**：每个诊断步骤都有APP使用提示

### 内容质量
- **专业术语**：准确的汽车诊断术语
- **实用信息**：具体的成本、时间、成功率
- **安全提醒**：重要的安全注意事项

## 📊 支持的代码类型

- **P-codes**: Powertrain（发动机、变速箱、排放系统）
- **B-codes**: Body（车身、安全气囊、照明、安防、空调）
- **C-codes**: Chassis（底盘、制动、转向、悬挂）
- **U-codes**: Network（网络通信、模块间通信）

## 🔧 故障排除

### 常见问题
1. **API调用失败**：检查API密钥和网络连接
2. **生成内容不完整**：检查API响应和JSON解析
3. **文件写入失败**：检查目录权限

### 日志查看
生成过程中的详细日志会显示在控制台，包括：
- 代码处理进度
- API调用状态
- 文件生成结果
- 错误信息和重试

## 📝 更新日志

### v2.0 (2025-08-01)
- ✅ 完全重构，匹配P0341.html模板
- ✅ 修复Common Questions数量（4个）
- ✅ 修复Repair Costs样式问题
- ✅ 优化HowTo结构化数据生成
- ✅ 增强GeekOBD APP集成

### v1.0 (2025-07-31)
- ✅ 基础DTC页面生成功能
- ✅ 多线程并发处理
- ✅ 状态跟踪系统
