import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Tuple
from openai import OpenAI
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 线程锁用于TXT文件写入
txt_lock = threading.Lock()

def get_dtc_category(code):
    """根据DTC代码获取类别标识符"""
    if code.startswith('P'):
        return 'engine'
    elif code.startswith('B'):
        return 'body'
    elif code.startswith('C'):
        return 'chassis'
    elif code.startswith('U'):
        return 'network'
    else:
        return 'generic'

def get_dtc_category_name(code):
    """根据DTC代码获取类别名称"""
    if code.startswith('P'):
        return 'Engine'
    elif code.startswith('B'):
        return 'Body'
    elif code.startswith('C'):
        return 'Chassis'
    elif code.startswith('U'):
        return 'Network'
    else:
        return 'Generic'


class FixedDTCGenerator:
    def __init__(self, api_key: str, base_url: str = 'https://one.close-api.com/v1', model: str = 'gpt-4o-mini'):
        self.client = OpenAI(api_key=api_key, base_url=base_url)
        self.model = model
        self.txt_file = 'dtc_codes.txt'
        self.data_dir = 'data'
        self.html_dir = '../'
        
        # 确保目录存在
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 如果TXT文件不存在，创建一个示例文件
        if not os.path.exists(self.txt_file):
            with open(self.txt_file, 'w', encoding='utf-8') as f:
                f.write("# DTC代码列表 - 每行一个代码\n")
                f.write("# 完成后会自动添加完成时间\n")
                f.write("# 格式: 代码 [完成时间]\n\n")
                f.write("B0011\n")
                f.write("P0420\n")
                f.write("P0171\n")
        
    def load_txt_data(self) -> List[Dict]:
        """加载TXT数据"""
        codes = []
        if os.path.exists(self.txt_file):
            with open(self.txt_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                    
                    # 解析格式: "CODE [完成时间]" 或 "CODE"
                    if '[' in line and ']' in line:
                        # 已完成的代码
                        code = line.split('[')[0].strip()
                        completed_time = line.split('[')[1].split(']')[0].strip()
                        codes.append({
                            'code': code.upper(),
                            'completed': True,
                            'completed_time': completed_time,
                            'line_num': line_num
                        })
                    else:
                        # 未完成的代码
                        codes.append({
                            'code': line.upper(),
                            'completed': False,
                            'completed_time': None,
                            'line_num': line_num
                        })
        return codes
    
    def update_txt_status(self, code: str):
        """更新TXT文件状态"""
        with txt_lock:
            if not os.path.exists(self.txt_file):
                return
                
            # 读取所有行
            with open(self.txt_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 更新对应的行
            updated = False
            for i, line in enumerate(lines):
                line_stripped = line.strip()
                if line_stripped and not line_stripped.startswith('#'):
                    if line_stripped.upper() == code.upper():
                        # 添加完成时间
                        completion_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        lines[i] = f"{code.upper()} [{completion_time}]\n"
                        updated = True
                        break
            
            # 写回文件
            if updated:
                with open(self.txt_file, 'w', encoding='utf-8') as f:
                    f.writelines(lines)

    def generate_data_for_code(self, code: str) -> bool:
        """为单个代码生成数据"""
        try:
            logger.info(f"生成 {code} 的数据...")
            
            prompt = f"""
Generate comprehensive diagnostic data for DTC code {code} in JSON format.
The response should be a valid JSON object with the following structure:

{{
    "title": "DTC {code} - Specific descriptive title",
    "definition": "Clear explanation of what this code means",
    "quickAnswer": {{
        "meaning": "Brief explanation",
        "severity": "High/Medium/Low",
        "urgency": "Specific urgency advice"
    }},
    "symptoms": [
        "Symptom 1",
        "Symptom 2",
        "Symptom 3"
    ],
    "causes": [
        "Cause 1",
        "Cause 2", 
        "Cause 3"
    ],
    "diagnosticSteps": {{
        "steps": [
            "Step 1 description",
            "Step 2 description",
            "Step 3 description"
        ]
    }},
    "costAnalysis": {{
        "repairOptions": [
            {{
                "name": "Option 1 name",
                "description": "Description",
                "cost": "$XXX - $XXX",
                "successRate": "XX%",
                "parts": ["Part 1", "Part 2"]
            }}
        ]
    }},
    "aiQuestions": [
        {{
            "question": "What are the main differences between {code} and similar codes?",
            "answer": "Detailed comparison with related diagnostic codes and their specific characteristics"
        }},
        {{
            "question": "What are the most common causes of {code}?",
            "answer": "Comprehensive list of typical root causes and contributing factors"
        }},
        {{
            "question": "How serious is {code} and can I continue driving?",
            "answer": "Assessment of safety risks and driving recommendations with urgency level"
        }},
        {{
            "question": "How can GeekOBD APP help diagnose {code}?",
            "answer": "Specific GeekOBD APP features and diagnostic capabilities for this code"
        }}
    ],
    "caseStudies": [
        {{
            "title": "Real repair case title 1",
            "vehicle": "Specific vehicle year/make/model/mileage",
            "problem": "Specific customer complaint and symptoms",
            "diagnosis": "Detailed diagnostic process and findings",
            "solution": "Specific repair procedure and parts used",
            "cost": "$XXX (specific cost breakdown)",
            "result": "Repair outcome and follow-up results"
        }},
        {{
            "title": "Real repair case title 2", 
            "vehicle": "Different vehicle year/make/model/mileage",
            "problem": "Different customer complaint and symptoms",
            "diagnosis": "Different diagnostic approach and findings",
            "solution": "Different repair procedure and parts used", 
            "cost": "$XXX (specific cost breakdown)",
            "result": "Different repair outcome and follow-up"
        }}
    ],
    "relatedCodes": [
        {{
            "code": "PXXXX",
            "description": "Brief description of related code"
        }},
        {{
            "code": "BXXXX", 
            "description": "Brief description of another related code"
        }},
        {{
            "code": "CXXXX",
            "description": "Brief description of third related code"
        }}
    ]
}}

Please provide realistic, accurate automotive diagnostic information for {code}.
"""
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.7
            )
            
            # 解析JSON响应
            json_content = response.choices[0].message.content.strip()
            if json_content.startswith('```json'):
                json_content = json_content[7:-3]
            elif json_content.startswith('```'):
                json_content = json_content[3:-3]
            
            data = json.loads(json_content)
            
            # 保存数据文件
            data_file = os.path.join(self.data_dir, f'{code.lower()}-data.json')
            with open(data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ {code} 数据生成成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ {code} 数据生成失败: {e}")
            return False

    def generate_html_for_code(self, code: str) -> bool:
        """为单个代码生成HTML"""
        try:
            logger.info(f"生成 {code} 的HTML...")
            
            # 读取数据文件
            data_file = os.path.join(self.data_dir, f'{code.lower()}-data.json')
            if not os.path.exists(data_file):
                logger.error(f"数据文件不存在: {data_file}")
                return False
            
            with open(data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 生成HTML
            html_content = self.generate_html_template(code, data)
            
            # 保存HTML文件
            html_file = os.path.join(self.html_dir, f'{code.lower()}.html')
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"✅ {code} HTML生成成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ {code} HTML生成失败: {e}")
            return False

    def generate_html_template(self, code: str, data: Dict) -> str:
        """生成HTML模板（严格按照原版B0011.html的结构）"""
        title = data.get('title', f'DTC {code}')
        definition = data.get('definition', '')
        symptoms = data.get('symptoms', [])
        causes = data.get('causes', [])
        quick_answer = data.get('quickAnswer', {})
        ai_questions = data.get('aiQuestions', [])
        cost_analysis = data.get('costAnalysis', {})
        case_studies = data.get('caseStudies', [])
        related_codes = data.get('relatedCodes', [])
        diagnostic_steps = data.get('diagnosticSteps', {}).get('steps', [])

        # 生成症状HTML
        symptoms_html = ""
        for symptom in symptoms:
            symptoms_html += f"                            <li>{symptom}</li>\n"

        # 生成原因HTML
        causes_html = ""
        for cause in causes:
            causes_html += f"                            <li>{cause}</li>\n"

        # 生成诊断步骤HTML（专业卡片格式，优化移动端显示）
        diagnostic_html = ""
        if diagnostic_steps:
            step_icons = ["fa-line-chart", "fa-wrench", "fa-cog", "fa-link", "fa-check-circle"]
            for i, step in enumerate(diagnostic_steps):
                icon = step_icons[i % len(step_icons)]
                step_num = i + 1
                # 提取步骤标题（更简洁）
                step_title = step.split('.')[0] if '.' in step else step.split(':')[0] if ':' in step else step[:30]
                diagnostic_html += f'''        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 12px 0; border-left: 4px solid #4a90e2;">
            <h5 style="color: #4a90e2; margin-bottom: 10px; font-size: 16px; font-weight: 600;"><i class="fa {icon}" style="margin-right: 8px;"></i>Step {step_num}: {step_title}</h5>
            <p style="margin-bottom: 12px; color: #555; font-size: 14px; line-height: 1.5;">{step}</p>
            <div style="background: #e3f2fd; padding: 12px; border-radius: 6px; margin-top: 12px;">
                <strong style="color: #1976d2; font-size: 13px;"><i class="fa fa-mobile" style="margin-right: 6px;"></i>GeekOBD APP Tip:</strong>
                <span style="color: #666; font-size: 13px;">Use GeekOBD APP to monitor real-time data during this diagnostic step for accurate results and professional-grade analysis.</span>
            </div>
        </div>
'''

        # 生成维修成本HTML
        repair_html = ""
        if cost_analysis:
            repair_options = cost_analysis.get('repairOptions', [])
            for option in repair_options:
                parts_html = ""
                for part in option.get('parts', []):
                    parts_html += f"<li>{part}</li>"

                # 选择颜色主题
                colors = [
                    {"color": "#4CAF50", "icon": "fa-wrench"},
                    {"color": "#FF9800", "icon": "fa-cog"},
                    {"color": "#2196F3", "icon": "fa-refresh"}
                ]
                color_theme = colors[len(repair_html.split('<div style="background: white;')) % len(colors)]

                repair_html += f"""
        <div style="background: white; padding: 15px; border-radius: 8px; border-left: 4px solid {color_theme['color']};">
            <h5 style="color: {color_theme['color']}; margin-bottom: 8px; font-size: 14px; font-weight: 600; word-wrap: break-word; word-break: break-word; overflow-wrap: break-word; line-height: 1.2; max-width: 100%;"><i class="fa {color_theme['icon']}" style="margin-right: 6px;"></i>{option.get('name', 'Repair Option')}</h5>
            <p style="margin-bottom: 12px; color: #666; font-size: 14px;">{option.get('description', '')}</p>
            <ul style="list-style: none; padding: 0;">
                <li style="margin-bottom: 6px; font-size: 14px;"><strong>Total:</strong> <span style="color: {color_theme['color']}; font-weight: bold;">{option.get('cost', 'N/A')}</span></li>
                <li style="color: #666; font-size: 13px;">Success rate: {option.get('successRate', 'N/A')}</li>
            </ul>
        </div>
        """

        # 生成案例研究HTML
        case_studies_html = ""
        for i, case in enumerate(case_studies, 1):
            case_studies_html += f"""
                    <div class="case-study" style="background: #f8f9fa; padding: 18px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #667eea;">
                        <h5 style="font-size: 13px; font-weight: 600; margin-bottom: 12px; color: #333; word-wrap: break-word; word-break: break-word; overflow-wrap: break-word; line-height: 1.2; max-width: 100%;"><i class="fa fa-file-text" style="margin-right: 6px;"></i>Case Study {i}: {case.get('title', 'Repair Case')}</h5>
                        <p style="font-size: 14px; margin-bottom: 8px; color: #555;"><strong>Vehicle:</strong> {case.get('vehicle', 'N/A')}</p>
                        <p style="font-size: 14px; margin-bottom: 8px; color: #555;"><strong>Problem:</strong> {case.get('problem', 'N/A')}</p>
                        <p style="font-size: 14px; margin-bottom: 8px; color: #555;"><strong>Diagnosis:</strong> {case.get('diagnosis', 'N/A')}</p>
                        <p style="font-size: 14px; margin-bottom: 8px; color: #555;"><strong>Solution:</strong> {case.get('solution', 'N/A')}</p>
                        <p style="font-size: 14px; margin-bottom: 8px; color: #555;"><strong>Cost:</strong> {case.get('cost', 'N/A')}</p>
                        <p style="font-size: 14px; margin-bottom: 8px; color: #555;"><strong>Result:</strong> {case.get('result', 'N/A')}</p>
                    </div>"""

        # 生成相关代码HTML（侧边栏样式）
        related_codes_html = ""
        for related in related_codes:
            code_name = related.get('code', '')
            description = related.get('description', '')
            related_codes_html += f'''        <a href="{code_name.lower()}.html" style="display: block; padding: 10px 15px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #4a90e2; transition: all 0.3s;">
            <strong style="color: #333; font-size: 14px;">{code_name}</strong>
            <span style="display: block; color: #666; font-size: 12px; margin-top: 3px;">{description}</span>
        </a>
'''

        # 生成常见问题HTML和Schema
        faq_html = ""
        faq_schema_items = []
        for qa in ai_questions:
            question = qa.get('question', '')
            answer = qa.get('answer', '')
            faq_html += f"""
                    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
                        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">{question}</h3>
                        <p style="color: #666; line-height: 1.6;">{answer}</p>
                    </div>"""

            # 添加到FAQ Schema
            faq_schema_items.append(f'''{{
      "@type": "Question",
      "name": "{question}",
      "acceptedAnswer": {{
        "@type": "Answer",
        "text": "{answer}"
      }}
    }}''')

        faq_schema = ',\n    '.join(faq_schema_items)

        # 预计算成本信息
        default_cost = '$150-$500'
        if cost_analysis and cost_analysis.get('repairOptions'):
            first_option = cost_analysis['repairOptions'][0]
            repair_cost = first_option.get('cost', default_cost)
        else:
            repair_cost = default_cost

        # 生成系统信息
        if code.startswith('P'):
            system_info = f"{code[0]} - Powertrain (Engine, Transmission, Emissions)"
        elif code.startswith('B'):
            system_info = f"{code[0]} - Body (Airbags, Lighting, Security, Climate Control)"
        elif code.startswith('C'):
            system_info = f"{code[0]} - Chassis (ABS, Steering, Suspension)"
        elif code.startswith('U'):
            system_info = f"{code[0]} - Network (Communication, CAN Bus)"
        else:
            system_info = f"{code[0] if code else 'Engine'} - Engine System"

        # 生成HowTo步骤Schema
        howto_steps = []
        if diagnostic_steps:
            for i, step in enumerate(diagnostic_steps):
                step_name = step.split('.')[0] if '.' in step else step[:30]
                step_text = step.replace('"', "'")  # 替换双引号为单引号
                howto_steps.append(f'''    {{
      "@type": "HowToStep",
      "position": {i + 1},
      "name": "Step {i + 1}: {step_name}...",
      "text": "{step_text}"
    }}''')
        else:
            # 默认步骤
            howto_steps = [
                '''    {
      "@type": "HowToStep",
      "position": 1,
      "name": "Scan and Monitor Data",
      "text": "Connect GeekOBD APP and scan for diagnostic codes. Monitor live data to understand the specific conditions."
    }''',
                '''    {
      "@type": "HowToStep",
      "position": 2,
      "name": "Inspect Components",
      "text": "Visually inspect related components and wiring for obvious damage, corrosion, or loose connections."
    }''',
                '''    {
      "@type": "HowToStep",
      "position": 3,
      "name": "Test System Function",
      "text": "Use GeekOBD APP to test system operation and compare readings with manufacturer specifications."
    }''',
                '''    {
      "@type": "HowToStep",
      "position": 4,
      "name": "Perform Repairs",
      "text": "Based on diagnostic results, perform necessary repairs such as cleaning, adjusting, or replacing components."
    }''',
                '''    {
      "@type": "HowToStep",
      "position": 5,
      "name": "Verify and Clear",
      "text": "Test drive vehicle and use GeekOBD APP to verify repair success. Clear codes and monitor for recurrence."
    }'''
            ]

        howto_steps_schema = ',\n'.join(howto_steps)

        html_template = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{code} - {title} | GeekOBD</title>
    <meta name="description" content="{definition[:150]}">
    <meta name="keywords" content="{code}, {title}, diagnostic trouble code, OBD2, car repair, engine problems, GeekOBD">
    <link rel="canonical" href="https://www.geekobd.com/dtc-codes/{code.lower()}.html">

    <!-- Open Graph -->
    <meta property="og:title" content="{code} - {title}">
    <meta property="og:description" content="{definition[:150]}">
    <meta property="og:url" content="https://www.geekobd.com/dtc-codes/{code.lower()}.html">
    <meta property="og:type" content="article">

    <!-- CSS -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../css/fonts/font-awesome/css/font-awesome.css">
    <link rel="stylesheet" href="../css/animations.css" media="screen">
    <link rel="stylesheet" href="../css/superfish.css" media="screen">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/colors/blue.css" id="colors">
    <link rel="stylesheet" href="../css/theme-responsive.css">
    <link rel="stylesheet" href="../css/seo-enhancements.css">
    <link rel="shortcut icon" href="../img/ico/favicon.ico">

    <style>
    .dtc-header {{
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 40px 0 30px;
    }}
    .dtc-code-badge {{
        display: inline-block;
        background: rgba(255,255,255,0.2);
        padding: 8px 16px;
        border-radius: 50px;
        font-size: 20px;
        font-weight: bold;
        margin-bottom: 15px;
    }}
    .severity-badge {{
        display: inline-block;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 11px;
        font-weight: bold;
        text-transform: uppercase;
        margin-left: 10px;
    }}
    .severity-high {{ background: #ff4757; color: white; }}
    .severity-medium {{ background: #ffa502; color: white; }}
    .content-section {{ padding: 30px 0; }}
    .info-box {{
        background: #f8f9fa;
        border-left: 4px solid #667eea;
        padding: 20px;
        margin: 20px 0;
        border-radius: 5px;
    }}
    .warning-box {{
        background: #fff3cd;
        border-left: 4px solid #ffc107;
        padding: 20px;
        margin: 20px 0;
        border-radius: 5px;
    }}
    .danger-box {{
        background: #f8d7da;
        border-left: 4px solid #dc3545;
        padding: 20px;
        margin: 20px 0;
        border-radius: 5px;
    }}
    .breadcrumb-custom {{
        background: none;
        padding: 20px 0;
        margin: 0;
    }}
    .breadcrumb-custom a {{
        color: #667eea;
        text-decoration: none;
    }}
    .breadcrumb-custom a:hover {{
        text-decoration: underline;
    }}

    /* 移动端优化 */
    @media (max-width: 768px) {{
        .dtc-header {{
            padding: 30px 0 20px;
        }}
        .dtc-code-badge {{
            font-size: 18px;
            padding: 6px 14px;
        }}
        .dtc-header h1 {{
            font-size: 24px !important;
            margin: 15px 0 !important;
        }}
        .dtc-header p {{
            font-size: 16px !important;
        }}
        .content-section {{
            padding: 20px 0;
        }}
        .severity-badge {{
            margin-left: 0;
            margin-top: 8px;
            display: block;
            width: fit-content;
        }}
        .container {{
            padding-left: 15px;
            padding-right: 15px;
        }}
        /* 修复移动端Grid布局超出问题 */
        .repair-cost-grid {{
            display: block !important;
        }}
        .repair-cost-grid > div {{
            margin-bottom: 15px;
            width: 100% !important;
        }}

        /* 修复移动端标题超出问题 */
        .repair-cost-grid h5 {{
            font-size: 16px !important;
            line-height: 1.3 !important;
            word-wrap: break-word !important;
            word-break: break-word !important;
            overflow-wrap: break-word !important;
            hyphens: auto !important;
            max-width: 100% !important;
        }}

        .repair-cost-grid div {{
            padding: 12px !important;
        }}

        /* 修复h4标题 - 使用最强的选择器覆盖所有外部样式 */
        html body div h4, html body .col-lg-4 h4, html body .col-md-4 h4,
        html body section h4, html body article h4, html body h4,
        .container h4, .row h4, .col-lg-4 .col-md-4 h4 {{
            font-size: 14px !important;
            line-height: 1.2 !important;
            word-wrap: break-word !important;
            word-break: break-word !important;
            overflow-wrap: break-word !important;
            hyphens: auto !important;
            max-width: calc(100vw - 60px) !important;
            white-space: normal !important;
        }}

        /* 强制覆盖所有H4标题 - 终极解决方案 */
        * h4 {{
            font-size: 14px !important;
        }}

        /* 修复h5标题 */
        h5 {{
            font-size: 14px !important;
            line-height: 1.2 !important;
            word-wrap: break-word !important;
            word-break: break-word !important;
            overflow-wrap: break-word !important;
            hyphens: auto !important;
            max-width: calc(100vw - 60px) !important;
            white-space: normal !important;
        }}

        /* 修复所有移动端长标题问题 - 使用更强的选择器覆盖内联样式 */
        div h5[style*="font-size"] {{
            font-size: 14px !important;
            line-height: 1.2 !important;
            word-wrap: break-word !important;
            word-break: break-word !important;
            overflow-wrap: break-word !important;
            hyphens: auto !important;
            max-width: calc(100vw - 60px) !important;
            white-space: normal !important;
        }}

        h5 {{
            font-size: 14px !important;
            line-height: 1.2 !important;
            word-wrap: break-word !important;
            word-break: break-word !important;
            overflow-wrap: break-word !important;
            hyphens: auto !important;
            max-width: calc(100vw - 60px) !important;
            white-space: normal !important;
        }}

        /* 修复案例研究标题 */
        .case-study h5 {{
            font-size: 13px !important;
            line-height: 1.1 !important;
        }}

        /* 确保所有容器不超出屏幕 */
        .container, .repair-cost-grid, .repair-cost-grid > div {{
            max-width: 100% !important;
            box-sizing: border-box !important;
            overflow-wrap: break-word !important;
        }}

        /* 强制所有文本元素换行 */
        p, span, div, h1, h2, h3, h4, h5, h6 {{
            word-wrap: break-word !important;
            word-break: break-word !important;
            overflow-wrap: break-word !important;
            max-width: 100% !important;
        }}
    }}

    /* 强制覆盖所有H4标题 - 终极解决方案 */
    html body * h4,
    html body h4,
    html body div h4,
    html body .container h4,
    html body .row h4,
    html body .col-lg-4 h4,
    html body .col-md-4 h4,
    html body .col-lg-8 h4,
    html body .col-md-8 h4 {{
        font-size: 18px !important;
    }}

    /* 针对特定H4标题的强制覆盖 */
    html body h4[style*="color: white"],
    html body h4[style*="margin-bottom"] {{
        font-size: 18px !important;
    }}
    </style>


<!-- Article Schema -->
<script type="application/ld+json">
{{
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": "{code} - {title}",
  "description": "{definition[:150]}",
  "author": {{
    "@type": "Organization",
    "name": "GeekOBD",
    "url": "https://www.geekobd.com"
  }},
  "publisher": {{
    "@type": "Organization",
    "name": "GeekOBD",
    "logo": {{
      "@type": "ImageObject",
      "url": "https://www.geekobd.com/images/logo.png"
    }}
  }},
  "datePublished": "2025-08-01",
  "dateModified": "2025-08-01",
  "mainEntityOfPage": {{
    "@type": "WebPage",
    "@id": "https://www.geekobd.com/dtc-codes/{code.lower()}.html"
  }}
}}
</script>

<!-- FAQ Schema -->
<script type="application/ld+json">
{{
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {faq_schema}
  ]
}}
</script>

<!-- HowTo Schema -->
<script type="application/ld+json">
{{
  "@context": "https://schema.org",
  "@type": "HowTo",
  "name": "How to Diagnose {code} {title}",
  "description": "Step-by-step guide to diagnose and fix {code}",
  "totalTime": "PT120M",
  "estimatedCost": {{
    "@type": "MonetaryAmount",
    "currency": "USD",
    "value": "{repair_cost} for most {code} repairs"
  }},
  "tool": [
    {{
      "@type": "HowToTool",
      "name": "GeekOBD APP with MOBD Adapter",
      "description": "Professional OBD2 diagnostic tool",
      "url": "https://www.geekobd.com/app.html"
    }}
  ],
  "step": [
{howto_steps_schema}
  ]
}}
</script>
</head>

<body class="page">
    <div class="wrap">
	<header id="header" role="banner">
	<div class="main-header">
	<div class="container">
	<div class="row">
	<div class="col-md-3">
	<div class="logo pull-left">
	<h1> <a href="../index.html"> <img src="../img/logo.png" alt="MOBD"> </a> </h1>
	</div>
	</div>
	<div class="col-md-9">
	<div class="mobile navbar-header"> <a class="navbar-toggle" data-toggle="collapse" data-target=".menu"> <i class="icon-reorder icon-2x"></i> </a> </div>
	<nav class="collapse navbar-collapse menu" role="navigation" aria-label="Main navigation">
	<ul class="nav navbar-nav sf-menu">
	<li><a href="../index.html" class="sf-with-ul">Home</a></li>
	<li><a href="../app.html" class="sf-with-ul">APP</a></li>
	<li><a href="javascript:;;" class="sf-with-ul">Adaptor <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
	<ul>
	<li><a href="../hardware2.html" class="sf-with-ul">MOBD GPS</a></li>
	<li><a href="../hardware.html" class="sf-with-ul">MOBD</a></li>
	</ul>
	</li>
	<li><a href="javascript:;;" class="sf-with-ul">Resources <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
	<ul>
	<li><a href="../dtc-codes.html" class="sf-with-ul">DTC Codes</a></li>
	<li><a href="../obd-diagnostic-guide.html" class="sf-with-ul">OBD Guide</a></li>
	<li><a href="../vehicle-compatibility.html" class="sf-with-ul">Compatibility</a></li>
	<li><a href="../fuel-efficiency-monitoring.html" class="sf-with-ul">Fuel Efficiency</a></li>
	<li><a href="../support.html" class="sf-with-ul">Support</a></li>
	<li><a href="../blog.html" class="sf-with-ul">Blog</a></li>
	</ul>
	</li>
	<li><a href="../about.html" class="sf-with-ul">About Us</a></li>
	<li><a href="//www.mobd.cn" class="sf-with-ul" target="_blank">中文版</a></li>
	</ul>
	</nav>
	</div>
	</div>
	</div>
	</div>
	</header>

    <!-- Breadcrumb -->
    <div class="container">
        <nav class="breadcrumb-custom">
            <a href="../index.html">Home</a> &raquo;
            <a href="../dtc-codes.html">DTC Codes</a> &raquo;
            <a href="{get_dtc_category(code)}/">{get_dtc_category_name(code)} Codes</a> &raquo;
            <span>{code}</span>
        </nav>
    </div>



<!-- Content Start -->
<main id="main" role="main">
<!-- Title, Breadcrumb Start-->
<section class="breadcrumb-wrapper">
<div class="container" style="min-height:86px">
<div class="row">
<div class="col-lg-12 col-md-12 col-xs-12 col-sm-12">
<h1 class="title">{code} - {title.replace(f'DTC {code} - ', '')}</h1>
</div>
</div>
</div>
</section>
<!-- Title, Breadcrumb End-->

<!-- Main Content start-->
<section class="content">
<div class="container">
<div class="row">
<div class="col-lg-8 col-md-8 col-xs-12 col-sm-12">
<article>
<div class="post-content">

<!-- Quick Answer Section for AI -->
<div id="quick-answer" style="background: #e8f4fd; border: 2px solid #4a90e2; border-radius: 10px; padding: 25px; margin-bottom: 30px;">
    <h2 style="color: #2c5aa0; margin-bottom: 15px;"><i class="fa fa-clock-o"></i> Quick Answer</h2>
    <p style="font-size: 18px; font-weight: 500; margin-bottom: 15px; color: #333;">
        <strong>{code} means:</strong> {quick_answer.get('meaning', definition)}
    </p>
    <div style="display: flex; flex-wrap: wrap; gap: 15px; margin-bottom: 15px;">
        <span style="background: #4CAF50; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
            <i class="fa fa-wrench"></i> Fix: {quick_answer.get('fix', 'Check and replace faulty components')}
        </span>
        <span style="background: #FF9800; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
            <i class="fa fa-dollar"></i> Cost: {repair_cost}
        </span>
        <span style="background: #2196F3; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
            <i class="fa fa-clock-o"></i> Time: 90-180 minutes
        </span>
    </div>
    <p style="margin: 0; color: #666; font-size: 14px;">
        <strong>Can I drive with {code}?</strong> {quick_answer.get('urgency', 'Address promptly to avoid safety issues')}
    </p>
</div>

<!-- AI-Friendly Q&A Section -->
<div id="ai-qa" style="background: white; border-radius: 10px; padding: 25px; margin-bottom: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
    <h2><i class="fa fa-comments"></i> Common Questions</h2>
    {faq_html}
</div>

<!-- Technical Overview -->
<div style="background: white; border-radius: 10px; padding: 25px; margin-bottom: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
    <h2><i class="fa fa-info-circle"></i> What is {code}?</h2>
    <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">{definition}</p>
    <div class="info-box">
        <p style="margin: 0;"><strong>System:</strong> {system_info}</p>
    </div>
</div>

<!-- Symptoms -->
<div id="symptoms" style="background: white; border-radius: 10px; padding: 25px; margin-bottom: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
    <h2><i class="fa fa-exclamation-triangle"></i> Symptoms</h2>
    <p style="color: #666; margin-bottom: 20px;">Common symptoms when {code} is present:</p>
    <ul style="color: #333; line-height: 1.8;">
{symptoms_html}
    </ul>
</div>

<!-- Possible Causes -->
<div id="causes" style="background: white; border-radius: 10px; padding: 25px; margin-bottom: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
    <h2><i class="fa fa-search"></i> Possible Causes</h2>
    <p style="color: #666; margin-bottom: 20px;">Most common causes of {code} (ordered by frequency):</p>
    <ol style="color: #333; line-height: 1.8;">
{causes_html}
    </ol>
</div>

<!-- Repair Cost Information -->
<div id="cost-info" style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
    <h2><i class="fa fa-calculator"></i> {code} Repair Costs</h2>

    <div style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin: 20px 0;">
        <h4 style="color: #2c3e50; margin-bottom: 15px; font-size: 18px; font-weight: 600;">Cost Breakdown by Repair Type</h4>

        <div class="repair-cost-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 15px; margin-bottom: 25px;">
            {repair_html}
        </div>

        <!-- Money-Saving Tips -->
        <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; border-left: 4px solid #2196F3;">
            <h5 style="color: #1976D2; margin-bottom: 12px; font-size: 14px; font-weight: 600; word-wrap: break-word; word-break: break-word; overflow-wrap: break-word; line-height: 1.2; max-width: 100%;"><i class="fa fa-lightbulb-o" style="margin-right: 6px;"></i>Money-Saving Tips for {code}</h5>
            <ul style="margin: 0; color: #333;">
                <li style="margin-bottom: 10px;">Start with the most common and least expensive repairs first</li>
                <li style="margin-bottom: 10px;">Use GeekOBD APP to confirm diagnosis before replacing expensive parts</li>
                <li style="margin-bottom: 10px;">Consider preventive maintenance to avoid future occurrences</li>
                <li style="margin-bottom: 10px;">Compare prices for OEM vs aftermarket parts based on your needs</li>
                <li>Address the issue promptly to prevent more expensive secondary damage</li>
            </ul>
        </div>
    </div>
</div>

<!-- Diagnostic Steps Section -->
<div id="diagnostic-steps" style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
    <h2><i class="fa fa-stethoscope"></i> Diagnostic Steps</h2>

    <div style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin: 20px 0;">
        <h3 style="color: #2c3e50; margin-bottom: 20px;">Professional {code} Diagnosis Process</h3>
        <p style="margin-bottom: 20px; color: #666;">Follow these systematic steps to accurately diagnose {code}. Each step builds on the previous one to ensure accurate diagnosis.</p>

        {diagnostic_html}

        <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin-top: 20px; border-left: 4px solid #ffc107;">
            <h4 style="color: #856404; margin-bottom: 15px; font-size: 18px;"><i class="fa fa-exclamation-triangle"></i> Important Notes</h4>
            <ul style="margin: 0; color: #856404;">
                <li style="margin-bottom: 8px;">Always verify the repair with GeekOBD APP after completing diagnostic steps</li>
                <li style="margin-bottom: 8px;">Clear codes and test drive to ensure the problem is resolved</li>
                <li style="margin-bottom: 8px;">Address underlying causes to prevent code recurrence</li>
            </ul>
        </div>
    </div>
</div>

<!-- Case Studies -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
    <h3 style="font-size: 20px; font-weight: 600; margin-bottom: 20px; color: #2c3e50;"><i class="fa fa-file-text" style="margin-right: 8px;"></i>Real Repair Case Studies</h3>
    {case_studies_html}
</div>

</div>
</article>
</div>

<div class="col-lg-4 col-md-4 col-xs-12 col-sm-12">

<!-- GeekOBD APP Promotion -->
<div style="background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%); color: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: white; font-size: 18px;"><i class="fa fa-mobile"></i> Diagnose {code}</h4>
    <p style="margin-bottom: 20px; opacity: 0.9; font-size: 14px;">Use GeekOBD APP for professional diagnosis!</p>
    <ul style="margin-bottom: 20px; padding-left: 20px; font-size: 14px;">
        <li style="margin-bottom: 8px;">Real-time data monitoring</li>
        <li style="margin-bottom: 8px;">Advanced diagnostic features</li>
        <li style="margin-bottom: 8px;">Step-by-step repair guidance</li>
        <li style="margin-bottom: 8px;">Professional-grade analysis</li>
    </ul>
    <a href="../app.html" style="display: inline-block; background: rgba(255,255,255,0.2); color: white; padding: 12px 25px; border-radius: 25px; text-decoration: none; font-weight: bold; transition: all 0.3s;">
        <i class="fa fa-download"></i> Download GeekOBD APP
    </a>
</div>

<!-- Code Information -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; font-size: 18px;"><i class="fa fa-info"></i> Code Information</h4>
    <table class="table table-borderless">
        <tr>
            <td><strong>Code:</strong></td>
            <td>{code}</td>
        </tr>
        <tr>
            <td><strong>System:</strong></td>
            <td>{system_info.split(' - ')[1] if ' - ' in system_info else system_info}</td>
        </tr>
        <tr>
            <td><strong>Severity:</strong></td>
            <td><span class="severity-badge severity-{quick_answer.get('severity', 'medium').lower()}">{quick_answer.get('severity', 'MEDIUM').upper()}</span></td>
        </tr>
        <tr>
            <td><strong>Category:</strong></td>
            <td>{'Body Codes' if code.startswith('B') else 'Engine Codes' if code.startswith('P') else 'Chassis Codes' if code.startswith('C') else 'Network Codes' if code.startswith('U') else 'Engine Codes'}</td>
        </tr>
    </table>
</div>

<!-- Related System Codes -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: #333; font-size: 18px;"><i class="fa fa-link"></i> Related {code[0] if code else 'System'} Codes</h4>
    <p style="margin-bottom: 15px; color: #666; font-size: 14px;">Related diagnostic trouble codes:</p>
    <div style="margin-bottom: 15px;">
        {related_codes_html}
    </div>
</div>

<!-- Diagnostic Resources -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: #333; font-size: 18px;"><i class="fa fa-tools"></i> Diagnostic Resources</h4>
    <div style="margin-bottom: 20px;">

        <a href="#diagnostic-steps" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-wrench" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">Diagnostic Procedures</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">Professional diagnostic steps for {code}</span>
        </a>
        <a href="../resources/obd2-diagnostic-guide.html" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-book" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">OBD2 Diagnostic Guide</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">Complete guide to OBD2 diagnostics</span>
        </a>
        <a href="../resources/repair-procedures.html" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-cogs" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">Repair Procedures</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">Step-by-step repair instructions</span>
        </a>
        <a href="../resources/troubleshooting-guide.html" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-search" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">Troubleshooting Guide</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">Advanced troubleshooting techniques</span>
        </a>
    </div>
</div>

<!-- Quick Navigation -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: #333; font-size: 18px;"><i class="fa fa-compass"></i> Quick Navigation</h4>
    <div style="display: flex; flex-direction: column; gap: 8px;">
        <a href="#quick-answer" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-bolt"></i> Quick Answer
        </a>
        <a href="#ai-qa" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-comments"></i> Common Questions
        </a>
        <a href="#symptoms" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-exclamation-triangle"></i> Symptoms
        </a>
        <a href="#causes" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-search"></i> Causes
        </a>
        <a href="#cost-info" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-calculator"></i> Repair Costs
        </a>
        <a href="#diagnostic-steps" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-wrench"></i> Diagnostic Steps
        </a>
    </div>
</div>

</div>
</div>
</div>
</section>
<!-- Main Content end-->
</main>
<!-- Content End -->
<!-- Footer Start -->
<footer id="footer" role="contentinfo">
<div class="footer-bottom">
<div class="container">
<div class="row">
<div class="col-lg-6 col-md-6 col-xs-12 col-sm-6">
Copyright &copy; 2005~<script type="text/javascript">var d = new Date();document.write(d.getUTCFullYear());</script> www.geekobd.com All Right Reserved. <a href='http://beian.miit.gov.cn' target='_blank'>京ICP备09047462号-6</a>
</div>
<div class="col-lg-6 col-md-6 col-xs-12 col-sm-6"></div>
</div>
</div>
</div>
</footer>

<!-- Scroll To Top -->
<a href="#" class="scrollup"><i class="icon-angle-up"></i></a>
</div>

<script src="../js/jquery.scrollTo-1.4.2-min.js"></script>
<script src="../js/jquery.validate.min.js"></script>
<script src="../js/jquery.easing.min.js"></script>
<script src="../js/custom.js"></script>

</body>
</html>"""

        return html_template

    def process_single_code(self, code_info: Dict) -> Tuple[str, bool, bool]:
        """处理单个代码（数据+HTML生成）"""
        code = code_info['code']
        completed = code_info['completed']

        # 如果已完成，跳过
        if completed:
            return code, True, True

        # 生成数据
        data_success = self.generate_data_for_code(code)
        if not data_success:
            return code, False, False

        # 生成HTML
        html_success = self.generate_html_for_code(code)
        if not html_success:
            return code, True, False

        # 两个都成功才标记完成
        self.update_txt_status(code)
        return code, True, True

    def run_batch_generation(self, max_workers: int = 3):
        """批量并发生成"""
        codes = self.load_txt_data()

        # 筛选需要处理的代码
        pending_codes = []
        for code_info in codes:
            if not code_info['completed']:
                pending_codes.append(code_info)

        if not pending_codes:
            logger.info("🎉 所有代码都已完成！")
            return

        logger.info(f"📋 发现 {len(pending_codes)} 个待处理代码")
        logger.info(f"🚀 开始并发生成，并发数: {max_workers}")

        # 并发处理
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_code = {
                executor.submit(self.process_single_code, code_info): code_info['code']
                for code_info in pending_codes
            }

            completed_count = 0
            total_count = len(pending_codes)

            # 处理完成的任务
            for future in as_completed(future_to_code):
                code = future_to_code[future]
                try:
                    _, data_success, html_success = future.result()
                    completed_count += 1

                    if data_success and html_success:
                        logger.info(f"✅ {code} 处理完成 ({completed_count}/{total_count})")
                    else:
                        logger.error(f"❌ {code} 处理失败 ({completed_count}/{total_count})")

                except Exception as e:
                    completed_count += 1
                    logger.error(f"❌ {code} 处理异常: {e} ({completed_count}/{total_count})")

        logger.info("🎉 批量生成完成！")


def main():
    """主程序入口"""
    import argparse

    parser = argparse.ArgumentParser(description='修复版DTC生成器 - 严格按照原版样式')
    parser.add_argument('--api-key', default='sk-RATxerbQFyyoSV4P49AbEbF53dBd486891C06979B18e3668', help='OpenAI API Key')
    parser.add_argument('--base-url', default='https://one.close-api.com/v1', help='API base URL')
    parser.add_argument('--model', default='gpt-4o-mini', help='Model to use')
    parser.add_argument('--workers', type=int, default=1, help='并发数量 (默认: 1, 推荐1-3)')

    args = parser.parse_args()

    # 创建生成器并运行
    generator = FixedDTCGenerator(
        api_key=args.api_key,
        base_url=args.base_url,
        model=args.model
    )

    generator.run_batch_generation(max_workers=args.workers)


if __name__ == '__main__':
    main()
