<!DOCTYPE html>
<!--[if IE 8]><html class="ie ie8"> <![endif]-->
<!--[if IE 9]><html class="ie ie9"> <![endif]-->
<!--[if gt IE 9]><!-->
<html>
<!--<![endif]-->

<head>
<meta charset="utf-8">
<title>Body System Codes (B0XXX) | GeekOBD DTC Database</title>
<meta name="description" content="Complete list of body system diagnostic trouble codes (B0XXX). Browse codes for airbags, lighting, climate control, and comfort systems with detailed explanations and repair solutions.">
<meta name="keywords" content="B0XXX codes, body diagnostic codes, airbag codes, lighting codes, climate control codes, body trouble codes, OBD body codes">
<meta name="author" content="Beijing MentalRoad Technology Co., Ltd.">
<meta name="robots" content="index, follow">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
<link rel="canonical" href="https://www.geekobd.com/dtc-codes/body/">

<link rel="stylesheet" href="../../css/bootstrap.css">
<link rel="stylesheet" href="../../css/fonts/font-awesome/css/font-awesome.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<link rel="stylesheet" href="../../css/animations.css" media="screen">
<link rel="stylesheet" href="../../css/superfish.css" media="screen">
<link rel="stylesheet" href="../../css/style.css">
<link rel="stylesheet" href="../../css/colors/blue.css" id="colors">
<link rel="stylesheet" href="../../css/theme-responsive.css">
<link rel="stylesheet" href="../../css/seo-enhancements.css">
<link rel="shortcut icon" href="../../img/ico/favicon.ico">
</head>

<body>
<div class="wrap">
<header id="header" role="banner">
<div class="main-header">
<div class="container">
<div class="row">
<div class="col-md-3">
<div class="logo pull-left">
<h1> <a href="../../index.html"> <img src="../../img/logo.png" alt="MOBD"> </a> </h1>
</div>
</div>
<div class="col-md-9">
<div class="mobile navbar-header"> <a class="navbar-toggle" data-toggle="collapse" data-target=".menu"> <i class="icon-reorder icon-2x"></i> </a> </div>
<nav class="collapse navbar-collapse menu" role="navigation" aria-label="Main navigation">
<ul class="nav navbar-nav sf-menu">
<li><a href="../../index.html" class="sf-with-ul">Home</a></li>
<li><a href="../../app.html" class="sf-with-ul">APP</a></li>
<li><a href="javascript:;;" class="sf-with-ul">Adaptor <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
<ul>
<li><a href="../../hardware2.html" class="sf-with-ul">MOBD GPS</a></li>
<li><a href="../../hardware.html" class="sf-with-ul">MOBD</a></li>
</ul>
</li>
<li><a href="javascript:;;" class="sf-with-ul">Resources <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
<ul>
<li><a href="../../dtc-codes.html" class="sf-with-ul" id="current">DTC Codes</a></li>
<li><a href="../../obd-diagnostic-guide.html" class="sf-with-ul">OBD Guide</a></li>
<li><a href="../../vehicle-compatibility.html" class="sf-with-ul">Compatibility</a></li>
<li><a href="../../fuel-efficiency-monitoring.html" class="sf-with-ul">Fuel Efficiency</a></li>
<li><a href="../../support.html" class="sf-with-ul">Support</a></li>
<li><a href="../../blog.html" class="sf-with-ul">Blog</a></li>
</ul>
</li>
<li><a href="../../about.html" class="sf-with-ul">About Us</a></li>
<li><a href="//www.mobd.cn" class="sf-with-ul" target="_blank">中文版</a></li>
</ul>
</nav>
</div>
</div>
</div>
</div>
</header>

<!-- Content Start -->
<main id="main" role="main">
<!-- Title, Breadcrumb Start-->
<section class="breadcrumb-wrapper">
<div class="container" style="min-height:86px">
<div class="row">
<div class="col-lg-12 col-md-12 col-xs-12 col-sm-12">
<h1 class="title">Body System Codes (B0XXX)</h1>
<nav aria-label="breadcrumb">
<ol class="breadcrumb" style="background: transparent; padding: 0; margin: 10px 0;">
<li class="breadcrumb-item" style="color: #666;"><a href="../../index.html" style="color: #007bff; text-decoration: none;">Home</a></li>
<li class="breadcrumb-item" style="color: #666;"><a href="../../dtc-codes.html" style="color: #007bff; text-decoration: none;">DTC Codes</a></li>
<li class="breadcrumb-item active" aria-current="page" style="color: #333;">Body Codes</li>
</ol>
</nav>
</div>
</div>
</div>
</section>
<!-- Title, Breadcrumb End-->

<!-- Main Content start-->
<section class="content">
<div class="container">
<div class="row">
<div class="posts-block col-md-8 col-sm-6 col-xs-12">
<article>
<div class="post-content">
<div style="background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 30px;">
<div style="display: flex; align-items: center; margin-bottom: 20px;">
<div style="width: 60px; height: 60px; background: #dc3545; border-radius: 50%; margin-right: 20px; display: flex; align-items: center; justify-content: center; flex-shrink: 0; box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3); transition: all 0.3s ease;" onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 6px 20px rgba(220, 53, 69, 0.4)'" onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 12px rgba(220, 53, 69, 0.3)'">
<i class="fa fa-car" style="font-size: 1.5em; color: white;"></i>
</div>
<div>
<h2 style="color: #333; margin: 0;">Body System Codes (B0XXX)</h2>
<p style="color: #666; margin: 5px 0 0 0;">Complete list of body system diagnostic trouble codes (B0XXX). Browse codes for airbags, lighting, climate control, and comfort systems.</p>
</div>
</div>
</div>

<h3 style="color: #333; margin-bottom: 20px;">Popular Body System Codes</h3>
<div class="codes-list">

<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 20px; margin-bottom: 15px; transition: all 0.3s ease;" onmouseover="this.style.borderColor='#dc3545'; this.style.boxShadow='0 2px 10px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'">
<div style="display: flex; align-items: center;">
<div style="background: #dc3545; color: white; padding: 8px 12px; border-radius: 4px; font-weight: bold; margin-right: 15px; min-width: 70px; text-align: center; font-size: 14px;">B0001</div>
<div style="flex: 1;">
<h4 style="color: #333; margin: 0 0 5px 0; font-size: 16px;">Driver Airbag Circuit</h4>
<p style="color: #666; margin: 0; font-size: 14px;">Driver Airbag Circuit malfunction</p>
</div>
<div><a href="../b0001.html" class="btn btn-danger btn-sm">View Details</a></div>
</div>
</div>

<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 20px; margin-bottom: 15px; transition: all 0.3s ease;" onmouseover="this.style.borderColor='#dc3545'; this.style.boxShadow='0 2px 10px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'">
<div style="display: flex; align-items: center;">
<div style="background: #dc3545; color: white; padding: 8px 12px; border-radius: 4px; font-weight: bold; margin-right: 15px; min-width: 70px; text-align: center; font-size: 14px;">B0051</div>
<div style="flex: 1;">
<h4 style="color: #333; margin: 0 0 5px 0; font-size: 16px;">Left Front Impact Sensor</h4>
<p style="color: #666; margin: 0; font-size: 14px;">Left Front Impact Sensor Circuit malfunction</p>
</div>
<div><a href="../b0051.html" class="btn btn-danger btn-sm">View Details</a></div>
</div>
</div>

<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 20px; margin-bottom: 15px; transition: all 0.3s ease;" onmouseover="this.style.borderColor='#dc3545'; this.style.boxShadow='0 2px 10px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'">
<div style="display: flex; align-items: center;">
<div style="background: #dc3545; color: white; padding: 8px 12px; border-radius: 4px; font-weight: bold; margin-right: 15px; min-width: 70px; text-align: center; font-size: 14px;">B1000</div>
<div style="flex: 1;">
<h4 style="color: #333; margin: 0 0 5px 0; font-size: 16px;">A/C Compressor Clutch Circuit</h4>
<p style="color: #666; margin: 0; font-size: 14px;">A/C Compressor Clutch Circuit malfunction</p>
</div>
<div><a href="../b1000.html" class="btn btn-danger btn-sm">View Details</a></div>
</div>
</div>

<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 20px; margin-bottom: 15px; transition: all 0.3s ease;" onmouseover="this.style.borderColor='#dc3545'; this.style.boxShadow='0 2px 10px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'">
<div style="display: flex; align-items: center;">
<div style="background: #dc3545; color: white; padding: 8px 12px; border-radius: 4px; font-weight: bold; margin-right: 15px; min-width: 70px; text-align: center; font-size: 14px;">B1001</div>
<div style="flex: 1;">
<h4 style="color: #333; margin: 0 0 5px 0; font-size: 16px;">A/C Compressor Clutch Relay</h4>
<p style="color: #666; margin: 0; font-size: 14px;">A/C Compressor Clutch Relay Circuit malfunction</p>
</div>
<div><a href="../b1001.html" class="btn btn-danger btn-sm">View Details</a></div>
</div>
</div>

<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 20px; margin-bottom: 15px; transition: all 0.3s ease;" onmouseover="this.style.borderColor='#dc3545'; this.style.boxShadow='0 2px 10px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'">
<div style="display: flex; align-items: center;">
<div style="background: #dc3545; color: white; padding: 8px 12px; border-radius: 4px; font-weight: bold; margin-right: 15px; min-width: 70px; text-align: center; font-size: 14px;">B2001</div>
<div style="flex: 1;">
<h4 style="color: #333; margin: 0 0 5px 0; font-size: 16px;">Headlight Low Beam Left</h4>
<p style="color: #666; margin: 0; font-size: 14px;">Headlight Low Beam Left Circuit malfunction</p>
</div>
<div><a href="../b2001.html" class="btn btn-danger btn-sm">View Details</a></div>
</div>
</div>

<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 20px; margin-bottom: 15px; transition: all 0.3s ease;" onmouseover="this.style.borderColor='#dc3545'; this.style.boxShadow='0 2px 10px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'">
<div style="display: flex; align-items: center;">
<div style="background: #dc3545; color: white; padding: 8px 12px; border-radius: 4px; font-weight: bold; margin-right: 15px; min-width: 70px; text-align: center; font-size: 14px;">B3001</div>
<div style="flex: 1;">
<h4 style="color: #333; margin: 0 0 5px 0; font-size: 16px;">Power Window Driver</h4>
<p style="color: #666; margin: 0; font-size: 14px;">Power Window Driver Circuit malfunction</p>
</div>
<div><a href="../b3001.html" class="btn btn-danger btn-sm">View Details</a></div>
</div>
</div>

</div>

<h3 style="color: #333; margin: 40px 0 20px 0;">Complete Body System Codes Index</h3>
<p style="color: #666; margin-bottom: 20px;">Browse all available body system diagnostic trouble codes (B0XXX) organized by system:</p>

<!-- B0000-B0099 - Body Control & Lighting -->
<div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
<h4 style="color: #333; margin-bottom: 15px; display: flex; align-items: center;">
<span style="background: #ffc107; color: #212529; padding: 5px 10px; border-radius: 4px; font-size: 14px; margin-right: 10px;">B0000-B0099</span>
Body Control & Lighting
</h4>
<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(220px, 1fr)); gap: 12px;">
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0001.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0001</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Driver Airbag Circuit Mal...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0002.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0002</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Passenger Airbag Circuit ...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0003.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0003</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Driver Side Airbag Circui...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0004.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0004</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Passenger Side Airbag Cir...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0005.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0005</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Driver Knee Airbag Circui...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0006.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0006</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Passenger Knee Airbag Cir...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0007.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0007</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Curtain Airbag Left Circu...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0008.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0008</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Curtain Airbag Right Circ...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0009.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0009</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Seat Belt Pretensioner Dr...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0010.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0010</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Seat Belt Pretensioner Pa...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0050.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0050</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Right Front Impact Sensor...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0051.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0051</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Left Front Impact Sensor ...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0052.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0052</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Right Side Impact Sensor ...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0053.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0053</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Left Side Impact Sensor C...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0054.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0054</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Rear Impact Sensor Circui...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0055.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0055</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Rollover Sensor Circuit</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0056.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0056</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Airbag Control Module Int...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0057.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0057</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Airbag Warning Light Circ...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0058.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0058</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Crash Data Recording Syst...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0059.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0059</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Occupant Classification S...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0060.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0060</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Seat Position Sensor Circ...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0061.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0061</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Seat Belt Buckle Switch D...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0062.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0062</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Seat Belt Buckle Switch P...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0063.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0063</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Airbag Disable Switch Cir...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0064.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0064</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Side Impact Sensor Left R...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0065.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0065</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Side Impact Sensor Right ...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0066.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0066</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Pedestrian Protection Sys...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0067.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0067</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Active Hood System Circui...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0068.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0068</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Airbag Deployment Loop Re...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0069.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0069</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Satellite Crash Sensor Fr...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0070.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0070</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Satellite Crash Sensor Fr...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0071.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0071</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Airbag Driver Circuit Res...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0072.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0072</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Airbag Passenger Circuit ...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0073.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0073</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Airbag Side Impact Circui...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0074.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0074</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Airbag Curtain Circuit Ma...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0075.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0075</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Seat Belt Pretensioner Ci...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0076.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0076</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Airbag Impact Sensor Circ...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0077.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0077</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Airbag Diagnostic Module ...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0078.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0078</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Airbag System Clock Sprin...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0079.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0079</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Airbag System Deployment ...</div>
  </div>
</div>
</div>

<!-- B0100-B0199 - Airbag & Safety Systems -->
<div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
<h4 style="color: #333; margin-bottom: 15px; display: flex; align-items: center;">
<span style="background: #ffc107; color: #212529; padding: 5px 10px; border-radius: 4px; font-size: 14px; margin-right: 10px;">B0100-B0199</span>
Airbag & Safety Systems
</h4>
<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(220px, 1fr)); gap: 12px;">
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0100.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0100</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Airbag System Sensor Circ...</div>
  </div>
</div>
</div>

<!-- B0200-B0299 - Airbag Control Modules -->
<div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
<h4 style="color: #333; margin-bottom: 15px; display: flex; align-items: center;">
<span style="background: #ffc107; color: #212529; padding: 5px 10px; border-radius: 4px; font-size: 14px; margin-right: 10px;">B0200-B0299</span>
Airbag Control Modules
</h4>
<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(220px, 1fr)); gap: 12px;">
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0200.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0200</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Left Front Turn Signal Ci...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0201.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0201</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Right Front Turn Signal C...</div>
  </div>
</div>
</div>

<!-- B0300-B0399 - Body Control Modules -->
<div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
<h4 style="color: #333; margin-bottom: 15px; display: flex; align-items: center;">
<span style="background: #ffc107; color: #212529; padding: 5px 10px; border-radius: 4px; font-size: 14px; margin-right: 10px;">B0300-B0399</span>
Body Control Modules
</h4>
<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(220px, 1fr)); gap: 12px;">
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0300.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0300</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Keyless Entry System Malf...</div>
  </div>
</div>
</div>

<!-- B0400-B0499 - Security & Anti-theft -->
<div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
<h4 style="color: #333; margin-bottom: 15px; display: flex; align-items: center;">
<span style="background: #ffc107; color: #212529; padding: 5px 10px; border-radius: 4px; font-size: 14px; margin-right: 10px;">B0400-B0499</span>
Security & Anti-theft
</h4>
<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(220px, 1fr)); gap: 12px;">
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0400.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0400</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Climate Control System Co...</div>
  </div>
</div>
</div>

<!-- B0500-B0599 - Advanced Body Controls -->
<div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
<h4 style="color: #333; margin-bottom: 15px; display: flex; align-items: center;">
<span style="background: #ffc107; color: #212529; padding: 5px 10px; border-radius: 4px; font-size: 14px; margin-right: 10px;">B0500-B0599</span>
Advanced Body Controls
</h4>
<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(220px, 1fr)); gap: 12px;">
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0500.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0500</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Driver Monitoring Camera ...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0501.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0501</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Driver Fatigue Detection ...</div>
  </div>
</div>
</div>

<!-- B0600-B0699 - Airbag System Advanced -->
<div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
<h4 style="color: #333; margin-bottom: 15px; display: flex; align-items: center;">
<span style="background: #ffc107; color: #212529; padding: 5px 10px; border-radius: 4px; font-size: 14px; margin-right: 10px;">B0600-B0699</span>
Airbag System Advanced
</h4>
<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(220px, 1fr)); gap: 12px;">
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0600.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0600</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Airbag Control Module Int...</div>
  </div>
</div>
</div>

<!-- B0700-B0799 - Power Window & Door Controls -->
<div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
<h4 style="color: #333; margin-bottom: 15px; display: flex; align-items: center;">
<span style="background: #ffc107; color: #212529; padding: 5px 10px; border-radius: 4px; font-size: 14px; margin-right: 10px;">B0700-B0799</span>
Power Window & Door Controls
</h4>
<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(220px, 1fr)); gap: 12px;">
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0700.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0700</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Power Window Control Modu...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b0701.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B0701</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Power Window Motor Circui...</div>
  </div>
</div>
</div>

<!-- B1000-B1099 - HVAC & Climate Control -->
<div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
<h4 style="color: #333; margin-bottom: 15px; display: flex; align-items: center;">
<span style="background: #ffc107; color: #212529; padding: 5px 10px; border-radius: 4px; font-size: 14px; margin-right: 10px;">B1000-B1099</span>
HVAC & Climate Control
</h4>
<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(220px, 1fr)); gap: 12px;">
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1000.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1000</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">HVAC Control Module Malfu...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1001.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1001</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Blower Motor Circuit Malf...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1002.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1002</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">A/C Pressure Switch Circu...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1003.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1003</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">A/C Refrigerant Pressure ...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1004.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1004</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">A/C Evaporator Temperatur...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1005.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1005</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">A/C Ambient Temperature S...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1006.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1006</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">A/C Blower Motor Circuit</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1007.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1007</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">A/C Blend Door Actuator C...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1008.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1008</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">A/C Mode Door Actuator Ci...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1009.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1009</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">A/C Recirculation Door Ac...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1010.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1010</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Cabin Temperature Sensor ...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1011.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1011</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Sunload Sensor Circuit</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1012.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1012</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">A/C Clutch Relay Control ...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1013.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1013</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">A/C High Pressure Switch</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1014.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1014</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">A/C Low Pressure Switch</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1015.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1015</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Heater Control Valve Circ...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1016.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1016</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Rear Window Defogger Circ...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1017.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1017</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Heated Mirror Circuit</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1018.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1018</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Heated Seat Driver Circui...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1019.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1019</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Heated Seat Passenger Cir...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1020.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1020</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Ventilated Seat Driver Ci...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1021.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1021</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Ventilated Seat Passenger...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1022.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1022</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">A/C Expansion Valve Circu...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1023.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1023</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">A/C Condenser Fan Circuit</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1024.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1024</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">A/C System Leak Detection</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1025.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1025</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Climate Control Module In...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1026.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1026</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">A/C Refrigerant Level Sen...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1027.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1027</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">A/C Compressor Speed Sens...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1028.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1028</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">A/C System Performance</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1029.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1029</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Automatic Climate Control...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1030.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1030</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Dual Zone Climate Control...</div>
  </div>
</div>
</div>

<!-- B1200-B1299 - Adaptive Lighting Systems -->
<div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
<h4 style="color: #333; margin-bottom: 15px; display: flex; align-items: center;">
<span style="background: #ffc107; color: #212529; padding: 5px 10px; border-radius: 4px; font-size: 14px; margin-right: 10px;">B1200-B1299</span>
Adaptive Lighting Systems
</h4>
<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(220px, 1fr)); gap: 12px;">
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1200.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1200</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Adaptive Headlight Contro...</div>
  </div>
</div>
</div>

<!-- B1300-B1399 - Power Seat Controls -->
<div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
<h4 style="color: #333; margin-bottom: 15px; display: flex; align-items: center;">
<span style="background: #ffc107; color: #212529; padding: 5px 10px; border-radius: 4px; font-size: 14px; margin-right: 10px;">B1300-B1399</span>
Power Seat Controls
</h4>
<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(220px, 1fr)); gap: 12px;">
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1300.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1300</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Power Seat Control Module...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1301.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1301</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Battery Voltage High</div>
  </div>
</div>
</div>

<!-- B1400-B1499 - Door Lock & Security Systems -->
<div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
<h4 style="color: #333; margin-bottom: 15px; display: flex; align-items: center;">
<span style="background: #ffc107; color: #212529; padding: 5px 10px; border-radius: 4px; font-size: 14px; margin-right: 10px;">B1400-B1499</span>
Door Lock & Security Systems
</h4>
<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(220px, 1fr)); gap: 12px;">
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b1400.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B1400</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Door Lock Control Module ...</div>
  </div>
</div>
</div>

<!-- B2000-B2099 - Manufacturer Specific Body -->
<div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
<h4 style="color: #333; margin-bottom: 15px; display: flex; align-items: center;">
<span style="background: #ffc107; color: #212529; padding: 5px 10px; border-radius: 4px; font-size: 14px; margin-right: 10px;">B2000-B2099</span>
Manufacturer Specific Body
</h4>
<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(220px, 1fr)); gap: 12px;">
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b2001.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B2001</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Headlight Low Beam Left C...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b2002.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B2002</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Headlight Low Beam Right ...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b2003.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B2003</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Headlight High Beam Left ...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b2004.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B2004</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Headlight High Beam Right...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b2005.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B2005</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Daytime Running Light Lef...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b2006.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B2006</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Daytime Running Light Rig...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b2007.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B2007</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Turn Signal Left Front Ci...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b2008.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B2008</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Turn Signal Right Front C...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b2009.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B2009</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Turn Signal Left Rear Cir...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b2010.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B2010</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Turn Signal Right Rear Ci...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b2011.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B2011</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Brake Light Left Circuit</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b2012.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B2012</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Brake Light Right Circuit</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b2013.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B2013</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Third Brake Light Circuit</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b2014.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B2014</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Tail Light Left Circuit</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b2015.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B2015</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Tail Light Right Circuit</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b2016.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B2016</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Reverse Light Left Circui...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b2017.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B2017</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Reverse Light Right Circu...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b2018.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B2018</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">License Plate Light Circu...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b2019.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B2019</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Fog Light Front Left Circ...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b2020.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B2020</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Fog Light Front Right Cir...</div>
  </div>
</div>
</div>

<!-- B3000-B3099 - Manufacturer Specific Body -->
<div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
<h4 style="color: #333; margin-bottom: 15px; display: flex; align-items: center;">
<span style="background: #ffc107; color: #212529; padding: 5px 10px; border-radius: 4px; font-size: 14px; margin-right: 10px;">B3000-B3099</span>
Manufacturer Specific Body
</h4>
<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(220px, 1fr)); gap: 12px;">
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b3001.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B3001</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Power Window Driver Circu...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b3002.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B3002</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Power Window Passenger Ci...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b3003.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B3003</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Power Window Rear Left Ci...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b3004.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B3004</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Power Window Rear Right C...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b3005.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B3005</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Door Lock Actuator Driver...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b3006.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B3006</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Door Lock Actuator Passen...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b3007.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B3007</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Door Lock Actuator Rear L...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b3008.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B3008</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Door Lock Actuator Rear R...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b3009.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B3009</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Central Locking System Ci...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b3010.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B3010</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Keyless Entry System Circ...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b3011.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B3011</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Door Ajar Switch Driver C...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b3012.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B3012</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Door Ajar Switch Passenge...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b3013.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B3013</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Door Ajar Switch Rear Lef...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b3014.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B3014</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Door Ajar Switch Rear Rig...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b3015.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B3015</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Hood Ajar Switch Circuit</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b3016.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B3016</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Trunk Ajar Switch Circuit</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b3017.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B3017</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Power Mirror Driver Circu...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b3018.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B3018</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Power Mirror Passenger Ci...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b3019.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B3019</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Mirror Fold Circuit</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b3020.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B3020</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Sunroof Motor Circuit</div>
  </div>
</div>
</div>

<!-- B4000-B4099 - Manufacturer Specific Body -->
<div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
<h4 style="color: #333; margin-bottom: 15px; display: flex; align-items: center;">
<span style="background: #ffc107; color: #212529; padding: 5px 10px; border-radius: 4px; font-size: 14px; margin-right: 10px;">B4000-B4099</span>
Manufacturer Specific Body
</h4>
<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(220px, 1fr)); gap: 12px;">
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b4000.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B4000</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Side Airbag Circuit Malfu...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b4001.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B4001</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Curtain Airbag Circuit Ma...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b4002.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B4002</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Knee Airbag Circuit Malfu...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b4003.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B4003</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Seat Belt Pretensioner Ci...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b4004.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B4004</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Airbag Impact Sensor Circ...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b4005.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B4005</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Airbag Warning Light Circ...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b4006.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B4006</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Airbag Diagnostic Module</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b4007.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B4007</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Airbag System Power Suppl...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b4008.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B4008</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Airbag Clock Spring Circu...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b4009.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B4009</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Airbag Occupant Classific...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b4010.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B4010</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Power Window Motor Circui...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b4011.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B4011</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Power Window Switch Circu...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b4012.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B4012</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Central Locking System</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b4013.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B4013</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Door Lock Actuator Circui...</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b4014.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B4014</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Keyless Entry System</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b4015.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B4015</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Interior Light Circuit</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b4016.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B4016</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Exterior Light Circuit</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b4017.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B4017</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Turn Signal Circuit</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b4018.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B4018</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Hazard Light Circuit</div>
  </div>
<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../b4019.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">B4019</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Horn Circuit Malfunction</div>
  </div>
</div>
</div>

</article>
</div>

<!-- Sidebar -->
<div class="sidebar col-md-4">
<aside>
<div class="widget">
<h3 class="widget-title">Other Categories</h3>
<ul style="list-style: none; padding: 0;">
<li style="margin-bottom: 10px;"><a href="../engine/index.html" style="color: #007bff;">Engine Codes (P0XXX)</a></li>
<li style="margin-bottom: 10px;"><a href="../chassis/index.html" style="color: #007bff;">Chassis Codes (C0XXX)</a></li>
<li style="margin-bottom: 10px;"><a href="../network/index.html" style="color: #007bff;">Network Codes (U0XXX)</a></li>
</ul>
</div>

<div class="widget">
<h3 class="widget-title">Quick Search</h3>
<form onsubmit="searchCode(event)">
<input type="text" id="quickSearch" placeholder="Enter DTC code (e.g., B1000)" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; margin-bottom: 10px;">
<button type="submit" style="width: 100%; padding: 10px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">Search</button>
</form>
</div>

<div class="widget">
<h3 class="widget-title">Popular Body Codes</h3>
<ul style="list-style: none; padding: 0;">
<li style="margin-bottom: 8px;"><a href="../b0001.html" style="color: #666;">B0001 - Driver Airbag Circuit</a></li>
<li style="margin-bottom: 8px;"><a href="../b1000.html" style="color: #666;">B1000 - ECU Internal Fault</a></li>
<li style="margin-bottom: 8px;"><a href="../b0100.html" style="color: #666;">B0100 - Airbag Warning Light</a></li>
<li style="margin-bottom: 8px;"><a href="../b0200.html" style="color: #666;">B0200 - Side Airbag Circuit</a></li>
<li style="margin-bottom: 8px;"><a href="../b0300.html" style="color: #666;">B0300 - Body Control Module</a></li>
</ul>
</div>
</aside>
</div>

</div>
</section>
<script src="../../js/jquery.min.js"></script>
<script src="../../js/bootstrap.js"></script>
<script src="../../js/superfish.js"></script>
<script src="../../js/custom.js"></script>

<script>
function searchCode(event) {
    event.preventDefault();
    var searchTerm = document.getElementById('quickSearch').value.trim().toUpperCase();
    if (searchTerm) {
        if (searchTerm.match(/^[PBCU]\d{4}$/)) {
            window.location.href = '../' + searchTerm.toLowerCase() + '.html';
        } else {
            alert('Please enter a valid DTC code (e.g., P0300, B1000, C0101, U0100)');
        }
    }
}
</script>

</body>
</html>
