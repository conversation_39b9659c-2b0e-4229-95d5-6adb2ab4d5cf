<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>P0112 - IAT Sensor Low Input | GeekOBD</title>
    <meta name="description" content="The Engine Control Module has detected that the Intake Air Temperature sensor is reading extremely hot temperatures when actual air temperature is cooler.">
    <meta name="keywords" content="P0112, P0112, diagnostic trouble code, OBD2, car repair, engine problems, GeekOBD">
    <link rel="canonical" href="https://www.geekobd.com/dtc-codes/p0112.html">
    
    <!-- Open Graph -->
    <meta property="og:title" content="P0112 - IAT Sensor Low Input">
    <meta property="og:description" content="The Engine Control Module has detected that the Intake Air Temperature sensor is reading extremely hot temperatures when actual air temperature is cooler.">
    <meta property="og:url" content="https://www.geekobd.com/dtc-codes/p0112.html">
    <meta property="og:type" content="article">
    
    <!-- CSS -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../css/fonts/font-awesome/css/font-awesome.css">
    <link rel="stylesheet" href="../css/animations.css" media="screen">
    <link rel="stylesheet" href="../css/superfish.css" media="screen">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/colors/blue.css" id="colors">
    <link rel="stylesheet" href="../css/theme-responsive.css">
    <link rel="stylesheet" href="../css/seo-enhancements.css">
    <link rel="shortcut icon" href="../img/ico/favicon.ico">

    <style>
    .dtc-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 60px 0 40px;
    }
    .dtc-code-badge {
        display: inline-block;
        background: rgba(255,255,255,0.2);
        padding: 10px 20px;
        border-radius: 50px;
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 20px;
    }
    .severity-badge {
        display: inline-block;
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
        text-transform: uppercase;
        margin-left: 15px;
    }
    .severity-high { background: #ff4757; color: white; }
    .severity-medium { background: #ffa502; color: white; }
    .content-section { padding: 50px 0; }
    .info-box {
        background: #f8f9fa;
        border-left: 4px solid #667eea;
        padding: 20px;
        margin: 20px 0;
        border-radius: 5px;
    }
    .warning-box {
        background: #fff3cd;
        border-left: 4px solid #ffc107;
        padding: 20px;
        margin: 20px 0;
        border-radius: 5px;
    }
    .danger-box {
        background: #f8d7da;
        border-left: 4px solid #dc3545;
        padding: 20px;
        margin: 20px 0;
        border-radius: 5px;
    }
    .breadcrumb-custom {
        background: none;
        padding: 20px 0;
        margin: 0;
    }
    .breadcrumb-custom a {
        color: #667eea;
        text-decoration: none;
    }
    .breadcrumb-custom a:hover {
        text-decoration: underline;
    }
    </style>

    
<!-- Article Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": "P0112 - IAT Sensor Low Input",
  "description": "The Engine Control Module has detected that the Intake Air Temperature sensor is reading extremely hot temperatures when actual air temperature is cooler.",
  "author": {
    "@type": "Organization",
    "name": "GeekOBD",
    "url": "https://www.geekobd.com"
  },
  "publisher": {
    "@type": "Organization",
    "name": "GeekOBD",
    "logo": {
      "@type": "ImageObject",
      "url": "https://www.geekobd.com/images/logo.png"
    }
  },
  "datePublished": "2025-07-31",
  "dateModified": "2025-07-31",
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "https://www.geekobd.com/dtc-codes/p0112.html"
  }
}
</script>

<!-- FAQ Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "What's the difference between P0112 and P0113 IAT sensor codes?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "P0112 indicates the IAT sensor is reading too hot (low input voltage), while P0113 indicates too cold readings (high input voltage). P0112 typically means a short circuit or sensor reading actual high temperatures, while P0113 usually indicates an open circuit or failed sensor defaulting to -40°F."
      }
    },
    {
      "@type": "Question",
      "name": "What causes an IAT sensor to read 300°F constantly?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "A constant high temperature reading typically indicates a short circuit in the sensor or wiring, causing low resistance. This can be caused by damaged wiring touching ground, a failed sensor with internal short, or conductive contamination bridging the sensor terminals."
      }
    },
    {
      "@type": "Question",
      "name": "Can a hot engine bay cause P0112?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "While extreme engine bay heat can affect IAT readings, P0112 typically indicates sensor readings far beyond normal hot air temperatures (300°F+). True P0112 is usually caused by electrical faults rather than actual high air temperatures, unless the sensor has been relocated to an extremely hot location."
      }
    },
    {
      "@type": "Question",
      "name": "How do I test an IAT sensor for P0112?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Measure resistance across sensor terminals - it should decrease as temperature increases. At room temperature (68°F), expect around 2,500 ohms. If you get very low resistance (under 100 ohms) regardless of temperature, the sensor has failed. GeekOBD APP can monitor live IAT readings to confirm the fault."
      }
    }
  ]
}
</script>

<!-- HowTo Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "HowTo",
  "name": "How to Diagnose P0112 IAT Sensor Low Input",
  "description": "Step-by-step guide to diagnose and fix P0112",
  "totalTime": "PT45M",
  "estimatedCost": {
    "@type": "MonetaryAmount",
    "currency": "USD",
    "value": "$75-$280 for most P0112 repairs"
  },
  "tool": [
    {
      "@type": "HowToTool",
      "name": "GeekOBD APP with MOBD Adapter",
      "description": "Professional OBD2 diagnostic tool",
      "url": "https://www.geekobd.com/app.html"
    }
  ],
  "step": [
    {
      "@type": "HowToStep",
      "position": 1,
      "name": "Verify P0112 Code and Symptoms",
      "text": "Connect scan tool and confirm P0112 is present. Check for additional codes that might indicate related issues. Note current IAT reading - should show extremely high temperature (300°F+)."
    },
    {
      "@type": "HowToStep",
      "position": 2,
      "name": "Visual Inspection of IAT Sensor",
      "text": "Locate IAT sensor (usually in air intake tube or air filter housing). Check for physical damage, contamination, or signs of overheating. Inspect connector for corrosion or damage."
    },
    {
      "@type": "HowToStep",
      "position": 3,
      "name": "Test IAT Sensor Resistance",
      "text": "Disconnect sensor and measure resistance across terminals with multimeter. At 68°F, expect ~2,500 ohms. If resistance is very low (under 100 ohms), sensor has internal short circuit."
    },
    {
      "@type": "HowToStep",
      "position": 4,
      "name": "Check Wiring and Connector",
      "text": "Inspect wiring harness for damage, shorts to ground, or pinched wires. Clean connector terminals and check for proper connection. Test continuity from sensor to ECM."
    },
    {
      "@type": "HowToStep",
      "position": 5,
      "name": "Verify Repair and Clear Codes",
      "text": "After replacing sensor or repairing wiring, clear codes and test drive. Monitor IAT readings to ensure they respond normally to temperature changes."
    }
  ]
}
</script>
</head>
<body>
    <div class="wrap">
	<header id="header" role="banner">
	<div class="main-header">
	<div class="container">
	<div class="row">
	<div class="col-md-3">
	<div class="logo pull-left">
	<h1> <a href="../index.html"> <img src="../img/logo.png" alt="MOBD"> </a> </h1>
	</div>
	</div>
	<div class="col-md-9">
	<div class="mobile navbar-header"> <a class="navbar-toggle" data-toggle="collapse" data-target=".menu"> <i class="icon-reorder icon-2x"></i> </a> </div>
	<nav class="collapse navbar-collapse menu" role="navigation" aria-label="Main navigation">
	<ul class="nav navbar-nav sf-menu">
	<li><a href="../index.html" class="sf-with-ul">Home</a></li>
	<li><a href="../app.html" class="sf-with-ul">APP</a></li>
	<li><a href="javascript:;;" class="sf-with-ul">Adaptor <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
	<ul>
	<li><a href="../hardware2.html" class="sf-with-ul">MOBD GPS</a></li>
	<li><a href="../hardware.html" class="sf-with-ul">MOBD</a></li>
	</ul>
	</li>
	<li><a href="javascript:;;" class="sf-with-ul">Resources <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
	<ul>
	<li><a href="../dtc-codes.html" class="sf-with-ul">DTC Codes</a></li>
	<li><a href="../obd-diagnostic-guide.html" class="sf-with-ul">OBD Guide</a></li>
	<li><a href="../vehicle-compatibility.html" class="sf-with-ul">Compatibility</a></li>
	<li><a href="../fuel-efficiency-monitoring.html" class="sf-with-ul">Fuel Efficiency</a></li>
	<li><a href="../support.html" class="sf-with-ul">Support</a></li>
	<li><a href="../blog.html" class="sf-with-ul">Blog</a></li>
	</ul>
	</li>
	<li><a href="../about.html" class="sf-with-ul">About Us</a></li>
	<li><a href="//www.mobd.cn" class="sf-with-ul" target="_blank">中文版</a></li>
	</ul>
	</nav>
	</div>
	</div>
	</div>
	</div>
	</header>

    <!-- Breadcrumb -->
    <div class="container">
        <nav class="breadcrumb-custom">
            <a href="../index.html">Home</a> &raquo;
            <a href="../dtc-codes.html">DTC Codes</a> &raquo;
            <a href="../dtc-codes.html#engine">Engine Codes</a> &raquo;
            <span>P0112</span>
        </nav>
    </div>

	<section class="dtc-header">
		<div class="container">
			<div class="row">
				<div class="col-md-12">
					<div class="dtc-code-badge">P0112</div>
					<span class="severity-badge severity-medium">MEDIUM Priority</span>
					<h1 style="margin: 20px 0; font-size: 36px;">IAT Sensor Low Input</h1>
					<p style="font-size: 18px; opacity: 0.9; margin-bottom: 0;">The Engine Control Module has detected that the Intake Air Temperature sensor is reading extremely hot temperatures when actual air temperature is cooler.</p>
				</div>
			</div>
		</div>
	</section>

    <!-- Main Content -->
    <section class="content-section">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    
<!-- Quick Answer Section for AI -->
<div id="quick-answer" style="background: #e8f4fd; border: 2px solid #4a90e2; border-radius: 10px; padding: 25px; margin-bottom: 30px;">
    <h2 style="color: #2c5aa0; margin-bottom: 15px;"><i class="fa fa-thermometer-full"></i> Quick Answer</h2>
    <p style="font-size: 18px; font-weight: 500; margin-bottom: 15px; color: #333;">
        <strong>P0112 means:</strong> IAT sensor reading extremely hot temperatures (300°F+) when actual air is cooler - usually short circuit or sensor failure.
    </p>
    <div style="display: flex; flex-wrap: wrap; gap: 15px; margin-bottom: 15px;">
        <span style="background: #4CAF50; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
            <i class="fa fa-wrench"></i> Fix: Replace IAT sensor or repair short circuit
        </span>
        <span style="background: #FF9800; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
            <i class="fa fa-dollar"></i> Cost: $75-$280
        </span>
        <span style="background: #2196F3; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
            <i class="fa fa-clock-o"></i> Time: 30-45 minutes
        </span>
    </div>
    <p style="margin: 0; color: #666; font-size: 14px;">
        <strong>Can I drive with P0112?</strong> Safe to drive short distances, but expect poor performance and potential engine knock. Repair soon to prevent engine damage.
    </p>
</div>
                    
<!-- AI-Friendly Q&A Section -->
<div id="ai-qa" style="background: white; border-radius: 10px; padding: 25px; margin-bottom: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
    <h2><i class="fa fa-comments"></i> Common Questions</h2>
    
    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">What's the difference between P0112 and P0113 IAT sensor codes?</h3>
        <p style="color: #666; line-height: 1.6;">P0112 indicates the IAT sensor is reading too hot (low input voltage), while P0113 indicates too cold readings (high input voltage). P0112 typically means a short circuit or sensor reading actual high temperatures, while P0113 usually indicates an open circuit or failed sensor defaulting to -40°F.</p>
    </div>
    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">What causes an IAT sensor to read 300°F constantly?</h3>
        <p style="color: #666; line-height: 1.6;">A constant high temperature reading typically indicates a short circuit in the sensor or wiring, causing low resistance. This can be caused by damaged wiring touching ground, a failed sensor with internal short, or conductive contamination bridging the sensor terminals.</p>
    </div>
    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">Can a hot engine bay cause P0112?</h3>
        <p style="color: #666; line-height: 1.6;">While extreme engine bay heat can affect IAT readings, P0112 typically indicates sensor readings far beyond normal hot air temperatures (300°F+). True P0112 is usually caused by electrical faults rather than actual high air temperatures, unless the sensor has been relocated to an extremely hot location.</p>
    </div>
    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; ">
        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">How do I test an IAT sensor for P0112?</h3>
        <p style="color: #666; line-height: 1.6;">Measure resistance across sensor terminals - it should decrease as temperature increases. At room temperature (68°F), expect around 2,500 ohms. If you get very low resistance (under 100 ohms) regardless of temperature, the sensor has failed. GeekOBD APP can monitor live IAT readings to confirm the fault.</p>
    </div>
</div>
                    
<!-- Main Content Section -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h2><i class="fa fa-info-circle"></i> What is P0112?</h2>
    <p style="font-size: 16px; line-height: 1.6; color: #333; margin-bottom: 20px;">The Engine Control Module has detected that the Intake Air Temperature (IAT) sensor is producing readings that indicate extremely hot air temperatures (typically 300°F or higher) when the actual intake air temperature should be much cooler. This sensor measures the temperature of air entering the engine to help the ECM calculate proper fuel injection timing and quantity. When the sensor reads too hot, it can cause lean fuel mixture, poor performance, and potential engine damage from running too lean.</p>

    <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; margin: 20px 0;">
        <strong><i class="fa fa-exclamation-triangle"></i> Performance Impact:</strong> P0112 causes the ECM to receive incorrect air temperature data, leading to overly lean fuel mixture, reduced engine power, potential engine knock, poor acceleration, and possible engine damage from running too lean.
    </div>
</div>

<!-- Symptoms Section -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h2><i class="fa fa-stethoscope"></i> Symptoms of P0112</h2>
    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
        <ul style="margin: 0; padding-left: 20px;">
            <li><strong>Check engine light illuminated - IAT sensor fault detected</strong></li>
								<li><strong>Poor engine performance - Lean fuel mixture from hot air reading</strong></li>
								<li><strong>Engine hesitation during acceleration - Insufficient fuel delivery</strong></li>
								<li><strong>Rough idle - Incorrect air/fuel mixture calculations</strong></li>
								<li><strong>Engine knocking or pinging - Lean mixture causing detonation</strong></li>
								<li><strong>Hard starting when engine is cold - ECM thinks air is hot</strong></li>
								<li><strong>Reduced power output - Engine running lean for protection</strong></li>
								<li><strong>Failed emissions test - Lean exhaust conditions</strong></li>
        </ul>
    </div>
</div>

<!-- Causes Section -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h2><i class="fa fa-search"></i> Common Causes of P0112</h2>
    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
        <ol style="margin: 0; padding-left: 20px;">
            <li>Faulty IAT sensor - Internal component failure causing low resistance</li>
									<li>Short circuit in IAT sensor wiring - Wire touching ground or power</li>
									<li>Corroded IAT sensor connector - Poor electrical contact causing resistance drop</li>
									<li>IAT sensor contamination - Conductive debris causing short circuit</li>
									<li>Damaged IAT sensor housing - Physical damage exposing sensor element</li>
									<li>ECM internal fault - Module misreading sensor signal</li>
									<li>Wiring harness damage - Short to ground in sensor circuit</li>
									<li>Aftermarket air intake modification - Sensor relocated to hot area</li>
        </ol>
    </div>
</div>
                    
<!-- Cost Analysis Section -->
<div id="cost-info" style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
    <h2><i class="fa fa-calculator"></i> P0112 Repair Costs</h2>

    <div style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin: 20px 0;">
        <h3 style="color: #2c3e50; margin-bottom: 20px;">Cost Breakdown by Repair Type</h3>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 25px;">
            
        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #4CAF50;">
            <h4 style="color: #4CAF50; margin-bottom: 10px;"><i class="fa fa-thermometer-full"></i> IAT Sensor Replacement</h4>
            <p style="margin-bottom: 15px; color: #666;">Most common fix - Replace faulty sensor (75% of cases)</p>
            <ul style="list-style: none; padding: 0;">
                <li style="margin-bottom: 8px;"><strong>IAT Sensor:</strong> $25-$85</li>
                <li style="margin-bottom: 8px;"><strong>Labor (0.5-1 hour):</strong> $50-$120</li>
                <li style="margin-bottom: 8px;"><strong>Total:</strong> <span style="color: #4CAF50; font-weight: bold;">$75-$205</span></li>
                <li style="color: #666; font-size: 14px;">Success rate: ~95% success rate%</li>
            </ul>
        </div>
        
        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #FF9800;">
            <h4 style="color: #FF9800; margin-bottom: 10px;"><i class="fa fa-plug"></i> Wiring Repair</h4>
            <p style="margin-bottom: 15px; color: #666;">Fix short circuit in sensor wiring (20% of cases)</p>
            <ul style="list-style: none; padding: 0;">
                <li style="margin-bottom: 8px;"><strong>Wire repair/splice:</strong> $15-$35</li>
                <li style="margin-bottom: 8px;"><strong>Diagnostic time:</strong> $80-$150</li>
                <li style="margin-bottom: 8px;"><strong>Labor (1-2 hours):</strong> $100-$240</li>
                <li style="margin-bottom: 8px;"><strong>Total:</strong> <span style="color: #FF9800; font-weight: bold;">$195-$425</span></li>
                <li style="color: #666; font-size: 14px;">Success rate: ~90% success rate%</li>
            </ul>
        </div>
        
        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #2196F3;">
            <h4 style="color: #2196F3; margin-bottom: 10px;"><i class="fa fa-wrench"></i> Connector Cleaning/Replacement</h4>
            <p style="margin-bottom: 15px; color: #666;">Clean corroded connector or replace if damaged (5% of cases)</p>
            <ul style="list-style: none; padding: 0;">
                <li style="margin-bottom: 8px;"><strong>Connector cleaning:</strong> $20-$40</li>
                <li style="margin-bottom: 8px;"><strong>New connector (if needed):</strong> $25-$60</li>
                <li style="margin-bottom: 8px;"><strong>Labor (0.5 hour):</strong> $50-$120</li>
                <li style="margin-bottom: 8px;"><strong>Total:</strong> <span style="color: #2196F3; font-weight: bold;">$95-$220</span></li>
                <li style="color: #666; font-size: 14px;">Success rate: ~85% success rate%</li>
            </ul>
        </div>
        </div>

        <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #4CAF50;">
            <h4 style="color: #2e7d32; margin-bottom: 15px;"><i class="fa fa-lightbulb-o"></i> Money-Saving Tips</h4>
            <ul style="margin: 0; color: #2e7d32;">
                <li style="margin-bottom: 8px;">Check connector first - 15% of P0112 cases are just corroded connections</li>
                <li style="margin-bottom: 8px;">IAT sensors are usually easy to access - consider DIY replacement to save $50-120 in labor</li>
                <li style="margin-bottom: 8px;">Test sensor resistance before buying parts - confirm failure first</li>
                <li style="margin-bottom: 8px;">Some aftermarket sensors cost 50% less than OEM with same reliability</li>
                <li style="margin-bottom: 8px;">If wiring repair is needed, fix the root cause to prevent recurrence</li>
            </ul>
        </div>
    </div>
</div>
                    
<!-- Diagnostic Steps Section -->
<div id="diagnostic-steps" style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
    <h2><i class="fa fa-stethoscope"></i> Diagnostic Steps</h2>

    <div style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin: 20px 0;">
        <h3 style="color: #2c3e50; margin-bottom: 20px;">Professional P0112 Diagnosis Process</h3>
        <p style="margin-bottom: 20px; color: #666;">Follow these systematic steps to accurately diagnose P0112. Each step builds on the previous one to ensure accurate diagnosis.</p>

        
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-search"></i> Step 1: Verify P0112 Code and Symptoms</h4>
            <p style="margin-bottom: 15px; color: #333;">Connect scan tool and confirm P0112 is present. Check for additional codes that might indicate related issues. Note current IAT reading - should show extremely high temperature (300°F+).</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> Use GeekOBD APP to monitor live IAT sensor data. Look for readings above 300°F when engine is cold - this confirms P0112 fault.
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-eye"></i> Step 2: Visual Inspection of IAT Sensor</h4>
            <p style="margin-bottom: 15px; color: #333;">Locate IAT sensor (usually in air intake tube or air filter housing). Check for physical damage, contamination, or signs of overheating. Inspect connector for corrosion or damage.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> GeekOBD APP can help locate sensor by showing which intake air temperature reading is faulty if multiple sensors are present.
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-thermometer-full"></i> Step 3: Test IAT Sensor Resistance</h4>
            <p style="margin-bottom: 15px; color: #333;">Disconnect sensor and measure resistance across terminals with multimeter. At 68°F, expect ~2,500 ohms. If resistance is very low (under 100 ohms), sensor has internal short circuit.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> Compare resistance readings with GeekOBD APP temperature charts to verify sensor is within specification for current ambient temperature.
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-plug"></i> Step 4: Check Wiring and Connector</h4>
            <p style="margin-bottom: 15px; color: #333;">Inspect wiring harness for damage, shorts to ground, or pinched wires. Clean connector terminals and check for proper connection. Test continuity from sensor to ECM.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> Use GeekOBD APP to monitor sensor voltage while wiggling wires - voltage should remain stable if wiring is good.
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-check-circle"></i> Step 5: Verify Repair and Clear Codes</h4>
            <p style="margin-bottom: 15px; color: #333;">After replacing sensor or repairing wiring, clear codes and test drive. Monitor IAT readings to ensure they respond normally to temperature changes.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> GeekOBD APP provides real-time verification - IAT should read close to ambient air temperature when engine is cold, and gradually increase as engine warms up.
            </div>
        </div>

        <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin-top: 20px; border-left: 4px solid #ffc107;">
            <h4 style="color: #856404; margin-bottom: 15px;"><i class="fa fa-exclamation-triangle"></i> Important Notes</h4>
            <ul style="margin: 0; color: #856404;">
                <li style="margin-bottom: 8px;">P0112 indicates sensor reading too hot - do not confuse with P0113 (too cold)</li>
                <li style="margin-bottom: 8px;">Always test sensor resistance before replacement - connector issues can mimic sensor failure</li>
                <li style="margin-bottom: 8px;">IAT sensor affects fuel mixture - driving with P0112 can cause engine knock and damage</li>
            </ul>
        </div>
    </div>
</div>
                    
<!-- Real Repair Case Studies -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
    <h2><i class="fa fa-file-text"></i> Real Repair Case Studies</h2>
    
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2;"><i class="fa fa-car"></i> Case 1: Ford F-150 IAT Sensor Short Circuit</h4>
            <p><strong>Vehicle:</strong> 2018 Ford F-150 5.0L V8, 85,000 miles</p>
            <p><strong>Problem:</strong> Customer complained of poor acceleration, engine knocking, and check engine light. Truck was running rough and had reduced power, especially during acceleration.</p>
            <p><strong>Diagnosis:</strong> GeekOBD APP showed P0112 code with IAT reading constant 315°F even when engine was cold. Resistance test revealed IAT sensor had only 45 ohms resistance (should be ~2,500 ohms at room temperature), indicating internal short circuit.</p>
            <p><strong>Solution:</strong> Replaced IAT sensor located in air intake tube between air filter and throttle body. Sensor was easily accessible and took 20 minutes to replace.</p>
            <p><strong>Cost:</strong> IAT sensor: $42, Labor: $60, Total: $102</p>
            <p><strong>Result:</strong> P0112 code cleared immediately. IAT now reads correctly (75°F cold, gradually increasing with engine temperature). Engine performance restored, no more knocking, smooth acceleration returned.</p>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 4px solid #28a745;">
            <h4 style="color: #28a745;"><i class="fa fa-car"></i> Case 2: Honda Civic Wiring Harness Damage</h4>
            <p><strong>Vehicle:</strong> 2016 Honda Civic 1.5L Turbo, 92,000 miles</p>
            <p><strong>Problem:</strong> Intermittent P0112 code with occasional poor idle and hesitation. Problem seemed to occur more often when driving over bumps or rough roads.</p>
            <p><strong>Diagnosis:</strong> Initial IAT sensor test showed normal resistance, but GeekOBD APP revealed intermittent spikes to 350°F+ during driving. Wire wiggle test found damaged section of harness near engine mount where wires had rubbed against bracket.</p>
            <p><strong>Solution:</strong> Repaired damaged section of IAT sensor wiring harness. Cut out damaged portion and spliced in new wire with proper insulation and protective covering.</p>
            <p><strong>Cost:</strong> Wire repair kit: $25, Diagnostic time: $120, Labor: $180, Total: $325</p>
            <p><strong>Result:</strong> P0112 code has not returned after 3 months. IAT readings remain stable during all driving conditions. Customer reports smooth operation and improved fuel economy.</p>
        </div>
</div>
                </div>

                <div class="col-md-4">
                    
<!-- GeekOBD APP Promotion -->
<div style="background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%); color: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: white;"><i class="fa fa-mobile"></i> Diagnose P0112</h4>
    <p style="margin-bottom: 20px; opacity: 0.9; font-size: 14px;">Use GeekOBD APP for professional IAT sensor diagnosis!</p>
    <ul style="margin-bottom: 20px; padding-left: 20px; font-size: 14px;">
        <li style="margin-bottom: 8px;">Live IAT temperature monitoring</li>
        <li style="margin-bottom: 8px;">Resistance testing guidance</li>
        <li style="margin-bottom: 8px;">Wiring diagram access</li>
        <li style="margin-bottom: 8px;">Repair verification tools</li>
    </ul>
    <a href="https://www.geekobd.com/app.html" style="display: inline-block; background: rgba(255,255,255,0.2); color: white; padding: 12px 25px; border-radius: 25px; text-decoration: none; font-weight: bold; transition: all 0.3s;">
        <i class="fa fa-download"></i> Download GeekOBD APP
    </a>
</div>

<!-- Related System Codes -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-link"></i> IAT Sensor Codes</h4>
    <p style="margin-bottom: 15px; color: #666; font-size: 14px;">Related Intake Air Temperature sensor codes:</p>
    <div style="margin-bottom: 15px;">
        
                <a href="p0113.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #e74c3c;">
                    <strong style="color: #e74c3c;">P0113</strong> - IAT Sensor High Input - Opposite condition (too cold readings)
                </a>
                <a href="p0110.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #4a90e2;">
                    <strong style="color: #4a90e2;">P0110</strong> - IAT Sensor Circuit Malfunction - General IAT circuit problem
                </a>
                <a href="p0114.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #f39c12;">
                    <strong style="color: #f39c12;">P0114</strong> - IAT Sensor Intermittent - Erratic IAT sensor readings
                </a>
                <a href="p0171.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #9b59b6;">
                    <strong style="color: #9b59b6;">P0171</strong> - System Too Lean Bank 1 - Can be caused by incorrect IAT readings
                </a>
                <a href="p0174.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #9b59b6;">
                    <strong style="color: #9b59b6;">P0174</strong> - System Too Lean Bank 2 - Can be caused by incorrect IAT readings
                </a>
                <a href="p0101.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #27ae60;">
                    <strong style="color: #27ae60;">P0101</strong> - MAF Sensor Range/Performance - Related air intake measurement
                </a>
                <a href="p0300.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #e67e22;">
                    <strong style="color: #e67e22;">P0300</strong> - Random Misfire - Can be caused by lean mixture from P0112
                </a>
    </div>
</div>

<!-- Diagnostic Resources -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-tools"></i> Diagnostic Resources</h4>
    <div style="margin-bottom: 20px;">
        
        <a href="#diagnostic-steps" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-thermometer-full" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">IAT Sensor Testing Guide</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">Professional resistance and voltage testing procedures</span>
        </a>
        <a href="../resources/iat-wiring-diagrams.html" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-sitemap" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">IAT Wiring Diagrams</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">Sensor circuit diagrams and pin configurations</span>
        </a>
        <a href="../resources/iat-temperature-charts.html" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-line-chart" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">Temperature Charts</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">IAT sensor resistance vs temperature specifications</span>
        </a>
        <a href="../resources/iat-sensor-locations.html" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-map-marker" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">Sensor Location Guide</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">IAT sensor locations by vehicle make and model</span>
        </a>
    </div>
</div>

<!-- Quick Navigation -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-compass"></i> Quick Navigation</h4>
    <div style="display: flex; flex-direction: column; gap: 8px;">
        <a href="#quick-answer" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-bolt"></i> Quick Answer
        </a>
        <a href="#ai-qa" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-comments"></i> Common Questions
        </a>
        <a href="#cost-info" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-calculator"></i> Repair Costs
        </a>
        <a href="#diagnostic-steps" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-stethoscope"></i> Diagnostic Steps
        </a>
    </div>
</div>

<!-- Code Information -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-info-circle"></i> Code Information</h4>
    <table style="width: 100%; font-size: 14px;">
        <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Code:</strong></td>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;">P0112</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>System:</strong></td>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;">Engine Management</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Severity:</strong></td>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><span class="severity-badge severity-medium">MEDIUM</span></td>
        </tr>
        <tr>
            <td style="padding: 8px 0;"><strong>Category:</strong></td>
            <td style="padding: 8px 0;">Intake Air Temperature</td>
        </tr>
    </table>
</div>
                </div>
            </div>
        </div>
    </section>

    </div>

<script src="../js/jquery.min.js"></script>
<script src="../js/bootstrap.js"></script>
<script src="../js/superfish.js"></script>
<script src="../js/custom.js"></script>
</body>
</html>