<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>P0113 - IAT Sensor High Input | GeekOBD</title>
    <meta name="description" content="The Engine Control Module has detected that the Intake Air Temperature sensor is reading extremely cold temperatures when actual air temperature is warmer.">
    <meta name="keywords" content="P0113, P0113, diagnostic trouble code, OBD2, car repair, engine problems, GeekOBD">
    <link rel="canonical" href="https://www.geekobd.com/dtc-codes/p0113.html">
    
    <!-- Open Graph -->
    <meta property="og:title" content="P0113 - IAT Sensor High Input">
    <meta property="og:description" content="The Engine Control Module has detected that the Intake Air Temperature sensor is reading extremely cold temperatures when actual air temperature is warmer.">
    <meta property="og:url" content="https://www.geekobd.com/dtc-codes/p0113.html">
    <meta property="og:type" content="article">
    
    <!-- CSS -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../css/fonts/font-awesome/css/font-awesome.css">
    <link rel="stylesheet" href="../css/animations.css" media="screen">
    <link rel="stylesheet" href="../css/superfish.css" media="screen">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/colors/blue.css" id="colors">
    <link rel="stylesheet" href="../css/theme-responsive.css">
    <link rel="stylesheet" href="../css/seo-enhancements.css">
    <link rel="shortcut icon" href="../img/ico/favicon.ico">

    <style>
    .dtc-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 60px 0 40px;
    }
    .dtc-code-badge {
        display: inline-block;
        background: rgba(255,255,255,0.2);
        padding: 10px 20px;
        border-radius: 50px;
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 20px;
    }
    .severity-badge {
        display: inline-block;
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
        text-transform: uppercase;
        margin-left: 15px;
    }
    .severity-high { background: #ff4757; color: white; }
    .severity-medium { background: #ffa502; color: white; }
    .content-section { padding: 50px 0; }
    .info-box {
        background: #f8f9fa;
        border-left: 4px solid #667eea;
        padding: 20px;
        margin: 20px 0;
        border-radius: 5px;
    }
    .warning-box {
        background: #fff3cd;
        border-left: 4px solid #ffc107;
        padding: 20px;
        margin: 20px 0;
        border-radius: 5px;
    }
    .danger-box {
        background: #f8d7da;
        border-left: 4px solid #dc3545;
        padding: 20px;
        margin: 20px 0;
        border-radius: 5px;
    }
    .breadcrumb-custom {
        background: none;
        padding: 20px 0;
        margin: 0;
    }
    .breadcrumb-custom a {
        color: #667eea;
        text-decoration: none;
    }
    .breadcrumb-custom a:hover {
        text-decoration: underline;
    }
    </style>

    
<!-- Article Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": "P0113 - IAT Sensor High Input",
  "description": "The Engine Control Module has detected that the Intake Air Temperature sensor is reading extremely cold temperatures when actual air temperature is warmer.",
  "author": {
    "@type": "Organization",
    "name": "GeekOBD",
    "url": "https://www.geekobd.com"
  },
  "publisher": {
    "@type": "Organization",
    "name": "GeekOBD",
    "logo": {
      "@type": "ImageObject",
      "url": "https://www.geekobd.com/images/logo.png"
    }
  },
  "datePublished": "2025-07-31",
  "dateModified": "2025-07-31",
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "https://www.geekobd.com/dtc-codes/p0113.html"
  }
}
</script>

<!-- FAQ Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "What's the difference between P0113 and P0112 IAT sensor codes?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "P0113 indicates the IAT sensor is reading too cold (high input voltage), while P0112 indicates too hot readings (low input voltage). P0113 typically means an open circuit or failed sensor, while P0112 usually indicates a short circuit or sensor reading actual high temperatures."
      }
    },
    {
      "@type": "Question",
      "name": "What causes an IAT sensor to read -40°F constantly?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "A constant -40°F reading typically indicates an open circuit in the sensor or wiring. When the ECM loses signal from the IAT sensor, it defaults to -40°F as a failsafe value. This is usually caused by a failed sensor, broken wire, or corroded connector."
      }
    },
    {
      "@type": "Question",
      "name": "Can a dirty IAT sensor cause P0113?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Yes, but it's less common. Heavy contamination with oil, dirt, or carbon deposits can insulate the sensor element and cause erratic readings. However, P0113 more commonly results from complete sensor failure or wiring issues rather than contamination."
      }
    },
    {
      "@type": "Question",
      "name": "How do I test an IAT sensor with a multimeter?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Disconnect the sensor and measure resistance across the sensor terminals. At 68°F, resistance should be around 2,500 ohms. As temperature increases, resistance decreases. If you get infinite resistance or no reading, the sensor has failed. GeekOBD APP can also monitor live IAT readings for easier diagnosis."
      }
    }
  ]
}
</script>

<!-- HowTo Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "HowTo",
  "name": "How to Diagnose P0113 IAT Sensor High Input",
  "description": "Step-by-step guide to diagnose and fix P0113",
  "totalTime": "PT30M",
  "estimatedCost": {
    "@type": "MonetaryAmount",
    "currency": "USD",
    "value": "200"
  },
  "tool": [
    {
      "@type": "HowToTool",
      "name": "GeekOBD APP with MOBD Adapter",
      "description": "Professional OBD2 diagnostic tool",
      "url": "https://www.geekobd.com/app.html"
    }
  ],
  "step": [
    {
      "@type": "HowToStep",
      "position": 1,
      "name": "Initial Code Verification",
      "text": "Connect GeekOBD APP and verify P0113 code is present. Check for additional codes that may indicate related issues. Clear codes and test drive to see if P0113 returns immediately."
    },
    {
      "@type": "HowToStep",
      "position": 2,
      "name": "Visual Inspection",
      "text": "Locate IAT sensor (usually in air intake tube or MAF housing). Inspect sensor, connector, and wiring for obvious damage, corrosion, or contamination. Check for loose connections."
    },
    {
      "@type": "HowToStep",
      "position": 3,
      "name": "Sensor Resistance Test",
      "text": "Disconnect IAT sensor connector and measure resistance across sensor terminals with multimeter. At 68°F, resistance should be approximately 2,500 ohms. Infinite resistance indicates sensor failure."
    },
    {
      "@type": "HowToStep",
      "position": 4,
      "name": "Wiring Circuit Test",
      "text": "If sensor tests good, check wiring continuity from sensor connector to ECM. Test for proper voltage supply (usually 5V) and ground circuit integrity."
    },
    {
      "@type": "HowToStep",
      "position": 5,
      "name": "Repair and Verification",
      "text": "Replace faulty sensor or repair damaged wiring. Clear codes with GeekOBD APP and test drive vehicle. Monitor IAT readings to ensure proper operation and verify code does not return."
    }
  ]
}
</script>
</head>
<body>
    <div class="wrap">
	<header id="header" role="banner">
	<div class="main-header">
	<div class="container">
	<div class="row">
	<div class="col-md-3">
	<div class="logo pull-left">
	<h1> <a href="../index.html"> <img src="../img/logo.png" alt="MOBD"> </a> </h1>
	</div>
	</div>
	<div class="col-md-9">
	<div class="mobile navbar-header"> <a class="navbar-toggle" data-toggle="collapse" data-target=".menu"> <i class="icon-reorder icon-2x"></i> </a> </div>
	<nav class="collapse navbar-collapse menu" role="navigation" aria-label="Main navigation">
	<ul class="nav navbar-nav sf-menu">
	<li><a href="../index.html" class="sf-with-ul">Home</a></li>
	<li><a href="../app.html" class="sf-with-ul">APP</a></li>
	<li><a href="javascript:;;" class="sf-with-ul">Adaptor <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
	<ul>
	<li><a href="../hardware2.html" class="sf-with-ul">MOBD GPS</a></li>
	<li><a href="../hardware.html" class="sf-with-ul">MOBD</a></li>
	</ul>
	</li>
	<li><a href="javascript:;;" class="sf-with-ul">Resources <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
	<ul>
	<li><a href="../dtc-codes.html" class="sf-with-ul">DTC Codes</a></li>
	<li><a href="../obd-diagnostic-guide.html" class="sf-with-ul">OBD Guide</a></li>
	<li><a href="../vehicle-compatibility.html" class="sf-with-ul">Compatibility</a></li>
	<li><a href="../fuel-efficiency-monitoring.html" class="sf-with-ul">Fuel Efficiency</a></li>
	<li><a href="../support.html" class="sf-with-ul">Support</a></li>
	<li><a href="../blog.html" class="sf-with-ul">Blog</a></li>
	</ul>
	</li>
	<li><a href="../about.html" class="sf-with-ul">About Us</a></li>
	<li><a href="//www.mobd.cn" class="sf-with-ul" target="_blank">中文版</a></li>
	</ul>
	</nav>
	</div>
	</div>
	</div>
	</div>
	</header>

    <!-- Breadcrumb -->
    <div class="container">
        <nav class="breadcrumb-custom">
            <a href="../index.html">Home</a> &raquo;
            <a href="../dtc-codes.html">DTC Codes</a> &raquo;
            <a href="../dtc-codes.html#engine">Engine Codes</a> &raquo;
            <span>P0113</span>
        </nav>
    </div>

	<section class="dtc-header">
		<div class="container">
			<div class="row">
				<div class="col-md-12">
					<div class="dtc-code-badge">P0113</div>
					<span class="severity-badge severity-medium">MEDIUM Priority</span>
					<h1 style="margin: 20px 0; font-size: 36px;">IAT Sensor High Input</h1>
					<p style="font-size: 18px; opacity: 0.9; margin-bottom: 0;">The Engine Control Module has detected that the Intake Air Temperature sensor is reading extremely cold temperatures when actual air temperature is warmer.</p>
				</div>
			</div>
		</div>
	</section>

    <!-- Main Content -->
    <section class="content-section">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    
<!-- Quick Answer Section for AI -->
<div id="quick-answer" style="background: #e8f4fd; border: 2px solid #4a90e2; border-radius: 10px; padding: 25px; margin-bottom: 30px;">
    <h2 style="color: #2c5aa0; margin-bottom: 15px;"><i class="fa fa-thermometer-empty"></i> Quick Answer</h2>
    <p style="font-size: 18px; font-weight: 500; margin-bottom: 15px; color: #333;">
        <strong>P0113 means:</strong> IAT sensor reading extremely cold temperatures (-40°F) when actual air is warmer - usually sensor failure.
    </p>
    <div style="display: flex; flex-wrap: wrap; gap: 15px; margin-bottom: 15px;">
        <span style="background: #4CAF50; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
            <i class="fa fa-wrench"></i> Fix: Replace IAT sensor or repair wiring
        </span>
        <span style="background: #FF9800; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
            <i class="fa fa-dollar"></i> Cost: $85-$320
        </span>
        <span style="background: #2196F3; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
            <i class="fa fa-clock-o"></i> Time: 30-60 minutes
        </span>
    </div>
    <p style="margin: 0; color: #666; font-size: 14px;">
        <strong>Can I drive with P0113?</strong> Safe to drive short distances, but expect poor performance and increased fuel consumption. Repair soon to prevent engine damage.
    </p>
</div>
                    
<!-- AI-Friendly Q&A Section -->
<div id="ai-qa" style="background: white; border-radius: 10px; padding: 25px; margin-bottom: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
    <h2><i class="fa fa-comments"></i> Common Questions</h2>
    
    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">What's the difference between P0113 and P0112 IAT sensor codes?</h3>
        <p style="color: #666; line-height: 1.6;">P0113 indicates the IAT sensor is reading too cold (high input voltage), while P0112 indicates too hot readings (low input voltage). P0113 typically means an open circuit or failed sensor, while P0112 usually indicates a short circuit or sensor reading actual high temperatures.</p>
    </div>
    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">What causes an IAT sensor to read -40°F constantly?</h3>
        <p style="color: #666; line-height: 1.6;">A constant -40°F reading typically indicates an open circuit in the sensor or wiring. When the ECM loses signal from the IAT sensor, it defaults to -40°F as a failsafe value. This is usually caused by a failed sensor, broken wire, or corroded connector.</p>
    </div>
    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">Can a dirty IAT sensor cause P0113?</h3>
        <p style="color: #666; line-height: 1.6;">Yes, but it's less common. Heavy contamination with oil, dirt, or carbon deposits can insulate the sensor element and cause erratic readings. However, P0113 more commonly results from complete sensor failure or wiring issues rather than contamination.</p>
    </div>
    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; ">
        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">How do I test an IAT sensor with a multimeter?</h3>
        <p style="color: #666; line-height: 1.6;">Disconnect the sensor and measure resistance across the sensor terminals. At 68°F, resistance should be around 2,500 ohms. As temperature increases, resistance decreases. If you get infinite resistance or no reading, the sensor has failed. GeekOBD APP can also monitor live IAT readings for easier diagnosis.</p>
    </div>
</div>
                    
<!-- Main Content Section -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h2><i class="fa fa-info-circle"></i> What is P0113?</h2>
    <p style="font-size: 16px; line-height: 1.6; color: #333; margin-bottom: 20px;">The Engine Control Module has detected that the Intake Air Temperature (IAT) sensor is producing readings that indicate extremely cold air temperatures (typically -40°F or lower) when the actual intake air temperature should be much warmer. This sensor measures the temperature of air entering the engine to help the ECM calculate proper fuel injection timing and quantity. When the sensor reads too cold, it can cause rich fuel mixture, poor performance, and increased emissions.</p>

    <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; margin: 20px 0;">
        <strong><i class="fa fa-exclamation-triangle"></i> Performance Impact:</strong> P0113 causes the ECM to receive incorrect air temperature data, leading to overly rich fuel mixture, reduced fuel economy, poor engine performance, increased emissions, and potential engine damage from running too rich.
    </div>
</div>

<!-- Symptoms Section -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h2><i class="fa fa-stethoscope"></i> Symptoms of P0113</h2>
    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
        <ul style="margin: 0; padding-left: 20px;">
            <li><strong>Check engine light illuminated - IAT sensor fault detected</strong></li>
								<li><strong>Poor engine performance - Rich fuel mixture from cold air reading</strong></li>
								<li><strong>Increased fuel consumption - ECM compensating for perceived cold air</strong></li>
								<li><strong>Rough idle - Incorrect air/fuel mixture calculations</strong></li>
								<li><strong>Black smoke from exhaust - Rich fuel condition</strong></li>
								<li><strong>Engine hesitation during acceleration - Fuel delivery issues</strong></li>
								<li><strong>Hard starting in warm weather - Incorrect fuel mixture</strong></li>
								<li><strong>Failed emissions test - Rich exhaust conditions</strong></li>
        </ul>
    </div>
</div>

<!-- Causes Section -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h2><i class="fa fa-search"></i> Common Causes of P0113</h2>
    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
        <ol style="margin: 0; padding-left: 20px;">
            <li>Faulty IAT sensor - Internal component failure causing high resistance</li>
									<li>Open circuit in IAT sensor wiring - Broken wire or poor connection</li>
									<li>Corroded IAT sensor connector - Poor electrical contact</li>
									<li>IAT sensor contamination - Oil, dirt, or debris affecting readings</li>
									<li>Damaged IAT sensor housing - Physical damage to sensor element</li>
									<li>ECM internal fault - Module misreading sensor signal</li>
									<li>Wiring harness damage - Chafing or rodent damage to wires</li>
									<li>Poor ground connection - Inadequate sensor ground circuit</li>
        </ol>
    </div>
</div>
                    
<!-- Cost Analysis Section -->
<div id="cost-info" style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
    <h2><i class="fa fa-calculator"></i> P0113 Repair Costs</h2>

    <div style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin: 20px 0;">
        <h3 style="color: #2c3e50; margin-bottom: 20px;">Cost Breakdown by Repair Type</h3>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 25px;">
            
        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #4CAF50;">
            <h4 style="color: #4CAF50; margin-bottom: 10px;"><i class="fa fa-thermometer-empty"></i> IAT Sensor Replacement</h4>
            <p style="margin-bottom: 15px; color: #666;">Most common and effective repair for P0113. Replace the faulty sensor with OEM or quality aftermarket part.</p>
            <ul style="list-style: none; padding: 0;">
                <li style="margin-bottom: 8px;"><strong>IAT Sensor:</strong> $45-$120</li>
                <li style="margin-bottom: 8px;"><strong>Labor (0.5-1 hr):</strong> $75-$100</li>
                <li style="margin-bottom: 8px;"><strong>Total:</strong> <span style="color: #4CAF50; font-weight: bold;">$120-$220</span></li>
                <li style="color: #666; font-size: 14px;">Success rate: ~95%</li>
            </ul>
        </div>
        
        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #FF9800;">
            <h4 style="color: #FF9800; margin-bottom: 10px;"><i class="fa fa-wrench"></i> Wiring Repair</h4>
            <p style="margin-bottom: 15px; color: #666;">When the sensor is good but wiring is damaged. Requires locating and repairing the damaged wire.</p>
            <ul style="list-style: none; padding: 0;">
                <li style="margin-bottom: 8px;"><strong>Wiring repair kit:</strong> $25-$45</li>
                <li style="margin-bottom: 8px;"><strong>Labor (1-2 hrs):</strong> $60-$135</li>
                <li style="margin-bottom: 8px;"><strong>Total:</strong> <span style="color: #FF9800; font-weight: bold;">$85-$180</span></li>
                <li style="color: #666; font-size: 14px;">Success rate: ~90%</li>
            </ul>
        </div>
        
        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #f44336;">
            <h4 style="color: #f44336; margin-bottom: 10px;"><i class="fa fa-exchange"></i> Harness Replacement</h4>
            <p style="margin-bottom: 15px; color: #666;">When multiple wires are damaged or connector is severely corroded. Most comprehensive but expensive option.</p>
            <ul style="list-style: none; padding: 0;">
                <li style="margin-bottom: 8px;"><strong>Wiring harness:</strong> $150-$320</li>
                <li style="margin-bottom: 8px;"><strong>Labor (2-3 hrs):</strong> $100-$200</li>
                <li style="margin-bottom: 8px;"><strong>Total:</strong> <span style="color: #f44336; font-weight: bold;">$250-$520</span></li>
                <li style="color: #666; font-size: 14px;">Success rate: ~98%</li>
            </ul>
        </div>
        </div>

        <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #4CAF50;">
            <h4 style="color: #2e7d32; margin-bottom: 15px;"><i class="fa fa-lightbulb-o"></i> Money-Saving Tips</h4>
            <ul style="margin: 0; color: #2e7d32;">
                <li style="margin-bottom: 8px;">Clean IAT sensor with MAF cleaner before replacing - may resolve contamination issues</li>
                <li style="margin-bottom: 8px;">Check connector for corrosion first - cleaning may fix the problem</li>
                <li style="margin-bottom: 8px;">Use GeekOBD APP to verify sensor readings before and after repair</li>
                <li style="margin-bottom: 8px;">Consider aftermarket sensors for older vehicles to save 30-50% on parts</li>
                <li style="margin-bottom: 8px;">Inspect entire air intake system while sensor is removed</li>
            </ul>
        </div>
    </div>
</div>
                    
<!-- Diagnostic Steps Section -->
<div id="diagnostic-steps" style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
    <h2><i class="fa fa-stethoscope"></i> Diagnostic Steps</h2>

    <div style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin: 20px 0;">
        <h3 style="color: #2c3e50; margin-bottom: 20px;">Professional P0113 Diagnosis Process</h3>
        <p style="margin-bottom: 20px; color: #666;">Follow these systematic steps to accurately diagnose P0113. Each step builds on the previous one to ensure accurate diagnosis.</p>

        
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-search"></i> Step 1: Initial Code Verification</h4>
            <p style="margin-bottom: 15px; color: #333;">Connect GeekOBD APP and verify P0113 code is present. Check for additional codes that may indicate related issues. Clear codes and test drive to see if P0113 returns immediately.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> Use GeekOBD APP's live data feature to monitor IAT sensor readings in real-time. A constant -40°F reading confirms the diagnosis.
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-eye"></i> Step 2: Visual Inspection</h4>
            <p style="margin-bottom: 15px; color: #333;">Locate IAT sensor (usually in air intake tube or MAF housing). Inspect sensor, connector, and wiring for obvious damage, corrosion, or contamination. Check for loose connections.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> Take photos with GeekOBD APP to document sensor condition and location for future reference.
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-bolt"></i> Step 3: Sensor Resistance Test</h4>
            <p style="margin-bottom: 15px; color: #333;">Disconnect IAT sensor connector and measure resistance across sensor terminals with multimeter. At 68°F, resistance should be approximately 2,500 ohms. Infinite resistance indicates sensor failure.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> Compare your readings with GeekOBD APP's sensor specifications database for your specific vehicle.
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-random"></i> Step 4: Wiring Circuit Test</h4>
            <p style="margin-bottom: 15px; color: #333;">If sensor tests good, check wiring continuity from sensor connector to ECM. Test for proper voltage supply (usually 5V) and ground circuit integrity.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> GeekOBD APP can display expected voltage values for your vehicle's IAT circuit during testing.
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-check"></i> Step 5: Repair and Verification</h4>
            <p style="margin-bottom: 15px; color: #333;">Replace faulty sensor or repair damaged wiring. Clear codes with GeekOBD APP and test drive vehicle. Monitor IAT readings to ensure proper operation and verify code does not return.</p>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> Use GeekOBD APP's post-repair monitoring feature to track IAT sensor performance over several drive cycles.
            </div>
        </div>

        <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin-top: 20px; border-left: 4px solid #ffc107;">
            <h4 style="color: #856404; margin-bottom: 15px;"><i class="fa fa-exclamation-triangle"></i> Important Notes</h4>
            <ul style="margin: 0; color: #856404;">
                <li style="margin-bottom: 8px;">Always disconnect battery before working on sensor connections</li>
                <li style="margin-bottom: 8px;">Use only specified torque when installing new sensor to avoid damage</li>
                <li style="margin-bottom: 8px;">Apply dielectric grease to connector to prevent future corrosion</li>
                <li style="margin-bottom: 8px;">Verify code does not return after several drive cycles</li>
            </ul>
        </div>
    </div>
</div>
                    
<!-- Real Repair Case Studies -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
    <h2><i class="fa fa-file-text"></i> Real Repair Case Studies</h2>
    
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2;"><i class="fa fa-car"></i> Case 1: 2017 Honda Civic - Faulty IAT Sensor</h4>
            <p><strong>Vehicle:</strong> 2017 Honda Civic LX 1.5L Turbo, 89,000 miles</p>
            <p><strong>Problem:</strong> Customer complained of hard starting in cold weather and poor fuel economy. GeekOBD scan revealed P0113 code with IAT sensor reading constant -40°F even when engine bay was warm.</p>
            <p><strong>Diagnosis:</strong> GeekOBD APP monitoring showed IAT sensor stuck at -40°F regardless of actual temperature. Resistance test confirmed sensor internal failure - infinite resistance instead of expected 2,500 ohms at 68°F.</p>
            <p><strong>Solution:</strong> Replaced IAT sensor with OEM Honda part. Sensor was integrated into air intake tube, requiring tube replacement. Cleared codes and verified proper temperature tracking.</p>
            <p><strong>Cost:</strong> $185 (IAT sensor/tube: $95, labor: $90)</p>
            <p><strong>Result:</strong> IAT sensor now reads accurate temperatures. Cold starting improved immediately and fuel economy returned to normal. Customer saved $200+ by avoiding unnecessary MAF sensor replacement.</p>
        </div>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 4px solid #28a745;">
            <h4 style="color: #28a745;"><i class="fa fa-car"></i> Case 2: 2018 Toyota Camry - Wiring Issue</h4>
            <p><strong>Vehicle:</strong> 2018 Toyota Camry LE 2.5L 4-cylinder, 65,000 miles</p>
            <p><strong>Problem:</strong> Intermittent P0113 code with occasional rough idle. IAT readings would jump between normal and -40°F during driving.</p>
            <p><strong>Diagnosis:</strong> GeekOBD APP data logging revealed intermittent signal loss. IAT sensor tested good with proper resistance. Found damaged signal wire in harness near battery tray from acid corrosion.</p>
            <p><strong>Solution:</strong> Repaired damaged signal wire with proper splice and heat shrink protection. Applied dielectric grease to connector to prevent future corrosion.</p>
            <p><strong>Cost:</strong> $125 (wire repair kit: $25, labor: $100)</p>
            <p><strong>Result:</strong> IAT sensor now provides accurate readings. Engine performance returned to normal and customer saved $150+ by avoiding unnecessary sensor replacement.</p>
        </div>
</div>
                </div>

                <div class="col-md-4">
                    
<!-- GeekOBD APP Promotion -->
<div style="background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%); color: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: white;"><i class="fa fa-thermometer-empty"></i> Monitor IAT Sensor</h4>
    <p style="margin-bottom: 20px; opacity: 0.9; font-size: 14px;">Track intake air temperature with our GeekOBD APP!</p>
    <ul style="margin-bottom: 20px; padding-left: 20px; font-size: 14px;">
        <li style="margin-bottom: 8px;">Real-time IAT readings</li>
        <li style="margin-bottom: 8px;">Temperature trend monitoring</li>
        <li style="margin-bottom: 8px;">Sensor response testing</li>
        <li style="margin-bottom: 8px;">Verify repair success</li>
    </ul>
    <a href="https://www.geekobd.com/app.html" style="display: inline-block; background: rgba(255,255,255,0.2); color: white; padding: 12px 25px; border-radius: 25px; text-decoration: none; font-weight: bold; transition: all 0.3s;">
        <i class="fa fa-download"></i> Download GeekOBD APP
    </a>
</div>

<!-- Related System Codes -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-link"></i> IAT System Codes</h4>
    <p style="margin-bottom: 15px; color: #666; font-size: 14px;">Related intake air temperature sensor diagnostic codes:</p>
    <div style="margin-bottom: 15px;">
        
                <a href="p0112.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #4a90e2;">
                    <strong style="color: #4a90e2;">P0112</strong> - IAT Sensor Low Input
                </a>
                <a href="p0114.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #28a745;">
                    <strong style="color: #28a745;">P0114</strong> - IAT Sensor Intermittent
                </a>
                <a href="p0110.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #ffc107;">
                    <strong style="color: #ffc107;">P0110</strong> - IAT Sensor Circuit
                </a>
                <a href="p0101.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #dc3545;">
                    <strong style="color: #dc3545;">P0101</strong> - MAF Sensor Range/Performance
                </a>
                <a href="p0102.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #6f42c1;">
                    <strong style="color: #6f42c1;">P0102</strong> - MAF Sensor Low Input
                </a>
                <a href="p0103.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #17a2b8;">
                    <strong style="color: #17a2b8;">P0103</strong> - MAF Sensor High Input
                </a>
                <a href="p0171.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #fd7e14;">
                    <strong style="color: #fd7e14;">P0171</strong> - System Too Lean Bank 1
                </a>
                <a href="p0172.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid #20c997;">
                    <strong style="color: #20c997;">P0172</strong> - System Too Rich Bank 1
                </a>
    </div>
</div>

<!-- Diagnostic Resources -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-tools"></i> Diagnostic Resources</h4>
    <div style="margin-bottom: 20px;">
        
        <a href="#diagnostic-steps" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-book" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">IAT Sensor Testing Guide</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">Step-by-step sensor testing procedures</span>
        </a>
        <a href="../resources/wiring-diagrams.html" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-sitemap" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">Wiring Diagrams</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">IAT sensor circuit diagrams and pinouts</span>
        </a>
        <a href="../resources/repair-procedures.html" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-wrench" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">Repair Procedures</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">Professional repair techniques and tips</span>
        </a>
    </div>
</div>

<!-- Quick Navigation -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-compass"></i> Quick Navigation</h4>
    <div style="display: flex; flex-direction: column; gap: 8px;">
        <a href="#quick-answer" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-bolt"></i> Quick Answer
        </a>
        <a href="#ai-qa" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-comments"></i> Common Questions
        </a>
        <a href="#cost-info" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-calculator"></i> Repair Costs
        </a>
        <a href="#diagnostic-steps" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-stethoscope"></i> Diagnostic Steps
        </a>
    </div>
</div>

<!-- Code Information -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-info-circle"></i> Code Information</h4>
    <table style="width: 100%; font-size: 14px;">
        <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Code:</strong></td>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;">P0113</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>System:</strong></td>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;">Engine Management</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Severity:</strong></td>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><span class="severity-badge severity-medium">MEDIUM</span></td>
        </tr>
        <tr>
            <td style="padding: 8px 0;"><strong>Category:</strong></td>
            <td style="padding: 8px 0;">Sensor Circuit</td>
        </tr>
    </table>
</div>
                </div>
            </div>
        </div>
    </section>

    </div>

<script src="../js/jquery.min.js"></script>
<script src="../js/bootstrap.js"></script>
<script src="../js/superfish.js"></script>
<script src="../js/custom.js"></script>
</body>
</html>