<!DOCTYPE html>
<!--[if IE 8]><html class="ie ie8"> <![endif]-->
<!--[if IE 9]><html class="ie ie9"> <![endif]-->
<!--[if gt IE 9]><!-->
<html>
<!--<![endif]-->

<head>
<meta charset="utf-8">
<title>P0172 - System Too Rich (Bank 1) | GeekOBD Diagnostic Guide</title>
<meta name="description" content="P0172 diagnostic trouble code: System Too Rich (Bank 1). Learn about symptoms, causes, diagnosis steps, and repair solutions for P0172 with GeekOBD professional tools.">
<meta name="keywords" content="P0172, P0172 code, P0172 diagnostic, system too rich, rich fuel mixture, oxygen sensor, fuel injector, OBD diagnostic code">
<meta name="author" content="Beijing MentalRoad Technology Co., Ltd.">
<meta name="robots" content="index, follow">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
<link rel="canonical" href="https://www.geekobd.com/dtc-codes/p0172.html">

<!-- Open Graph / Facebook -->
<meta property="og:type" content="article">
<meta property="og:url" content="https://www.geekobd.com/dtc-codes/p0172.html">
<meta property="og:title" content="P0172 - System Too Rich (Bank 1) | Diagnostic Code Guide">
<meta property="og:description" content="P0172 diagnostic trouble code: System Too Rich (Bank 1). Complete guide with symptoms, causes, and repair solutions.">
<meta property="og:image" content="https://www.geekobd.com/img/logo.png">
<meta property="og:site_name" content="GeekOBD">

<!-- Twitter -->
<meta property="twitter:card" content="summary_large_image">
<meta property="twitter:url" content="https://www.geekobd.com/dtc-codes/p0172.html">
<meta property="twitter:title" content="P0172 - System Too Rich (Bank 1) | Diagnostic Code Guide">
<meta property="twitter:description" content="P0172 diagnostic trouble code: System Too Rich (Bank 1). Complete guide with symptoms, causes, and repair solutions.">
<meta property="twitter:image" content="https://www.geekobd.com/img/logo.png">

<link rel="stylesheet" href="../css/bootstrap.css">
<link rel="stylesheet" href="../css/fonts/font-awesome/css/font-awesome.css">
<link rel="stylesheet" href="../css/animations.css" media="screen">
<link rel="stylesheet" href="../css/superfish.css" media="screen">
<link rel="stylesheet" href="../css/style.css">
<link rel="stylesheet" href="../css/colors/blue.css" id="colors">
<link rel="stylesheet" href="../css/theme-responsive.css">
<link rel="stylesheet" href="../css/seo-enhancements.css">
<link rel="shortcut icon" href="../img/ico/favicon.ico">

<!-- Custom styles for DTC pages -->
<style>
.dtc-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 60px 0 40px;
}

.dtc-code-badge {
    display: inline-block;
    background: rgba(255,255,255,0.2);
    padding: 10px 20px;
    border-radius: 50px;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
}

.severity-badge {
    display: inline-block;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
    margin-left: 15px;
}

.severity-high { background: #ff4757; color: white; }
.severity-medium { background: #ffa502; color: white; }
.severity-low { background: #2ed573; color: white; }

.content-section {
    padding: 50px 0;
}

.info-box {
    background: #f8f9fa;
    border-left: 4px solid #667eea;
    padding: 20px;
    margin: 20px 0;
    border-radius: 5px;
}

.warning-box {
    background: #fff3cd;
    border-left: 4px solid #ffc107;
    padding: 20px;
    margin: 20px 0;
    border-radius: 5px;
}

.step-list {
    counter-reset: step-counter;
    list-style: none;
    padding: 0;
}

.step-list li {
    counter-increment: step-counter;
    margin: 20px 0;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    position: relative;
    padding-left: 80px;
}

.step-list li::before {
    content: counter(step-counter);
    position: absolute;
    left: 20px;
    top: 20px;
    background: #667eea;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 18px;
}

.related-codes {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 15px;
    margin: 30px 0;
}

.code-link {
    display: inline-block;
    background: white;
    padding: 10px 15px;
    margin: 5px;
    border-radius: 25px;
    text-decoration: none;
    color: #667eea;
    font-weight: bold;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.code-link:hover {
    background: #667eea;
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
}

.breadcrumb-custom {
    background: none;
    padding: 20px 0;
    margin: 0;
}

.breadcrumb-custom a {
    color: #667eea;
    text-decoration: none;
}

.breadcrumb-custom a:hover {
    text-decoration: underline;
}

.toc {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
}

.toc ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.toc li {
    margin: 10px 0;
}

.toc a {
    color: #667eea;
    text-decoration: none;
    padding: 5px 0;
    display: block;
}

.toc a:hover {
    text-decoration: underline;
}
</style>

<!-- JSON-LD Structured Data -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": "P0172 - System Too Rich (Bank 1)",
  "description": "Complete diagnostic guide for P0172 trouble code including symptoms, causes, diagnosis steps, and repair solutions.",
  "author": {
    "@type": "Organization",
    "name": "GeekOBD",
    "url": "https://www.geekobd.com"
  },
  "publisher": {
    "@type": "Organization",
    "name": "GeekOBD",
    "logo": {
      "@type": "ImageObject",
      "url": "https://www.geekobd.com/img/logo.png"
    }
  },
  "datePublished": "2025-01-26",
  "dateModified": "2025-01-26",
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "https://www.geekobd.com/dtc-codes/p0172.html"
  },
  "articleSection": "Automotive Diagnostics",
  "keywords": "P0172, system too rich, rich fuel mixture, oxygen sensor, fuel injector",
  "about": {
    "@type": "Thing",
    "name": "P0172 Diagnostic Trouble Code",
    "description": "System too rich condition detected on bank 1 of the engine"
  }
}
</script>

<!-- FAQ Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "What does P0172 mean?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "P0172 indicates that the engine control module has detected a rich fuel condition on bank 1. This means there is too much fuel or not enough air in the air/fuel mixture."
      }
    },
    {
      "@type": "Question",
      "name": "What are the symptoms of P0172?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Common symptoms include check engine light, poor fuel economy, black exhaust smoke, rough idle, and strong fuel odor."
      }
    },
    {
      "@type": "Question",
      "name": "How do I fix P0172?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Common fixes include cleaning fuel injectors, replacing faulty oxygen sensors, checking fuel pressure regulator, and replacing air filter or MAF sensor."
      }
    },
    {
      "@type": "Question",
      "name": "How much does P0172 repair cost?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "P0172 repair costs typically range from $120-$800. Fuel injector cleaning costs $120-$250, while fuel pressure regulator replacement can cost $300-$600."
      }
    }
  ]
}
</script>

<!-- HowTo Schema for P0172 Diagnostic Steps -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "HowTo",
  "name": "How to Diagnose P0172 System Too Rich Bank 1",
  "description": "Step-by-step guide to diagnose and fix P0172 rich fuel mixture condition on bank 1",
  "image": "https://www.geekobd.com/img/logo.png",
  "totalTime": "PT70M",
  "estimatedCost": {
    "@type": "MonetaryAmount",
    "currency": "USD",
    "value": "120"
  },
  "tool": [
    {
      "@type": "HowToTool",
      "name": "GeekOBD APP with MOBD Adapter",
      "description": "Professional OBD2 diagnostic tool with fuel trim and O2 sensor monitoring",
      "url": "https://www.geekobd.com/app.html"
    },
    {
      "@type": "HowToTool",
      "name": "Fuel Pressure Gauge",
      "description": "For testing fuel system pressure and regulator function"
    },
    {
      "@type": "HowToTool",
      "name": "Digital Multimeter",
      "description": "For testing O2 sensor voltage and fuel injector resistance"
    }
  ],
  "supply": [
    {
      "@type": "HowToSupply",
      "name": "Fuel Injector Cleaner"
    },
    {
      "@type": "HowToSupply",
      "name": "New Air Filter"
    },
    {
      "@type": "HowToSupply",
      "name": "O2 Sensor (if needed)"
    }
  ],
  "step": [
    {
      "@type": "HowToStep",
      "position": 1,
      "name": "Scan and Check Fuel Trims",
      "text": "Connect GeekOBD APP and scan for P0172 code. Monitor live fuel trim data - negative LTFT values below -25% indicate rich condition on bank 1.",
      "image": "https://www.geekobd.com/img/geekobd-rich-fuel-trims.jpg"
    },
    {
      "@type": "HowToStep",
      "position": 2,
      "name": "Test Fuel System Pressure",
      "text": "Use fuel pressure gauge to test fuel pressure and regulator. High fuel pressure (above spec) can cause rich conditions. Check for stuck fuel pressure regulator.",
      "image": "https://www.geekobd.com/img/fuel-pressure-test-rich.jpg"
    },
    {
      "@type": "HowToStep",
      "position": 3,
      "name": "Check O2 Sensor Response",
      "text": "Monitor bank 1 O2 sensor voltage with GeekOBD APP. Stuck rich sensors (voltage above 0.8V) or slow response can cause P0172 codes.",
      "image": "https://www.geekobd.com/img/o2-sensor-rich-response.jpg"
    },
    {
      "@type": "HowToStep",
      "position": 4,
      "name": "Test Fuel Injectors",
      "text": "Check fuel injector resistance and spray patterns. Leaking or stuck-open injectors on bank 1 will cause rich conditions. Clean or replace as needed.",
      "image": "https://www.geekobd.com/img/fuel-injector-test-rich.jpg"
    },
    {
      "@type": "HowToStep",
      "position": 5,
      "name": "Verify Repair Success",
      "text": "Clear P0172 code with GeekOBD APP, drive for 15-20 minutes, and monitor fuel trims to ensure they return to normal range (-10% to +10%).",
      "image": "https://www.geekobd.com/img/geekobd-normal-fuel-trims.jpg"
    }
  ]
}
</script>

<!--[if lt IE 9]>
<script src="../js/html5.js"></script>
<script src="../js/css3-mediaqueries.js"></script>
<![endif]-->

<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-RD6767XBCL"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-RD6767XBCL');
</script>
</head>

<body>

<div class="wrap">
	<!-- Header Start -->
	<header id="header" role="banner">
	<!-- Main Header Start -->
	<div class="main-header">
	<div class="container">
	<div class="row">
	<div class="col-md-3">
	<!-- Logo Start -->
	<div class="logo pull-left">
	<h1> <a href="../index.html"> <img src="../img/logo.png" alt="MOBD"> </a> </h1>
	</div>
	<!-- Logo End --> 
	</div>
	<div class="col-md-9">
	<!-- Mobile Menu Start -->
	<div class="mobile navbar-header"> <a class="navbar-toggle" data-toggle="collapse" data-target=".menu"> <i class="icon-reorder icon-2x"></i> </a> </div>
	<!-- Mobile Menu End --> 
	<!-- Menu Start -->
	<nav class="collapse navbar-collapse menu" role="navigation" aria-label="Main navigation">
	<ul class="nav navbar-nav sf-menu">
	<li><a href="../index.html" class="sf-with-ul">Home</a></li>
	<li><a href="../app.html" class="sf-with-ul">APP</a></li>
	<li><a href="javascript:;;" class="sf-with-ul">Adaptor <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
	<ul>
	<li><a href="../hardware2.html" class="sf-with-ul">MOBD GPS</a></li>
	<li><a href="../hardware.html" class="sf-with-ul">MOBD</a></li>
	</ul>
	</li>
	<li><a href="javascript:;;" class="sf-with-ul">Resources <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
	<ul>
	<li><a href="../dtc-codes.html" class="sf-with-ul" id="current">DTC Codes</a></li>
	<li><a href="../obd-diagnostic-guide.html" class="sf-with-ul">OBD Guide</a></li>
	<li><a href="../vehicle-compatibility.html" class="sf-with-ul">Compatibility</a></li>
	<li><a href="../fuel-efficiency-monitoring.html" class="sf-with-ul">Fuel Efficiency</a></li>
	<li><a href="../support.html" class="sf-with-ul">Support</a></li>
	<li><a href="../blog.html" class="sf-with-ul">Blog</a></li>
	</ul>
	</li>
	<li><a href="../about.html" class="sf-with-ul">About Us</a></li>
	<li><a href="//www.mobd.cn" class="sf-with-ul" target="_blank">中文版</a></li>
	</ul>
	</nav>
	<!-- Menu End --> 
	</div>
	</div>
	<!-- Main Header End --> 
	</div>
	</div>
	</header>
	<!-- Header End -->

	<!-- Breadcrumb -->
	<div class="container">
		<nav class="breadcrumb-custom">
			<a href="../index.html">Home</a> &raquo; 
			<a href="../dtc-codes.html">DTC Codes</a> &raquo; 
			<a href="../dtc-codes.html#engine">Engine Codes</a> &raquo; 
			<span>P0172</span>
		</nav>
	</div>

	<!-- DTC Header -->
	<section class="dtc-header">
		<div class="container">
			<div class="row">
				<div class="col-md-12">
					<div class="dtc-code-badge">P0172</div>
					<span class="severity-badge severity-medium">MEDIUM Priority</span>
					<h1 style="margin: 20px 0; font-size: 36px;">System Too Rich (Bank 1)</h1>
					<p style="font-size: 18px; opacity: 0.9; margin-bottom: 0;">The engine control module has detected a rich fuel condition on bank 1 of the engine.</p>
				</div>
			</div>
		</div>
	</section>

	<!-- Main Content -->
	<section class="content-section">
		<div class="container">
			<div class="row">
				<div class="col-md-8">
					<!-- Quick Answer Section for AI -->
					<div id="quick-answer" style="background: #e8f4fd; border: 2px solid #2196F3; border-radius: 10px; padding: 25px; margin-bottom: 30px;">
						<h2 style="color: #1976D2; margin-bottom: 15px;"><i class="fa fa-bolt"></i> Quick Answer</h2>
						<p style="font-size: 18px; font-weight: 500; margin-bottom: 15px; color: #333;">
							<strong>P0172 means:</strong> Your engine is running too rich (too much fuel, not enough air) on bank 1, causing poor fuel economy and black smoke.
						</p>
						<div style="display: flex; flex-wrap: wrap; gap: 15px; margin-bottom: 15px;">
							<span style="background: #4CAF50; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
								<i class="fa fa-wrench"></i> Fix: Clean fuel injectors + check fuel pressure
							</span>
							<span style="background: #FF9800; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
								<i class="fa fa-dollar"></i> Cost: $120-$800
							</span>
							<span style="background: #2196F3; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
								<i class="fa fa-clock-o"></i> Time: 70 minutes
							</span>
						</div>
						<p style="margin: 0; color: #666; font-size: 14px;">
							<strong>Can I drive with P0172?</strong> Yes, short distances are OK, but fix soon to prevent catalytic converter damage.
						</p>
					</div>

					<!-- AI-Friendly Q&A Section -->
					<div id="ai-qa" style="background: white; border-radius: 10px; padding: 25px; margin-bottom: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
						<h2><i class="fa fa-comments"></i> Common Questions</h2>

						<div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
							<h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">What's the difference between P0171 and P0172?</h3>
							<p style="color: #666; line-height: 1.6;">P0171 = too lean (not enough fuel), P0172 = too rich (too much fuel). Both affect Bank 1. P0172 causes black smoke and poor fuel economy, while P0171 causes rough idle and potential engine damage from running lean.</p>
						</div>

						<div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
							<h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">What causes P0172 rich condition?</h3>
							<p style="color: #666; line-height: 1.6;">P0172 is commonly caused by leaking fuel injectors, high fuel pressure from faulty regulator, dirty air filter restricting airflow, or faulty O2 sensors giving incorrect readings to the ECM.</p>
						</div>

						<div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
							<h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">Will P0172 damage my engine?</h3>
							<p style="color: #666; line-height: 1.6;">P0172 won't immediately damage your engine, but prolonged rich conditions can foul spark plugs, damage the catalytic converter (expensive repair), and cause carbon buildup in the combustion chambers.</p>
						</div>

						<div class="qa-item" style="margin-bottom: 0;">
							<h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">How do I know if fuel injectors are causing P0172?</h3>
							<p style="color: #666; line-height: 1.6;">Use GeekOBD APP to monitor fuel trims. If Bank 1 LTFT is below -25%, test fuel injector resistance and spray patterns. Leaking injectors will show low resistance or poor spray patterns and cause rich conditions.</p>
						</div>
					</div>

					<!-- Overview Section -->
					<div id="overview">
						<h2><i class="fa fa-info-circle"></i> Technical Overview</h2>
						<div class="info-box">
							<h4>P0172 Definition</h4>
							<p>P0172 indicates that the Engine Control Module (ECM) has detected a rich fuel condition on bank 1 of the engine. This means there is too much fuel or not enough air in the air/fuel mixture. The ECM uses oxygen sensors to monitor the exhaust gases and determine if the fuel mixture is correct. When the mixture is too rich, it can cause poor fuel economy, increased emissions, and potential engine damage.</p>
						</div>

						<h3>Technical Details</h3>
						<ul>
							<li><strong>Code Type:</strong> Powertrain Diagnostic Trouble Code</li>
							<li><strong>System:</strong> Fuel System / Engine Management</li>
							<li><strong>Severity:</strong> Medium - Can cause poor performance and emissions</li>
							<li><strong>Driving Safety:</strong> Safe to drive short distances, but should be repaired soon</li>
						</ul>
					</div>

					<!-- Symptoms Section -->
					<div id="symptoms">
						<h2><i class="fa fa-exclamation-triangle"></i> Symptoms</h2>
						<p>When P0172 is triggered, you may experience:</p>
						<ul>
							<li>Check Engine Light illuminated</li>
							<li>Poor fuel economy</li>
							<li>Black smoke from exhaust</li>
							<li>Strong fuel smell from exhaust</li>
							<li>Rough idle or engine hesitation</li>
							<li>Engine may run poorly or stall</li>
							<li>Spark plugs may foul quickly</li>
							<li>Failed emissions test</li>
							<li>Possible catalytic converter damage over time</li>
						</ul>

						<div class="warning-box">
							<strong><i class="fa fa-warning"></i> Important:</strong> A rich fuel condition can damage the catalytic converter and cause poor fuel economy. While not immediately dangerous, it should be diagnosed and repaired promptly.
						</div>
					</div>

					<!-- Causes Section -->
					<div id="causes">
						<h2><i class="fa fa-search"></i> Possible Causes</h2>
						<p>P0172 can be triggered by several different issues:</p>
						<ol>
							<li><strong>Faulty oxygen sensor (O2 sensor)</strong> - Most common cause</li>
							<li><strong>Dirty or clogged air filter</strong></li>
							<li><strong>Leaking or faulty fuel injectors</strong></li>
							<li><strong>Faulty Mass Air Flow (MAF) sensor</strong></li>
							<li><strong>High fuel pressure</strong></li>
							<li><strong>Faulty fuel pressure regulator</strong></li>
							<li><strong>Vacuum leaks</strong></li>
							<li><strong>Faulty Engine Coolant Temperature (ECT) sensor</strong></li>
							<li><strong>Carbon buildup in intake system</strong></li>
							<li><strong>Faulty ECM (rare)</strong></li>
						</ol>
					</div>

					<!-- Diagnosis Section -->
					<div id="diagnosis">
						<h2><i class="fa fa-wrench"></i> Diagnosis Steps</h2>
						<ol>
							<li><strong>Scan for codes</strong> - Use GeekOBD to confirm P0172 and check for related codes</li>
							<li><strong>Check air filter</strong> - Inspect and replace if dirty or clogged</li>
							<li><strong>Test oxygen sensors</strong> - Check O2 sensor operation and response</li>
							<li><strong>Inspect MAF sensor</strong> - Clean or test Mass Air Flow sensor</li>
							<li><strong>Check fuel pressure</strong> - Test fuel pressure and pressure regulator</li>
							<li><strong>Inspect for vacuum leaks</strong> - Check intake manifold and vacuum lines</li>
							<li><strong>Test fuel injectors</strong> - Check for leaking or stuck-open injectors</li>
						</ol>
					</div>

					<!-- Related Codes Section -->
					<div id="related" class="related-codes">
						<h2><i class="fa fa-link"></i> Related Diagnostic Codes</h2>

						<div style="margin-bottom: 25px;">
							<h3 style="color: #333; font-size: 18px; margin-bottom: 15px;">Rich/Lean Condition Codes</h3>
							<p style="margin-bottom: 15px; color: #666;">These fuel mixture codes often appear with P0172:</p>
							<div style="margin-bottom: 20px;">
								<a href="p0171.html" class="code-link" title="System Too Lean Bank 1">P0171 - System Too Lean Bank 1</a>
								<a href="p0174.html" class="code-link" title="System Too Lean Bank 2">P0174 - System Too Lean Bank 2</a>
								<a href="p0175.html" class="code-link" title="System Too Rich Bank 2">P0175 - System Too Rich Bank 2</a>
								<a href="p0173.html" class="code-link" title="Fuel Trim Malfunction Bank 2">P0173 - Fuel Trim Bank 2</a>
							</div>
						</div>

						<div style="margin-bottom: 25px;">
							<h3 style="color: #333; font-size: 18px; margin-bottom: 15px;">Fuel System Related Codes</h3>
							<p style="margin-bottom: 15px; color: #666;">Fuel system issues that can cause P0172:</p>
							<div style="margin-bottom: 20px;">
								<a href="p0201.html" class="code-link" title="Injector Circuit Malfunction Cylinder 1">P0201 - Injector Circuit #1</a>
								<a href="p0202.html" class="code-link" title="Injector Circuit Malfunction Cylinder 2">P0202 - Injector Circuit #2</a>
								<a href="p0300.html" class="code-link" title="Random Multiple Cylinder Misfire">P0300 - Random Misfire</a>
								<a href="p0420.html" class="code-link" title="Catalyst System Efficiency">P0420 - Catalyst Efficiency</a>
							</div>
						</div>

						<div style="margin-bottom: 25px;">
							<h3 style="color: #333; font-size: 18px; margin-bottom: 15px;">MAF/Air Intake Codes</h3>
							<p style="margin-bottom: 15px; color: #666;">Air intake issues that can cause rich conditions:</p>
							<div style="margin-bottom: 20px;">
								<a href="p0100.html" class="code-link" title="MAF Circuit Malfunction">P0100 - MAF Circuit Malfunction</a>
								<a href="p0101.html" class="code-link" title="MAF Range/Performance Problem">P0101 - MAF Range/Performance</a>
								<a href="p0102.html" class="code-link" title="MAF Circuit Low Input">P0102 - MAF Circuit Low</a>
								<a href="p0130.html" class="code-link" title="O2 Sensor Circuit Bank 1">P0130 - O2 Sensor Bank 1</a>
							</div>
						</div>

						<div style="background: #f8f9fa; padding: 20px; border-radius: 10px;">
							<h3 style="color: #333; font-size: 18px; margin-bottom: 15px;">System Categories</h3>
							<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
								<div>
									<h4 style="color: #667eea; margin-bottom: 10px;"><a href="engine/" style="color: #667eea; text-decoration: none;">Engine Codes (P0XXX)</a></h4>
									<ul style="list-style: none; padding: 0; margin: 0;">
										<li style="margin-bottom: 5px;"><a href="../dtc-codes.html#engine" style="color: #666; text-decoration: none; font-size: 14px;">View all engine codes →</a></li>
									</ul>
								</div>
								<div>
									<h4 style="color: #667eea; margin-bottom: 10px;"><a href="body/" style="color: #667eea; text-decoration: none;">Body Codes (B0XXX)</a></h4>
									<ul style="list-style: none; padding: 0; margin: 0;">
										<li style="margin-bottom: 5px;"><a href="../dtc-codes.html#body" style="color: #666; text-decoration: none; font-size: 14px;">View all body codes →</a></li>
									</ul>
								</div>
								<div>
									<h4 style="color: #667eea; margin-bottom: 10px;"><a href="chassis/" style="color: #667eea; text-decoration: none;">Chassis Codes (C0XXX)</a></h4>
									<ul style="list-style: none; padding: 0; margin: 0;">
										<li style="margin-bottom: 5px;"><a href="../dtc-codes.html#chassis" style="color: #666; text-decoration: none; font-size: 14px;">View all chassis codes →</a></li>
									</ul>
								</div>
								<div>
									<h4 style="color: #667eea; margin-bottom: 10px;"><a href="network/" style="color: #667eea; text-decoration: none;">Network Codes (U0XXX)</a></h4>
									<ul style="list-style: none; padding: 0; margin: 0;">
										<li style="margin-bottom: 5px;"><a href="../dtc-codes.html#network" style="color: #666; text-decoration: none; font-size: 14px;">View all network codes →</a></li>
									</ul>
								</div>
							</div>
						</div>
					</div>

					<!-- Repair Cost Information -->
					<div id="cost-info" style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
						<h2><i class="fa fa-calculator"></i> P0172 Repair Costs</h2>

						<div style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin: 20px 0;">
							<h3 style="color: #2c3e50; margin-bottom: 20px;">Cost Breakdown by Repair Type</h3>

							<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 25px;">
								<div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #4CAF50;">
									<h4 style="color: #4CAF50; margin-bottom: 10px;"><i class="fa fa-filter"></i> Air Filter + MAF Cleaning</h4>
									<p style="margin-bottom: 15px; color: #666;">Most common P0172 fix</p>
									<ul style="list-style: none; padding: 0;">
										<li style="margin-bottom: 8px;"><strong>Parts:</strong> $35-50 (filter + cleaner)</li>
										<li style="margin-bottom: 8px;"><strong>Labor:</strong> $80-100 (1-1.25 hours)</li>
										<li style="margin-bottom: 8px;"><strong>Total:</strong> <span style="color: #4CAF50; font-weight: bold;">$115-150</span></li>
										<li style="color: #666; font-size: 14px;">Success rate: ~55%</li>
									</ul>
								</div>

								<div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #FF9800;">
									<h4 style="color: #FF9800; margin-bottom: 10px;"><i class="fa fa-cog"></i> Fuel Injector Service</h4>
									<p style="margin-bottom: 15px; color: #666;">Professional cleaning/replacement</p>
									<ul style="list-style: none; padding: 0;">
										<li style="margin-bottom: 8px;"><strong>Cleaning service:</strong> $150-300</li>
										<li style="margin-bottom: 8px;"><strong>Injector replacement:</strong> $400-800</li>
										<li style="margin-bottom: 8px;"><strong>Single injector:</strong> $120-250</li>
										<li style="color: #666; font-size: 14px;">Success rate: ~85%</li>
									</ul>
								</div>
							</div>

							<div style="background: #fff3e0; padding: 20px; border-radius: 8px; border-left: 4px solid #FF9800; margin-bottom: 20px;">
								<h4 style="color: #F57C00; margin-bottom: 15px;"><i class="fa fa-exclamation-triangle"></i> Additional P0172 Repair Costs</h4>
								<p style="margin-bottom: 10px; color: #333;">P0172 may require additional repairs:</p>
								<ul style="margin: 0; color: #333;">
									<li style="margin-bottom: 8px;"><strong>Fuel pressure regulator:</strong> $200-450 (if pressure too high)</li>
									<li style="margin-bottom: 8px;"><strong>O2 sensor replacement:</strong> $180-350 (Bank 1 sensors)</li>
									<li style="margin-bottom: 8px;"><strong>Catalytic converter:</strong> $800-2000 (if damaged by rich condition)</li>
									<li style="margin-bottom: 8px;"><strong>Spark plug replacement:</strong> $80-200 (if fouled by rich mixture)</li>
									<li><strong>Diagnostic fee:</strong> $100-150 (often waived with repair)</li>
								</ul>
							</div>

							<div style="background: #e3f2fd; padding: 20px; border-radius: 8px; border-left: 4px solid #2196F3;">
								<h4 style="color: #1976D2; margin-bottom: 15px;"><i class="fa fa-lightbulb-o"></i> Money-Saving Tips for P0172</h4>
								<ul style="margin: 0; color: #333;">
									<li style="margin-bottom: 10px;">Start with air filter and MAF cleaning - cheapest fixes first</li>
									<li style="margin-bottom: 10px;">Test fuel pressure before replacing expensive components</li>
									<li style="margin-bottom: 10px;">Use GeekOBD APP to monitor fuel trims and confirm repair success</li>
									<li style="margin-bottom: 10px;">Consider fuel injector cleaning service before replacement</li>
									<li>Address P0172 quickly to prevent catalytic converter damage</li>
								</ul>
							</div>
						</div>
					</div>

					<!-- Case Study -->
					<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
						<h2><i class="fa fa-file-text"></i> Real Repair Case Studies</h2>

						<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
							<h4>Case 1: 2017 Toyota Camry - Air Filter/MAF Issue</h4>
							<p><strong>Vehicle:</strong> 2017 Toyota Camry LE 2.5L, 89,000 miles</p>
							<p><strong>Problem:</strong> Customer reported poor fuel economy and check engine light. GeekOBD scan showed P0172 code with Bank 1 LTFT at -18%.</p>
							<p><strong>Solution:</strong> Diagnosis revealed a severely dirty air filter and contaminated MAF sensor restricting airflow. Replaced the air filter and cleaned the MAF sensor. Cleared codes and test drove - fuel economy improved significantly.</p>
							<p><strong>Cost:</strong> $95 (parts: $35, labor: $60)</p>
							<p><strong>Time:</strong> 45 minutes</p>
						</div>

						<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
							<h4>Case 2: 2015 Honda Accord - Leaking Fuel Injector</h4>
							<p><strong>Vehicle:</strong> 2015 Honda Accord EX 2.4L, 105,000 miles</p>
							<p><strong>Problem:</strong> P0172 code with strong fuel smell and black exhaust smoke. Bank 1 LTFT showing -28%.</p>
							<p><strong>Solution:</strong> Found cylinder #2 fuel injector leaking internally. Replaced the faulty injector and performed fuel system cleaning. Issue completely resolved.</p>
							<p><strong>Cost:</strong> $385 (parts: $165, labor: $220)</p>
							<p><strong>Time:</strong> 2.5 hours</p>
						</div>
					</div>
				</div>

				<!-- Sidebar -->
				<div class="col-md-4">
					<!-- GeekOBD APP Promotion -->
					<div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
						<h4 style="margin-bottom: 20px; color: white;"><i class="fa fa-mobile"></i> Monitor Fuel System</h4>
						<p style="margin-bottom: 20px; opacity: 0.9; font-size: 14px;">Track fuel mixture and oxygen sensor data with our GeekOBD APP!</p>
						<ul style="margin-bottom: 20px; padding-left: 20px; font-size: 14px;">
							<li style="margin-bottom: 8px;">Real-time fuel trim monitoring</li>
							<li style="margin-bottom: 8px;">Oxygen sensor data analysis</li>
							<li style="margin-bottom: 8px;">Fuel system diagnostics</li>
							<li style="margin-bottom: 8px;">Clear codes after repair</li>
						</ul>
						<a href="../app.html" class="btn btn-block" style="background: white; color: #667eea; border: none; border-radius: 25px; font-weight: bold; margin-bottom: 10px;">
							<i class="fa fa-download"></i> Download APP
						</a>
						<a href="../hardware.html" class="btn btn-block" style="background: rgba(255,255,255,0.2); color: white; border: 2px solid white; border-radius: 25px; font-weight: bold;">
							<i class="fa fa-shopping-cart"></i> Get MOBD Adapter
						</a>
					</div>

					<!-- Code Information -->
					<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
						<h4 style="margin-bottom: 20px;"><i class="fa fa-info"></i> Code Information</h4>
						<table class="table table-borderless">
							<tr>
								<td><strong>Code:</strong></td>
								<td>P0172</td>
							</tr>
							<tr>
								<td><strong>System:</strong></td>
								<td>Fuel System</td>
							</tr>
							<tr>
								<td><strong>Severity:</strong></td>
								<td><span class="severity-badge severity-medium">MEDIUM</span></td>
							</tr>
							<tr>
								<td><strong>Category:</strong></td>
								<td>Fuel System Codes</td>
							</tr>
						</table>
					</div>

					<!-- Popular Codes -->
					<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
						<h4 style="margin-bottom: 20px;"><i class="fa fa-star"></i> Related Codes</h4>
						<ul style="list-style: none; padding: 0;">
							<li style="margin-bottom: 10px;"><a href="p0171.html" style="color: #667eea;">P0171 - System Too Lean Bank 1</a></li>
							<li style="margin-bottom: 10px;"><a href="p0174.html" style="color: #667eea;">P0174 - System Too Lean Bank 2</a></li>
							<li style="margin-bottom: 10px;"><a href="p0175.html" style="color: #667eea;">P0175 - System Too Rich Bank 2</a></li>
							<li style="margin-bottom: 10px;"><a href="p0300.html" style="color: #667eea;">P0300 - Random Misfire</a></li>
							<li><a href="../dtc-codes.html" style="color: #667eea;">View All Codes →</a></li>
						</ul>
					</div>

					<!-- Diagnostic Tools -->
					<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
						<h4 style="margin-bottom: 20px;"><i class="fa fa-wrench"></i> Diagnostic Resources</h4>
						<ul style="list-style: none; padding: 0;">
							<li style="margin-bottom: 10px;"><a href="../obd-diagnostic-guide.html" style="color: #667eea;"><i class="fa fa-book"></i> OBD Diagnostic Guide</a></li>
							<li style="margin-bottom: 10px;"><a href="../vehicle-compatibility.html" style="color: #667eea;"><i class="fa fa-car"></i> Vehicle Compatibility</a></li>
							<li style="margin-bottom: 10px;"><a href="../fuel-efficiency-monitoring.html" style="color: #667eea;"><i class="fa fa-tachometer"></i> Fuel Efficiency Tips</a></li>
							<li style="margin-bottom: 10px;"><a href="../support.html" style="color: #667eea;"><i class="fa fa-support"></i> Technical Support</a></li>
							<li><a href="../blog.html" style="color: #667eea;"><i class="fa fa-newspaper-o"></i> Latest Articles</a></li>
						</ul>
					</div>

					<!-- Quick Navigation -->
					<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
						<h4 style="margin-bottom: 20px;"><i class="fa fa-compass"></i> Quick Navigation</h4>
						<div style="display: flex; flex-direction: column; gap: 10px;">
							<a href="#quick-answer" style="color: #667eea; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
								<i class="fa fa-bolt"></i> Quick Answer
							</a>
							<a href="#ai-qa" style="color: #667eea; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
								<i class="fa fa-comments"></i> Common Questions
							</a>
							<a href="#symptoms" style="color: #667eea; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
								<i class="fa fa-exclamation-triangle"></i> Symptoms
							</a>
							<a href="#causes" style="color: #667eea; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
								<i class="fa fa-search"></i> Causes
							</a>
							<a href="#cost-info" style="color: #667eea; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
								<i class="fa fa-calculator"></i> Repair Costs
							</a>
							<a href="#related" style="color: #667eea; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
								<i class="fa fa-link"></i> Related Codes
							</a>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Footer -->
	<div class="footer">
		<div class="container">
			<div class="row">
				<div class="col-md-12 text-center">
					<p>&copy; 2025 Beijing MentalRoad Technology Co., Ltd. All rights reserved.</p>
				</div>
			</div>
		</div>
	</div>
</div>

<!-- JavaScript -->
<script src="../js/jquery.min.js"></script>
<script src="../js/bootstrap.js"></script>
<script src="../js/superfish.js"></script>
<script src="../js/custom.js"></script>

</body>
</html>
