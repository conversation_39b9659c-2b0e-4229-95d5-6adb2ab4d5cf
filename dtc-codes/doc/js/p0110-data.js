const { DTCData } = require('./dtc-template-generator');

// P0110 IAT Sensor Circuit Malfunction 的完整数据结构
const p0110Data = new DTCData({
  code: 'P0110',
  title: 'IAT Sensor Circuit Malfunction',
  description: 'The Engine Control Module has detected an electrical malfunction in the Intake Air Temperature sensor circuit.',
  definition: 'The Engine Control Module has detected an electrical malfunction in the Intake Air Temperature (IAT) sensor circuit. This indicates a problem with the electrical components of the IAT sensor system rather than the sensor readings themselves. The IAT sensor circuit includes the sensor, wiring harness, connector, and ECM connections. P0110 is triggered when there are voltage irregularities, open circuits, short circuits, or other electrical faults that prevent proper communication between the IAT sensor and ECM.',
  
  symptoms: [
    'Check engine light illuminated - ECM detected IAT sensor circuit electrical fault',
    'Engine running in default mode - ECM using backup air temperature calculations',
    'Poor cold weather performance - Incorrect fuel mixture during cold starts',
    'Rough idle when cold - ECM unable to compensate for actual air temperature',
    'Hard starting in cold conditions - Improper fuel enrichment calculations',
    'Poor fuel economy - Non-optimized fuel delivery without IAT data',
    'Engine hesitation during warm-up - Incorrect air/fuel mixture calculations',
    'Black smoke during cold start - Rich mixture from default cold-start settings',
    'Failed emissions test - Engine not operating with optimal air/fuel ratios'
  ],
  
  causes: [
    'Open circuit in IAT sensor wiring - Broken wire preventing signal transmission',
    'Short circuit in IAT sensor harness - Wire touching ground or power',
    'Faulty IAT sensor connector - Corroded, damaged, or loose connection',
    'Failed IAT sensor - Internal electrical failure in sensor components',
    'ECM internal fault - Control module unable to process IAT signals',
    'Damaged wiring harness - Physical damage from heat, vibration, or rodents',
    'Poor ground connection - Inadequate ground circuit for IAT sensor',
    'Water damage to electrical components - Moisture causing circuit problems'
  ],
  
  performanceImpact: 'P0110 forces the ECM to operate without IAT sensor data, using default air temperature assumptions that may not match actual conditions, resulting in poor cold-weather performance, fuel economy issues, and emissions problems.',
  
  quickAnswer: {
    icon: 'flash',
    meaning: 'Electrical problem in IAT sensor circuit - wiring, connector, or sensor electrical failure.',
    fix: 'Check wiring, test connections, replace IAT sensor if needed',
    cost: '$75-$280',
    time: '45-90 minutes',
    drivingSafety: 'Safe to drive but expect poor cold-weather performance. Repair soon for optimal fuel economy and emissions.'
  },
  
  aiQuestions: [
    {
      question: 'What\'s the difference between P0110 and P0112/P0113 IAT codes?',
      answer: 'P0110 indicates an electrical circuit problem (wiring, connector, or sensor electrical failure), while P0112/P0113 indicate the sensor is working electrically but providing readings outside expected range. P0110 is typically easier to diagnose with electrical testing.'
    },
    {
      question: 'Can cold weather cause P0110?',
      answer: 'Cold weather itself doesn\'t cause P0110, but it can worsen existing electrical problems. Brittle wiring may crack in cold, corroded connections may fail, and moisture can freeze in connectors. P0110 indicates an electrical fault, not a temperature reading issue.'
    },
    {
      question: 'How do I test IAT sensor circuit for P0110?',
      answer: 'Use a multimeter to check for 5V reference voltage, good ground, and signal wire continuity. GeekOBD APP can show if the ECM is receiving any signal from the IAT sensor - no data or fixed values indicate circuit problems rather than sensor range issues.'
    },
    {
      question: 'Why does P0110 affect cold-weather starting?',
      answer: 'Without IAT sensor data, the ECM cannot accurately determine how much extra fuel is needed for cold starts. It uses default assumptions that may not match actual air temperature, resulting in too much or too little fuel enrichment during cold-weather starting.'
    }
  ],

  costAnalysis: {
    averageCost: '$75-$280 for most P0110 repairs',
    repairOptions: [
      {
        title: 'IAT Sensor Replacement',
        description: 'Most common fix - Replace sensor with internal electrical failure (65% of cases)',
        color: '#4CAF50',
        icon: 'thermometer-full',
        items: [
          { name: 'IAT sensor', cost: '$25-$85' },
          { name: 'Labor (30-60 minutes)', cost: '$50-$120' }
        ],
        total: '$75-$205',
        successRate: '95% success rate'
      },
      {
        title: 'Wiring Harness Repair',
        description: 'Fix damaged IAT sensor wiring (25% of cases)',
        color: '#FF9800',
        icon: 'flash',
        items: [
          { name: 'Wiring repair materials', cost: '$20-$50' },
          { name: 'Diagnostic time', cost: '$80-$120' },
          { name: 'Labor (1-1.5 hours)', cost: '$100-$180' }
        ],
        total: '$200-$350',
        successRate: '90% success rate'
      },
      {
        title: 'Connector Service',
        description: 'Clean or replace corroded IAT sensor connector (10% of cases)',
        color: '#2196F3',
        icon: 'plug',
        items: [
          { name: 'Connector cleaning/replacement', cost: '$15-$45' },
          { name: 'Dielectric grease', cost: '$8-$15' },
          { name: 'Labor (30-45 minutes)', cost: '$50-$90' }
        ],
        total: '$73-$150',
        successRate: '85% success rate'
      }
    ],
    savingTips: [
      'Check connector first - 15% of P0110 cases are just corroded connections',
      'IAT sensors are usually easy to access - consider DIY replacement to save labor',
      'Use multimeter to test circuits before replacing expensive components',
      'GeekOBD APP can help identify if problem is sensor or wiring related',
      'Address moisture source to prevent recurrence of circuit problems'
    ]
  },

  diagnosticSteps: {
    estimatedTime: 'PT60M',
    steps: [
      {
        title: 'Check for IAT Sensor Data',
        icon: 'search',
        description: 'Connect GeekOBD APP and check if IAT sensor data is available. With P0110, you may see no data, fixed values, or complete absence of temperature readings.',
        geekobdTip: 'GeekOBD APP will show if ECM is receiving IAT sensor signals - complete absence of data indicates circuit failure rather than sensor range issues.'
      },
      {
        title: 'Visual Inspection',
        icon: 'eye',
        description: 'Inspect IAT sensor, wiring harness, and connector for obvious damage. Look for corroded pins, damaged wires, water damage, or signs of physical damage to sensor.',
        geekobdTip: 'Use GeekOBD APP to monitor for any signal while wiggling wires - intermittent data indicates wiring problems.'
      },
      {
        title: 'Electrical Circuit Testing',
        icon: 'bolt',
        description: 'Test IAT sensor power supply (5V reference), ground circuit, and signal wire continuity with multimeter. Check for proper voltage at sensor connector with key on.',
        geekobdTip: 'GeekOBD APP should show stable readings when circuits are good - use multimeter to verify actual voltages match expected values.'
      },
      {
        title: 'Sensor Resistance Testing',
        icon: 'thermometer-full',
        description: 'Disconnect IAT sensor and test resistance across terminals. Compare readings to temperature specifications - infinite resistance indicates open sensor.',
        geekobdTip: 'Monitor GeekOBD APP while testing - sensor should show temperature readings when good sensor is connected to good circuit.'
      },
      {
        title: 'Component Replacement and Verification',
        icon: 'check-circle',
        description: 'Replace faulty wiring, connector, or IAT sensor as diagnosed. Clear codes and verify IAT sensor data is now available and responding to temperature changes.',
        geekobdTip: 'GeekOBD APP should now show stable IAT sensor readings that respond appropriately to temperature changes, confirming circuit repair.'
      }
    ],
    importantNotes: [
      'P0110 is electrical circuit problem, not sensor range issue',
      'Test circuits with multimeter before replacing components',
      'IAT sensor affects cold-weather performance most significantly'
    ]
  },

  caseStudies: [
    {
      title: 'Ford Focus Rodent Damage',
      vehicle: '2016 Ford Focus 2.0L 4-cylinder, 87,000 miles',
      problem: 'Customer reported poor cold-weather starting and rough idle when cold. P0110 code was present along with evidence of rodent activity in engine bay.',
      diagnosis: 'GeekOBD APP showed no IAT sensor data available. Visual inspection revealed IAT sensor wiring had been chewed by rodents, creating open circuit in signal wire.',
      solution: 'Repaired damaged IAT sensor wiring and installed protective conduit to prevent future rodent damage. Used proper automotive wire and heat-shrink connections.',
      cost: 'Wiring repair kit: $25, Protective conduit: $15, Labor: $95, Total: $135',
      result: 'P0110 code cleared immediately. Cold-weather starting improved significantly and IAT data now available for proper fuel calculations.'
    },
    {
      title: 'Subaru Impreza Corroded Connector',
      vehicle: '2017 Subaru Impreza 2.0L 4-cylinder, 75,000 miles',
      problem: 'Intermittent P0110 code with poor fuel economy and occasional rough idle. Problem seemed worse in wet weather conditions.',
      diagnosis: 'Found IAT sensor connector had severe corrosion on all pins, creating intermittent open circuits. GeekOBD APP showed IAT data would disappear randomly, especially in humid conditions.',
      solution: 'Cleaned corroded connector pins thoroughly, applied dielectric grease, and ensured proper connector seal to prevent future moisture intrusion.',
      cost: 'Connector cleaning kit: $18, Dielectric grease: $10, Labor: $75, Total: $103',
      result: 'P0110 code has not returned after 8 months including through wet winter weather. IAT sensor data remains stable in all conditions.'
    }
  ],

  relatedCodes: [
    { code: 'P0112', description: 'IAT Sensor Low Input - Sensor reading too hot temperatures', color: '#e74c3c' },
    { code: 'P0113', description: 'IAT Sensor High Input - Sensor reading too cold temperatures', color: '#3498db' },
    { code: 'P0114', description: 'IAT Sensor Intermittent - Intermittent sensor readings', color: '#f39c12' },
    { code: 'P0171', description: 'System Too Lean Bank 1 - Can result from missing IAT data', color: '#9b59b6' },
    { code: 'P0172', description: 'System Too Rich Bank 1 - Can result from default fuel maps', color: '#9b59b6' },
    { code: 'P0101', description: 'MAF Sensor Range/Performance - Related air intake measurement', color: '#4a90e2' },
    { code: 'P0300', description: 'Random Misfire - Poor performance from non-optimal fuel delivery', color: '#e67e22' }
  ],

  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P0110',
      description: 'Use GeekOBD APP for IAT sensor circuit diagnosis!',
      features: [
        'Circuit connectivity testing',
        'Real-time temperature monitoring',
        'Wiring problem identification',
        'Cold-weather performance analysis'
      ]
    },
    systemCodes: {
      title: 'IAT Sensor Codes',
      description: 'Related intake air temperature sensor codes:'
    },
    diagnosticResources: [
      {
        title: 'Electrical Circuit Testing',
        description: 'Professional procedures for testing IAT sensor circuits',
        icon: 'flash',
        url: '#diagnostic-steps'
      },
      {
        title: 'Cold Weather Diagnosis',
        description: 'Diagnosing cold-weather performance problems',
        icon: 'snowflake-o',
        url: '../resources/cold-weather-diagnosis.html'
      },
      {
        title: 'Temperature Sensor Guide',
        description: 'Complete guide to automotive temperature sensors',
        icon: 'thermometer-full',
        url: '../resources/temperature-sensor-guide.html'
      },
      {
        title: 'Wiring Protection',
        description: 'Protecting automotive wiring from damage',
        icon: 'shield',
        url: '../resources/wiring-protection.html'
      }
    ],
    codeInfo: {
      system: 'Engine Management',
      severity: 'MEDIUM',
      category: 'Electrical Circuit'
    }
  }
});

module.exports = p0110Data;
