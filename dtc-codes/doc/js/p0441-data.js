const { DTCData } = require('./dtc-template-generator');

// P0441 EVAP Purge Flow Incorrect 的完整数据结构
const p0441Data = new DTCData({
  code: 'P0441',
  title: 'EVAP Purge Flow Incorrect',
  description: 'The Engine Control Module has detected incorrect purge flow in the Evaporative Emission Control System.',
  definition: 'The Engine Control Module has detected incorrect purge flow in the Evaporative Emission Control (EVAP) System. The EVAP system uses a purge valve to control the flow of fuel vapors from the charcoal canister into the intake manifold for combustion. When the ECM commands purge flow but detects flow that doesn\'t match the expected amount (too much, too little, or no flow), P0441 is triggered. This typically indicates problems with the purge valve, purge lines, or canister.',
  
  symptoms: [
    'Check engine light illuminated - ECM detected incorrect EVAP purge flow',
    'Rough idle - Incorrect purge flow affecting air/fuel mixture at idle',
    'Engine hesitation - Inconsistent purge flow causing performance issues',
    'Poor fuel economy - EVAP system not operating efficiently',
    'Engine stalling - Excessive purge flow causing overly rich mixture',
    'Failed emissions test - EVAP system not controlling fuel vapor emissions properly',
    'Fuel smell - Vapors not being properly purged into engine',
    'Engine surging - Inconsistent purge flow causing mixture fluctuations',
    'Hard starting - Purge system affecting startup fuel mixture'
  ],
  
  causes: [
    'Faulty EVAP purge valve - Valve stuck open, closed, or not responding properly',
    'Clogged purge line - Blockage preventing proper vapor flow to intake',
    'Vacuum leak in purge system - Affecting purge valve operation',
    'Saturated charcoal canister - Canister unable to release vapors properly',
    'Faulty purge valve solenoid - Electrical control problems',
    'Blocked canister vent - Preventing proper canister operation',
    'Damaged purge line - Cracked or collapsed line affecting flow',
    'ECM software issues - Control module not properly commanding purge operation'
  ],
  
  performanceImpact: 'P0441 can cause rough idle, poor fuel economy, engine hesitation, and failed emissions testing due to incorrect fuel vapor purging affecting the air/fuel mixture and emissions control.',
  
  quickAnswer: {
    icon: 'exchange',
    meaning: 'EVAP purge system not flowing vapors correctly - usually faulty purge valve or clogged lines.',
    fix: 'Replace EVAP purge valve, check purge lines, test canister operation',
    cost: '$150-$480',
    time: '60-150 minutes',
    drivingSafety: 'Safe to drive but may experience rough idle and poor performance. Replace purge valve to restore proper operation.'
  },
  
  aiQuestions: [
    {
      question: 'What\'s the difference between P0440 and P0441?',
      answer: 'P0440 is a general EVAP system malfunction, while P0441 specifically indicates incorrect purge flow. P0441 focuses on the purge valve and purge system operation, while P0440 can indicate various EVAP system problems including leaks.'
    },
    {
      question: 'Can a bad purge valve cause rough idle?',
      answer: 'Yes, a faulty purge valve can cause rough idle. If the valve is stuck open, it allows continuous fuel vapor flow into the intake, enriching the mixture and causing rough idle. If stuck closed, the system can\'t purge vapors properly.'
    },
    {
      question: 'How do I test the EVAP purge valve?',
      answer: 'Use GeekOBD APP to command purge valve operation while monitoring engine RPM at idle. The RPM should change when the valve opens/closes. You can also test valve resistance and check for proper vacuum operation.'
    },
    {
      question: 'Can P0441 cause engine stalling?',
      answer: 'Yes, P0441 can cause stalling if the purge valve is stuck open, allowing excessive fuel vapors into the intake. This creates an overly rich mixture that can cause the engine to stall, especially at idle.'
    }
  ],

  costAnalysis: {
    averageCost: '$150-$480 for most P0441 repairs',
    repairOptions: [
      {
        title: 'EVAP Purge Valve Replacement',
        description: 'Replace faulty purge valve (70% of cases)',
        color: '#4CAF50',
        icon: 'exchange',
        items: [
          { name: 'EVAP purge valve', cost: '$60-$150' },
          { name: 'Labor (1-1.5 hours)', cost: '$100-$180' }
        ],
        total: '$160-$330',
        successRate: '90% success rate'
      },
      {
        title: 'Purge Line Repair',
        description: 'Fix clogged or damaged purge lines (20% of cases)',
        color: '#2196F3',
        icon: 'wrench',
        items: [
          { name: 'Purge line/fittings', cost: '$30-$80' },
          { name: 'Labor (1-2 hours)', cost: '$100-$240' }
        ],
        total: '$130-$320',
        successRate: '85% success rate'
      },
      {
        title: 'Charcoal Canister Replacement',
        description: 'Replace saturated or damaged canister (10% of cases)',
        color: '#FF9800',
        icon: 'filter',
        items: [
          { name: 'Charcoal canister', cost: '$150-$350' },
          { name: 'Labor (1.5-2.5 hours)', cost: '$150-$300' }
        ],
        total: '$300-$650',
        successRate: '95% success rate'
      }
    ],
    savingTips: [
      'Test purge valve operation with GeekOBD APP before replacement',
      'Check purge lines for obvious blockages before assuming valve failure',
      'Purge valve replacement is often DIY-friendly, saving $100-180 in labor',
      'Address P0441 promptly to prevent rough idle and performance issues',
      'Consider canister replacement if vehicle has high mileage and multiple EVAP codes'
    ]
  },

  diagnosticSteps: {
    estimatedTime: 'PT90M',
    steps: [
      {
        title: 'Test Purge Valve Operation',
        icon: 'play',
        description: 'Connect GeekOBD APP and command purge valve operation while monitoring engine RPM at idle. RPM should change when valve opens/closes.',
        geekobdTip: 'GeekOBD APP can command purge valve operation - significant RPM change indicates valve is working, no change suggests stuck or failed valve.'
      },
      {
        title: 'Check Purge Valve Electrical',
        icon: 'bolt',
        description: 'Test purge valve electrical connections, resistance, and control circuit operation. Verify ECM is sending proper control signals.',
        geekobdTip: 'Use GeekOBD APP to monitor purge valve duty cycle and commands - compare commanded vs actual operation to identify electrical problems.'
      },
      {
        title: 'Inspect Purge System Components',
        icon: 'eye',
        description: 'Visual inspection of purge valve, purge lines, and connections for damage, blockages, or vacuum leaks affecting system operation.',
        geekobdTip: 'Monitor purge operation with GeekOBD APP while inspecting - intermittent operation may indicate loose connections or damaged components.'
      },
      {
        title: 'Test Canister and Vent Operation',
        icon: 'filter',
        description: 'Check charcoal canister condition and vent valve operation. Verify canister can properly store and release fuel vapors.',
        geekobdTip: 'GeekOBD APP can show EVAP system pressure during purge operation - abnormal pressure patterns may indicate canister problems.'
      },
      {
        title: 'Component Replacement and Verification',
        icon: 'check-circle',
        description: 'Replace faulty purge valve or other components as diagnosed. Clear codes and verify purge system operates correctly.',
        geekobdTip: 'Use GeekOBD APP to verify purge valve now responds properly to commands and engine RPM changes appropriately during purge operation.'
      }
    ],
    importantNotes: [
      'P0441 usually indicates purge valve problems rather than leaks',
      'Test purge valve operation before replacement',
      'Check purge lines for blockages that can mimic valve failure'
    ]
  },

  caseStudies: [
    {
      title: 'Nissan Altima Stuck Purge Valve',
      vehicle: '2016 Nissan Altima 2.5L 4-cylinder, 125,000 miles',
      problem: 'Customer reported rough idle, occasional stalling, and P0441 code. Problem was worse after fuel fill-ups.',
      diagnosis: 'GeekOBD APP showed purge valve was not responding to close commands. Valve was stuck open, causing continuous fuel vapor flow into intake.',
      solution: 'Replaced EVAP purge valve located near intake manifold. Valve was internally damaged and could not close properly.',
      cost: 'EVAP purge valve: $75, Labor: $95, Total: $170',
      result: 'P0441 code cleared immediately. Rough idle eliminated and no more stalling problems. Purge valve now operates properly on command.'
    },
    {
      title: 'Ford Escape Clogged Purge Line',
      vehicle: '2017 Ford Escape 1.6L Turbo, 89,000 miles',
      problem: 'P0441 code with poor fuel economy and occasional engine hesitation. Purge valve had been replaced previously but code returned.',
      diagnosis: 'Purge valve tested good electrically and mechanically, but GeekOBD APP showed no purge flow when commanded. Found purge line was clogged with debris.',
      solution: 'Cleaned clogged purge line between canister and intake manifold. Line had accumulated debris that blocked vapor flow.',
      cost: 'Purge line cleaning: $0, Labor: $120, Total: $120',
      result: 'P0441 code cleared and purge system now flows properly. Fuel economy improved and no more hesitation during acceleration.'
    }
  ],

  relatedCodes: [
    { code: 'P0440', description: 'EVAP System Malfunction - General EVAP system problem', color: '#e74c3c' },
    { code: 'P0442', description: 'EVAP System Small Leak - Small leak detected in system', color: '#3498db' },
    { code: 'P0443', description: 'EVAP Purge Valve Circuit - Electrical problems with purge valve', color: '#f39c12' },
    { code: 'P0446', description: 'EVAP Vent Control Circuit - Vent valve electrical problems', color: '#9b59b6' },
    { code: 'P0455', description: 'EVAP System Large Leak - Large leak detected in system', color: '#e67e22' },
    { code: 'P0171', description: 'System Too Lean - Can be affected by purge system problems', color: '#4a90e2' },
    { code: 'P0172', description: 'System Too Rich - Can be caused by excessive purge flow', color: '#27ae60' }
  ],

  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P0441',
      description: 'Use GeekOBD APP for EVAP purge system testing!',
      features: [
        'Purge valve command testing',
        'Flow verification',
        'Duty cycle monitoring',
        'System pressure analysis'
      ]
    },
    systemCodes: {
      title: 'EVAP System Codes',
      description: 'Related evaporative emission control codes:'
    },
    diagnosticResources: [
      {
        title: 'EVAP Purge Testing',
        description: 'Professional procedures for testing EVAP purge system',
        icon: 'exchange',
        url: '#diagnostic-steps'
      },
      {
        title: 'Purge Valve Replacement',
        description: 'Step-by-step purge valve replacement procedures',
        icon: 'wrench',
        url: '../resources/purge-valve-replacement.html'
      },
      {
        title: 'Idle Quality Diagnosis',
        description: 'Diagnosing and fixing rough idle problems',
        icon: 'cog',
        url: '../resources/idle-quality-diagnosis.html'
      },
      {
        title: 'EVAP System Operation',
        description: 'Understanding how EVAP systems control fuel vapors',
        icon: 'info-circle',
        url: '../resources/evap-system-operation.html'
      }
    ],
    codeInfo: {
      system: 'Emissions Control',
      severity: 'MEDIUM',
      category: 'EVAP System'
    }
  }
});

module.exports = p0441Data;
