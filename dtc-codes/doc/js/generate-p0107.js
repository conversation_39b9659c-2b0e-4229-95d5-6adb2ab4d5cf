const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0107Data = require('./p0107-data');

console.log('🚀 Generating P0107 - MAP Sensor Low Input Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0107Data);
  const outputPath = path.join(__dirname, '../../p0107.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0107 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0107 page:', error.message);
  process.exit(1);
}
