const { DTCData } = require('./dtc-template-generator');

// P0115 ECT Sensor Circuit Malfunction 的完整数据结构
const p0115Data = new DTCData({
  code: 'P0115',
  title: 'ECT Sensor Circuit Malfunction',
  description: 'The Engine Control Module has detected an electrical malfunction in the Engine Coolant Temperature sensor circuit.',
  definition: 'The Engine Control Module has detected an electrical malfunction in the Engine Coolant Temperature (ECT) sensor circuit. This indicates a problem with the electrical components of the ECT sensor system rather than the sensor readings themselves. The ECT sensor circuit includes the sensor, wiring harness, connector, and ECM connections. P0115 is triggered when there are voltage irregularities, open circuits, short circuits, or other electrical faults that prevent proper communication between the ECT sensor and ECM.',
  
  symptoms: [
    'Check engine light illuminated - ECM detected ECT sensor circuit electrical fault',
    'Engine running in default mode - ECM using backup coolant temperature calculations',
    'Cooling fans running constantly - ECM assumes engine is overheating without data',
    'Poor cold weather performance - Incorrect fuel mixture during cold starts',
    'Hard starting when cold - Improper cold-start fuel enrichment',
    'Engine overheating - Cooling fans may not activate without temperature data',
    'Poor fuel economy - Non-optimized fuel delivery without ECT data',
    'Transmission shifting problems - Incorrect temperature data affects shift points',
    'Temperature gauge not working - If connected to same sensor circuit'
  ],
  
  causes: [
    'Open circuit in ECT sensor wiring - Broken wire preventing signal transmission',
    'Short circuit in ECT sensor harness - Wire touching ground or power',
    'Faulty ECT sensor connector - Corroded, damaged, or loose connection',
    'Failed ECT sensor - Internal electrical failure in sensor components',
    'ECM internal fault - Control module unable to process ECT signals',
    'Damaged wiring harness - Physical damage from heat, vibration, or corrosion',
    'Poor ground connection - Inadequate ground circuit for ECT sensor',
    'Coolant contamination - Conductive coolant causing electrical shorts'
  ],
  
  performanceImpact: 'P0115 forces the ECM to operate without ECT sensor data, using default temperature assumptions that may cause overheating, poor cold-weather performance, incorrect fuel delivery, and potential engine damage from inadequate cooling system control.',
  
  quickAnswer: {
    icon: 'flash',
    meaning: 'Electrical problem in ECT sensor circuit - wiring, connector, or sensor electrical failure.',
    fix: 'Check wiring, test connections, replace ECT sensor if needed',
    cost: '$85-$320',
    time: '60-120 minutes',
    drivingSafety: 'Risky to drive - cooling fans may not work properly. Monitor temperature closely and repair immediately.'
  },
  
  aiQuestions: [
    {
      question: 'What\'s the difference between P0115 and P0117/P0118 ECT codes?',
      answer: 'P0115 indicates an electrical circuit problem (wiring, connector, or sensor electrical failure), while P0117/P0118 indicate the sensor is working electrically but providing readings outside expected range. P0115 is typically easier to diagnose with electrical testing.'
    },
    {
      question: 'Can P0115 cause engine overheating?',
      answer: 'Yes, P0115 can cause overheating because the ECM cannot monitor actual coolant temperature and may not activate cooling fans when needed. Without ECT data, the cooling system cannot respond properly to temperature changes, potentially leading to engine damage.'
    },
    {
      question: 'How do I test ECT sensor circuit for P0115?',
      answer: 'Use a multimeter to check for 5V reference voltage, good ground, and signal wire continuity. GeekOBD APP can show if the ECM is receiving any signal from the ECT sensor - no data or fixed values indicate circuit problems rather than sensor range issues.'
    },
    {
      question: 'Why does P0115 affect transmission shifting?',
      answer: 'Many transmissions use ECT sensor data to determine shift points and torque converter lockup. Without this data, the transmission may shift harshly, stay in lower gears longer, or not engage lockup properly, affecting performance and fuel economy.'
    }
  ],

  costAnalysis: {
    averageCost: '$85-$320 for most P0115 repairs',
    repairOptions: [
      {
        title: 'ECT Sensor Replacement',
        description: 'Most common fix - Replace sensor with internal electrical failure (70% of cases)',
        color: '#4CAF50',
        icon: 'thermometer-full',
        items: [
          { name: 'ECT sensor', cost: '$35-$95' },
          { name: 'Coolant (if drained)', cost: '$15-$35' },
          { name: 'Labor (45-90 minutes)', cost: '$60-$150' }
        ],
        total: '$110-$280',
        successRate: '95% success rate'
      },
      {
        title: 'Wiring Harness Repair',
        description: 'Fix damaged ECT sensor wiring (20% of cases)',
        color: '#FF9800',
        icon: 'flash',
        items: [
          { name: 'Wiring repair materials', cost: '$25-$60' },
          { name: 'Diagnostic time', cost: '$100-$160' },
          { name: 'Labor (1-2 hours)', cost: '$100-$240' }
        ],
        total: '$225-$460',
        successRate: '90% success rate'
      },
      {
        title: 'Connector Service',
        description: 'Clean or replace corroded ECT sensor connector (10% of cases)',
        color: '#2196F3',
        icon: 'plug',
        items: [
          { name: 'Connector cleaning/replacement', cost: '$20-$50' },
          { name: 'Dielectric grease', cost: '$8-$15' },
          { name: 'Labor (30-60 minutes)', cost: '$50-$120' }
        ],
        total: '$78-$185',
        successRate: '85% success rate'
      }
    ],
    savingTips: [
      'Check connector first - 15% of P0115 cases are just corroded connections',
      'ECT sensors are usually accessible - consider DIY replacement to save labor',
      'Use multimeter to test circuits before replacing expensive components',
      'GeekOBD APP can help identify if problem is sensor or wiring related',
      'Monitor coolant temperature closely until repair to prevent overheating'
    ]
  },

  diagnosticSteps: {
    estimatedTime: 'PT75M',
    steps: [
      {
        title: 'Check for ECT Sensor Data',
        icon: 'search',
        description: 'Connect GeekOBD APP and check if ECT sensor data is available. With P0115, you may see no data, fixed values, or complete absence of temperature readings.',
        geekobdTip: 'GeekOBD APP will show if ECM is receiving ECT sensor signals - complete absence of data indicates circuit failure rather than sensor range issues.'
      },
      {
        title: 'Monitor Cooling System Operation',
        icon: 'tint',
        description: 'Check if cooling fans are operating properly and monitor actual engine temperature with infrared thermometer. P0115 may prevent proper cooling system control.',
        geekobdTip: 'Use GeekOBD APP to command cooling fan operation if available - this helps verify ECM can control cooling system despite missing ECT data.'
      },
      {
        title: 'Visual Inspection',
        icon: 'eye',
        description: 'Inspect ECT sensor, wiring harness, and connector for obvious damage. Look for corroded pins, damaged wires, coolant leaks, or signs of overheating damage.',
        geekobdTip: 'Use GeekOBD APP to monitor for any signal while wiggling wires - intermittent data indicates wiring problems.'
      },
      {
        title: 'Electrical Circuit Testing',
        icon: 'bolt',
        description: 'Test ECT sensor power supply (5V reference), ground circuit, and signal wire continuity with multimeter. Check for proper voltage at sensor connector.',
        geekobdTip: 'GeekOBD APP should show stable readings when circuits are good - use multimeter to verify actual voltages match expected values.'
      },
      {
        title: 'Component Replacement and Verification',
        icon: 'check-circle',
        description: 'Replace faulty wiring, connector, or ECT sensor as diagnosed. Clear codes and verify ECT sensor data is available and cooling system operates properly.',
        geekobdTip: 'GeekOBD APP should now show stable ECT sensor readings and cooling fans should activate at proper temperature, confirming circuit repair.'
      }
    ],
    importantNotes: [
      'P0115 can cause overheating - monitor temperature closely during diagnosis',
      'Test circuits with multimeter before replacing components',
      'Cooling fans may not work properly with P0115 - drive carefully'
    ]
  },

  caseStudies: [
    {
      title: 'Chevrolet Silverado Overheating Issue',
      vehicle: '2018 Chevrolet Silverado 5.3L V8, 95,000 miles',
      problem: 'Customer experienced engine overheating with cooling fans not turning on. P0115 code was present and temperature gauge showed no reading.',
      diagnosis: 'GeekOBD APP showed no ECT sensor data available. Visual inspection revealed ECT sensor connector had melted due to previous overheating, creating open circuit.',
      solution: 'Replaced damaged ECT sensor connector and sensor. Also repaired heat-damaged wiring section and added heat shielding to prevent recurrence.',
      cost: 'ECT sensor: $58, Connector: $35, Wiring: $25, Labor: $150, Total: $268',
      result: 'P0115 code cleared immediately. Cooling fans now operate properly and temperature gauge works normally. No overheating issues after 6 months.'
    },
    {
      title: 'Honda Accord Transmission Problems',
      vehicle: '2016 Honda Accord 2.4L 4-cylinder, 108,000 miles',
      problem: 'Customer reported harsh shifting and poor fuel economy. P0115 code was present along with transmission-related symptoms.',
      diagnosis: 'Found ECT sensor wiring had been damaged during previous radiator replacement, creating intermittent open circuit. GeekOBD APP showed ECT data would disappear randomly.',
      solution: 'Repaired damaged ECT sensor wiring with proper automotive wire and connectors. Ensured proper routing away from heat sources.',
      cost: 'Wiring repair kit: $30, Labor: $120, Total: $150',
      result: 'P0115 code cleared and transmission shifting returned to normal. Fuel economy improved by 3 MPG with proper ECT data available.'
    }
  ],

  relatedCodes: [
    { code: 'P0117', description: 'ECT Sensor Low Input - Sensor reading too hot temperatures', color: '#e74c3c' },
    { code: 'P0118', description: 'ECT Sensor High Input - Sensor reading too cold temperatures', color: '#3498db' },
    { code: 'P0125', description: 'Insufficient Coolant Temperature - Related coolant temperature issue', color: '#f39c12' },
    { code: 'P0128', description: 'Coolant Thermostat Rationality - Related cooling system problem', color: '#9b59b6' },
    { code: 'P0172', description: 'System Too Rich Bank 1 - Can result from default fuel maps', color: '#9b59b6' },
    { code: 'P0175', description: 'System Too Rich Bank 2 - Can result from default fuel maps', color: '#9b59b6' },
    { code: 'P0300', description: 'Random Misfire - Poor performance from non-optimal fuel delivery', color: '#e67e22' }
  ],

  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P0115',
      description: 'Use GeekOBD APP for ECT sensor circuit diagnosis!',
      features: [
        'Circuit connectivity testing',
        'Cooling system monitoring',
        'Temperature data verification',
        'Overheating prevention alerts'
      ]
    },
    systemCodes: {
      title: 'ECT Sensor Codes',
      description: 'Related engine coolant temperature sensor codes:'
    },
    diagnosticResources: [
      {
        title: 'Electrical Circuit Testing',
        description: 'Professional procedures for testing ECT sensor circuits',
        icon: 'flash',
        url: '#diagnostic-steps'
      },
      {
        title: 'Cooling System Safety',
        description: 'Preventing overheating during ECT sensor diagnosis',
        icon: 'fire-extinguisher',
        url: '../resources/cooling-system-safety.html'
      },
      {
        title: 'Temperature Sensor Guide',
        description: 'Complete guide to automotive temperature sensors',
        icon: 'thermometer-full',
        url: '../resources/temperature-sensor-guide.html'
      },
      {
        title: 'Overheating Prevention',
        description: 'Protecting your engine from overheating damage',
        icon: 'shield',
        url: '../resources/overheating-prevention.html'
      }
    ],
    codeInfo: {
      system: 'Engine Management',
      severity: 'HIGH',
      category: 'Electrical Circuit'
    }
  }
});

module.exports = p0115Data;
