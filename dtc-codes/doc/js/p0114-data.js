const { DTCData } = require('./dtc-template-generator');

// P0114 IAT Sensor Intermittent 的完整数据结构
const p0114Data = new DTCData({
  code: 'P0114',
  title: 'IAT Sensor Intermittent',
  description: 'The Engine Control Module has detected intermittent or erratic readings from the Intake Air Temperature sensor circuit.',
  definition: 'The Engine Control Module has detected intermittent or erratic readings from the Intake Air Temperature (IAT) sensor circuit. This means the sensor signal is unstable, jumping between different temperature readings, dropping out completely, or showing values that don\'t correlate with actual air temperature changes. The ECM expects consistent, gradual temperature changes that match environmental conditions and engine operation.',

  symptoms: [
    'Check engine light illuminated - Intermittent IAT sensor fault detected',
    'Intermittent poor engine performance - Fuel mixture varies unpredictably',
    'Engine hesitation or stumbling - Especially during temperature changes',
    'Erratic idle quality - RPM fluctuations due to changing fuel calculations',
    'Occasional engine knock or ping - When sensor reads incorrectly low temperatures',
    'Inconsistent fuel economy - Varying air/fuel mixture calculations',
    'Hard starting in certain conditions - When sensor fails during startup',
    'Engine stalling - Particularly when sensor signal drops out completely',
    'Rough acceleration - Inconsistent power delivery due to fuel mixture changes'
  ],

  causes: [
    'Loose or corroded IAT sensor connector - Intermittent electrical contact',
    'Damaged IAT sensor wiring - Broken strands causing intermittent connection',
    'Failing IAT sensor - Internal component degradation causing erratic readings',
    'Vibration-induced wiring damage - Harness rubbing or flexing causing breaks',
    'Moisture in connector - Causing intermittent shorts or open circuits',
    'ECM connector issues - Poor connection at engine control module',
    'Aftermarket air intake interference - Modified systems affecting sensor operation',
    'Temperature cycling damage - Repeated heating/cooling causing sensor failure'
  ],
  
  performanceImpact: 'P0114 causes unpredictable engine performance due to erratic fuel mixture calculations, leading to inconsistent power delivery, poor fuel economy, potential engine knock, and possible stalling during critical driving situations.',

  quickAnswer: {
    icon: 'exclamation-triangle',
    meaning: 'IAT sensor providing inconsistent, erratic temperature readings - usually loose connection or failing sensor.',
    fix: 'Check connector, test wiring, replace sensor if needed',
    cost: '$65-$350',
    time: '45-90 minutes',
    drivingSafety: 'Generally safe to drive, but expect unpredictable performance. Fix promptly to avoid stalling in traffic.'
  },

  aiQuestions: [
    {
      question: 'What\'s the difference between P0114 and other IAT codes?',
      answer: 'P0114 indicates intermittent/erratic readings, while P0112 shows constant hot readings and P0113 shows constant cold readings. P0114 is often the most challenging to diagnose because the problem comes and goes, making it harder to pinpoint the exact cause.'
    },
    {
      question: 'Why does P0114 cause intermittent symptoms?',
      answer: 'Because the IAT sensor signal is unstable, the ECM receives varying temperature data, causing it to constantly adjust fuel mixture. This results in inconsistent engine performance - sometimes running well, sometimes poorly, depending on what temperature the sensor is reading at that moment.'
    },
    {
      question: 'How can I reproduce P0114 for diagnosis?',
      answer: 'Try wiggling the IAT sensor connector and wiring harness while monitoring live data with GeekOBD APP. Temperature readings should remain stable - if they jump around during wire movement, you\'ve found the problem. Also test during temperature changes (cold start to warm engine).'
    },
    {
      question: 'Can weather affect P0114 symptoms?',
      answer: 'Yes, temperature and humidity changes can worsen P0114 symptoms. Corroded connectors may work fine in dry conditions but fail when moisture is present. Similarly, damaged wiring may expand/contract with temperature changes, causing intermittent connections.'
    }
  ],
  
  costAnalysis: {
    averageCost: '$65-$350 for most P0114 repairs',
    repairOptions: [
      {
        title: 'Connector Cleaning/Repair',
        description: 'Most common fix - Clean corroded connector (40% of cases)',
        color: '#4CAF50',
        icon: 'plug',
        items: [
          { name: 'Connector cleaning kit', cost: '$15-$25' },
          { name: 'Dielectric grease', cost: '$8-$15' },
          { name: 'Labor (0.5-1 hour)', cost: '$50-$120' }
        ],
        total: '$73-$160',
        successRate: '85% success rate'
      },
      {
        title: 'IAT Sensor Replacement',
        description: 'Replace failing sensor (35% of cases)',
        color: '#FF9800',
        icon: 'thermometer-full',
        items: [
          { name: 'IAT Sensor', cost: '$25-$85' },
          { name: 'Labor (0.5-1 hour)', cost: '$50-$120' }
        ],
        total: '$75-$205',
        successRate: '95% success rate'
      },
      {
        title: 'Wiring Harness Repair',
        description: 'Fix damaged wiring (25% of cases)',
        color: '#2196F3',
        icon: 'wrench',
        items: [
          { name: 'Wire repair materials', cost: '$20-$45' },
          { name: 'Diagnostic time', cost: '$80-$160' },
          { name: 'Labor (1-2 hours)', cost: '$100-$240' }
        ],
        total: '$200-$445',
        successRate: '90% success rate'
      }
    ],
    savingTips: [
      'Start with connector cleaning - 40% of P0114 cases are just corroded connections',
      'Use GeekOBD APP wire wiggle test to pinpoint exact location of wiring problem',
      'Check for TSBs (Technical Service Bulletins) - some vehicles have known harness issues',
      'Consider aftermarket sensors - often 30-50% less than dealer parts',
      'If intermittent, document when problem occurs to help technician diagnose faster'
    ]
  },
  
  diagnosticSteps: {
    estimatedTime: 'PT60M',
    steps: [
      {
        title: 'Verify P0114 Code and Monitor Live Data',
        icon: 'search',
        description: 'Connect scan tool and confirm P0114 is present. Monitor live IAT data while engine runs - look for erratic readings, sudden jumps, or values that don\'t match ambient temperature.',
        geekobdTip: 'Use GeekOBD APP to record IAT data over time. Look for sudden spikes, drops, or readings that don\'t correlate with actual temperature changes.'
      },
      {
        title: 'Perform Wire Wiggle Test',
        icon: 'hand-paper-o',
        description: 'While monitoring live IAT data, gently wiggle the sensor connector and wiring harness. Watch for sudden changes in readings that indicate loose connections or damaged wires.',
        geekobdTip: 'GeekOBD APP\'s real-time graphing feature is perfect for this test - you\'ll see immediate spikes or drops when you move problem areas.'
      },
      {
        title: 'Inspect Connector and Terminals',
        icon: 'eye',
        description: 'Disconnect IAT sensor and inspect connector for corrosion, bent pins, or moisture. Clean terminals with electrical contact cleaner and check for proper fit.',
        geekobdTip: 'Use GeekOBD APP to monitor sensor voltage before and after cleaning - voltage should be stable around 2-3V at room temperature.'
      },
      {
        title: 'Test Sensor Resistance Stability',
        icon: 'thermometer-full',
        description: 'With sensor disconnected, measure resistance while gently flexing sensor body and leads. Resistance should remain stable - any fluctuation indicates internal sensor damage.',
        geekobdTip: 'Compare resistance readings with GeekOBD APP temperature specifications. Resistance should change smoothly with temperature, not jump erratically.'
      },
      {
        title: 'Verify Repair and Road Test',
        icon: 'check-circle',
        description: 'After repair, clear codes and road test under various conditions. Monitor IAT readings during temperature changes, acceleration, and vibration to ensure stability.',
        geekobdTip: 'Use GeekOBD APP to log data during test drive. IAT should respond gradually to temperature changes without sudden jumps or dropouts.'
      }
    ],
    importantNotes: [
      'P0114 is intermittent - problem may not be present during initial diagnosis',
      'Document when symptoms occur (cold start, hot weather, bumpy roads) to help locate cause',
      'Wire wiggle test is crucial - many P0114 cases are wiring-related'
    ]
  },
  
  caseStudies: [
    {
      title: 'Jeep Wrangler Intermittent IAT Issues',
      vehicle: '2017 Jeep Wrangler 3.6L V6, 78,000 miles',
      problem: 'Customer reported intermittent rough idle, occasional stalling, and check engine light that would come and go. Problems seemed worse on bumpy roads and during cold mornings.',
      diagnosis: 'P0114 code present intermittently. GeekOBD APP monitoring showed IAT readings jumping from normal 85°F to -40°F and back during wire wiggle test. Found IAT sensor connector had corroded terminals and loose connection due to off-road driving exposure.',
      solution: 'Cleaned corroded IAT sensor connector terminals with electrical contact cleaner, applied dielectric grease, and secured connection. Also relocated harness away from vibration point near engine mount.',
      cost: 'Cleaning supplies: $18, Dielectric grease: $12, Labor: $85, Total: $115',
      result: 'P0114 code has not returned after 6 months. Customer reports smooth idle and no more stalling. IAT readings remain stable even during off-road driving.'
    },
    {
      title: 'Toyota Camry Temperature Cycling Failure',
      vehicle: '2015 Toyota Camry 2.5L 4-cylinder, 105,000 miles',
      problem: 'Intermittent P0114 code appearing only during hot summer days. Engine would hesitate and stumble during acceleration, but only when ambient temperature exceeded 90°F.',
      diagnosis: 'IAT sensor tested normal resistance at room temperature, but GeekOBD APP showed erratic readings when sensor was heated with hair dryer. Sensor was failing internally due to repeated temperature cycling over years of use.',
      solution: 'Replaced IAT sensor with OEM part. Sensor was located in air intake tube and easily accessible, taking only 15 minutes to replace.',
      cost: 'IAT sensor: $58, Labor: $75, Total: $133',
      result: 'P0114 code cleared and has not returned through two hot summers. Engine performance is now consistent regardless of ambient temperature.'
    }
  ],

  relatedCodes: [
    { code: 'P0112', description: 'IAT Sensor Low Input - Constant hot temperature readings', color: '#e74c3c' },
    { code: 'P0113', description: 'IAT Sensor High Input - Constant cold temperature readings', color: '#3498db' },
    { code: 'P0110', description: 'IAT Sensor Circuit Malfunction - General IAT circuit problem', color: '#4a90e2' },
    { code: 'P0171', description: 'System Too Lean Bank 1 - Can be caused by erratic IAT readings', color: '#9b59b6' },
    { code: 'P0174', description: 'System Too Lean Bank 2 - Can be caused by erratic IAT readings', color: '#9b59b6' },
    { code: 'P0300', description: 'Random Misfire - Can be caused by inconsistent fuel mixture', color: '#e67e22' },
    { code: 'P0101', description: 'MAF Sensor Range/Performance - Related air intake measurement', color: '#27ae60' }
  ],
  
  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P0114',
      description: 'Use GeekOBD APP for intermittent IAT sensor diagnosis!',
      features: [
        'Real-time IAT monitoring',
        'Wire wiggle test guidance',
        'Data logging for intermittent faults',
        'Temperature correlation analysis'
      ]
    },
    systemCodes: {
      title: 'IAT Sensor Codes',
      description: 'Related Intake Air Temperature sensor codes:'
    },
    diagnosticResources: [
      {
        title: 'Intermittent Fault Guide',
        description: 'Specialized procedures for intermittent IAT problems',
        icon: 'exclamation-triangle',
        url: '#diagnostic-steps'
      },
      {
        title: 'Wire Wiggle Testing',
        description: 'Step-by-step connector and wiring testing',
        icon: 'hand-paper-o',
        url: '../resources/wire-wiggle-testing.html'
      },
      {
        title: 'IAT Sensor Specifications',
        description: 'Resistance values and voltage specifications',
        icon: 'line-chart',
        url: '../resources/iat-specifications.html'
      },
      {
        title: 'Connector Repair Guide',
        description: 'Professional connector cleaning and repair procedures',
        icon: 'plug',
        url: '../resources/connector-repair.html'
      }
    ],
    codeInfo: {
      system: 'Engine Management',
      severity: 'MEDIUM',
      category: 'Intake Air Temperature'
    }
  }
});

module.exports = p0114Data;