const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0202Data = require('./p0202-data');

console.log('🚀 Generating P0202 - Injector Circuit Malfunction Cylinder 2 Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0202Data);
  const outputPath = path.join(__dirname, '../../p0202.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0202 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0202 page:', error.message);
  process.exit(1);
}
