const { DTCData } = require('./dtc-template-generator');

// P0301 Cylinder 1 Misfire Detected 的完整数据结构
const p0301Data = new DTCData({
  code: 'P0301',
  title: 'Cylinder 1 Misfire Detected',
  description: 'The Engine Control Module has detected a misfire condition specifically in cylinder 1 of the engine.',
  definition: 'The Engine Control Module has detected a misfire condition specifically in cylinder 1 of the engine. [需要详细定义]',
  
  symptoms: [
    // 需要添加具体症状
    'Check engine light illuminated',
    'Engine performance issues',
    // 添加更多症状...
  ],
  
  causes: [
    // 需要添加具体原因
    'Faulty sensor or component',
    'Wiring issues',
    // 添加更多原因...
  ],
  
  performanceImpact: 'P0301 causes [具体影响描述]',
  
  quickAnswer: {
    icon: 'wrench', // 需要选择合适的图标
    meaning: 'P0301 means: [简洁描述]',
    fix: '[主要修复方法]',
    cost: '$[成本范围]',
    time: '[时间估计]',
    drivingSafety: '[驾驶安全建议]'
  },
  
  aiQuestions: [
    {
      question: 'What causes P0301?',
      answer: '[详细回答]'
    },
    {
      question: 'How serious is P0301?',
      answer: '[严重程度说明]'
    },
    {
      question: 'Can I drive with P0301?',
      answer: '[驾驶安全建议]'
    },
    {
      question: 'How to diagnose P0301?',
      answer: '[诊断方法，突出GeekOBD APP]'
    }
  ],
  
  costAnalysis: {
    averageCost: '[平均成本]',
    repairOptions: [
      {
        title: '[最常见修复]',
        description: '[描述]',
        color: '#4CAF50',
        icon: 'wrench',
        items: [
          { name: '[部件1]', cost: '$[范围]' },
          { name: '[人工费]', cost: '$[范围]' }
        ],
        total: '$[总计范围]',
        successRate: '[成功率]'
      }
      // 添加更多修复选项...
    ],
    savingTips: [
      '[省钱建议1]',
      '[省钱建议2]',
      // 添加更多建议...
    ]
  },
  
  diagnosticSteps: {
    estimatedTime: 'PT30M',
    steps: [
      {
        title: 'Initial Diagnosis',
        icon: 'search',
        description: '[诊断步骤描述]',
        geekobdTip: '[GeekOBD APP使用建议]'
      }
      // 添加更多步骤...
    ],
    importantNotes: [
      '[重要注意事项1]',
      '[重要注意事项2]'
    ]
  },
  
  caseStudies: [
    {
      title: '[案例标题]',
      vehicle: '[车辆信息]',
      problem: '[问题描述]',
      diagnosis: '[诊断过程]',
      solution: '[解决方案]',
      cost: '[实际成本]',
      result: '[修复结果]'
    }
    // 添加更多案例...
  ],
  
  relatedCodes: [
    // 需要添加相关代码
    { code: '[相关代码1]', description: '[描述]', color: '#4a90e2' }
    // 添加更多相关代码...
  ],
  
  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P0301',
      description: 'Use GeekOBD APP for professional P0301 diagnosis!',
      features: [
        'Real-time data monitoring',
        'Step-by-step diagnosis',
        'Repair verification',
        'Cost estimation'
      ]
    },
    systemCodes: {
      title: '[系统名称] Codes',
      description: 'Related diagnostic trouble codes:'
    },
    diagnosticResources: [
      {
        title: 'P0301 Testing Guide',
        description: 'Professional diagnostic procedures',
        icon: 'book',
        url: '#diagnostic-steps'
      },
      {
        title: 'Wiring Diagrams',
        description: 'Circuit diagrams and specifications',
        icon: 'sitemap',
        url: '../resources/wiring-diagrams.html'
      },
      {
        title: 'Repair Procedures',
        description: 'Step-by-step repair instructions',
        icon: 'wrench',
        url: '../resources/repair-procedures.html'
      }
    ],
    codeInfo: {
      system: '[系统名称]',
      severity: 'MEDIUM', // HIGH, MEDIUM, LOW
      category: '[类别]'
    }
  }
});

module.exports = p0301Data;