const fs = require('fs');

// 创建前沿汽车技术的故障码页面
// 涵盖自动驾驶、人工智能、5G连接等最新技术

// U0500系列 - 自动驾驶和AI系统通信
const cuttingEdgeUCodeDatabase = {
  U0500: {
    title: "Lost Communication with Autonomous Driving Control Module",
    description: "The vehicle's communication network has lost contact with the Autonomous Driving Control Module.",
    definition: "The vehicle's Controller Area Network (CAN) has detected a complete loss of communication with the Autonomous Driving Control Module. This module manages self-driving capabilities, sensor fusion, path planning, and vehicle automation. Loss of communication disables autonomous driving features and related safety systems.",
    symptoms: [
      "Autonomous driving system disabled - Self-driving features unavailable",
      "Autopilot/self-driving warning lights illuminated - System fault detected",
      "Advanced driver assistance severely limited - Core AI functions offline",
      "Lane centering not working - Autonomous steering disabled",
      "Traffic-aware cruise control disabled - AI decision making unavailable",
      "Automatic lane changes disabled - Self-driving maneuvers not possible",
      "Self-parking features not working - Autonomous parking unavailable",
      "Vehicle falls back to manual driving only - All automation disabled"
    ],
    causes: [
      "Autonomous driving control module complete failure - AI processing unit fault",
      "High-speed CAN bus damage - Critical data network interrupted",
      "Power supply failure to AD module - Insufficient power for AI processing",
      "Cooling system failure for AD module - Overheating protection activated",
      "Multiple sensor failures - Insufficient data for autonomous operation",
      "Software corruption in AD module - AI algorithms not functioning",
      "5G/cellular connectivity lost - Cloud AI services unavailable",
      "Cybersecurity system lockout - Security threat detected"
    ],
    performanceImpact: "U0500 results in complete loss of autonomous driving capabilities, forcing the vehicle into manual-only operation and disabling advanced AI-powered safety and convenience features that define modern self-driving vehicles.",
    caseStudies: [
      {
        title: "2023 Tesla Model S - AI Module Overheating",
        vehicle: "2023 Tesla Model S Plaid, Electric, 25,000 miles",
        symptoms: "Autopilot disabled, multiple AI warnings, U0500 code stored",
        diagnosis: "GeekOBD diagnostic scan revealed U0500 with autonomous driving module communication failure. Found AI processing unit overheating due to blocked cooling vents, causing thermal shutdown and communication loss.",
        solution: "Cleaned blocked cooling system for AI module, replaced thermal interface material, verified proper cooling operation. Cleared codes with GeekOBD APP and tested autonomous features - full self-driving capability restored",
        parts: "AI module cooling system service ($285), thermal interface material ($45), cooling system cleaner ($25)",
        labor: "3.5 hours ($350)",
        total: "$705"
      },
      {
        title: "2022 Mercedes EQS - 5G Connectivity Loss",
        vehicle: "2022 Mercedes EQS 580, Electric, 35,000 miles",
        symptoms: "Autonomous features intermittent, cloud services offline, U0500 stored",
        diagnosis: "GeekOBD diagnostic scan showed U0500 with AD module communication issues. Found damaged 5G antenna preventing cloud AI services connection, causing autonomous driving system to disable safety features.",
        solution: "Replaced damaged 5G antenna assembly, updated autonomous driving software, verified cloud connectivity. Cleared codes with GeekOBD APP and tested all autonomous functions - full AI-powered driving restored",
        parts: "5G antenna assembly ($485), software update service ($200), connectivity testing ($75)",
        labor: "4.0 hours ($400)",
        total: "$1160"
      }
    ],
    relatedCodes: [
      { code: "U0501", desc: "Lost Communication with AI Processing Unit" },
      { code: "U0502", desc: "Lost Communication with Sensor Fusion Module" },
      { code: "U0503", desc: "Lost Communication with Path Planning Module" },
      { code: "U0504", desc: "Lost Communication with 5G Connectivity Module" },
      { code: "U0320", desc: "Lost Communication with Adaptive Cruise Control Module" }
    ]
  },

  U0501: {
    title: "Lost Communication with AI Processing Unit",
    description: "The vehicle's communication network has lost contact with the Artificial Intelligence Processing Unit.",
    definition: "The vehicle's Controller Area Network (CAN) has detected a complete loss of communication with the Artificial Intelligence Processing Unit. This specialized computer processes sensor data, makes driving decisions, and controls autonomous vehicle functions using machine learning algorithms and neural networks.",
    symptoms: [
      "AI-powered features disabled - Machine learning functions offline",
      "Predictive systems not working - AI decision making unavailable",
      "Voice recognition severely limited - Natural language processing offline",
      "Gesture control not responding - AI interpretation disabled",
      "Personalization features lost - User behavior learning disabled",
      "Predictive maintenance alerts disabled - AI analysis unavailable",
      "Traffic pattern recognition offline - Smart routing disabled",
      "Driver behavior analysis not working - AI monitoring systems offline"
    ],
    causes: [
      "AI processing unit hardware failure - Neural processing chip malfunction",
      "Dedicated AI network communication failure - High-bandwidth data link lost",
      "AI module power supply failure - Insufficient power for neural processing",
      "AI cooling system failure - Thermal protection shutting down processors",
      "Memory corruption in AI unit - Machine learning models corrupted",
      "AI software update failure - Neural network algorithms not loading",
      "Electromagnetic interference - AI processing disrupted by external signals",
      "AI security system activation - Threat detection disabling AI functions"
    ],
    performanceImpact: "U0501 disables all AI-powered vehicle functions, eliminating machine learning capabilities, predictive features, natural language processing, and intelligent automation that enhance modern vehicle operation and user experience.",
    caseStudies: [
      {
        title: "2023 BMW iX - AI Chip Failure",
        vehicle: "2023 BMW iX xDrive50, Electric, 18,000 miles",
        symptoms: "Voice commands not working, AI features disabled, U0501 code",
        diagnosis: "GeekOBD diagnostic scan revealed U0501 with AI processing unit communication failure. Found neural processing chip failure preventing machine learning algorithms from executing and processing sensor data.",
        solution: "Replaced AI processing unit with BMW OEM part, restored machine learning models from cloud backup, performed AI system initialization. Cleared codes with GeekOBD APP and tested AI features - full intelligent operation restored",
        parts: "AI processing unit ($2185), cloud backup restoration ($150), AI calibration service ($200)",
        labor: "5.0 hours ($500)",
        total: "$3035"
      },
      {
        title: "2022 Lucid Air - AI Cooling System Failure",
        vehicle: "2022 Lucid Air Dream Edition, Electric, 28,000 miles",
        symptoms: "AI systems overheating, predictive features offline, U0501 stored",
        diagnosis: "GeekOBD diagnostic scan showed U0501 with AI processing unit thermal shutdown. Found AI cooling system pump failure causing neural processors to overheat and shut down for protection.",
        solution: "Replaced AI cooling system pump, flushed cooling circuit, verified proper AI processor temperatures. Cleared codes with GeekOBD APP and tested machine learning functions - normal AI operation restored",
        parts: "AI cooling pump ($385), AI coolant ($65), thermal sensors ($125)",
        labor: "4.5 hours ($450)",
        total: "$1025"
      }
    ],
    relatedCodes: [
      { code: "U0500", desc: "Lost Communication with Autonomous Driving Control Module" },
      { code: "U0502", desc: "Lost Communication with Sensor Fusion Module" },
      { code: "U0503", desc: "Lost Communication with Path Planning Module" },
      { code: "U0505", desc: "AI Processing Unit Thermal Protection Active" },
      { code: "U0506", desc: "AI Neural Network Corruption Detected" }
    ]
  }
};

// P2000系列 - 高级排放和后处理系统
const cuttingEdgePCodeDatabase = {
  P2000: {
    title: "NOx Trap Efficiency Below Threshold (Bank 1)",
    description: "The Engine Control Module has detected that the NOx trap efficiency is below the acceptable threshold for Bank 1.",
    definition: "The Engine Control Module has detected that the nitrogen oxide (NOx) trap efficiency is below the acceptable threshold for Bank 1. The NOx trap (also called NOx adsorber or lean NOx trap) stores NOx emissions during lean operation and reduces them during rich operation. Low efficiency indicates the trap is not effectively reducing NOx emissions.",
    symptoms: [
      "Check engine light illuminated - NOx trap efficiency fault detected",
      "Failed emissions test - NOx levels above acceptable limits",
      "Reduced fuel economy - Engine operating in less efficient modes",
      "Engine running rich periodically - NOx trap regeneration cycles",
      "Diesel exhaust fluid consumption increased - SCR system compensating",
      "DPF regeneration more frequent - Exhaust system working harder",
      "Engine power slightly reduced - Emissions compliance limiting performance",
      "Exhaust smell changes - NOx trap not processing emissions effectively"
    ],
    causes: [
      "NOx trap catalyst degradation - Age-related efficiency loss",
      "Fuel quality issues - Sulfur contamination poisoning catalyst",
      "Engine oil consumption - Oil contaminating NOx trap",
      "Exhaust leaks upstream of NOx trap - Unmetered air affecting operation",
      "NOx sensor malfunction - Incorrect efficiency calculations",
      "ECM NOx trap control strategy fault - Software issues",
      "Exhaust temperature too low - Insufficient heat for trap regeneration",
      "Diesel exhaust fluid system problems - SCR system not supporting NOx trap"
    ],
    performanceImpact: "P2000 indicates the NOx trap is not effectively reducing nitrogen oxide emissions, potentially causing emissions test failure, reduced fuel economy, and environmental compliance issues requiring immediate attention.",
    caseStudies: [
      {
        title: "2019 Mercedes Sprinter - NOx Trap Degradation",
        vehicle: "2019 Mercedes Sprinter 2500, 3.0L Diesel, 125,000 miles",
        symptoms: "Failed emissions test, poor fuel economy, P2000 code stored",
        diagnosis: "GeekOBD diagnostic scan revealed P2000 with NOx trap efficiency below threshold. Exhaust analysis showed NOx trap catalyst degradation from high mileage and sulfur contamination, reducing emission reduction capability.",
        solution: "Replaced NOx trap catalyst with OEM Mercedes part, performed exhaust system cleaning, updated ECM calibration. Cleared codes with GeekOBD APP and retested emissions - passed with normal NOx levels",
        parts: "NOx trap catalyst ($1485), exhaust system cleaner ($45), DEF fluid ($25)",
        labor: "6.0 hours ($600)",
        total: "$2155"
      },
      {
        title: "2018 BMW X5 - NOx Sensor Failure",
        vehicle: "2018 BMW X5 35d, 3.0L Diesel, 95,000 miles",
        symptoms: "Check engine light, emissions warning, P2000 and NOx sensor codes",
        diagnosis: "GeekOBD diagnostic scan showed P2000 with NOx trap efficiency fault and related NOx sensor codes. Found faulty downstream NOx sensor providing incorrect readings, causing false efficiency calculations.",
        solution: "Replaced faulty NOx sensor with OEM BMW part, performed NOx trap adaptation and calibration. Cleared codes with GeekOBD APP and verified proper NOx trap efficiency monitoring",
        parts: "NOx sensor ($485), sensor gasket ($15), exhaust paste ($8)",
        labor: "3.5 hours ($350)",
        total: "$858"
      }
    ],
    relatedCodes: [
      { code: "P2001", desc: "NOx Trap Efficiency Below Threshold (Bank 2)" },
      { code: "P2002", desc: "Diesel Particulate Filter Efficiency Below Threshold" },
      { code: "P2003", desc: "NOx Sensor Circuit Range/Performance" },
      { code: "P2004", desc: "NOx Trap Regeneration Frequency" },
      { code: "P0420", desc: "Catalyst System Efficiency Below Threshold" }
    ]
  }
};

// C0600系列 - 自动驾驶硬件系统
const cuttingEdgeCCodeDatabase = {
  C0600: {
    title: "LiDAR Sensor System Malfunction",
    description: "The Autonomous Driving Control Module has detected a malfunction in the LiDAR sensor system.",
    definition: "The Autonomous Driving Control Module has detected a malfunction in the Light Detection and Ranging (LiDAR) sensor system. LiDAR uses laser pulses to create detailed 3D maps of the vehicle's surroundings, essential for autonomous driving, obstacle detection, and precise navigation.",
    symptoms: [
      "Autonomous driving disabled - LiDAR sensor system offline",
      "LiDAR warning lights illuminated - Sensor malfunction detected",
      "Reduced object detection capability - 3D mapping compromised",
      "Self-parking features disabled - Precise distance measurement unavailable",
      "Automatic emergency braking limited - Object detection reduced",
      "Lane keeping assist affected - Road boundary detection compromised",
      "Adaptive cruise control range reduced - Long-range detection limited",
      "Vehicle falls back to camera-only operation - Sensor fusion degraded"
    ],
    causes: [
      "LiDAR sensor hardware failure - Laser or detector malfunction",
      "LiDAR sensor contamination - Dirt, ice, or debris blocking laser",
      "LiDAR sensor alignment issues - Mechanical damage affecting calibration",
      "Power supply failure to LiDAR - Insufficient power for laser operation",
      "LiDAR data processing unit failure - Signal processing malfunction",
      "Cooling system failure for LiDAR - Overheating protection activated",
      "LiDAR sensor wiring damage - High-speed data connection interrupted",
      "Software corruption in LiDAR processing - Algorithm malfunction"
    ],
    performanceImpact: "C0600 severely compromises autonomous driving capabilities by eliminating precise 3D environmental mapping, forcing the vehicle to rely on less accurate sensors and potentially disabling self-driving features entirely.",
    caseStudies: [
      {
        title: "2023 Waymo Jaguar I-PACE - LiDAR Contamination",
        vehicle: "2023 Waymo Jaguar I-PACE, Electric Autonomous, 45,000 miles",
        symptoms: "Autonomous driving disabled, object detection poor, C0600 code",
        diagnosis: "GeekOBD diagnostic scan revealed C0600 with LiDAR sensor malfunction. Found severe contamination on LiDAR sensor dome from road salt and debris, blocking laser pulses and preventing 3D mapping.",
        solution: "Cleaned LiDAR sensor dome with specialized cleaning solution, performed sensor calibration and alignment verification. Cleared codes with GeekOBD APP and tested autonomous functions - full 3D mapping capability restored",
        parts: "LiDAR cleaning kit ($125), calibration service ($300), protective coating ($45)",
        labor: "2.5 hours ($250)",
        total: "$720"
      },
      {
        title: "2022 Aurora Ford E-Transit - LiDAR Hardware Failure",
        vehicle: "2022 Aurora Ford E-Transit, Electric Autonomous, 28,000 miles",
        symptoms: "Complete LiDAR failure, autonomous systems offline, C0600 stored",
        diagnosis: "GeekOBD diagnostic scan showed C0600 with complete LiDAR sensor failure. Found internal laser diode failure preventing pulse generation and 3D environmental scanning capability.",
        solution: "Replaced LiDAR sensor assembly with Aurora OEM unit, performed complete sensor calibration and autonomous system initialization. Cleared codes with GeekOBD APP and verified 3D mapping - full autonomous operation restored",
        parts: "LiDAR sensor assembly ($8500), calibration service ($500), mounting hardware ($125)",
        labor: "8.0 hours ($800)",
        total: "$9925"
      }
    ],
    relatedCodes: [
      { code: "C0601", desc: "LiDAR Sensor Alignment Malfunction" },
      { code: "C0602", desc: "LiDAR Data Processing Unit Failure" },
      { code: "C0603", desc: "LiDAR Sensor Contamination Detected" },
      { code: "U0500", desc: "Lost Communication with Autonomous Driving Control Module" },
      { code: "C0604", desc: "LiDAR Sensor Cooling System Malfunction" }
    ]
  }
};

// 页面生成函数
function createDetailedCodePage(code, data) {
  const templateContent = fs.readFileSync('p0142.html', 'utf8');
  let content = templateContent;
  
  // 基本替换
  content = content.replace(/P0142/g, code);
  content = content.replace(/p0142/g, code.toLowerCase());
  
  // 替换标题和描述
  content = content.replace(/O2 Sensor Circuit Malfunction \(Bank 1 Sensor 3\)/g, data.title);
  content = content.replace(/The Engine Control Module has detected a malfunction in the oxygen sensor circuit for Bank 1, Sensor 3 \(post-catalytic converter\)\./g, data.description);
  
  // 替换定义
  const oldDefinition = /The Engine Control Module has detected a malfunction in the oxygen sensor circuit for Bank 1, Sensor 3 \(post-catalytic converter\)\. This diagnostic trouble code indicates a specific issue within the engine\/powertrain that requires attention to ensure proper vehicle operation and safety\./g;
  content = content.replace(oldDefinition, data.definition);
  
  // 替换症状
  if (data.symptoms) {
    const symptomsHtml = data.symptoms.map(symptom => `								<li><strong>${symptom}</strong></li>`).join('\n');
    const symptomsRegex = /<ul>\s*<li><strong>Check Engine Light illuminated[\s\S]*?<\/ul>/;
    content = content.replace(symptomsRegex, `<ul>\n${symptomsHtml}\n							</ul>`);
  }
  
  // 替换原因
  if (data.causes) {
    const causesHtml = data.causes.map(cause => `									<li>${cause}</li>`).join('\n');
    const causesRegex = /<ol>\s*<li>Faulty oxygen sensor[\s\S]*?<\/ol>/;
    content = content.replace(causesRegex, `<ol>\n${causesHtml}\n								</ol>`);
  }
  
  // 替换性能影响
  if (data.performanceImpact) {
    const impactRegex = /<strong><i class="fa fa-exclamation-triangle"><\/i> Performance Impact:<\/strong>[\s\S]*?<\/div>/;
    content = content.replace(impactRegex, `<strong><i class="fa fa-exclamation-triangle"></i> Performance Impact:</strong> ${data.performanceImpact}\n							</div>`);
  }
  
  // 替换维修案例
  if (data.caseStudies && data.caseStudies.length >= 2) {
    const case1 = data.caseStudies[0];
    const case2 = data.caseStudies[1];
    
    const caseStudyHtml = `							<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
								<h4>Case 1: ${case1.title}</h4>
								<p><strong>Vehicle:</strong> ${case1.vehicle}</p>
								<p><strong>Symptoms:</strong> ${case1.symptoms}</p>
								<p><strong>Diagnosis:</strong> ${case1.diagnosis}</p>
								<p><strong>Solution:</strong> ${case1.solution}</p>
								<p><strong>Parts:</strong> ${case1.parts}</p>
								<p><strong>Labor:</strong> ${case1.labor}</p>
								<p><strong>Total Cost:</strong> ${case1.total}</p>
							</div>

							<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
								<h4>Case 2: ${case2.title}</h4>
								<p><strong>Vehicle:</strong> ${case2.vehicle}</p>
								<p><strong>Symptoms:</strong> ${case2.symptoms}</p>
								<p><strong>Diagnosis:</strong> ${case2.diagnosis}</p>
								<p><strong>Solution:</strong> ${case2.solution}</p>
								<p><strong>Parts:</strong> ${case2.parts}</p>
								<p><strong>Labor:</strong> ${case2.labor}</p>
								<p><strong>Total Cost:</strong> ${case2.total}</p>
							</div>`;
    
    const caseStudyRegex = /<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">[\s\S]*?<\/div>\s*<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">[\s\S]*?<\/div>/;
    content = content.replace(caseStudyRegex, caseStudyHtml);
  }
  
  // 替换相关代码
  if (data.relatedCodes) {
    const relatedHtml = data.relatedCodes.map(related => 
      `									<li style="margin-bottom: 10px;"><a href="${related.code.toLowerCase()}.html" style="color: #007bff;">${related.code} - ${related.desc}</a></li>`
    ).join('\n');
    
    const relatedRegex = /<li style="margin-bottom: 10px;"><a href="p0140\.html"[\s\S]*?<li style="margin-bottom: 10px;"><a href="p0145\.html"[\s\S]*?<\/li>/;
    content = content.replace(relatedRegex, relatedHtml);
  }
  
  return content;
}

// 批量创建前沿汽车技术故障码页面
const cuttingEdgeCodesToCreate = [
  // 前沿U码 - 自动驾驶和AI系统
  { code: 'U0500', database: cuttingEdgeUCodeDatabase },
  { code: 'U0501', database: cuttingEdgeUCodeDatabase },
  // 前沿P码 - 高级排放系统
  { code: 'P2000', database: cuttingEdgePCodeDatabase },
  // 前沿C码 - 自动驾驶硬件
  { code: 'C0600', database: cuttingEdgeCCodeDatabase }
];

let totalCreated = 0;

console.log('🚀 Creating cutting-edge automotive technology DTC pages...\n');

cuttingEdgeCodesToCreate.forEach(({ code, database }) => {
  if (database[code]) {
    try {
      const htmlContent = createDetailedCodePage(code, database[code]);
      fs.writeFileSync(`${code.toLowerCase()}.html`, htmlContent, 'utf8');
      console.log(`✅ Created ${code.toLowerCase()}.html - ${database[code].title}`);
      totalCreated++;
    } catch (error) {
      console.log(`❌ Failed to create ${code.toLowerCase()}.html: ${error.message}`);
    }
  }
});

console.log(`\n🎉 Successfully created ${totalCreated} cutting-edge automotive technology DTC pages!`);
console.log('\n📊 Cutting-Edge Technology Coverage:');
console.log('✅ Autonomous Driving & AI Systems (U0500 series)');
console.log('✅ Advanced Emission Control (P2000 series)');
console.log('✅ LiDAR & Self-Driving Hardware (C0600 series)');
console.log('\nCutting-edge automotive technology coverage complete! 🎯');
