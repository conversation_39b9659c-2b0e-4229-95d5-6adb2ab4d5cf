const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0106Data = require('./p0106-data');

console.log('🚀 Generating P0106 - MAP Sensor Range/Performance Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0106Data);
  const outputPath = path.join(__dirname, '../../p0106.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0106 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0106 page:', error.message);
  process.exit(1);
}
