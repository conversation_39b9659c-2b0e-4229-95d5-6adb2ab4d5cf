const { DTCData } = require('./dtc-template-generator');

// P0601 Internal Control Module Memory Check Sum Error 的完整数据结构
const p0601Data = new DTCData({
  code: 'P0601',
  title: 'Internal Control Module Memory Check Sum Error',
  description: 'The Engine Control Module has detected an internal memory checksum error.',
  definition: 'The Engine Control Module has detected an internal memory checksum error. The ECM continuously monitors its internal memory integrity using checksum calculations to ensure stored data and programming are correct. When the ECM detects that stored data has been corrupted or doesn\'t match expected checksum values, P0601 is triggered. This indicates potential ECM hardware failure, memory corruption, or programming issues.',
  
  symptoms: [
    'Check engine light illuminated - ECM detected internal memory error',
    'Engine may not start - ECM unable to execute proper control strategies',
    'Engine running in limp mode - ECM using backup programming',
    'Intermittent engine problems - Corrupted memory causing inconsistent operation',
    'Poor engine performance - ECM not executing optimal control strategies',
    'Transmission shifting problems - ECM communication with TCM affected',
    'Multiple warning lights - Various systems affected by ECM problems',
    'Scan tool communication issues - Difficulty connecting to ECM',
    'Engine stalling - ECM losing critical operating parameters'
  ],
  
  causes: [
    'ECM hardware failure - Internal memory circuits failing',
    'Power supply problems - Voltage spikes or drops corrupting memory',
    'ECM software corruption - Programming data becoming corrupted',
    'Electromagnetic interference - External interference affecting memory',
    'Age-related ECM failure - Normal wear causing memory degradation',
    'Improper ECM programming - Incorrect software installation',
    'Water damage to ECM - Moisture causing internal component failure',
    'Excessive heat damage - High temperatures affecting ECM memory circuits'
  ],
  
  performanceImpact: 'P0601 can cause severe engine performance problems, no-start conditions, limp mode operation, and potential failure of multiple vehicle systems due to ECM memory corruption.',
  
  quickAnswer: {
    icon: 'microchip',
    meaning: 'ECM internal memory corrupted - usually requires ECM replacement or reprogramming.',
    fix: 'Reprogram ECM, check power supply, replace ECM if memory hardware failed',
    cost: '$300-$1500',
    time: '120-240 minutes',
    drivingSafety: 'May not start or run poorly. If running, drive carefully to repair shop as ECM may fail completely.'
  },
  
  aiQuestions: [
    {
      question: 'What is a checksum error in the ECM?',
      answer: 'A checksum error means the ECM has detected that stored data in its memory doesn\'t match expected values. The ECM uses mathematical calculations to verify data integrity, and when these calculations don\'t match, it indicates memory corruption.'
    },
    {
      question: 'Can P0601 be fixed by reprogramming?',
      answer: 'Sometimes P0601 can be fixed by reprogramming the ECM if the memory corruption is software-related. However, if the ECM hardware has failed, reprogramming won\'t work and ECM replacement is necessary.'
    },
    {
      question: 'What causes ECM memory corruption?',
      answer: 'ECM memory corruption can be caused by power supply problems (voltage spikes/drops), electromagnetic interference, age-related component failure, water damage, excessive heat, or improper programming procedures.'
    },
    {
      question: 'Can I drive with P0601?',
      answer: 'Driving with P0601 is risky because the ECM may fail completely at any time. If the engine runs, it may be in limp mode with poor performance. Have it diagnosed immediately to prevent being stranded.'
    }
  ],

  costAnalysis: {
    averageCost: '$300-$1500 for most P0601 repairs',
    repairOptions: [
      {
        title: 'ECM Reprogramming',
        description: 'Reprogram ECM if memory corruption is software-related (30% success rate)',
        color: '#4CAF50',
        icon: 'code',
        items: [
          { name: 'ECM programming', cost: '$150-$300' },
          { name: 'Diagnostic time', cost: '$100-$200' },
          { name: 'Labor (2-3 hours)', cost: '$200-$360' }
        ],
        total: '$450-$860',
        successRate: '30% success rate'
      },
      {
        title: 'ECM Replacement (Remanufactured)',
        description: 'Replace ECM with remanufactured unit (most common solution)',
        color: '#2196F3',
        icon: 'microchip',
        items: [
          { name: 'Remanufactured ECM', cost: '$400-$800' },
          { name: 'Programming', cost: '$150-$300' },
          { name: 'Labor (2-3 hours)', cost: '$200-$360' }
        ],
        total: '$750-$1460',
        successRate: '95% success rate'
      },
      {
        title: 'ECM Replacement (New)',
        description: 'Replace ECM with new unit (premium option)',
        color: '#FF9800',
        icon: 'star',
        items: [
          { name: 'New ECM', cost: '$800-$1500' },
          { name: 'Programming', cost: '$150-$300' },
          { name: 'Labor (2-3 hours)', cost: '$200-$360' }
        ],
        total: '$1150-$2160',
        successRate: '98% success rate'
      }
    ],
    savingTips: [
      'Try ECM reprogramming first if available - may fix software corruption',
      'Remanufactured ECMs offer significant savings over new units',
      'Check warranty coverage - some ECM failures may be covered',
      'Ensure proper power supply before ECM replacement to prevent recurrence',
      'Keep old ECM for core exchange credit when buying remanufactured unit'
    ]
  },

  diagnosticSteps: {
    estimatedTime: 'PT150M',
    steps: [
      {
        title: 'Verify ECM Communication',
        icon: 'search',
        description: 'Connect GeekOBD APP and attempt communication with ECM. Check if ECM responds to scan tool commands and can provide data.',
        geekobdTip: 'GeekOBD APP communication status indicates ECM health - intermittent or failed communication suggests memory corruption.'
      },
      {
        title: 'Check Power Supply',
        icon: 'battery',
        description: 'Test ECM power supply voltage, ground circuits, and charging system. Voltage problems can cause memory corruption.',
        geekobdTip: 'Monitor system voltage with GeekOBD APP - voltage should be stable 12V+ with engine off, 13.5V+ running.'
      },
      {
        title: 'Attempt ECM Reprogramming',
        icon: 'code',
        description: 'If ECM communicates, attempt to reprogram with latest software. This may resolve software-related memory corruption.',
        geekobdTip: 'GeekOBD APP can show if reprogramming is successful - P0601 should clear if memory corruption was software-related.'
      },
      {
        title: 'ECM Hardware Testing',
        icon: 'microchip',
        description: 'If reprogramming fails, ECM hardware has likely failed. Test ECM internal circuits if equipment available.',
        geekobdTip: 'Use GeekOBD APP to monitor ECM responses during testing - inconsistent responses indicate hardware failure.'
      },
      {
        title: 'ECM Replacement and Programming',
        icon: 'check-circle',
        description: 'Replace ECM with remanufactured or new unit. Program with vehicle-specific software and verify all systems operate properly.',
        geekobdTip: 'Use GeekOBD APP to verify new ECM communicates properly and all engine systems function correctly after replacement.'
      }
    ],
    importantNotes: [
      'P0601 usually requires ECM replacement - memory hardware has typically failed',
      'Check power supply before ECM replacement to prevent recurrence',
      'ECM programming requires vehicle-specific software and security codes'
    ]
  },

  caseStudies: [
    {
      title: 'Honda Civic ECM Memory Failure',
      vehicle: '2016 Honda Civic 1.5L Turbo, 135,000 miles',
      problem: 'Customer reported engine running poorly, multiple warning lights, and P0601 code. Engine would start but run in limp mode.',
      diagnosis: 'GeekOBD APP could communicate with ECM intermittently. Attempted reprogramming failed, indicating ECM hardware memory failure.',
      solution: 'Replaced ECM with remanufactured unit and programmed with Honda-specific software. All vehicle systems restored to normal operation.',
      cost: 'Remanufactured ECM: $485, Programming: $180, Labor: $240, Total: $905',
      result: 'P0601 code cleared permanently. Engine runs normally with full power and all warning lights turned off.'
    },
    {
      title: 'Ford F-150 Power Supply Damage',
      vehicle: '2017 Ford F-150 3.5L V6, 98,000 miles',
      problem: 'P0601 code appeared after jump-starting with incorrect polarity. Engine would start but had poor performance.',
      diagnosis: 'Reverse polarity jump-start had damaged ECM memory circuits. ECM could not maintain proper checksum calculations.',
      solution: 'Replaced ECM with new unit due to electrical damage. Also repaired damaged charging system components from reverse polarity.',
      cost: 'New ECM: $950, Programming: $200, Charging system repair: $180, Labor: $320, Total: $1650',
      result: 'P0601 code cleared and engine performance fully restored. No recurrence after proper electrical system repair.'
    }
  ],

  relatedCodes: [
    { code: 'P0600', description: 'Serial Communication Link Malfunction - Communication problems', color: '#e74c3c' },
    { code: 'P0602', description: 'Control Module Programming Error - ECM programming issues', color: '#3498db' },
    { code: 'P0603', description: 'Internal Control Module Keep Alive Memory Error - ECM memory problems', color: '#f39c12' },
    { code: 'P0604', description: 'Internal Control Module Random Access Memory Error - ECM RAM problems', color: '#9b59b6' },
    { code: 'P0605', description: 'Internal Control Module Read Only Memory Error - ECM ROM problems', color: '#4a90e2' },
    { code: 'P0606', description: 'ECM/PCM Processor Fault - ECM processor problems', color: '#e67e22' },
    { code: 'U0100', description: 'Lost Communication with ECM/PCM - Communication network problems', color: '#27ae60' }
  ],

  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P0601',
      description: 'Use GeekOBD APP for ECM memory testing!',
      features: [
        'ECM communication testing',
        'Memory integrity verification',
        'Programming status monitoring',
        'System voltage analysis'
      ]
    },
    systemCodes: {
      title: 'ECM Memory Codes',
      description: 'Related ECM internal memory codes:'
    },
    diagnosticResources: [
      {
        title: 'ECM Memory Testing',
        description: 'Professional procedures for testing ECM memory integrity',
        icon: 'microchip',
        url: '#diagnostic-steps'
      },
      {
        title: 'ECM Replacement Guide',
        description: 'Complete ECM replacement and programming procedures',
        icon: 'wrench',
        url: '../resources/ecm-replacement-guide.html'
      },
      {
        title: 'ECM Programming',
        description: 'Understanding ECM programming and calibration',
        icon: 'code',
        url: '../resources/ecm-programming.html'
      },
      {
        title: 'Power Supply Protection',
        description: 'Protecting ECM from electrical damage',
        icon: 'shield',
        url: '../resources/power-supply-protection.html'
      }
    ],
    codeInfo: {
      system: 'Engine Management',
      severity: 'HIGH',
      category: 'ECM Memory'
    }
  }
});

module.exports = p0601Data;
