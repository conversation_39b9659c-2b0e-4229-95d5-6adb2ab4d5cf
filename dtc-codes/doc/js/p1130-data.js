const { DTCData } = require('./dtc-template-generator');

// P1130 Lack of Upstream Heated Oxygen Sensor Switch - Sensor Indicates Lean 的完整数据结构
const p1130Data = new DTCData({
  code: 'P1130',
  title: 'Lack of Upstream Heated Oxygen Sensor Switch - Sensor Indicates Lean',
  description: 'The upstream heated oxygen sensor is not switching properly and indicates a lean condition.',
  definition: 'P1130 indicates that the upstream heated oxygen sensor (HO2S) is not switching properly between rich and lean readings and is stuck indicating a lean condition. This is primarily a Ford-specific code. The upstream oxygen sensor should rapidly switch between approximately 0.1V (lean) and 0.9V (rich) as the ECM adjusts the fuel mixture. When the sensor remains at low voltage (lean indication) and doesn\'t switch properly, P1130 is triggered.',
  
  symptoms: [
    'Check engine light illuminated - ECM detected oxygen sensor switching problem',
    'Poor fuel economy - ECM compensating for perceived lean condition',
    'Engine hesitation - Incorrect fuel mixture affecting performance',
    'Rough idle - Unstable fuel mixture causing idle problems',
    'Engine surging - Fluctuating power from incorrect fuel control',
    'Black smoke from exhaust - Rich mixture from ECM overcompensation',
    'Engine stalling - Severe fuel mixture problems causing shutdown',
    'Poor acceleration - Incorrect air/fuel ratio affecting power delivery',
    'Failed emissions test - Improper fuel control affecting emissions'
  ],
  
  causes: [
    'Faulty upstream oxygen sensor - Sensor not switching properly between rich/lean',
    'Contaminated oxygen sensor - Oil, coolant, or fuel additives affecting sensor',
    'Vacuum leaks - Unmetered air causing actual lean condition',
    'Fuel system problems - Low fuel pressure or clogged injectors causing lean mixture',
    'Exhaust leaks before sensor - Outside air affecting sensor readings',
    'Damaged oxygen sensor wiring - Electrical problems affecting sensor signal',
    'ECM internal fault - Control module not properly interpreting sensor signals',
    'Mass airflow sensor problems - Incorrect air measurement affecting fuel control'
  ],
  
  performanceImpact: 'P1130 causes poor fuel economy, rough running, engine hesitation, and potential stalling due to incorrect fuel mixture control based on faulty oxygen sensor feedback.',
  
  quickAnswer: {
    icon: 'thermometer-empty',
    meaning: 'Upstream oxygen sensor stuck reading lean - usually faulty sensor or actual lean condition.',
    fix: 'Check for vacuum leaks, test oxygen sensor operation, replace O2 sensor if needed',
    cost: '$180-$450',
    time: '90-150 minutes',
    drivingSafety: 'Safe to drive but expect poor performance and fuel economy. Check for vacuum leaks first.'
  },
  
  aiQuestions: [
    {
      question: 'What does "lack of switch" mean for oxygen sensors?',
      answer: 'Oxygen sensors should rapidly switch between rich (high voltage) and lean (low voltage) readings as the ECM adjusts fuel mixture. "Lack of switch" means the sensor is stuck at one reading and not responding to mixture changes.'
    },
    {
      question: 'Can vacuum leaks cause P1130?',
      answer: 'Yes, vacuum leaks can cause P1130 by creating an actual lean condition. The oxygen sensor correctly reads lean, but doesn\'t switch to rich because the engine is actually running lean due to unmetered air entering through the leak.'
    },
    {
      question: 'How do I test if my oxygen sensor is working?',
      answer: 'Use GeekOBD APP to monitor oxygen sensor voltage while the engine runs. The sensor should switch between approximately 0.1V and 0.9V several times per minute. A stuck sensor will show constant low or high voltage.'
    },
    {
      question: 'Why does the ECM think it\'s lean when it might be rich?',
      answer: 'The ECM relies on oxygen sensor feedback to determine mixture. If the sensor is faulty and stuck reading lean, the ECM will add fuel thinking the mixture is lean, potentially creating an overly rich condition despite the lean reading.'
    }
  ],

  costAnalysis: {
    averageCost: '$180-$450 for most P1130 repairs',
    repairOptions: [
      {
        title: 'Vacuum Leak Repair',
        description: 'Fix vacuum leaks causing actual lean condition (40% of cases)',
        color: '#4CAF50',
        icon: 'compress',
        items: [
          { name: 'Vacuum hoses/gaskets', cost: '$20-$80' },
          { name: 'Labor (1-2 hours)', cost: '$100-$240' }
        ],
        total: '$120-$320',
        successRate: '85% success rate'
      },
      {
        title: 'Upstream O2 Sensor Replacement',
        description: 'Replace faulty upstream oxygen sensor (50% of cases)',
        color: '#2196F3',
        icon: 'thermometer-empty',
        items: [
          { name: 'Upstream O2 sensor', cost: '$80-$200' },
          { name: 'Labor (1-1.5 hours)', cost: '$100-$180' }
        ],
        total: '$180-$380',
        successRate: '90% success rate'
      },
      {
        title: 'Fuel System Repair',
        description: 'Fix fuel delivery problems causing lean condition (10% of cases)',
        color: '#FF9800',
        icon: 'gas-pump',
        items: [
          { name: 'Fuel system components', cost: '$100-$300' },
          { name: 'Labor (2-3 hours)', cost: '$200-$360' }
        ],
        total: '$300-$660',
        successRate: '95% success rate'
      }
    ],
    savingTips: [
      'Check for vacuum leaks first - common cause and inexpensive fix',
      'Use GeekOBD APP to monitor O2 sensor switching before replacement',
      'Test fuel pressure if no vacuum leaks found',
      'Address P1130 promptly to prevent catalytic converter damage',
      'Upstream O2 sensors are usually more accessible than downstream sensors'
    ]
  },

  diagnosticSteps: {
    estimatedTime: 'PT120M',
    steps: [
      {
        title: 'Monitor O2 Sensor Operation',
        icon: 'search',
        description: 'Connect GeekOBD APP and monitor upstream oxygen sensor voltage. Sensor should switch between 0.1V and 0.9V several times per minute.',
        geekobdTip: 'GeekOBD APP can show O2 sensor voltage in real-time - healthy sensor switches rapidly, stuck sensor shows constant low voltage.'
      },
      {
        title: 'Check for Vacuum Leaks',
        icon: 'compress',
        description: 'Inspect vacuum lines, intake manifold, and throttle body for leaks that could cause actual lean condition.',
        geekobdTip: 'Monitor fuel trims with GeekOBD APP during leak testing - positive fuel trims indicate vacuum leaks causing lean conditions.'
      },
      {
        title: 'Test Fuel System Pressure',
        icon: 'gas-pump',
        description: 'Check fuel pressure and flow to ensure adequate fuel delivery. Low fuel pressure can cause lean conditions.',
        geekobdTip: 'Use GeekOBD APP to monitor fuel trims - high positive trims may indicate fuel delivery problems causing lean operation.'
      },
      {
        title: 'Inspect Exhaust System',
        icon: 'wind',
        description: 'Check for exhaust leaks before the oxygen sensor that could allow outside air to affect sensor readings.',
        geekobdTip: 'Monitor O2 sensor readings with GeekOBD APP while checking exhaust - readings may fluctuate if outside air is entering.'
      },
      {
        title: 'Component Replacement and Verification',
        icon: 'check-circle',
        description: 'Replace oxygen sensor or repair identified problems. Clear codes and verify proper O2 sensor switching.',
        geekobdTip: 'Use GeekOBD APP to verify new O2 sensor switches properly between rich and lean readings during normal operation.'
      }
    ],
    importantNotes: [
      'P1130 can indicate faulty sensor or actual lean condition',
      'Check for vacuum leaks before replacing oxygen sensor',
      'Monitor O2 sensor switching pattern to verify proper operation'
    ]
  },

  caseStudies: [
    {
      title: 'Ford Explorer Vacuum Leak',
      vehicle: '2016 Ford Explorer 3.5L V6, 125,000 miles',
      problem: 'Customer reported poor fuel economy, engine hesitation, and P1130 code. Engine seemed to run rough at idle.',
      diagnosis: 'GeekOBD APP showed upstream O2 sensor stuck at low voltage. Found large vacuum leak in intake manifold gasket.',
      solution: 'Replaced intake manifold gasket to repair vacuum leak. O2 sensor was actually working correctly but reading actual lean condition.',
      cost: 'Intake manifold gasket: $65, Labor: $240, Total: $305',
      result: 'P1130 code cleared after vacuum leak repair. O2 sensor now switches properly and fuel economy improved by 4 MPG.'
    },
    {
      title: 'Ford F-150 Failed O2 Sensor',
      vehicle: '2017 Ford F-150 5.0L V8, 135,000 miles',
      problem: 'P1130 code with black smoke from exhaust and poor performance. No vacuum leaks found during inspection.',
      diagnosis: 'Upstream O2 sensor was stuck at low voltage and would not switch. Sensor had failed internally and was not responding to mixture changes.',
      solution: 'Replaced upstream oxygen sensor with OEM part. Sensor was contaminated and could not switch between rich and lean readings.',
      cost: 'Upstream O2 sensor: $125, Labor: $95, Total: $220',
      result: 'P1130 code cleared and O2 sensor now switches properly. Black smoke eliminated and engine performance restored.'
    }
  ],

  relatedCodes: [
    { code: 'P0131', description: 'O2 Sensor Circuit Low Voltage Bank 1 Sensor 1 - Related upstream O2 sensor', color: '#e74c3c' },
    { code: 'P0132', description: 'O2 Sensor Circuit High Voltage Bank 1 Sensor 1 - Related upstream O2 sensor', color: '#3498db' },
    { code: 'P0133', description: 'O2 Sensor Circuit Slow Response Bank 1 Sensor 1 - Related O2 sensor response', color: '#f39c12' },
    { code: 'P0171', description: 'System Too Lean Bank 1 - Related lean condition', color: '#9b59b6' },
    { code: 'P0174', description: 'System Too Lean Bank 2 - Related lean condition', color: '#4a90e2' },
    { code: 'P1131', description: 'Lack of Upstream HO2S Switch - Sensor Indicates Rich - Opposite condition', color: '#e67e22' },
    { code: 'P0420', description: 'Catalyst System Efficiency Below Threshold - Can result from O2 sensor problems', color: '#27ae60' }
  ],

  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P1130',
      description: 'Use GeekOBD APP for oxygen sensor testing!',
      features: [
        'Real-time O2 sensor monitoring',
        'Switching pattern analysis',
        'Fuel trim monitoring',
        'Lean condition detection'
      ]
    },
    systemCodes: {
      title: 'Oxygen Sensor Codes',
      description: 'Related oxygen sensor and fuel system codes:'
    },
    diagnosticResources: [
      {
        title: 'O2 Sensor Testing',
        description: 'Professional procedures for testing oxygen sensors',
        icon: 'thermometer-empty',
        url: '#diagnostic-steps'
      },
      {
        title: 'Vacuum Leak Detection',
        description: 'Finding and repairing vacuum leaks',
        icon: 'compress',
        url: '../resources/vacuum-leak-detection.html'
      },
      {
        title: 'Fuel System Diagnosis',
        description: 'Testing fuel pressure and delivery systems',
        icon: 'gas-pump',
        url: '../resources/fuel-system-diagnosis.html'
      },
      {
        title: 'Lean Condition Diagnosis',
        description: 'Diagnosing and fixing lean running conditions',
        icon: 'balance-scale',
        url: '../resources/lean-condition-diagnosis.html'
      }
    ],
    codeInfo: {
      system: 'Fuel System',
      severity: 'MEDIUM',
      category: 'Oxygen Sensor'
    }
  }
});

module.exports = p1130Data;
