const { DTCData } = require('./dtc-template-generator');

// P1001 Key On Engine Running (KOER) Test Not Able to Complete 的完整数据结构
const p1001Data = new DTCData({
  code: 'P1001',
  title: 'Key On Engine Running (KOER) Test Not Able to Complete',
  description: 'The Key On Engine Running self-test could not be completed.',
  definition: 'P1001 indicates that the Key On Engine Running (KOER) self-test could not be completed. This is primarily a Ford-specific code that appears when the ECM cannot complete its engine-running diagnostic tests. The KOER test is part of Ford\'s self-diagnostic system that checks various engine components and systems while the engine is running. When this test cannot complete due to system problems or test conditions not being met, P1001 is triggered.',
  
  symptoms: [
    'Check engine light illuminated - P1001 code present in system',
    'Failed emissions test - KOER test incomplete',
    'Other diagnostic codes present - Underlying problems preventing test completion',
    'Engine performance problems - Issues that prevent proper testing',
    'Rough idle or stalling - Conditions that interfere with testing',
    'Engine overheating - Thermal conditions preventing test completion',
    'Abnormal engine operation - Problems that make testing impossible',
    'Scan tool communication issues - Problems accessing test functions'
  ],
  
  causes: [
    'Engine performance problems - Rough running preventing stable test conditions',
    'Cooling system problems - Engine overheating during test procedures',
    'Fuel system issues - Poor fuel delivery affecting test completion',
    'Ignition system problems - Misfiring or timing issues during testing',
    'Vacuum leaks - Affecting engine stability during test procedures',
    'Sensor malfunctions - Faulty sensors preventing accurate test results',
    'ECM communication problems - Control module unable to complete test sequence',
    'Exhaust system restrictions - Backpressure affecting engine operation during tests'
  ],
  
  performanceImpact: 'P1001 indicates underlying engine problems that prevent proper diagnostic testing, which can affect emissions compliance and may indicate performance issues that need attention.',
  
  quickAnswer: {
    icon: 'exclamation-triangle',
    meaning: 'Engine running test cannot complete - usually indicates underlying engine problems.',
    fix: 'Diagnose and fix underlying engine problems, check cooling system, repair performance issues',
    cost: '$150-$800',
    time: '120-360 minutes',
    drivingSafety: 'May be unsafe if engine has performance problems. Diagnose underlying issues that prevent test completion.'
  },
  
  aiQuestions: [
    {
      question: 'What is a KOER test?',
      answer: 'KOER (Key On Engine Running) test is a Ford diagnostic procedure where the ECM runs specific tests while the engine is running to check various systems and components. It\'s part of Ford\'s comprehensive self-diagnostic system.'
    },
    {
      question: 'Why can\'t the KOER test complete?',
      answer: 'The KOER test requires stable engine operation and specific conditions. If the engine has performance problems, overheating, rough idle, or other issues, the ECM cannot complete the test because conditions aren\'t suitable for accurate testing.'
    },
    {
      question: 'Is P1001 serious?',
      answer: 'P1001 itself isn\'t dangerous, but it indicates underlying engine problems that prevent proper testing. These underlying issues may be serious and should be diagnosed and repaired to ensure proper engine operation.'
    },
    {
      question: 'How do I fix P1001?',
      answer: 'P1001 is fixed by addressing the underlying problems that prevent the KOER test from completing. This typically involves diagnosing and repairing engine performance issues, cooling problems, or other system malfunctions.'
    }
  ],

  costAnalysis: {
    averageCost: '$150-$800 for most P1001 repairs',
    repairOptions: [
      {
        title: 'Engine Performance Repair',
        description: 'Fix underlying engine problems preventing test completion (60% of cases)',
        color: '#4CAF50',
        icon: 'wrench',
        items: [
          { name: 'Various engine repairs', cost: '$100-$500' },
          { name: 'Diagnostic and labor', cost: '$150-$300' }
        ],
        total: '$250-$800',
        successRate: '85% success rate'
      },
      {
        title: 'Cooling System Repair',
        description: 'Fix overheating issues preventing test completion (25% of cases)',
        color: '#2196F3',
        icon: 'thermometer-half',
        items: [
          { name: 'Cooling system components', cost: '$50-$300' },
          { name: 'Labor (1-3 hours)', cost: '$100-$360' }
        ],
        total: '$150-$660',
        successRate: '90% success rate'
      },
      {
        title: 'Sensor/Electrical Repair',
        description: 'Fix sensor or electrical problems (15% of cases)',
        color: '#FF9800',
        icon: 'bolt',
        items: [
          { name: 'Sensors or wiring repair', cost: '$80-$250' },
          { name: 'Labor (1-2 hours)', cost: '$100-$240' }
        ],
        total: '$180-$490',
        successRate: '95% success rate'
      }
    ],
    savingTips: [
      'Diagnose underlying problems first - P1001 is a symptom, not the root cause',
      'Use GeekOBD APP to identify specific engine problems preventing test completion',
      'Address cooling system issues first if engine is overheating',
      'Check for other codes that may indicate the root cause',
      'P1001 will clear once underlying problems are fixed'
    ]
  },

  diagnosticSteps: {
    estimatedTime: 'PT180M',
    steps: [
      {
        title: 'Check for Other Codes',
        icon: 'search',
        description: 'Scan for additional diagnostic codes that may indicate the underlying problems preventing KOER test completion.',
        geekobdTip: 'GeekOBD APP can scan all systems - other codes often reveal the specific problems preventing test completion.'
      },
      {
        title: 'Monitor Engine Operation',
        icon: 'tachometer',
        description: 'Monitor engine parameters during idle and operation. Check for rough running, overheating, or abnormal operation.',
        geekobdTip: 'Use GeekOBD APP to monitor engine RPM, temperature, fuel trims, and other parameters - identify unstable conditions.'
      },
      {
        title: 'Check Cooling System',
        icon: 'thermometer-half',
        description: 'Verify cooling system operation and engine temperature. Overheating can prevent KOER test completion.',
        geekobdTip: 'Monitor coolant temperature with GeekOBD APP - engine must be at proper operating temperature for testing.'
      },
      {
        title: 'Test Engine Performance',
        icon: 'cog',
        description: 'Check fuel system, ignition system, and engine mechanical condition. Poor performance prevents stable test conditions.',
        geekobdTip: 'GeekOBD APP can show fuel trims, misfire data, and other performance indicators - identify systems causing instability.'
      },
      {
        title: 'Repair and Verify',
        icon: 'check-circle',
        description: 'Repair identified problems and attempt KOER test again. P1001 should clear once underlying issues are resolved.',
        geekobdTip: 'Use GeekOBD APP to verify engine parameters are stable and KOER test can complete successfully after repairs.'
      }
    ],
    importantNotes: [
      'P1001 indicates underlying engine problems that prevent testing',
      'Fix root causes rather than just clearing the code',
      'Engine must operate stably for KOER test to complete'
    ]
  },

  caseStudies: [
    {
      title: 'Ford Mustang Overheating Issue',
      vehicle: '2016 Ford Mustang 3.7L V6, 85,000 miles',
      problem: 'Customer reported P1001 code and failed emissions test. Engine was running hot and had rough idle.',
      diagnosis: 'GeekOBD APP showed engine temperature was too high for KOER test completion. Found faulty thermostat causing overheating.',
      solution: 'Replaced faulty thermostat and performed cooling system service. Engine now maintains proper operating temperature.',
      cost: 'Thermostat: $45, Coolant: $25, Labor: $120, Total: $190',
      result: 'P1001 code cleared after cooling system repair. KOER test now completes successfully and vehicle passed emissions test.'
    },
    {
      title: 'Ford F-150 Vacuum Leak',
      vehicle: '2017 Ford F-150 5.0L V8, 95,000 miles',
      problem: 'P1001 code with rough idle and poor performance. Engine would not maintain stable RPM during testing.',
      diagnosis: 'Engine had severe vacuum leak causing unstable idle. GeekOBD APP showed erratic fuel trims and RPM fluctuations.',
      solution: 'Found and repaired large vacuum leak in intake manifold gasket. Engine now runs smoothly with stable idle.',
      cost: 'Intake manifold gasket: $85, Labor: $240, Total: $325',
      result: 'P1001 cleared after vacuum leak repair. Engine runs smoothly and KOER test completes without issues.'
    }
  ],

  relatedCodes: [
    { code: 'P1000', description: 'OBD System Readiness Test Not Complete - Related system readiness', color: '#3498db' },
    { code: 'P0300', description: 'Random Misfire - May prevent KOER test completion', color: '#e74c3c' },
    { code: 'P0171', description: 'System Too Lean - May cause unstable engine operation', color: '#f39c12' },
    { code: 'P0128', description: 'Coolant Thermostat - May prevent proper test conditions', color: '#9b59b6' },
    { code: 'P0401', description: 'EGR Flow Insufficient - May affect engine stability during testing', color: '#4a90e2' },
    { code: 'P0506', description: 'Idle Air Control RPM Lower Than Expected - May prevent stable test conditions', color: '#e67e22' },
    { code: 'P0507', description: 'Idle Air Control RPM Higher Than Expected - May prevent stable test conditions', color: '#27ae60' }
  ],

  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P1001',
      description: 'Use GeekOBD APP for KOER test diagnosis!',
      features: [
        'Engine parameter monitoring',
        'Performance analysis',
        'Temperature tracking',
        'System stability verification'
      ]
    },
    systemCodes: {
      title: 'Ford Diagnostic Codes',
      description: 'Related Ford-specific diagnostic codes:'
    },
    diagnosticResources: [
      {
        title: 'KOER Test Procedures',
        description: 'Understanding Ford KOER diagnostic testing',
        icon: 'exclamation-triangle',
        url: '#diagnostic-steps'
      },
      {
        title: 'Engine Performance Diagnosis',
        description: 'Diagnosing engine performance problems',
        icon: 'cog',
        url: '../resources/engine-performance-diagnosis.html'
      },
      {
        title: 'Cooling System Service',
        description: 'Cooling system diagnosis and repair',
        icon: 'thermometer-half',
        url: '../resources/cooling-system-service.html'
      },
      {
        title: 'Ford Diagnostic Systems',
        description: 'Understanding Ford-specific diagnostic procedures',
        icon: 'wrench',
        url: '../resources/ford-diagnostic-systems.html'
      }
    ],
    codeInfo: {
      system: 'Ford Diagnostics',
      severity: 'MEDIUM',
      category: 'System Test'
    }
  }
});

module.exports = p1001Data;
