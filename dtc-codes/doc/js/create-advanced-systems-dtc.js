const fs = require('fs');

// 创建高级汽车系统的故障码页面
// 涵盖制造商特定代码、高级安全系统、自动驾驶技术

// P1000系列 - 制造商特定发动机管理代码
const advancedPCodeDatabase = {
  P1000: {
    title: "OBDII Monitor Testing Not Complete",
    description: "The Engine Control Module indicates that OBDII monitor testing has not been completed.",
    definition: "The Engine Control Module indicates that the On-Board Diagnostics II (OBDII) monitor testing cycle has not been completed. This code appears when the vehicle's emission control systems have not been fully tested by the ECM's self-diagnostic routines. This typically occurs after battery disconnection, ECM replacement, or code clearing.",
    symptoms: [
      "Check engine light may be illuminated - Monitor testing incomplete",
      "Vehicle may fail emissions testing - OBDII readiness monitors not set",
      "Diagnostic scanner shows monitors not ready - Testing cycle incomplete",
      "No other symptoms typically present - System operational but untested",
      "Emissions test rejection - Required monitors not completed",
      "ECM learning process ongoing - System adapting to driving conditions",
      "Fuel trim learning in progress - Long-term adjustments being made",
      "Catalyst efficiency testing pending - Emissions system evaluation incomplete"
    ],
    causes: [
      "Recent battery disconnection - ECM memory cleared",
      "ECM replacement or reprogramming - New module learning process",
      "Diagnostic trouble codes recently cleared - Monitor reset required",
      "Insufficient driving cycles completed - Testing conditions not met",
      "Vehicle not driven under required conditions - Monitor enabling criteria not satisfied",
      "Short trips only - Monitors require extended driving for completion",
      "Cold weather operation - Some monitors require specific temperature conditions",
      "Fuel system issues preventing monitor completion - Underlying problems affecting testing"
    ],
    performanceImpact: "P1000 does not affect vehicle performance but prevents emissions testing compliance. The vehicle will fail emissions inspection until all required OBDII monitors complete their testing cycles through proper driving conditions.",
    caseStudies: [
      {
        title: "2018 Ford F-150 - Post Battery Replacement",
        vehicle: "2018 Ford F-150, 5.0L V8, 75,000 miles",
        symptoms: "Emissions test failure, P1000 code, monitors not ready",
        diagnosis: "GeekOBD diagnostic scan revealed P1000 with multiple monitors showing 'not ready' status after battery replacement. Vehicle required specific driving cycle to complete monitor testing.",
        solution: "Performed Ford-specific drive cycle including city and highway driving, idle periods, and specific temperature conditions. Cleared codes with GeekOBD APP and verified all monitors ready - passed emissions test",
        parts: "No parts required - drive cycle procedure only",
        labor: "1.0 hour drive cycle instruction ($100)",
        total: "$100"
      },
      {
        title: "2016 Honda Civic - After ECM Programming",
        vehicle: "2016 Honda Civic, 2.0L 4-cylinder, 95,000 miles",
        symptoms: "Check engine light, P1000 stored after ECM update",
        diagnosis: "GeekOBD diagnostic scan showed P1000 following ECM software update. All emission monitors required completion through Honda-specific drive cycle procedures.",
        solution: "Completed Honda drive cycle requirements including warm-up, steady cruise, deceleration, and idle phases. Monitored with GeekOBD APP until all systems ready - normal operation restored",
        parts: "No parts required - drive cycle completion",
        labor: "Drive cycle guidance and verification (1.5 hours) ($150)",
        total: "$150"
      }
    ],
    relatedCodes: [
      { code: "P1001", desc: "KOER Test Cannot Be Completed" },
      { code: "P1002", desc: "KOEO Test Cannot Be Completed" },
      { code: "P1003", desc: "Hard Fault Present" },
      { code: "P1100", desc: "Mass Air Flow Sensor Intermittent" },
      { code: "P0420", desc: "Catalyst System Efficiency Below Threshold" }
    ]
  },

  P1001: {
    title: "KOER Test Cannot Be Completed",
    description: "The Engine Control Module cannot complete the Key On Engine Running (KOER) self-test.",
    definition: "The Engine Control Module cannot complete the Key On Engine Running (KOER) self-test procedure. This diagnostic test runs while the engine is operating and checks various engine systems and sensors. Failure to complete this test indicates underlying issues preventing proper system evaluation.",
    symptoms: [
      "Check engine light illuminated - KOER test failure detected",
      "Diagnostic test procedures cannot complete - Self-test interrupted",
      "Engine performance issues may be present - Underlying problems affecting test",
      "Emissions testing may fail - Required diagnostics incomplete",
      "Scanner shows test incomplete status - KOER procedure not finished",
      "Engine may run rough during testing - System evaluation problems",
      "Intermittent engine operation - Conditions preventing test completion",
      "Multiple system warnings possible - Various components not tested"
    ],
    causes: [
      "Engine mechanical problems - Preventing stable operation during test",
      "Vacuum leaks - Affecting engine stability during KOER test",
      "Fuel system issues - Inconsistent fuel delivery during testing",
      "Ignition system problems - Misfires interrupting test procedure",
      "Sensor malfunctions - Providing inconsistent data during test",
      "ECM internal faults - Module cannot execute test properly",
      "Wiring issues - Electrical problems affecting test completion",
      "Engine not at proper operating temperature - Test conditions not met"
    ],
    performanceImpact: "P1001 indicates the ECM cannot properly evaluate engine systems, potentially masking other problems and preventing accurate diagnosis of engine performance issues and emissions system functionality.",
    caseStudies: [
      {
        title: "2017 Ford Mustang - Vacuum Leak Interference",
        vehicle: "2017 Ford Mustang, 5.0L V8, 68,000 miles",
        symptoms: "Rough idle, KOER test failure, P1001 code stored",
        diagnosis: "GeekOBD diagnostic scan revealed P1001 with KOER test unable to complete. Found large vacuum leak at intake manifold causing unstable idle preventing test completion.",
        solution: "Repaired vacuum leak at intake manifold gasket, verified stable idle operation, completed KOER test successfully. Cleared codes with GeekOBD APP and verified all systems tested properly",
        parts: "Intake manifold gasket ($85), vacuum hoses ($25)",
        labor: "3.0 hours ($300)",
        total: "$410"
      },
      {
        title: "2016 Lincoln MKZ - Fuel Pressure Issues",
        vehicle: "2016 Lincoln MKZ, 2.0L Turbo, 105,000 miles",
        symptoms: "Engine hesitation, KOER test incomplete, P1001 stored",
        diagnosis: "GeekOBD diagnostic scan showed P1001 with KOER test failure. Fuel pressure testing revealed inconsistent pressure during test causing engine instability and test interruption.",
        solution: "Replaced fuel pump and fuel filter, verified stable fuel pressure, completed KOER test procedure. Cleared codes with GeekOBD APP and confirmed all diagnostic tests passing",
        parts: "Fuel pump ($285), fuel filter ($45), fuel pressure regulator ($125)",
        labor: "4.5 hours ($450)",
        total: "$905"
      }
    ],
    relatedCodes: [
      { code: "P1000", desc: "OBDII Monitor Testing Not Complete" },
      { code: "P1002", desc: "KOEO Test Cannot Be Completed" },
      { code: "P1003", desc: "Hard Fault Present" },
      { code: "P0171", desc: "System Too Lean (Bank 1)" },
      { code: "P0300", desc: "Random/Multiple Cylinder Misfire" }
    ]
  }
};

// C0500系列 - 高级底盘控制系统
const advancedCCodeDatabase = {
  C0500: {
    title: "Vehicle Dynamics Control System Malfunction",
    description: "The Vehicle Dynamics Control module has detected a system malfunction affecting vehicle stability management.",
    definition: "The Vehicle Dynamics Control module has detected a system malfunction that affects the vehicle's ability to maintain optimal stability and handling characteristics. This advanced system integrates multiple vehicle systems including steering, braking, suspension, and powertrain to provide enhanced vehicle control and safety.",
    symptoms: [
      "Vehicle dynamics warning light illuminated - System malfunction detected",
      "Reduced vehicle stability - Electronic control compromised",
      "Handling characteristics changed - Vehicle dynamics altered",
      "Electronic stability control affected - Integrated system issues",
      "Active suspension not responding - Dynamic control disabled",
      "Steering feel abnormal - Electronic assistance affected",
      "Traction control performance reduced - System integration problems",
      "Sport/comfort modes not functioning - Drive mode selection disabled"
    ],
    causes: [
      "Vehicle dynamics control module failure - Central processing unit malfunction",
      "Multiple sensor failures - Yaw rate, lateral acceleration, or steering angle sensors",
      "Hydraulic system malfunction - Active suspension or steering components",
      "CAN bus communication errors - Module communication interrupted",
      "Power supply issues to VDC system - Voltage or ground problems",
      "Software corruption in control module - Programming errors",
      "Mechanical component failures - Actuators or control mechanisms",
      "Integration system conflicts - Multiple control modules not coordinating"
    ],
    performanceImpact: "C0500 significantly reduces vehicle dynamics control capabilities, potentially affecting handling, stability, ride quality, and overall vehicle safety during dynamic driving conditions and emergency maneuvers.",
    caseStudies: [
      {
        title: "2019 Porsche 911 - VDC Module Failure",
        vehicle: "2019 Porsche 911, 3.0L Turbo, 45,000 miles",
        symptoms: "Multiple stability warnings, handling issues, C0500 code",
        diagnosis: "GeekOBD diagnostic scan revealed C0500 with vehicle dynamics control module internal failure. Module could not coordinate between steering, suspension, and stability systems.",
        solution: "Replaced vehicle dynamics control module with Porsche OEM unit, performed complete system programming and calibration. Cleared codes with GeekOBD APP and tested all dynamic systems - full functionality restored",
        parts: "VDC control module ($2185), programming service ($300), calibration ($200)",
        labor: "5.0 hours ($500)",
        total: "$3185"
      },
      {
        title: "2017 BMW M3 - Sensor Integration Failure",
        vehicle: "2017 BMW M3, 3.0L Twin Turbo, 75,000 miles",
        symptoms: "Sport mode not working, stability control issues, C0500 stored",
        diagnosis: "GeekOBD diagnostic scan showed C0500 with multiple sensor communication failures. Found damaged wiring harness affecting sensor integration and vehicle dynamics control.",
        solution: "Repaired damaged sensor wiring harness, performed sensor calibration and system initialization. Cleared codes with GeekOBD APP and verified all drive modes functioning - normal dynamics control restored",
        parts: "Sensor wiring harness ($385), protective sheathing ($45), connectors ($65)",
        labor: "4.0 hours ($400)",
        total: "$895"
      }
    ],
    relatedCodes: [
      { code: "C0501", desc: "Vehicle Dynamics Yaw Rate Sensor Malfunction" },
      { code: "C0502", desc: "Vehicle Dynamics Lateral Acceleration Sensor Malfunction" },
      { code: "C0503", desc: "Vehicle Dynamics Steering Angle Sensor Malfunction" },
      { code: "C0400", desc: "Electronic Stability Control System Malfunction" },
      { code: "C0504", desc: "Vehicle Dynamics Hydraulic System Malfunction" }
    ]
  }
};

// B0300系列 - 高级车身电子系统
const advancedBCodeDatabase = {
  B0300: {
    title: "Keyless Entry System Malfunction",
    description: "The Body Control Module has detected a malfunction in the keyless entry system.",
    definition: "The Body Control Module has detected a malfunction in the keyless entry system that prevents proper remote access control. This system manages door locks, trunk release, panic alarm, and remote start functions through radio frequency communication between the key fob and vehicle receiver.",
    symptoms: [
      "Remote key fob not working - No response to button presses",
      "Doors not locking/unlocking remotely - Keyless entry disabled",
      "Remote start not functioning - Key fob communication failure",
      "Panic alarm not working - Emergency function disabled",
      "Trunk release not responding - Remote access unavailable",
      "Keyless entry warning lights - System fault indication",
      "Intermittent remote operation - Unreliable key fob response",
      "Vehicle security system affected - Anti-theft integration issues"
    ],
    causes: [
      "Keyless entry receiver module failure - Internal component malfunction",
      "Key fob battery low or dead - Insufficient transmission power",
      "Damaged keyless entry antenna - Signal reception problems",
      "Body control module keyless entry circuit fault - Module malfunction",
      "RF interference - External signals disrupting communication",
      "Key fob programming lost - Synchronization with vehicle lost",
      "Wiring damage to keyless entry system - Electrical connection problems",
      "Door lock actuator failures - Mechanical components not responding"
    ],
    performanceImpact: "B0300 prevents remote vehicle access and security functions, requiring manual key operation and potentially compromising vehicle security and convenience features that depend on keyless entry operation.",
    caseStudies: [
      {
        title: "2018 Toyota Camry - Receiver Module Failure",
        vehicle: "2018 Toyota Camry, 2.5L 4-cylinder, 85,000 miles",
        symptoms: "Key fob not working, no remote functions, B0300 code stored",
        diagnosis: "GeekOBD diagnostic scan revealed B0300 with keyless entry system fault. Receiver module testing showed internal failure preventing RF signal reception and processing.",
        solution: "Replaced keyless entry receiver module with OEM Toyota part, programmed key fobs to new module. Cleared codes with GeekOBD APP and tested all remote functions - full keyless entry operation restored",
        parts: "Keyless entry receiver module ($285), programming service ($75)",
        labor: "2.0 hours ($200)",
        total: "$560"
      },
      {
        title: "2016 Honda Accord - Antenna Damage",
        vehicle: "2016 Honda Accord, 2.0L Turbo, 105,000 miles",
        symptoms: "Intermittent key fob operation, reduced range, B0300 stored",
        diagnosis: "GeekOBD diagnostic scan showed B0300 with keyless entry communication issues. Found damaged keyless entry antenna from aftermarket radio installation, causing poor signal reception.",
        solution: "Replaced damaged keyless entry antenna, properly routed antenna cable away from interference sources. Cleared codes with GeekOBD APP and verified full range keyless operation",
        parts: "Keyless entry antenna ($125), antenna cable ($45), mounting hardware ($15)",
        labor: "2.5 hours ($250)",
        total: "$435"
      }
    ],
    relatedCodes: [
      { code: "B0301", desc: "Key Fob Battery Low" },
      { code: "B0302", desc: "Keyless Entry Antenna Circuit Malfunction" },
      { code: "B0303", desc: "Remote Start System Malfunction" },
      { code: "B0304", desc: "Door Lock Actuator Circuit Malfunction" },
      { code: "B0305", desc: "Keyless Entry Programming Error" }
    ]
  },

  P1002: {
    title: "KOEO Test Cannot Be Completed",
    description: "The Engine Control Module cannot complete the Key On Engine Off (KOEO) self-test.",
    definition: "The Engine Control Module cannot complete the Key On Engine Off (KOEO) self-test procedure. This diagnostic test runs with the key on but engine off, checking various electrical systems, sensors, and circuits. Failure to complete indicates electrical or sensor issues preventing proper system evaluation.",
    symptoms: [
      "Check engine light illuminated - KOEO test failure detected",
      "Diagnostic procedures cannot complete - Self-test interrupted",
      "Scanner shows test incomplete status - KOEO procedure not finished",
      "Electrical system warnings possible - Various circuits not tested",
      "Sensor readings may be erratic - Underlying electrical issues",
      "Engine may not start properly - Electrical problems affecting operation",
      "Intermittent electrical operation - Conditions preventing test completion",
      "Multiple system codes possible - Various components not evaluated"
    ],
    causes: [
      "Electrical system problems - Wiring or connector issues",
      "Sensor circuit malfunctions - Open or short circuits",
      "ECM power supply issues - Voltage or ground problems",
      "Battery voltage too low - Insufficient power for test completion",
      "Alternator charging problems - Electrical system instability",
      "Corroded electrical connections - High resistance affecting test",
      "ECM internal faults - Module cannot execute test properly",
      "Blown fuses - Electrical circuits not powered during test"
    ],
    performanceImpact: "P1002 indicates the ECM cannot properly evaluate electrical systems, potentially masking electrical problems and preventing accurate diagnosis of sensor circuits and electrical component functionality.",
    caseStudies: [
      {
        title: "2018 Chevrolet Silverado - Battery Voltage Issues",
        vehicle: "2018 Chevrolet Silverado, 5.3L V8, 85,000 miles",
        symptoms: "KOEO test failure, electrical issues, P1002 code stored",
        diagnosis: "GeekOBD diagnostic scan revealed P1002 with KOEO test unable to complete. Battery testing showed low voltage (11.8V) preventing proper electrical system evaluation during test.",
        solution: "Replaced weak battery with OEM Chevrolet battery, tested charging system, completed KOEO test successfully. Cleared codes with GeekOBD APP and verified all electrical systems tested properly",
        parts: "Battery ($185), battery terminals ($25), terminal cleaner ($8)",
        labor: "1.5 hours ($150)",
        total: "$368"
      },
      {
        title: "2016 Ford Explorer - Corroded Ground Connection",
        vehicle: "2016 Ford Explorer, 3.5L V6, 115,000 miles",
        symptoms: "Intermittent electrical issues, KOEO test incomplete, P1002 stored",
        diagnosis: "GeekOBD diagnostic scan showed P1002 with KOEO test failure. Found severely corroded main ground connection causing electrical instability and preventing test completion.",
        solution: "Cleaned and repaired corroded ground connections, applied protective coating, verified stable electrical operation. Cleared codes with GeekOBD APP and confirmed KOEO test completion",
        parts: "Ground strap ($35), terminal cleaner ($8), protective coating ($12)",
        labor: "2.0 hours ($200)",
        total: "$255"
      }
    ],
    relatedCodes: [
      { code: "P1000", desc: "OBDII Monitor Testing Not Complete" },
      { code: "P1001", desc: "KOER Test Cannot Be Completed" },
      { code: "P1003", desc: "Hard Fault Present" },
      { code: "P0562", desc: "System Voltage Low" },
      { code: "P0563", desc: "System Voltage High" }
    ]
  },

  P1100: {
    title: "Mass Air Flow Sensor Intermittent",
    description: "The Engine Control Module has detected intermittent operation of the Mass Air Flow sensor.",
    definition: "The Engine Control Module has detected intermittent operation of the Mass Air Flow (MAF) sensor. This sensor measures the amount of air entering the engine for proper fuel mixture calculation. Intermittent operation causes inconsistent air flow readings, affecting fuel delivery and engine performance.",
    symptoms: [
      "Check engine light illuminated - MAF sensor intermittent fault detected",
      "Engine hesitation during acceleration - Inconsistent air flow readings",
      "Rough idle - Intermittent sensor causing fuel mixture problems",
      "Poor fuel economy - Incorrect air/fuel ratio calculations",
      "Engine stalling - Sensor dropout causing fuel delivery issues",
      "Black exhaust smoke - Rich fuel mixture from sensor malfunction",
      "Engine surging - Intermittent sensor causing power fluctuations",
      "Failed emissions test - Improper fuel mixture affecting emissions"
    ],
    causes: [
      "Contaminated MAF sensor element - Dirt or oil affecting sensor operation",
      "Loose MAF sensor connector - Intermittent electrical connection",
      "Damaged MAF sensor wiring - Intermittent open or short circuits",
      "MAF sensor element degradation - Age-related sensitivity loss",
      "Air filter contamination - Debris reaching sensor element",
      "Intake air leaks - Unmetered air affecting sensor readings",
      "ECM MAF input circuit intermittent fault - Module connection issues",
      "Vibration affecting sensor mounting - Mechanical connection problems"
    ],
    performanceImpact: "P1100 causes intermittent fuel mixture problems, resulting in poor engine performance, reduced fuel economy, emissions issues, and potential engine damage from incorrect air/fuel ratios during sensor dropout periods.",
    caseStudies: [
      {
        title: "2017 Nissan Altima - Contaminated MAF Sensor",
        vehicle: "2017 Nissan Altima, 2.5L 4-cylinder, 95,000 miles",
        symptoms: "Engine hesitation, poor fuel economy, P1100 code stored",
        diagnosis: "GeekOBD diagnostic scan revealed P1100 with intermittent MAF sensor operation. Sensor inspection showed contamination from dirty air filter causing intermittent readings.",
        solution: "Cleaned MAF sensor with appropriate cleaner, replaced dirty air filter, verified stable sensor operation. Cleared codes with GeekOBD APP and road tested - smooth engine operation restored",
        parts: "MAF sensor cleaner ($15), air filter ($28), intake cleaner ($12)",
        labor: "1.0 hour ($100)",
        total: "$155"
      },
      {
        title: "2016 Honda Accord - Loose Connector",
        vehicle: "2016 Honda Accord, 2.0L Turbo, 105,000 miles",
        symptoms: "Intermittent stalling, engine surging, P1100 stored",
        diagnosis: "GeekOBD diagnostic scan showed P1100 with MAF sensor intermittent fault. Found loose MAF sensor connector causing intermittent signal loss and fuel mixture problems.",
        solution: "Secured MAF sensor connector, cleaned connection points, applied dielectric grease for protection. Cleared codes with GeekOBD APP and verified consistent sensor operation",
        parts: "Connector repair kit ($25), dielectric grease ($8), contact cleaner ($5)",
        labor: "0.5 hours ($50)",
        total: "$88"
      }
    ],
    relatedCodes: [
      { code: "P0101", desc: "Mass Air Flow Sensor Circuit Range/Performance" },
      { code: "P0102", desc: "Mass Air Flow Sensor Circuit Low Input" },
      { code: "P0103", desc: "Mass Air Flow Sensor Circuit High Input" },
      { code: "P0171", desc: "System Too Lean (Bank 1)" },
      { code: "P0174", desc: "System Too Lean (Bank 2)" }
    ]
  }
};

// 更多高级C码
const additionalAdvancedCCodeDatabase = {
  C0501: {
    title: "Vehicle Dynamics Yaw Rate Sensor Malfunction",
    description: "The Vehicle Dynamics Control module has detected a malfunction in the yaw rate sensor.",
    definition: "The Vehicle Dynamics Control module has detected a malfunction in the yaw rate sensor that measures the vehicle's rotation around its vertical axis. This sensor is critical for electronic stability control, providing real-time data about vehicle rotation to help prevent skidding and maintain directional control.",
    symptoms: [
      "Electronic stability control warning light illuminated - Yaw sensor fault detected",
      "ESC system disabled - No rotational motion detection",
      "Vehicle dynamics control not functioning - Stability management compromised",
      "Traction control affected - Integrated system issues",
      "ABS performance may be reduced - Shared sensor data",
      "Hill start assist disabled - Yaw sensor input required",
      "Sport/track modes not available - Dynamic control systems offline",
      "Increased risk of skidding - No electronic intervention for rotation control"
    ],
    causes: [
      "Faulty yaw rate sensor - Internal gyroscope failure",
      "Damaged yaw sensor wiring - Cut, chafed, or corroded wires",
      "Corroded sensor connector - Poor electrical connection",
      "Sensor mounting issues - Improper installation or damage",
      "VDC module yaw input circuit fault - Module malfunction",
      "Sensor calibration drift - Age-related accuracy loss",
      "Water damage to yaw sensor - Moisture affecting electronics",
      "Sensor power supply issues - Voltage or ground problems"
    ],
    performanceImpact: "C0501 disables yaw rate monitoring, preventing electronic stability control from detecting and correcting vehicle rotation, significantly increasing the risk of skidding and loss of control during cornering and emergency maneuvers.",
    caseStudies: [
      {
        title: "2018 Audi Q5 - Yaw Sensor Internal Failure",
        vehicle: "2018 Audi Q5, 3.0L Turbo, 75,000 miles",
        symptoms: "ESC light on, stability control not working, C0501 code",
        diagnosis: "GeekOBD diagnostic scan revealed C0501 with yaw rate sensor malfunction. Sensor testing showed internal gyroscope failure preventing accurate rotation measurement.",
        solution: "Replaced yaw rate sensor with OEM Audi part, performed sensor calibration and ESC system initialization. Cleared codes with GeekOBD APP and tested stability control - normal operation restored",
        parts: "Yaw rate sensor ($485), sensor calibration service ($125)",
        labor: "2.5 hours ($250)",
        total: "$860"
      },
      {
        title: "2016 Mercedes C-Class - Sensor Mounting Damage",
        vehicle: "2016 Mercedes C-Class, 2.0L Turbo, 105,000 miles",
        symptoms: "Multiple stability warnings, C0501 and related codes stored",
        diagnosis: "GeekOBD diagnostic scan showed C0501 with yaw sensor fault. Found damaged sensor mounting bracket from collision, causing sensor misalignment and incorrect readings.",
        solution: "Replaced damaged yaw sensor mounting bracket, realigned sensor position, performed complete system calibration. Cleared codes with GeekOBD APP and verified all stability functions",
        parts: "Yaw sensor mounting bracket ($185), alignment tools ($45), calibration service ($150)",
        labor: "3.5 hours ($350)",
        total: "$730"
      }
    ],
    relatedCodes: [
      { code: "C0500", desc: "Vehicle Dynamics Control System Malfunction" },
      { code: "C0502", desc: "Vehicle Dynamics Lateral Acceleration Sensor Malfunction" },
      { code: "C0503", desc: "Vehicle Dynamics Steering Angle Sensor Malfunction" },
      { code: "C0400", desc: "Electronic Stability Control System Malfunction" },
      { code: "C0504", desc: "Vehicle Dynamics Hydraulic System Malfunction" }
    ]
  }
};

// 页面生成函数
function createDetailedCodePage(code, data) {
  const templateContent = fs.readFileSync('p0142.html', 'utf8');
  let content = templateContent;
  
  // 基本替换
  content = content.replace(/P0142/g, code);
  content = content.replace(/p0142/g, code.toLowerCase());
  
  // 替换标题和描述
  content = content.replace(/O2 Sensor Circuit Malfunction \(Bank 1 Sensor 3\)/g, data.title);
  content = content.replace(/The Engine Control Module has detected a malfunction in the oxygen sensor circuit for Bank 1, Sensor 3 \(post-catalytic converter\)\./g, data.description);
  
  // 替换定义
  const oldDefinition = /The Engine Control Module has detected a malfunction in the oxygen sensor circuit for Bank 1, Sensor 3 \(post-catalytic converter\)\. This diagnostic trouble code indicates a specific issue within the engine\/powertrain that requires attention to ensure proper vehicle operation and safety\./g;
  content = content.replace(oldDefinition, data.definition);
  
  // 替换症状
  if (data.symptoms) {
    const symptomsHtml = data.symptoms.map(symptom => `								<li><strong>${symptom}</strong></li>`).join('\n');
    const symptomsRegex = /<ul>\s*<li><strong>Check Engine Light illuminated[\s\S]*?<\/ul>/;
    content = content.replace(symptomsRegex, `<ul>\n${symptomsHtml}\n							</ul>`);
  }
  
  // 替换原因
  if (data.causes) {
    const causesHtml = data.causes.map(cause => `									<li>${cause}</li>`).join('\n');
    const causesRegex = /<ol>\s*<li>Faulty oxygen sensor[\s\S]*?<\/ol>/;
    content = content.replace(causesRegex, `<ol>\n${causesHtml}\n								</ol>`);
  }
  
  // 替换性能影响
  if (data.performanceImpact) {
    const impactRegex = /<strong><i class="fa fa-exclamation-triangle"><\/i> Performance Impact:<\/strong>[\s\S]*?<\/div>/;
    content = content.replace(impactRegex, `<strong><i class="fa fa-exclamation-triangle"></i> Performance Impact:</strong> ${data.performanceImpact}\n							</div>`);
  }
  
  // 替换维修案例
  if (data.caseStudies && data.caseStudies.length >= 2) {
    const case1 = data.caseStudies[0];
    const case2 = data.caseStudies[1];
    
    const caseStudyHtml = `							<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
								<h4>Case 1: ${case1.title}</h4>
								<p><strong>Vehicle:</strong> ${case1.vehicle}</p>
								<p><strong>Symptoms:</strong> ${case1.symptoms}</p>
								<p><strong>Diagnosis:</strong> ${case1.diagnosis}</p>
								<p><strong>Solution:</strong> ${case1.solution}</p>
								<p><strong>Parts:</strong> ${case1.parts}</p>
								<p><strong>Labor:</strong> ${case1.labor}</p>
								<p><strong>Total Cost:</strong> ${case1.total}</p>
							</div>

							<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
								<h4>Case 2: ${case2.title}</h4>
								<p><strong>Vehicle:</strong> ${case2.vehicle}</p>
								<p><strong>Symptoms:</strong> ${case2.symptoms}</p>
								<p><strong>Diagnosis:</strong> ${case2.diagnosis}</p>
								<p><strong>Solution:</strong> ${case2.solution}</p>
								<p><strong>Parts:</strong> ${case2.parts}</p>
								<p><strong>Labor:</strong> ${case2.labor}</p>
								<p><strong>Total Cost:</strong> ${case2.total}</p>
							</div>`;
    
    const caseStudyRegex = /<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">[\s\S]*?<\/div>\s*<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">[\s\S]*?<\/div>/;
    content = content.replace(caseStudyRegex, caseStudyHtml);
  }
  
  // 替换相关代码
  if (data.relatedCodes) {
    const relatedHtml = data.relatedCodes.map(related => 
      `									<li style="margin-bottom: 10px;"><a href="${related.code.toLowerCase()}.html" style="color: #007bff;">${related.code} - ${related.desc}</a></li>`
    ).join('\n');
    
    const relatedRegex = /<li style="margin-bottom: 10px;"><a href="p0140\.html"[\s\S]*?<li style="margin-bottom: 10px;"><a href="p0145\.html"[\s\S]*?<\/li>/;
    content = content.replace(relatedRegex, relatedHtml);
  }
  
  return content;
}

// 批量创建高级汽车系统故障码页面
const advancedSystemsCodesToCreate = [
  // 高级P码 - 制造商特定诊断代码
  { code: 'P1000', database: advancedPCodeDatabase },
  { code: 'P1001', database: advancedPCodeDatabase },
  { code: 'P1002', database: advancedPCodeDatabase },
  { code: 'P1100', database: advancedPCodeDatabase },
  // 高级C码 - 车辆动态控制系统
  { code: 'C0500', database: advancedCCodeDatabase },
  { code: 'C0501', database: additionalAdvancedCCodeDatabase },
  // 高级B码 - 车身电子系统
  { code: 'B0300', database: advancedBCodeDatabase }
];

let totalCreated = 0;

console.log('🚀 Creating advanced automotive systems DTC pages...\n');

advancedSystemsCodesToCreate.forEach(({ code, database }) => {
  if (database[code]) {
    try {
      const htmlContent = createDetailedCodePage(code, database[code]);
      fs.writeFileSync(`${code.toLowerCase()}.html`, htmlContent, 'utf8');
      console.log(`✅ Created ${code.toLowerCase()}.html - ${database[code].title}`);
      totalCreated++;
    } catch (error) {
      console.log(`❌ Failed to create ${code.toLowerCase()}.html: ${error.message}`);
    }
  }
});

console.log(`\n🎉 Successfully created ${totalCreated} advanced automotive systems DTC pages!`);
console.log('\n📊 Advanced Systems Coverage:');
console.log('✅ Manufacturer-Specific Diagnostic Codes (P1000 series)');
console.log('✅ Vehicle Dynamics Control Systems (C0500 series)');
console.log('✅ Advanced Body Electronics (B0300 series)');
console.log('\nAdvanced automotive systems coverage complete! 🎯');
