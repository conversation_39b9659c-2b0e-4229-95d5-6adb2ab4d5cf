const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0118Data = require('./p0118-data');

console.log('🚀 Generating P0118 - ECT Sensor High Input Page\n');

try {
  // 创建模板生成器实例
  const generator = new DTCTemplateGenerator();
  
  // 生成HTML内容
  console.log('📝 Generating HTML content...');
  const htmlContent = generator.generatePage(p0118Data);
  
  // 保存到文件
  const outputPath = path.join(__dirname, '../../p0118.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0118 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
  // 统计信息
  console.log('\n📊 Content Statistics:');
  console.log(`   • Total file size: ${Math.round(htmlContent.length / 1024)}KB`);
  console.log(`   • Symptoms: ${p0118Data.symptoms.length} items`);
  console.log(`   • Causes: ${p0118Data.causes.length} items`);
  console.log(`   • AI Questions: ${p0118Data.aiQuestions.length} items`);
  console.log(`   • Repair Options: ${p0118Data.costAnalysis.repairOptions.length} options`);
  console.log(`   • Diagnostic Steps: ${p0118Data.diagnosticSteps.steps.length} steps`);
  console.log(`   • Case Studies: ${p0118Data.caseStudies.length} studies`);
  console.log(`   • Related Codes: ${p0118Data.relatedCodes.length} codes`);
  
  // 验证关键组件
  console.log('\n🔍 Component Verification:');
  console.log(`   ✅ Quick Answer: ${p0118Data.quickAnswer ? 'Present' : 'Missing'}`);
  console.log(`   ✅ Cost Analysis: ${p0118Data.costAnalysis ? 'Present' : 'Missing'}`);
  console.log(`   ✅ Diagnostic Steps: ${p0118Data.diagnosticSteps ? 'Present' : 'Missing'}`);
  console.log(`   ✅ Case Studies: ${p0118Data.caseStudies.length > 0 ? 'Present' : 'Missing'}`);
  console.log(`   ✅ Sidebar: ${p0118Data.sidebarData ? 'Present' : 'Missing'}`);
  
  console.log('\n🎉 P0118 optimization completed successfully!');
  console.log('\n💡 Key Features:');
  console.log('   • CRITICAL overheating prevention procedures');
  console.log('   • Emergency diagnostic protocols for open circuits');
  console.log('   • Real-world Ford Explorer and Nissan Altima case studies');
  console.log('   • High-priority repair strategies ($90-$380 range)');
  console.log('   • GeekOBD APP integration for emergency monitoring');
  console.log('   • Complete ECT sensor network with safety emphasis');
  
  console.log('\n⚠️  SAFETY NOTICE:');
  console.log('   • P0118 can cause engine overheating - HIGH PRIORITY repair');
  console.log('   • Cooling fans may not operate - monitor temperature closely');
  console.log('   • Severity level: HIGH (vs MEDIUM for other ECT codes)');
  
  console.log('\n🔄 Next steps:');
  console.log('   1. Review generated p0118.html for accuracy');
  console.log('   2. Test emergency procedures and safety warnings');
  console.log('   3. Continue with P0125 (Insufficient Coolant Temperature)');
  console.log('   4. Complete coolant temperature sensor diagnostic series');
  
} catch (error) {
  console.error('❌ Error generating P0118 page:', error.message);
  console.error(error.stack);
  process.exit(1);
}
