const fs = require('fs');
const path = require('path');

// 从HTML文件中提取故障码标题
function extractTitleFromFile(filename) {
  try {
    const content = fs.readFileSync(filename, 'utf8');
    const titleMatch = content.match(/<title>([^|]+)/);
    if (titleMatch) {
      const fullTitle = titleMatch[1].trim();
      // 提取故障码和描述，例如 "P0100 - Mass Air Flow Circuit Malfunction"
      const parts = fullTitle.split(' - ');
      if (parts.length >= 2) {
        return parts[1].trim(); // 返回描述部分
      }
    }
    return '';
  } catch (error) {
    return '';
  }
}

// 获取所有现有的故障码文件及其描述
function getAllDTCFiles() {
  const files = fs.readdirSync('.');
  const dtcFiles = {
    P: [],
    C: [],
    B: [],
    U: []
  };

  files.forEach(file => {
    if (file.endsWith('.html') && file.match(/^[pcbu]\d+\.html$/i)) {
      const code = file.replace('.html', '').toUpperCase();
      const category = code[0];
      if (dtcFiles[category]) {
        const description = extractTitleFromFile(file);
        dtcFiles[category].push({
          code: code,
          description: description
        });
      }
    }
  });

  // 排序
  Object.keys(dtcFiles).forEach(category => {
    dtcFiles[category].sort((a, b) => {
      const numA = parseInt(a.code.substring(1));
      const numB = parseInt(b.code.substring(1));
      return numA - numB;
    });
  });

  return dtcFiles;
}

// 生成链接HTML（带描述的卡片式布局）
function generateLinkHTML(codeObj) {
  const filename = codeObj.code.toLowerCase() + '.html';
  const shortDescription = codeObj.description.length > 25 ?
    codeObj.description.substring(0, 25) + '...' : codeObj.description;

  return `<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../${filename}'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">${codeObj.code}</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">${shortDescription}</div>
  </div>`;
}

// 更新引擎索引页面
function updateEngineIndex(pCodes) {
  const indexPath = 'engine/index.html';
  let content = fs.readFileSync(indexPath, 'utf8');
  
  // 按系列分组P码
  const series = {
    'P0100-P0199': { codes: [], title: 'Air/Fuel Metering & Auxiliary Emission Controls' },
    'P0200-P0299': { codes: [], title: 'Fuel System (Injector Circuit)' },
    'P0300-P0399': { codes: [], title: 'Ignition System or Misfire' },
    'P0400-P0499': { codes: [], title: 'Auxiliary Emission Controls' },
    'P0500-P0599': { codes: [], title: 'Vehicle Speed Controls and Idle Control System' },
    'P0600-P0699': { codes: [], title: 'Computer Output Circuit' },
    'P0700-P0799': { codes: [], title: 'Transmission' },
    'P0800-P0899': { codes: [], title: 'Transmission' },
    'P0900-P0999': { codes: [], title: 'Transmission' },
    'P1000-P1999': { codes: [], title: 'Manufacturer Specific' },
    'P2000-P2999': { codes: [], title: 'Manufacturer Specific' },
    'P3000-P3999': { codes: [], title: 'Manufacturer Specific' },
    'P4000-P4999': { codes: [], title: 'Manufacturer Specific' }
  };
  
  pCodes.forEach(codeObj => {
    const num = parseInt(codeObj.code.substring(1));
    if (num >= 100 && num <= 199) series['P0100-P0199'].codes.push(codeObj);
    else if (num >= 200 && num <= 299) series['P0200-P0299'].codes.push(codeObj);
    else if (num >= 300 && num <= 399) series['P0300-P0399'].codes.push(codeObj);
    else if (num >= 400 && num <= 499) series['P0400-P0499'].codes.push(codeObj);
    else if (num >= 500 && num <= 599) series['P0500-P0599'].codes.push(codeObj);
    else if (num >= 600 && num <= 699) series['P0600-P0699'].codes.push(codeObj);
    else if (num >= 700 && num <= 799) series['P0700-P0799'].codes.push(codeObj);
    else if (num >= 800 && num <= 899) series['P0800-P0899'].codes.push(codeObj);
    else if (num >= 900 && num <= 999) series['P0900-P0999'].codes.push(codeObj);
    else if (num >= 1000 && num <= 1999) series['P1000-P1999'].codes.push(codeObj);
    else if (num >= 2000 && num <= 2999) series['P2000-P2999'].codes.push(codeObj);
    else if (num >= 3000 && num <= 3999) series['P3000-P3999'].codes.push(codeObj);
    else if (num >= 4000 && num <= 4999) series['P4000-P4999'].codes.push(codeObj);
  });
  
  // 生成新的索引内容
  let newIndexContent = '';
  Object.keys(series).forEach(seriesKey => {
    if (series[seriesKey].codes.length > 0) {
      newIndexContent += `
<!-- ${seriesKey} - ${series[seriesKey].title} -->
<div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
<h4 style="color: #333; margin-bottom: 15px; display: flex; align-items: center;">
<span style="background: #28a745; color: white; padding: 5px 10px; border-radius: 4px; font-size: 14px; margin-right: 10px;">${seriesKey}</span>
${series[seriesKey].title}
</h4>
<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(220px, 1fr)); gap: 12px;">
${series[seriesKey].codes.map(codeObj => generateLinkHTML(codeObj)).join('\n')}
</div>
</div>
`;
    }
  });
  
  // 查找现有的P码内容区域并替换
  const startMarker = '<!-- P0100';
  const startIndex = content.indexOf(startMarker);
  if (startIndex !== -1) {
    const beforeContent = content.substring(0, startIndex);
    const afterMarker = '</div>\n</div>\n</div>';
    const endIndex = content.lastIndexOf(afterMarker);
    const afterContent = endIndex !== -1 ? content.substring(endIndex + afterMarker.length) : '';

    content = beforeContent + newIndexContent.trim() + '\n\n' + afterContent;
  } else {
    // 如果找不到标记，在主要内容区域添加
    const mainContentMarker = '<div class="container" style="max-width: 1200px; margin: 0 auto; padding: 20px;">';
    const mainIndex = content.indexOf(mainContentMarker);
    if (mainIndex !== -1) {
      const insertPoint = content.indexOf('</div>', mainIndex + mainContentMarker.length);
      if (insertPoint !== -1) {
        content = content.substring(0, insertPoint) + newIndexContent + '\n' + content.substring(insertPoint);
      }
    }
  }
  
  fs.writeFileSync(indexPath, content, 'utf8');
  console.log(`✅ Updated engine/index.html with ${pCodes.length} P-codes`);
}

// 更新底盘索引页面
function updateChassisIndex(cCodes) {
  const indexPath = 'chassis/index.html';
  let content = fs.readFileSync(indexPath, 'utf8');
  
  // 按系列分组C码
  const series = {
    'C0000-C0099': { codes: [], title: 'Vehicle Speed & Brake Controls' },
    'C0100-C0199': { codes: [], title: 'ABS & Wheel Speed Sensors' },
    'C0200-C0299': { codes: [], title: 'ABS System Controls' },
    'C0300-C0399': { codes: [], title: 'Power Steering Controls' },
    'C0400-C0499': { codes: [], title: 'Electronic Stability Control' },
    'C0500-C0599': { codes: [], title: 'Vehicle Dynamics Control' },
    'C0600-C0699': { codes: [], title: 'Advanced Driver Assistance' },
    'C0700-C0799': { codes: [], title: 'Electronic Brake Distribution' },
    'C0800-C0899': { codes: [], title: 'Adaptive Cruise Control' },
    'C0900-C0999': { codes: [], title: 'Lane Keeping & Collision Avoidance' },
    'C1000-C1099': { codes: [], title: 'Advanced Chassis Systems' },
    'C1100-C1199': { codes: [], title: 'Wheel Speed & Position Sensors' },
    'C1200-C1299': { codes: [], title: 'Electronic Brake Force Distribution' },
    'C1300-C1399': { codes: [], title: 'Suspension Control Systems' },
    'C1400-C1499': { codes: [], title: 'Electronic Stability Control Advanced' },
    'C1500-C1599': { codes: [], title: 'Brake Assist Systems' }
  };
  
  cCodes.forEach(codeObj => {
    const num = parseInt(codeObj.code.substring(1));
    if (num >= 0 && num <= 99) series['C0000-C0099'].codes.push(codeObj);
    else if (num >= 100 && num <= 199) series['C0100-C0199'].codes.push(codeObj);
    else if (num >= 200 && num <= 299) series['C0200-C0299'].codes.push(codeObj);
    else if (num >= 300 && num <= 399) series['C0300-C0399'].codes.push(codeObj);
    else if (num >= 400 && num <= 499) series['C0400-C0499'].codes.push(codeObj);
    else if (num >= 500 && num <= 599) series['C0500-C0599'].codes.push(codeObj);
    else if (num >= 600 && num <= 699) series['C0600-C0699'].codes.push(codeObj);
    else if (num >= 700 && num <= 799) series['C0700-C0799'].codes.push(codeObj);
    else if (num >= 800 && num <= 899) series['C0800-C0899'].codes.push(codeObj);
    else if (num >= 900 && num <= 999) series['C0900-C0999'].codes.push(codeObj);
    else if (num >= 1000 && num <= 1099) series['C1000-C1099'].codes.push(codeObj);
    else if (num >= 1100 && num <= 1199) series['C1100-C1199'].codes.push(codeObj);
    else if (num >= 1200 && num <= 1299) series['C1200-C1299'].codes.push(codeObj);
    else if (num >= 1300 && num <= 1399) series['C1300-C1399'].codes.push(codeObj);
    else if (num >= 1400 && num <= 1499) series['C1400-C1499'].codes.push(codeObj);
    else if (num >= 1500 && num <= 1599) series['C1500-C1599'].codes.push(codeObj);
  });
  
  // 生成新的索引内容
  let newIndexContent = '';
  Object.keys(series).forEach(seriesKey => {
    if (series[seriesKey].codes.length > 0) {
      newIndexContent += `
<!-- ${seriesKey} - ${series[seriesKey].title} -->
<div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
<h4 style="color: #333; margin-bottom: 15px; display: flex; align-items: center;">
<span style="background: #dc3545; color: white; padding: 5px 10px; border-radius: 4px; font-size: 14px; margin-right: 10px;">${seriesKey}</span>
${series[seriesKey].title}
</h4>
<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(220px, 1fr)); gap: 12px;">
${series[seriesKey].codes.map(codeObj => generateLinkHTML(codeObj)).join('\n')}
</div>
</div>
`;
    }
  });
  
  // 查找并替换现有内容
  const startMarker = '<!-- C0';
  const startIndex = content.indexOf(startMarker);
  if (startIndex !== -1) {
    const beforeContent = content.substring(0, startIndex);
    const afterMarker = '</div>\n</div>\n</div>';
    const endIndex = content.lastIndexOf(afterMarker);
    const afterContent = endIndex !== -1 ? content.substring(endIndex + afterMarker.length) : '';
    
    content = beforeContent + newIndexContent.trim() + '\n\n' + afterContent;
  }
  
  fs.writeFileSync(indexPath, content, 'utf8');
  console.log(`✅ Updated chassis/index.html with ${cCodes.length} C-codes`);
}

// 更新车身索引页面
function updateBodyIndex(bCodes) {
  const indexPath = 'body/index.html';
  let content = fs.readFileSync(indexPath, 'utf8');
  
  // 按系列分组B码
  const series = {
    'B0000-B0099': { codes: [], title: 'Body Control & Lighting' },
    'B0100-B0199': { codes: [], title: 'Airbag & Safety Systems' },
    'B0200-B0299': { codes: [], title: 'Airbag Control Modules' },
    'B0300-B0399': { codes: [], title: 'Body Control Modules' },
    'B0400-B0499': { codes: [], title: 'Security & Anti-theft' },
    'B0500-B0599': { codes: [], title: 'Advanced Body Controls' },
    'B0600-B0699': { codes: [], title: 'Airbag System Advanced' },
    'B0700-B0799': { codes: [], title: 'Power Window & Door Controls' },
    'B1000-B1099': { codes: [], title: 'HVAC & Climate Control' },
    'B1200-B1299': { codes: [], title: 'Adaptive Lighting Systems' },
    'B1300-B1399': { codes: [], title: 'Power Seat Controls' },
    'B1400-B1499': { codes: [], title: 'Door Lock & Security Systems' },
    'B2000-B2099': { codes: [], title: 'Manufacturer Specific Body' },
    'B3000-B3099': { codes: [], title: 'Manufacturer Specific Body' },
    'B4000-B4099': { codes: [], title: 'Manufacturer Specific Body' }
  };
  
  bCodes.forEach(code => {
    const num = parseInt(code.substring(1));
    if (num >= 0 && num <= 99) series['B0000-B0099'].codes.push(code);
    else if (num >= 100 && num <= 199) series['B0100-B0199'].codes.push(code);
    else if (num >= 200 && num <= 299) series['B0200-B0299'].codes.push(code);
    else if (num >= 300 && num <= 399) series['B0300-B0399'].codes.push(code);
    else if (num >= 400 && num <= 499) series['B0400-B0499'].codes.push(code);
    else if (num >= 500 && num <= 599) series['B0500-B0599'].codes.push(code);
    else if (num >= 600 && num <= 699) series['B0600-B0699'].codes.push(code);
    else if (num >= 700 && num <= 799) series['B0700-B0799'].codes.push(code);
    else if (num >= 1000 && num <= 1099) series['B1000-B1099'].codes.push(code);
    else if (num >= 1200 && num <= 1299) series['B1200-B1299'].codes.push(code);
    else if (num >= 1300 && num <= 1399) series['B1300-B1399'].codes.push(code);
    else if (num >= 1400 && num <= 1499) series['B1400-B1499'].codes.push(code);
    else if (num >= 2000 && num <= 2099) series['B2000-B2099'].codes.push(code);
    else if (num >= 3000 && num <= 3099) series['B3000-B3099'].codes.push(code);
    else if (num >= 4000 && num <= 4099) series['B4000-B4099'].codes.push(code);
  });
  
  // 生成新的索引内容
  let newIndexContent = '';
  Object.keys(series).forEach(seriesKey => {
    if (series[seriesKey].codes.length > 0) {
      newIndexContent += `
<!-- ${seriesKey} - ${series[seriesKey].title} -->
<div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
<h4 style="color: #333; margin-bottom: 15px; display: flex; align-items: center;">
<span style="background: #ffc107; color: #212529; padding: 5px 10px; border-radius: 4px; font-size: 14px; margin-right: 10px;">${seriesKey}</span>
${series[seriesKey].title}
</h4>
<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 10px;">
${series[seriesKey].codes.map(code => generateLinkHTML(code)).join('\n')}
</div>
</div>
`;
    }
  });
  
  // 查找并替换现有内容
  const startMarker = '<!-- B0';
  const startIndex = content.indexOf(startMarker);
  if (startIndex !== -1) {
    const beforeContent = content.substring(0, startIndex);
    const afterMarker = '</div>\n</div>\n</div>';
    const endIndex = content.lastIndexOf(afterMarker);
    const afterContent = endIndex !== -1 ? content.substring(endIndex + afterMarker.length) : '';
    
    content = beforeContent + newIndexContent.trim() + '\n\n' + afterContent;
  }
  
  fs.writeFileSync(indexPath, content, 'utf8');
  console.log(`✅ Updated body/index.html with ${bCodes.length} B-codes`);
}

// 更新网络索引页面
function updateNetworkIndex(uCodes) {
  const indexPath = 'network/index.html';
  let content = fs.readFileSync(indexPath, 'utf8');
  
  // 按系列分组U码
  const series = {
    'U0100-U0199': { codes: [], title: 'Engine & Powertrain Communication' },
    'U0200-U0299': { codes: [], title: 'Chassis & Body Communication' },
    'U0300-U0399': { codes: [], title: 'Advanced Driver Assistance Communication' },
    'U0400-U0499': { codes: [], title: 'Telematics & Connectivity' },
    'U0500-U0599': { codes: [], title: 'Autonomous Driving Communication' },
    'U0600-U0699': { codes: [], title: 'Vehicle-to-Everything (V2X)' },
    'U0700-U0799': { codes: [], title: 'Advanced Control Modules' },
    'U0800-U0899': { codes: [], title: 'Transmission Communication' }
  };
  
  uCodes.forEach(code => {
    const num = parseInt(code.substring(1));
    if (num >= 100 && num <= 199) series['U0100-U0199'].codes.push(code);
    else if (num >= 200 && num <= 299) series['U0200-U0299'].codes.push(code);
    else if (num >= 300 && num <= 399) series['U0300-U0399'].codes.push(code);
    else if (num >= 400 && num <= 499) series['U0400-U0499'].codes.push(code);
    else if (num >= 500 && num <= 599) series['U0500-U0599'].codes.push(code);
    else if (num >= 600 && num <= 699) series['U0600-U0699'].codes.push(code);
    else if (num >= 700 && num <= 799) series['U0700-U0799'].codes.push(code);
    else if (num >= 800 && num <= 899) series['U0800-U0899'].codes.push(code);
  });
  
  // 生成新的索引内容
  let newIndexContent = '';
  Object.keys(series).forEach(seriesKey => {
    if (series[seriesKey].codes.length > 0) {
      newIndexContent += `
<!-- ${seriesKey} - ${series[seriesKey].title} -->
<div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
<h4 style="color: #333; margin-bottom: 15px; display: flex; align-items: center;">
<span style="background: #17a2b8; color: white; padding: 5px 10px; border-radius: 4px; font-size: 14px; margin-right: 10px;">${seriesKey}</span>
${series[seriesKey].title}
</h4>
<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 10px;">
${series[seriesKey].codes.map(code => generateLinkHTML(code)).join('\n')}
</div>
</div>
`;
    }
  });
  
  // 查找并替换现有内容
  const startMarker = '<!-- U0';
  const startIndex = content.indexOf(startMarker);
  if (startIndex !== -1) {
    const beforeContent = content.substring(0, startIndex);
    const afterMarker = '</div>\n</div>\n</div>';
    const endIndex = content.lastIndexOf(afterMarker);
    const afterContent = endIndex !== -1 ? content.substring(endIndex + afterMarker.length) : '';
    
    content = beforeContent + newIndexContent.trim() + '\n\n' + afterContent;
  }
  
  fs.writeFileSync(indexPath, content, 'utf8');
  console.log(`✅ Updated network/index.html with ${uCodes.length} U-codes`);
}

// 主函数
function main() {
  console.log('🚀 Updating DTC index pages with all available codes...\n');
  
  const dtcFiles = getAllDTCFiles();
  
  console.log('📊 Found DTC files:');
  console.log(`P-codes: ${dtcFiles.P.length}`);
  console.log(`C-codes: ${dtcFiles.C.length}`);
  console.log(`B-codes: ${dtcFiles.B.length}`);
  console.log(`U-codes: ${dtcFiles.U.length}`);
  console.log(`Total: ${dtcFiles.P.length + dtcFiles.C.length + dtcFiles.B.length + dtcFiles.U.length}\n`);
  
  // 更新各个索引页面
  updateEngineIndex(dtcFiles.P);
  updateChassisIndex(dtcFiles.C);
  updateBodyIndex(dtcFiles.B);
  updateNetworkIndex(dtcFiles.U);
  
  console.log('\n🎉 All index pages updated successfully!');
  console.log('✅ Engine index updated with P-codes');
  console.log('✅ Chassis index updated with C-codes');
  console.log('✅ Body index updated with B-codes');
  console.log('✅ Network index updated with U-codes');
}

main();
