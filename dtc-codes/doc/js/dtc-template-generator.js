const fs = require('fs');
const path = require('path');

/**
 * DTC页面模板生成器
 * 根据数据结构生成完全优化的DTC页面
 */

// DTC数据结构定义
class DTCData {
  constructor(config) {
    this.code = config.code;
    this.title = config.title;
    this.description = config.description;
    this.definition = config.definition;
    this.symptoms = config.symptoms || [];
    this.causes = config.causes || [];
    this.performanceImpact = config.performanceImpact;
    
    // SEO优化数据
    this.quickAnswer = config.quickAnswer;
    this.aiQuestions = config.aiQuestions || [];
    this.costAnalysis = config.costAnalysis;
    this.diagnosticSteps = config.diagnosticSteps;
    this.caseStudies = config.caseStudies || [];
    this.relatedCodes = config.relatedCodes || [];
    
    // 侧边栏数据
    this.sidebarData = config.sidebarData;
  }
}

// 模板生成器类
class DTCTemplateGenerator {
  constructor() {
    this.baseTemplate = this.loadBaseTemplate();
  }

  loadBaseTemplate() {
    // 基础HTML模板
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{CODE}} - {{TITLE}} | GeekOBD</title>
    <meta name="description" content="{{DESCRIPTION}}">
    <meta name="keywords" content="{{CODE}}, {{KEYWORDS}}">
    <link rel="canonical" href="https://www.geekobd.com/dtc-codes/{{CODE_LOWER}}.html">
    
    <!-- Open Graph -->
    <meta property="og:title" content="{{CODE}} - {{TITLE}}">
    <meta property="og:description" content="{{DESCRIPTION}}">
    <meta property="og:url" content="https://www.geekobd.com/dtc-codes/{{CODE_LOWER}}.html">
    <meta property="og:type" content="article">
    
    <!-- CSS -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../css/fonts/font-awesome/css/font-awesome.css">
    <link rel="stylesheet" href="../css/animations.css" media="screen">
    <link rel="stylesheet" href="../css/superfish.css" media="screen">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/colors/blue.css" id="colors">
    <link rel="stylesheet" href="../css/theme-responsive.css">
    <link rel="stylesheet" href="../css/seo-enhancements.css">
    <link rel="shortcut icon" href="../img/ico/favicon.ico">

    <style>
    .dtc-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 60px 0 40px;
    }
    .dtc-code-badge {
        display: inline-block;
        background: rgba(255,255,255,0.2);
        padding: 10px 20px;
        border-radius: 50px;
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 20px;
    }
    .severity-badge {
        display: inline-block;
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
        text-transform: uppercase;
        margin-left: 15px;
    }
    .severity-high { background: #ff4757; color: white; }
    .severity-medium { background: #ffa502; color: white; }
    .content-section { padding: 50px 0; }
    .info-box {
        background: #f8f9fa;
        border-left: 4px solid #667eea;
        padding: 20px;
        margin: 20px 0;
        border-radius: 5px;
    }
    .warning-box {
        background: #fff3cd;
        border-left: 4px solid #ffc107;
        padding: 20px;
        margin: 20px 0;
        border-radius: 5px;
    }
    .danger-box {
        background: #f8d7da;
        border-left: 4px solid #dc3545;
        padding: 20px;
        margin: 20px 0;
        border-radius: 5px;
    }
    .breadcrumb-custom {
        background: none;
        padding: 20px 0;
        margin: 0;
    }
    .breadcrumb-custom a {
        color: #667eea;
        text-decoration: none;
    }
    .breadcrumb-custom a:hover {
        text-decoration: underline;
    }
    </style>

    {{STRUCTURED_DATA}}
</head>
<body>
    {{HEADER}}

    <!-- Breadcrumb -->
    <div class="container">
        <nav class="breadcrumb-custom">
            <a href="../index.html">Home</a> &raquo;
            <a href="../dtc-codes.html">DTC Codes</a> &raquo;
            <a href="../dtc-codes.html#engine">Engine Codes</a> &raquo;
            <span>{{CODE}}</span>
        </nav>
    </div>

	<section class="dtc-header">
		<div class="container">
			<div class="row">
				<div class="col-md-12">
					<div class="dtc-code-badge">{{CODE}}</div>
					<span class="severity-badge severity-{{SEVERITY_LOWER}}">{{SEVERITY}} Priority</span>
					<h1 style="margin: 20px 0; font-size: 36px;">{{TITLE}}</h1>
					<p style="font-size: 18px; opacity: 0.9; margin-bottom: 0;">{{DESCRIPTION}}</p>
				</div>
			</div>
		</div>
	</section>

    <!-- Main Content -->
    <section class="content-section">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    {{QUICK_ANSWER}}
                    {{AI_QUESTIONS}}
                    {{MAIN_CONTENT}}
                    {{COST_ANALYSIS}}
                    {{DIAGNOSTIC_STEPS}}
                    {{CASE_STUDIES}}
                </div>

                <div class="col-md-4">
                    {{SIDEBAR}}
                </div>
            </div>
        </div>
    </section>

    {{FOOTER}}
</body>
</html>`;
  }

  generateStructuredData(data) {
    const articleSchema = {
      "@context": "https://schema.org",
      "@type": "Article",
      "headline": `${data.code} - ${data.title}`,
      "description": data.description,
      "author": {
        "@type": "Organization",
        "name": "GeekOBD",
        "url": "https://www.geekobd.com"
      },
      "publisher": {
        "@type": "Organization",
        "name": "GeekOBD",
        "logo": {
          "@type": "ImageObject",
          "url": "https://www.geekobd.com/images/logo.png"
        }
      },
      "datePublished": new Date().toISOString().split('T')[0],
      "dateModified": new Date().toISOString().split('T')[0],
      "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": `https://www.geekobd.com/dtc-codes/${data.code.toLowerCase()}.html`
      }
    };

    const faqSchema = {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": data.aiQuestions.map(q => ({
        "@type": "Question",
        "name": q.question,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": q.answer
        }
      }))
    };

    const howToSchema = {
      "@context": "https://schema.org",
      "@type": "HowTo",
      "name": `How to Diagnose ${data.code} ${data.title}`,
      "description": `Step-by-step guide to diagnose and fix ${data.code}`,
      "totalTime": data.diagnosticSteps.estimatedTime || "PT30M",
      "estimatedCost": {
        "@type": "MonetaryAmount",
        "currency": "USD",
        "value": data.costAnalysis.averageCost || "200"
      },
      "tool": [{
        "@type": "HowToTool",
        "name": "GeekOBD APP with MOBD Adapter",
        "description": "Professional OBD2 diagnostic tool",
        "url": "https://www.geekobd.com/app.html"
      }],
      "step": data.diagnosticSteps.steps.map((step, index) => ({
        "@type": "HowToStep",
        "position": index + 1,
        "name": step.title,
        "text": step.description
      }))
    };

    return `
<!-- Article Schema -->
<script type="application/ld+json">
${JSON.stringify(articleSchema, null, 2)}
</script>

<!-- FAQ Schema -->
<script type="application/ld+json">
${JSON.stringify(faqSchema, null, 2)}
</script>

<!-- HowTo Schema -->
<script type="application/ld+json">
${JSON.stringify(howToSchema, null, 2)}
</script>`;
  }

  generateQuickAnswer(data) {
    const qa = data.quickAnswer;
    return `
<!-- Quick Answer Section for AI -->
<div id="quick-answer" style="background: #e8f4fd; border: 2px solid #4a90e2; border-radius: 10px; padding: 25px; margin-bottom: 30px;">
    <h2 style="color: #2c5aa0; margin-bottom: 15px;"><i class="fa fa-${qa.icon}"></i> Quick Answer</h2>
    <p style="font-size: 18px; font-weight: 500; margin-bottom: 15px; color: #333;">
        <strong>${data.code} means:</strong> ${qa.meaning}
    </p>
    <div style="display: flex; flex-wrap: wrap; gap: 15px; margin-bottom: 15px;">
        <span style="background: #4CAF50; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
            <i class="fa fa-wrench"></i> Fix: ${qa.fix}
        </span>
        <span style="background: #FF9800; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
            <i class="fa fa-dollar"></i> Cost: ${qa.cost}
        </span>
        <span style="background: #2196F3; color: white; padding: 8px 15px; border-radius: 20px; font-size: 14px;">
            <i class="fa fa-clock-o"></i> Time: ${qa.time}
        </span>
    </div>
    <p style="margin: 0; color: #666; font-size: 14px;">
        <strong>Can I drive with ${data.code}?</strong> ${qa.drivingSafety}
    </p>
</div>`;
  }

  generateAIQuestions(data) {
    const questionsHtml = data.aiQuestions.map((q, index) => `
    <div class="qa-item" style="margin-bottom: 25px; padding-bottom: 20px; ${index < data.aiQuestions.length - 1 ? 'border-bottom: 1px solid #eee;' : ''}">
        <h3 style="color: #333; font-size: 18px; margin-bottom: 10px;">${q.question}</h3>
        <p style="color: #666; line-height: 1.6;">${q.answer}</p>
    </div>`).join('');

    return `
<!-- AI-Friendly Q&A Section -->
<div id="ai-qa" style="background: white; border-radius: 10px; padding: 25px; margin-bottom: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
    <h2><i class="fa fa-comments"></i> Common Questions</h2>
    ${questionsHtml}
</div>`;
  }

  generatePage(data) {
    let html = this.baseTemplate;
    
    // 基本替换
    html = html.replace(/{{CODE}}/g, data.code);
    html = html.replace(/{{CODE_LOWER}}/g, data.code.toLowerCase());
    html = html.replace(/{{TITLE}}/g, data.title);
    html = html.replace(/{{DESCRIPTION}}/g, data.description);
    html = html.replace(/{{KEYWORDS}}/g, this.generateKeywords(data));
    html = html.replace(/{{SEVERITY}}/g, data.sidebarData.codeInfo.severity);
    html = html.replace(/{{SEVERITY_LOWER}}/g, data.sidebarData.codeInfo.severity.toLowerCase());
    
    // 生成各个部分
    html = html.replace('{{STRUCTURED_DATA}}', this.generateStructuredData(data));
    html = html.replace('{{QUICK_ANSWER}}', this.generateQuickAnswer(data));
    html = html.replace('{{AI_QUESTIONS}}', this.generateAIQuestions(data));
    
    // 其他部分将在后续添加
    html = html.replace('{{MAIN_CONTENT}}', this.generateMainContent(data));
    html = html.replace('{{COST_ANALYSIS}}', this.generateCostAnalysis(data));
    html = html.replace('{{DIAGNOSTIC_STEPS}}', this.generateDiagnosticSteps(data));
    html = html.replace('{{CASE_STUDIES}}', this.generateCaseStudies(data));
    html = html.replace('{{SIDEBAR}}', this.generateSidebar(data));
    html = html.replace('{{HEADER}}', this.generateHeader());
    html = html.replace('{{FOOTER}}', this.generateFooter());
    
    return html;
  }

  generateKeywords(data) {
    return `${data.code}, diagnostic trouble code, OBD2, car repair, engine problems, GeekOBD`;
  }

  generateMainContent(data) {
    const symptomsHtml = data.symptoms.map(symptom => `<li><strong>${symptom}</strong></li>`).join('\n\t\t\t\t\t\t\t\t');
    const causesHtml = data.causes.map(cause => `<li>${cause}</li>`).join('\n\t\t\t\t\t\t\t\t\t');

    return `
<!-- Main Content Section -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h2><i class="fa fa-info-circle"></i> What is ${data.code}?</h2>
    <p style="font-size: 16px; line-height: 1.6; color: #333; margin-bottom: 20px;">${data.definition}</p>

    <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; margin: 20px 0;">
        <strong><i class="fa fa-exclamation-triangle"></i> Performance Impact:</strong> ${data.performanceImpact}
    </div>
</div>

<!-- Symptoms Section -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h2><i class="fa fa-stethoscope"></i> Symptoms of ${data.code}</h2>
    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
        <ul style="margin: 0; padding-left: 20px;">
            ${symptomsHtml}
        </ul>
    </div>
</div>

<!-- Causes Section -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h2><i class="fa fa-search"></i> Common Causes of ${data.code}</h2>
    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
        <ol style="margin: 0; padding-left: 20px;">
            ${causesHtml}
        </ol>
    </div>
</div>`;
  }

  generateCostAnalysis(data) {
    const cost = data.costAnalysis;
    const repairOptionsHtml = cost.repairOptions.map(option => `
        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid ${option.color};">
            <h4 style="color: ${option.color}; margin-bottom: 10px;"><i class="fa fa-${option.icon}"></i> ${option.title}</h4>
            <p style="margin-bottom: 15px; color: #666;">${option.description}</p>
            <ul style="list-style: none; padding: 0;">
                ${option.items.map(item => `<li style="margin-bottom: 8px;"><strong>${item.name}:</strong> ${item.cost}</li>`).join('\n                ')}
                <li style="margin-bottom: 8px;"><strong>Total:</strong> <span style="color: ${option.color}; font-weight: bold;">${option.total}</span></li>
                <li style="color: #666; font-size: 14px;">Success rate: ~${option.successRate}%</li>
            </ul>
        </div>`).join('\n        ');

    return `
<!-- Cost Analysis Section -->
<div id="cost-info" style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
    <h2><i class="fa fa-calculator"></i> ${data.code} Repair Costs</h2>

    <div style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin: 20px 0;">
        <h3 style="color: #2c3e50; margin-bottom: 20px;">Cost Breakdown by Repair Type</h3>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 25px;">
            ${repairOptionsHtml}
        </div>

        <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #4CAF50;">
            <h4 style="color: #2e7d32; margin-bottom: 15px;"><i class="fa fa-lightbulb-o"></i> Money-Saving Tips</h4>
            <ul style="margin: 0; color: #2e7d32;">
                ${cost.savingTips.map(tip => `<li style="margin-bottom: 8px;">${tip}</li>`).join('\n                ')}
            </ul>
        </div>
    </div>
</div>`;
  }

  generateDiagnosticSteps(data) {
    const steps = data.diagnosticSteps;
    const stepsHtml = steps.steps.map((step, index) => `
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4a90e2;">
            <h4 style="color: #4a90e2; margin-bottom: 15px;"><i class="fa fa-${step.icon}"></i> Step ${index + 1}: ${step.title}</h4>
            <p style="margin-bottom: 15px; color: #333;">${step.description}</p>
            ${step.geekobdTip ? `<div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong style="color: #1976d2;"><i class="fa fa-mobile"></i> GeekOBD APP Tip:</strong> ${step.geekobdTip}
            </div>` : ''}
        </div>`).join('');

    return `
<!-- Diagnostic Steps Section -->
<div id="diagnostic-steps" style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
    <h2><i class="fa fa-stethoscope"></i> Diagnostic Steps</h2>

    <div style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin: 20px 0;">
        <h3 style="color: #2c3e50; margin-bottom: 20px;">Professional ${data.code} Diagnosis Process</h3>
        <p style="margin-bottom: 20px; color: #666;">Follow these systematic steps to accurately diagnose ${data.code}. Each step builds on the previous one to ensure accurate diagnosis.</p>

        ${stepsHtml}

        <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin-top: 20px; border-left: 4px solid #ffc107;">
            <h4 style="color: #856404; margin-bottom: 15px;"><i class="fa fa-exclamation-triangle"></i> Important Notes</h4>
            <ul style="margin: 0; color: #856404;">
                ${steps.importantNotes.map(note => `<li style="margin-bottom: 8px;">${note}</li>`).join('\n                ')}
            </ul>
        </div>
    </div>
</div>`;
  }

  generateCaseStudies(data) {
    const casesHtml = data.caseStudies.map((caseStudy, index) => `
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 4px solid ${index === 0 ? '#4a90e2' : '#28a745'};">
            <h4 style="color: ${index === 0 ? '#4a90e2' : '#28a745'};"><i class="fa fa-car"></i> Case ${index + 1}: ${caseStudy.title}</h4>
            <p><strong>Vehicle:</strong> ${caseStudy.vehicle}</p>
            <p><strong>Problem:</strong> ${caseStudy.problem}</p>
            <p><strong>Diagnosis:</strong> ${caseStudy.diagnosis}</p>
            <p><strong>Solution:</strong> ${caseStudy.solution}</p>
            <p><strong>Cost:</strong> ${caseStudy.cost}</p>
            <p><strong>Result:</strong> ${caseStudy.result}</p>
        </div>`).join('');

    return `
<!-- Real Repair Case Studies -->
<div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin: 30px 0;">
    <h2><i class="fa fa-file-text"></i> Real Repair Case Studies</h2>
    ${casesHtml}
</div>`;
  }

  generateSidebar(data) {
    const sidebar = data.sidebarData;
    const relatedCodesHtml = data.relatedCodes.map(code => `
                <a href="${code.code.toLowerCase()}.html" style="display: block; padding: 10px; background: #f8f9fa; border-radius: 5px; text-decoration: none; margin-bottom: 8px; border-left: 3px solid ${code.color || '#4a90e2'};">
                    <strong style="color: ${code.color || '#4a90e2'};">${code.code}</strong> - ${code.description}
                </a>`).join('');

    return `
<!-- GeekOBD APP Promotion -->
<div style="background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%); color: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: white;"><i class="fa fa-${sidebar.appPromo.icon}"></i> ${sidebar.appPromo.title}</h4>
    <p style="margin-bottom: 20px; opacity: 0.9; font-size: 14px;">${sidebar.appPromo.description}</p>
    <ul style="margin-bottom: 20px; padding-left: 20px; font-size: 14px;">
        ${sidebar.appPromo.features.map(feature => `<li style="margin-bottom: 8px;">${feature}</li>`).join('\n        ')}
    </ul>
    <a href="https://www.geekobd.com/app.html" style="display: inline-block; background: rgba(255,255,255,0.2); color: white; padding: 12px 25px; border-radius: 25px; text-decoration: none; font-weight: bold; transition: all 0.3s;">
        <i class="fa fa-download"></i> Download GeekOBD APP
    </a>
</div>

<!-- Related System Codes -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-link"></i> ${sidebar.systemCodes.title}</h4>
    <p style="margin-bottom: 15px; color: #666; font-size: 14px;">${sidebar.systemCodes.description}</p>
    <div style="margin-bottom: 15px;">
        ${relatedCodesHtml}
    </div>
</div>

<!-- Diagnostic Resources -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-tools"></i> Diagnostic Resources</h4>
    <div style="margin-bottom: 20px;">
        ${sidebar.diagnosticResources.map(resource => `
        <a href="${resource.url}" style="display: block; padding: 12px; background: #f8f9fa; border-radius: 8px; text-decoration: none; margin-bottom: 10px; border-left: 3px solid #28a745;">
            <i class="fa fa-${resource.icon}" style="color: #28a745; margin-right: 8px;"></i>
            <strong style="color: #333;">${resource.title}</strong>
            <span style="display: block; color: #666; font-size: 13px; margin-top: 5px;">${resource.description}</span>
        </a>`).join('')}
    </div>
</div>

<!-- Quick Navigation -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-compass"></i> Quick Navigation</h4>
    <div style="display: flex; flex-direction: column; gap: 8px;">
        <a href="#quick-answer" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-bolt"></i> Quick Answer
        </a>
        <a href="#ai-qa" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-comments"></i> Common Questions
        </a>
        <a href="#cost-info" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-calculator"></i> Repair Costs
        </a>
        <a href="#diagnostic-steps" style="color: #4a90e2; text-decoration: none; padding: 8px 12px; background: #f8f9fa; border-radius: 5px; font-size: 14px;">
            <i class="fa fa-stethoscope"></i> Diagnostic Steps
        </a>
    </div>
</div>

<!-- Code Information -->
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
    <h4 style="margin-bottom: 20px; color: #333;"><i class="fa fa-info-circle"></i> Code Information</h4>
    <table style="width: 100%; font-size: 14px;">
        <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Code:</strong></td>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;">${data.code}</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>System:</strong></td>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;">${sidebar.codeInfo.system}</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Severity:</strong></td>
            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><span class="severity-badge severity-${sidebar.codeInfo.severity.toLowerCase()}">${sidebar.codeInfo.severity}</span></td>
        </tr>
        <tr>
            <td style="padding: 8px 0;"><strong>Category:</strong></td>
            <td style="padding: 8px 0;">${sidebar.codeInfo.category}</td>
        </tr>
    </table>
</div>`;
  }

  generateHeader() {
    return `<div class="wrap">
	<header id="header" role="banner">
	<div class="main-header">
	<div class="container">
	<div class="row">
	<div class="col-md-3">
	<div class="logo pull-left">
	<h1> <a href="../index.html"> <img src="../img/logo.png" alt="MOBD"> </a> </h1>
	</div>
	</div>
	<div class="col-md-9">
	<div class="mobile navbar-header"> <a class="navbar-toggle" data-toggle="collapse" data-target=".menu"> <i class="icon-reorder icon-2x"></i> </a> </div>
	<nav class="collapse navbar-collapse menu" role="navigation" aria-label="Main navigation">
	<ul class="nav navbar-nav sf-menu">
	<li><a href="../index.html" class="sf-with-ul">Home</a></li>
	<li><a href="../app.html" class="sf-with-ul">APP</a></li>
	<li><a href="javascript:;;" class="sf-with-ul">Adaptor <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
	<ul>
	<li><a href="../hardware2.html" class="sf-with-ul">MOBD GPS</a></li>
	<li><a href="../hardware.html" class="sf-with-ul">MOBD</a></li>
	</ul>
	</li>
	<li><a href="javascript:;;" class="sf-with-ul">Resources <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
	<ul>
	<li><a href="../dtc-codes.html" class="sf-with-ul">DTC Codes</a></li>
	<li><a href="../obd-diagnostic-guide.html" class="sf-with-ul">OBD Guide</a></li>
	<li><a href="../vehicle-compatibility.html" class="sf-with-ul">Compatibility</a></li>
	<li><a href="../fuel-efficiency-monitoring.html" class="sf-with-ul">Fuel Efficiency</a></li>
	<li><a href="../support.html" class="sf-with-ul">Support</a></li>
	<li><a href="../blog.html" class="sf-with-ul">Blog</a></li>
	</ul>
	</li>
	<li><a href="../about.html" class="sf-with-ul">About Us</a></li>
	<li><a href="//www.mobd.cn" class="sf-with-ul" target="_blank">中文版</a></li>
	</ul>
	</nav>
	</div>
	</div>
	</div>
	</div>
	</header>`;
  }

  generateFooter() {
    return `</div>

<script src="../js/jquery.min.js"></script>
<script src="../js/bootstrap.js"></script>
<script src="../js/superfish.js"></script>
<script src="../js/custom.js"></script>`;
  }
}

module.exports = { DTCData, DTCTemplateGenerator };
