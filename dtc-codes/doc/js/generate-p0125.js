const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0125Data = require('./p0125-data');

console.log('🚀 Generating P0125 - Insufficient Coolant Temperature Page\n');

try {
  // 创建模板生成器实例
  const generator = new DTCTemplateGenerator();
  
  // 生成HTML内容
  console.log('📝 Generating HTML content...');
  const htmlContent = generator.generatePage(p0125Data);
  
  // 保存到文件
  const outputPath = path.join(__dirname, '../../p0125.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0125 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
  // 统计信息
  console.log('\n📊 Content Statistics:');
  console.log(`   • Total file size: ${Math.round(htmlContent.length / 1024)}KB`);
  console.log(`   • Symptoms: ${p0125Data.symptoms.length} items`);
  console.log(`   • Causes: ${p0125Data.causes.length} items`);
  console.log(`   • AI Questions: ${p0125Data.aiQuestions.length} items`);
  console.log(`   • Repair Options: ${p0125Data.costAnalysis.repairOptions.length} options`);
  console.log(`   • Diagnostic Steps: ${p0125Data.diagnosticSteps.steps.length} steps`);
  console.log(`   • Case Studies: ${p0125Data.caseStudies.length} studies`);
  console.log(`   • Related Codes: ${p0125Data.relatedCodes.length} codes`);
  
  // 验证关键组件
  console.log('\n🔍 Component Verification:');
  console.log(`   ✅ Quick Answer: ${p0125Data.quickAnswer ? 'Present' : 'Missing'}`);
  console.log(`   ✅ Cost Analysis: ${p0125Data.costAnalysis ? 'Present' : 'Missing'}`);
  console.log(`   ✅ Diagnostic Steps: ${p0125Data.diagnosticSteps ? 'Present' : 'Missing'}`);
  console.log(`   ✅ Case Studies: ${p0125Data.caseStudies.length > 0 ? 'Present' : 'Missing'}`);
  console.log(`   ✅ Sidebar: ${p0125Data.sidebarData ? 'Present' : 'Missing'}`);
  
  console.log('\n🎉 P0125 optimization completed successfully!');
  console.log('\n💡 Key Features:');
  console.log('   • Comprehensive thermostat diagnosis procedures');
  console.log('   • Engine warm-up time monitoring methodology');
  console.log('   • Real-world Toyota Camry and Honda Civic case studies');
  console.log('   • Cost-effective repair strategies ($120-$450 range)');
  console.log('   • GeekOBD APP integration for temperature tracking');
  console.log('   • Complete cooling system diagnostic network');
  
  console.log('\n🌡️  Temperature Focus:');
  console.log('   • Insufficient coolant temperature (not sensor electrical issue)');
  console.log('   • Thermostat operation testing and replacement');
  console.log('   • Cooling system bleeding and air pocket removal');
  console.log('   • Open-loop vs closed-loop operation impact');
  
  console.log('\n🔄 Next steps:');
  console.log('   1. Review generated p0125.html for accuracy');
  console.log('   2. Test temperature monitoring procedures');
  console.log('   3. Continue with P0300 (Random Misfire)');
  console.log('   4. Begin misfire diagnostic series');
  
  console.log('\n📈 Series Progress:');
  console.log('   ✅ IAT Sensor Series: P0112, P0113, P0114 (Complete)');
  console.log('   ✅ ECT Sensor Series: P0117, P0118, P0125 (Complete)');
  console.log('   🔄 Next: Misfire Series (P0300, P0301, P0302)');
  
} catch (error) {
  console.error('❌ Error generating P0125 page:', error.message);
  console.error(error.stack);
  process.exit(1);
}
