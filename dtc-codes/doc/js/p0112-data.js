const { DTCData } = require('./dtc-template-generator');

// P0112 IAT Sensor Low Input 的完整数据结构
const p0112Data = new DTCData({
  code: 'P0112',
  title: 'IAT Sensor Low Input',
  description: 'The Engine Control Module has detected that the Intake Air Temperature sensor is reading extremely hot temperatures when actual air temperature is cooler.',
  definition: 'The Engine Control Module has detected that the Intake Air Temperature (IAT) sensor is producing readings that indicate extremely hot air temperatures (typically 300°F or higher) when the actual intake air temperature should be much cooler. This sensor measures the temperature of air entering the engine to help the ECM calculate proper fuel injection timing and quantity. When the sensor reads too hot, it can cause lean fuel mixture, poor performance, and potential engine damage from running too lean.',

  symptoms: [
    'Check engine light illuminated - IAT sensor fault detected',
    'Poor engine performance - Lean fuel mixture from hot air reading',
    'Engine hesitation during acceleration - Insufficient fuel delivery',
    'Rough idle - Incorrect air/fuel mixture calculations',
    'Engine knocking or pinging - Lean mixture causing detonation',
    'Hard starting when engine is cold - ECM thinks air is hot',
    'Reduced power output - Engine running lean for protection',
    'Failed emissions test - Lean exhaust conditions'
  ],

  causes: [
    'Faulty IAT sensor - Internal component failure causing low resistance',
    'Short circuit in IAT sensor wiring - Wire touching ground or power',
    'Corroded IAT sensor connector - Poor electrical contact causing resistance drop',
    'IAT sensor contamination - Conductive debris causing short circuit',
    'Damaged IAT sensor housing - Physical damage exposing sensor element',
    'ECM internal fault - Module misreading sensor signal',
    'Wiring harness damage - Short to ground in sensor circuit',
    'Aftermarket air intake modification - Sensor relocated to hot area'
  ],
  
  performanceImpact: 'P0112 causes the ECM to receive incorrect air temperature data, leading to overly lean fuel mixture, reduced engine power, potential engine knock, poor acceleration, and possible engine damage from running too lean.',

  quickAnswer: {
    icon: 'thermometer-full',
    meaning: 'IAT sensor reading extremely hot temperatures (300°F+) when actual air is cooler - usually short circuit or sensor failure.',
    fix: 'Replace IAT sensor or repair short circuit',
    cost: '$75-$280',
    time: '30-45 minutes',
    drivingSafety: 'Safe to drive short distances, but expect poor performance and potential engine knock. Repair soon to prevent engine damage.'
  },
  
  aiQuestions: [
    {
      question: 'What\'s the difference between P0112 and P0113 IAT sensor codes?',
      answer: 'P0112 indicates the IAT sensor is reading too hot (low input voltage), while P0113 indicates too cold readings (high input voltage). P0112 typically means a short circuit or sensor reading actual high temperatures, while P0113 usually indicates an open circuit or failed sensor defaulting to -40°F.'
    },
    {
      question: 'What causes an IAT sensor to read 300°F constantly?',
      answer: 'A constant high temperature reading typically indicates a short circuit in the sensor or wiring, causing low resistance. This can be caused by damaged wiring touching ground, a failed sensor with internal short, or conductive contamination bridging the sensor terminals.'
    },
    {
      question: 'Can a hot engine bay cause P0112?',
      answer: 'While extreme engine bay heat can affect IAT readings, P0112 typically indicates sensor readings far beyond normal hot air temperatures (300°F+). True P0112 is usually caused by electrical faults rather than actual high air temperatures, unless the sensor has been relocated to an extremely hot location.'
    },
    {
      question: 'How do I test an IAT sensor for P0112?',
      answer: 'Measure resistance across sensor terminals - it should decrease as temperature increases. At room temperature (68°F), expect around 2,500 ohms. If you get very low resistance (under 100 ohms) regardless of temperature, the sensor has failed. GeekOBD APP can monitor live IAT readings to confirm the fault.'
    }
  ],
  
  costAnalysis: {
    averageCost: '$75-$280 for most P0112 repairs',
    repairOptions: [
      {
        title: 'IAT Sensor Replacement',
        description: 'Most common fix - Replace faulty sensor (75% of cases)',
        color: '#4CAF50',
        icon: 'thermometer-full',
        items: [
          { name: 'IAT Sensor', cost: '$25-$85' },
          { name: 'Labor (0.5-1 hour)', cost: '$50-$120' }
        ],
        total: '$75-$205',
        successRate: '95% success rate'
      },
      {
        title: 'Wiring Repair',
        description: 'Fix short circuit in sensor wiring (20% of cases)',
        color: '#FF9800',
        icon: 'plug',
        items: [
          { name: 'Wire repair/splice', cost: '$15-$35' },
          { name: 'Diagnostic time', cost: '$80-$150' },
          { name: 'Labor (1-2 hours)', cost: '$100-$240' }
        ],
        total: '$195-$425',
        successRate: '90% success rate'
      },
      {
        title: 'Connector Cleaning/Replacement',
        description: 'Clean corroded connector or replace if damaged (5% of cases)',
        color: '#2196F3',
        icon: 'wrench',
        items: [
          { name: 'Connector cleaning', cost: '$20-$40' },
          { name: 'New connector (if needed)', cost: '$25-$60' },
          { name: 'Labor (0.5 hour)', cost: '$50-$120' }
        ],
        total: '$95-$220',
        successRate: '85% success rate'
      }
    ],
    savingTips: [
      'Check connector first - 15% of P0112 cases are just corroded connections',
      'IAT sensors are usually easy to access - consider DIY replacement to save $50-120 in labor',
      'Test sensor resistance before buying parts - confirm failure first',
      'Some aftermarket sensors cost 50% less than OEM with same reliability',
      'If wiring repair is needed, fix the root cause to prevent recurrence'
    ]
  },
  
  diagnosticSteps: {
    estimatedTime: 'PT45M',
    steps: [
      {
        title: 'Verify P0112 Code and Symptoms',
        icon: 'search',
        description: 'Connect scan tool and confirm P0112 is present. Check for additional codes that might indicate related issues. Note current IAT reading - should show extremely high temperature (300°F+).',
        geekobdTip: 'Use GeekOBD APP to monitor live IAT sensor data. Look for readings above 300°F when engine is cold - this confirms P0112 fault.'
      },
      {
        title: 'Visual Inspection of IAT Sensor',
        icon: 'eye',
        description: 'Locate IAT sensor (usually in air intake tube or air filter housing). Check for physical damage, contamination, or signs of overheating. Inspect connector for corrosion or damage.',
        geekobdTip: 'GeekOBD APP can help locate sensor by showing which intake air temperature reading is faulty if multiple sensors are present.'
      },
      {
        title: 'Test IAT Sensor Resistance',
        icon: 'thermometer-full',
        description: 'Disconnect sensor and measure resistance across terminals with multimeter. At 68°F, expect ~2,500 ohms. If resistance is very low (under 100 ohms), sensor has internal short circuit.',
        geekobdTip: 'Compare resistance readings with GeekOBD APP temperature charts to verify sensor is within specification for current ambient temperature.'
      },
      {
        title: 'Check Wiring and Connector',
        icon: 'plug',
        description: 'Inspect wiring harness for damage, shorts to ground, or pinched wires. Clean connector terminals and check for proper connection. Test continuity from sensor to ECM.',
        geekobdTip: 'Use GeekOBD APP to monitor sensor voltage while wiggling wires - voltage should remain stable if wiring is good.'
      },
      {
        title: 'Verify Repair and Clear Codes',
        icon: 'check-circle',
        description: 'After replacing sensor or repairing wiring, clear codes and test drive. Monitor IAT readings to ensure they respond normally to temperature changes.',
        geekobdTip: 'GeekOBD APP provides real-time verification - IAT should read close to ambient air temperature when engine is cold, and gradually increase as engine warms up.'
      }
    ],
    importantNotes: [
      'P0112 indicates sensor reading too hot - do not confuse with P0113 (too cold)',
      'Always test sensor resistance before replacement - connector issues can mimic sensor failure',
      'IAT sensor affects fuel mixture - driving with P0112 can cause engine knock and damage'
    ]
  },
  
  caseStudies: [
    {
      title: 'Ford F-150 IAT Sensor Short Circuit',
      vehicle: '2018 Ford F-150 5.0L V8, 85,000 miles',
      problem: 'Customer complained of poor acceleration, engine knocking, and check engine light. Truck was running rough and had reduced power, especially during acceleration.',
      diagnosis: 'GeekOBD APP showed P0112 code with IAT reading constant 315°F even when engine was cold. Resistance test revealed IAT sensor had only 45 ohms resistance (should be ~2,500 ohms at room temperature), indicating internal short circuit.',
      solution: 'Replaced IAT sensor located in air intake tube between air filter and throttle body. Sensor was easily accessible and took 20 minutes to replace.',
      cost: 'IAT sensor: $42, Labor: $60, Total: $102',
      result: 'P0112 code cleared immediately. IAT now reads correctly (75°F cold, gradually increasing with engine temperature). Engine performance restored, no more knocking, smooth acceleration returned.'
    },
    {
      title: 'Honda Civic Wiring Harness Damage',
      vehicle: '2016 Honda Civic 1.5L Turbo, 92,000 miles',
      problem: 'Intermittent P0112 code with occasional poor idle and hesitation. Problem seemed to occur more often when driving over bumps or rough roads.',
      diagnosis: 'Initial IAT sensor test showed normal resistance, but GeekOBD APP revealed intermittent spikes to 350°F+ during driving. Wire wiggle test found damaged section of harness near engine mount where wires had rubbed against bracket.',
      solution: 'Repaired damaged section of IAT sensor wiring harness. Cut out damaged portion and spliced in new wire with proper insulation and protective covering.',
      cost: 'Wire repair kit: $25, Diagnostic time: $120, Labor: $180, Total: $325',
      result: 'P0112 code has not returned after 3 months. IAT readings remain stable during all driving conditions. Customer reports smooth operation and improved fuel economy.'
    }
  ],
  
  relatedCodes: [
    { code: 'P0113', description: 'IAT Sensor High Input - Opposite condition (too cold readings)', color: '#e74c3c' },
    { code: 'P0110', description: 'IAT Sensor Circuit Malfunction - General IAT circuit problem', color: '#4a90e2' },
    { code: 'P0114', description: 'IAT Sensor Intermittent - Erratic IAT sensor readings', color: '#f39c12' },
    { code: 'P0171', description: 'System Too Lean Bank 1 - Can be caused by incorrect IAT readings', color: '#9b59b6' },
    { code: 'P0174', description: 'System Too Lean Bank 2 - Can be caused by incorrect IAT readings', color: '#9b59b6' },
    { code: 'P0101', description: 'MAF Sensor Range/Performance - Related air intake measurement', color: '#27ae60' },
    { code: 'P0300', description: 'Random Misfire - Can be caused by lean mixture from P0112', color: '#e67e22' }
  ],

  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P0112',
      description: 'Use GeekOBD APP for professional IAT sensor diagnosis!',
      features: [
        'Live IAT temperature monitoring',
        'Resistance testing guidance',
        'Wiring diagram access',
        'Repair verification tools'
      ]
    },
    systemCodes: {
      title: 'IAT Sensor Codes',
      description: 'Related Intake Air Temperature sensor codes:'
    },
    diagnosticResources: [
      {
        title: 'IAT Sensor Testing Guide',
        description: 'Professional resistance and voltage testing procedures',
        icon: 'thermometer-full',
        url: '#diagnostic-steps'
      },
      {
        title: 'IAT Wiring Diagrams',
        description: 'Sensor circuit diagrams and pin configurations',
        icon: 'sitemap',
        url: '../resources/iat-wiring-diagrams.html'
      },
      {
        title: 'Temperature Charts',
        description: 'IAT sensor resistance vs temperature specifications',
        icon: 'line-chart',
        url: '../resources/iat-temperature-charts.html'
      },
      {
        title: 'Sensor Location Guide',
        description: 'IAT sensor locations by vehicle make and model',
        icon: 'map-marker',
        url: '../resources/iat-sensor-locations.html'
      }
    ],
    codeInfo: {
      system: 'Engine Management',
      severity: 'MEDIUM',
      category: 'Intake Air Temperature'
    }
  }
});

module.exports = p0112Data;