const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0133Data = require('./p0133-data');

console.log('🚀 Generating P0133 - O2 Sensor Slow Response Bank 1 Sensor 1 Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0133Data);
  const outputPath = path.join(__dirname, '../../p0133.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0133 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0133 page:', error.message);
  process.exit(1);
}
