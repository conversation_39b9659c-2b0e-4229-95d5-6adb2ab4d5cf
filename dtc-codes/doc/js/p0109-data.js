const { DTCData } = require('./dtc-template-generator');

// P0109 MAP Sensor Intermittent 的完整数据结构
const p0109Data = new DTCData({
  code: 'P0109',
  title: 'MAP Sensor Intermittent',
  description: 'The Engine Control Module has detected intermittent or erratic readings from the Manifold Absolute Pressure sensor circuit.',
  definition: 'The Engine Control Module has detected intermittent or erratic readings from the Manifold Absolute Pressure (MAP) sensor circuit. This means the sensor signal is unstable, dropping out occasionally, or showing values that don\'t consistently correlate with throttle position and engine load. Unlike P0105 (circuit malfunction) or P0106 (range/performance), P0109 indicates the sensor works sometimes but fails intermittently, making diagnosis more challenging.',
  
  symptoms: [
    'Check engine light illuminated - ECM detected intermittent MAP sensor fault',
    'Intermittent poor engine performance - Power varies unpredictably',
    'Engine hesitation or stumbling - Especially during load changes or acceleration',
    'Erratic idle quality - RPM fluctuations due to changing pressure readings',
    'Occasional engine stalling - When sensor signal drops out completely',
    'Inconsistent fuel economy - Varying fuel mixture calculations',
    'Engine surging at cruise speeds - Intermittent pressure readings cause fuel changes',
    'Hard starting in certain conditions - When sensor fails during startup',
    'Intermittent black or white smoke - Rich or lean mixture when sensor malfunctions'
  ],
  
  causes: [
    'Loose or corroded MAP sensor connector - Intermittent electrical contact',
    'Damaged MAP sensor wiring - Broken wire strands causing intermittent connection',
    'Failing MAP sensor - Internal component degradation causing erratic operation',
    'Vibration-induced sensor damage - Loose mounting or internal damage from vibration',
    'Vacuum line problems - Intermittent leaks or restrictions in pressure line',
    'Temperature cycling damage - Repeated heating/cooling causing sensor failure',
    'ECM connector issues - Poor connection at engine control module',
    'Moisture in electrical connections - Causing intermittent shorts or open circuits'
  ],
  
  performanceImpact: 'P0109 causes unpredictable engine performance as the ECM alternates between using MAP sensor data and default values, leading to inconsistent fuel delivery, power fluctuations, and difficulty maintaining optimal engine operation.',
  
  quickAnswer: {
    icon: 'exclamation-triangle',
    meaning: 'MAP sensor providing inconsistent, intermittent pressure readings - usually loose connection or failing sensor.',
    fix: 'Check connections, test wiring, replace MAP sensor if needed',
    cost: '$85-$380',
    time: '60-120 minutes',
    drivingSafety: 'Generally safe to drive but expect unpredictable performance. Fix promptly to avoid stalling in traffic.'
  },
  
  aiQuestions: [
    {
      question: 'What\'s the difference between P0109 and other MAP sensor codes?',
      answer: 'P0109 indicates intermittent/erratic readings, while P0107 shows constant low readings and P0108 shows constant high readings. P0109 is often the most challenging to diagnose because the problem comes and goes, making it harder to reproduce during testing.'
    },
    {
      question: 'Can engine vibration cause P0109?',
      answer: 'Yes, excessive engine vibration can cause P0109 by loosening MAP sensor mounting, damaging internal sensor components, or causing intermittent wiring connections. This is more common on high-mileage vehicles or engines with worn motor mounts.'
    },
    {
      question: 'How can I reproduce P0109 for diagnosis?',
      answer: 'Try tapping gently on the MAP sensor and wiring while monitoring live data with GeekOBD APP. Pressure readings should remain stable - if they jump around during tapping, you\'ve found the problem. Also test during temperature changes and road vibration conditions.'
    },
    {
      question: 'Why does P0109 cause engine surging?',
      answer: 'Because the MAP sensor signal is unstable, the ECM receives varying pressure data, causing it to constantly adjust fuel mixture. When the sensor reads correctly, fuel delivery is proper; when it fails, the ECM uses default values, creating the surging sensation.'
    }
  ],

  costAnalysis: {
    averageCost: '$85-$380 for most P0109 repairs',
    repairOptions: [
      {
        title: 'Connector Cleaning/Repair',
        description: 'Most common fix - Clean corroded or loose connector (45% of cases)',
        color: '#4CAF50',
        icon: 'plug',
        items: [
          { name: 'Connector cleaning kit', cost: '$15-$25' },
          { name: 'Dielectric grease', cost: '$8-$15' },
          { name: 'Labor (45-75 minutes)', cost: '$60-$150' }
        ],
        total: '$83-$190',
        successRate: '80% success rate'
      },
      {
        title: 'MAP Sensor Replacement',
        description: 'Replace failing sensor (40% of cases)',
        color: '#2196F3',
        icon: 'exchange',
        items: [
          { name: 'MAP sensor', cost: '$40-$95' },
          { name: 'Labor (45-75 minutes)', cost: '$60-$150' }
        ],
        total: '$100-$245',
        successRate: '95% success rate'
      },
      {
        title: 'Wiring Harness Repair',
        description: 'Fix intermittent wiring problems (15% of cases)',
        color: '#FF9800',
        icon: 'wrench',
        items: [
          { name: 'Wiring repair materials', cost: '$20-$50' },
          { name: 'Diagnostic time', cost: '$100-$160' },
          { name: 'Labor (1-2 hours)', cost: '$100-$240' }
        ],
        total: '$220-$450',
        successRate: '90% success rate'
      }
    ],
    savingTips: [
      'Start with connector cleaning - fixes 45% of P0109 cases for under $100',
      'Use GeekOBD APP tap test to pinpoint exact location of intermittent problem',
      'Check vacuum line condition - simple hose replacement may fix the issue',
      'MAP sensor replacement is usually DIY-friendly, saving $60-150 in labor',
      'Document when problem occurs to help technician diagnose faster'
    ]
  },

  diagnosticSteps: {
    estimatedTime: 'PT90M',
    steps: [
      {
        title: 'Monitor Live MAP Data',
        icon: 'line-chart',
        description: 'Connect GeekOBD APP and monitor live MAP sensor readings during various driving conditions. Look for sudden dropouts, spikes, or erratic values that don\'t correlate with throttle input.',
        geekobdTip: 'GeekOBD APP can log MAP data over time - look for patterns like dropouts during acceleration, temperature changes, or vibration conditions.'
      },
      {
        title: 'Perform Tap and Wiggle Test',
        icon: 'hand-paper-o',
        description: 'While monitoring live MAP data, gently tap the sensor housing and wiggle the wiring harness. Watch for sudden changes in readings that indicate loose connections or intermittent faults.',
        geekobdTip: 'GeekOBD APP\'s real-time graphing is perfect for tap testing - you\'ll see immediate changes when you tap problem areas.'
      },
      {
        title: 'Visual Inspection',
        icon: 'eye',
        description: 'Inspect MAP sensor, vacuum line, and electrical connections for damage. Look for corroded pins, loose connectors, cracked vacuum hoses, or signs of vibration damage.',
        geekobdTip: 'Use GeekOBD APP to monitor readings while inspecting - intermittent data changes during inspection indicate connection problems.'
      },
      {
        title: 'Electrical Connection Testing',
        icon: 'plug',
        description: 'Clean MAP sensor connector and test for proper connection. Check wiring continuity and look for intermittent open circuits by flexing harness.',
        geekobdTip: 'Monitor MAP voltage with GeekOBD APP while testing connections - stable readings indicate good connections, fluctuations show wiring problems.'
      },
      {
        title: 'Component Replacement and Road Test',
        icon: 'check-circle',
        description: 'Replace faulty MAP sensor, repair wiring, or fix vacuum line as diagnosed. Clear codes and perform extended road test under various conditions.',
        geekobdTip: 'Use GeekOBD APP to log data during test drive - MAP readings should be stable and respond smoothly to throttle changes without dropouts.'
      }
    ],
    importantNotes: [
      'P0109 is intermittent - problem may not be present during initial diagnosis',
      'Tap test is crucial for finding loose connections or internal sensor damage',
      'Document conditions when problem occurs to help locate intermittent fault'
    ]
  },

  caseStudies: [
    {
      title: 'Volkswagen Jetta Vibration Damage',
      vehicle: '2015 Volkswagen Jetta 1.4L Turbo, 95,000 miles',
      problem: 'Customer reported intermittent engine hesitation and surging, especially on rough roads. P0109 code appeared sporadically, making diagnosis difficult.',
      diagnosis: 'GeekOBD APP monitoring showed MAP readings would occasionally spike or drop out, then return to normal. Tap test revealed MAP sensor was loose in its mounting, causing intermittent connection issues.',
      solution: 'Tightened MAP sensor mounting and replaced worn rubber grommet that had allowed sensor to vibrate loose. Also secured nearby wiring harness.',
      cost: 'Mounting grommet: $8, Hardware: $5, Labor: $75, Total: $88',
      result: 'P0109 code has not returned after 5 months. MAP readings are now stable even on rough roads and engine performance is consistent.'
    },
    {
      title: 'Honda CR-V Corroded Connector',
      vehicle: '2017 Honda CR-V 1.5L Turbo, 78,000 miles',
      problem: 'Intermittent P0109 code with occasional poor fuel economy and engine hesitation. Problem seemed worse in humid weather.',
      diagnosis: 'MAP sensor tested normal when stationary, but GeekOBD APP showed intermittent signal dropouts. Found MAP sensor connector had corrosion on pins that worsened with moisture.',
      solution: 'Cleaned corroded connector pins with electrical contact cleaner, applied dielectric grease, and ensured proper connector seal to prevent future moisture intrusion.',
      cost: 'Connector cleaning kit: $18, Dielectric grease: $10, Labor: $85, Total: $113',
      result: 'P0109 code cleared and has not returned through humid summer weather. MAP sensor data remains stable in all conditions.'
    }
  ],

  relatedCodes: [
    { code: 'P0105', description: 'MAP Sensor Circuit Malfunction - Electrical circuit problems', color: '#e74c3c' },
    { code: 'P0106', description: 'MAP Sensor Range/Performance - General MAP sensor performance issues', color: '#4a90e2' },
    { code: 'P0107', description: 'MAP Sensor Low Input - Constant low pressure readings', color: '#3498db' },
    { code: 'P0108', description: 'MAP Sensor High Input - Constant high pressure readings', color: '#f39c12' },
    { code: 'P0171', description: 'System Too Lean Bank 1 - Can be caused by erratic MAP readings', color: '#9b59b6' },
    { code: 'P0172', description: 'System Too Rich Bank 1 - Can be caused by erratic MAP readings', color: '#9b59b6' },
    { code: 'P0300', description: 'Random Misfire - Can be caused by inconsistent fuel mixture', color: '#e67e22' }
  ],

  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P0109',
      description: 'Use GeekOBD APP for intermittent MAP sensor diagnosis!',
      features: [
        'Real-time MAP data logging',
        'Intermittent fault detection',
        'Tap test monitoring',
        'Vibration analysis'
      ]
    },
    systemCodes: {
      title: 'MAP Sensor Codes',
      description: 'Related manifold pressure sensor codes:'
    },
    diagnosticResources: [
      {
        title: 'Intermittent Fault Guide',
        description: 'Specialized procedures for intermittent MAP problems',
        icon: 'exclamation-triangle',
        url: '#diagnostic-steps'
      },
      {
        title: 'Tap Testing Procedures',
        description: 'Professional techniques for finding intermittent connections',
        icon: 'hand-paper-o',
        url: '../resources/tap-testing-procedures.html'
      },
      {
        title: 'Vibration Diagnosis',
        description: 'Identifying and fixing vibration-related sensor problems',
        icon: 'cog',
        url: '../resources/vibration-diagnosis.html'
      },
      {
        title: 'Connector Maintenance',
        description: 'Preventing and repairing corroded electrical connections',
        icon: 'plug',
        url: '../resources/connector-maintenance.html'
      }
    ],
    codeInfo: {
      system: 'Engine Management',
      severity: 'MEDIUM',
      category: 'Sensor Intermittent'
    }
  }
});

module.exports = p0109Data;
