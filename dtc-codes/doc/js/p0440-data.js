const { DTCData } = require('./dtc-template-generator');

// P0440 EVAP System Malfunction 的完整数据结构
const p0440Data = new DTCData({
  code: 'P0440',
  title: 'EVAP System Malfunction',
  description: 'The Engine Control Module has detected a malfunction in the Evaporative Emission Control System.',
  definition: 'The Engine Control Module has detected a malfunction in the Evaporative Emission Control (EVAP) System. The EVAP system captures fuel vapors from the fuel tank and stores them in a charcoal canister, then purges them into the engine for combustion to prevent fuel vapors from escaping to the atmosphere. P0440 is a general code indicating the ECM has detected a problem with the EVAP system operation, which could include leaks, blockages, or component failures.',
  
  symptoms: [
    'Check engine light illuminated - ECM detected EVAP system malfunction',
    'Failed emissions test - EVAP system not controlling fuel vapor emissions',
    'Fuel smell around vehicle - Fuel vapors escaping due to system malfunction',
    'Poor fuel economy - EVAP system not recovering fuel vapors efficiently',
    'Difficulty filling fuel tank - EVAP system blockage affecting tank venting',
    'Fuel tank pressure buildup - System not properly venting tank pressure',
    'Engine hesitation - EVAP purge problems affecting air/fuel mixture',
    'Rough idle - Incorrect EVAP purge flow affecting idle stability',
    'Fuel tank deformation - Excessive vacuum or pressure from system malfunction'
  ],
  
  causes: [
    'EVAP system leak - Cracked or damaged vapor lines, connections, or components',
    'Faulty purge valve - Valve stuck open or closed affecting vapor flow',
    'Faulty vent valve - Preventing proper tank venting or sealing',
    'Clogged charcoal canister - Saturated or damaged canister restricting flow',
    'Loose or missing gas cap - Allowing fuel vapors to escape',
    'Damaged fuel tank - Cracks or holes allowing vapor leaks',
    'Faulty EVAP pressure sensor - Providing incorrect pressure readings',
    'Blocked vapor lines - Restrictions preventing proper vapor flow'
  ],
  
  performanceImpact: 'P0440 primarily affects emissions control and may cause fuel odors, failed emissions testing, and minor drivability issues. The main concern is environmental impact from fuel vapor emissions.',
  
  quickAnswer: {
    icon: 'cloud',
    meaning: 'EVAP system not controlling fuel vapors properly - usually leak, faulty valve, or clogged canister.',
    fix: 'Check gas cap, test for EVAP leaks, replace faulty purge/vent valves',
    cost: '$120-$580',
    time: '60-180 minutes',
    drivingSafety: 'Safe to drive but may smell fuel vapors and fail emissions test. Check gas cap first as simple fix.'
  },
  
  aiQuestions: [
    {
      question: 'Can a loose gas cap cause P0440?',
      answer: 'Yes, a loose, damaged, or missing gas cap is one of the most common causes of P0440. The gas cap seals the fuel system, and if it\'s not sealing properly, the EVAP system cannot maintain proper pressure for leak testing.'
    },
    {
      question: 'Is it safe to drive with P0440?',
      answer: 'Yes, it\'s generally safe to drive with P0440. The main concerns are fuel vapor emissions (environmental impact) and potential fuel odors. However, address it promptly to pass emissions testing and prevent fuel vapor exposure.'
    },
    {
      question: 'How do I test for EVAP system leaks?',
      answer: 'Professional shops use smoke machines to find EVAP leaks. You can check obvious items like the gas cap, visible vapor lines, and connections. GeekOBD APP can show EVAP system pressure during leak tests to help identify problems.'
    },
    {
      question: 'Why does my fuel tank make noise with P0440?',
      answer: 'P0440 can cause fuel tank pressure buildup or excessive vacuum if the EVAP system isn\'t venting properly. This can cause the tank to make popping or creaking noises as it flexes under pressure changes.'
    }
  ],

  costAnalysis: {
    averageCost: '$120-$580 for most P0440 repairs',
    repairOptions: [
      {
        title: 'Gas Cap Replacement',
        description: 'Replace faulty gas cap (25% of cases)',
        color: '#4CAF50',
        icon: 'circle',
        items: [
          { name: 'Gas cap', cost: '$15-$45' },
          { name: 'Labor (15-30 minutes)', cost: '$25-$60' }
        ],
        total: '$40-$105',
        successRate: '95% success rate'
      },
      {
        title: 'EVAP Purge/Vent Valve Replacement',
        description: 'Replace faulty EVAP control valves (40% of cases)',
        color: '#2196F3',
        icon: 'exchange',
        items: [
          { name: 'Purge or vent valve', cost: '$60-$180' },
          { name: 'Labor (1-2 hours)', cost: '$100-$240' }
        ],
        total: '$160-$420',
        successRate: '85% success rate'
      },
      {
        title: 'EVAP System Repair',
        description: 'Fix vapor line leaks or replace canister (35% of cases)',
        color: '#FF9800',
        icon: 'wrench',
        items: [
          { name: 'Vapor lines/canister', cost: '$80-$300' },
          { name: 'Labor (1.5-3 hours)', cost: '$150-$360' }
        ],
        total: '$230-$660',
        successRate: '90% success rate'
      }
    ],
    savingTips: [
      'Check gas cap first - fixes 25% of P0440 cases for under $50',
      'Use GeekOBD APP to monitor EVAP system operation before expensive repairs',
      'Look for obvious vapor line damage before assuming internal component failure',
      'EVAP system repairs often require specialized leak testing equipment',
      'Address P0440 before emissions testing to avoid test failure'
    ]
  },

  diagnosticSteps: {
    estimatedTime: 'PT120M',
    steps: [
      {
        title: 'Check Gas Cap',
        icon: 'circle',
        description: 'Inspect gas cap for damage, proper sealing, and correct installation. Ensure cap clicks properly when tightened and sealing surface is clean.',
        geekobdTip: 'Clear codes after gas cap service and drive vehicle through complete drive cycle - GeekOBD APP can monitor EVAP system readiness status.'
      },
      {
        title: 'Test EVAP System Operation',
        icon: 'play',
        description: 'Connect GeekOBD APP and monitor EVAP system operation during purge and leak tests. Check purge valve and vent valve operation.',
        geekobdTip: 'GeekOBD APP can command EVAP purge valve operation and show system pressure during leak tests - monitor for proper valve response.'
      },
      {
        title: 'Visual Inspection',
        icon: 'eye',
        description: 'Inspect visible EVAP components including vapor lines, canister, purge valve, and vent valve for damage, cracks, or loose connections.',
        geekobdTip: 'Use GeekOBD APP to monitor EVAP pressure while inspecting - pressure changes during inspection may indicate leak locations.'
      },
      {
        title: 'Perform Leak Test',
        icon: 'search',
        description: 'Use smoke machine or pressure testing to locate EVAP system leaks. Check all vapor lines, connections, and components for leaks.',
        geekobdTip: 'GeekOBD APP can show EVAP system pressure during professional leak testing - helps identify leak severity and location.'
      },
      {
        title: 'Component Replacement and Verification',
        icon: 'check-circle',
        description: 'Replace faulty components as diagnosed. Clear codes and perform EVAP system drive cycle to verify proper operation.',
        geekobdTip: 'Use GeekOBD APP to monitor EVAP system readiness and verify all monitors complete successfully after repairs.'
      }
    ],
    importantNotes: [
      'Check gas cap first - most common and cheapest fix for P0440',
      'EVAP system requires complete drive cycle to verify repairs',
      'Professional leak testing equipment often needed for accurate diagnosis'
    ]
  },

  caseStudies: [
    {
      title: 'Toyota Camry Gas Cap Issue',
      vehicle: '2016 Toyota Camry 2.5L 4-cylinder, 95,000 miles',
      problem: 'Customer reported P0440 code and occasional fuel smell around vehicle. Code appeared after recent fuel fill-up.',
      diagnosis: 'Visual inspection revealed gas cap was not clicking properly when tightened. Cap sealing surface was cracked and not maintaining proper seal.',
      solution: 'Replaced gas cap with OEM part. Cleared codes and performed complete drive cycle to verify EVAP system operation.',
      cost: 'Gas cap: $28, Labor: $35, Total: $63',
      result: 'P0440 code cleared and has not returned after 4 months. EVAP system now passes all readiness monitors and no more fuel odors.'
    },
    {
      title: 'Honda Accord Purge Valve Failure',
      vehicle: '2017 Honda Accord 2.4L 4-cylinder, 108,000 miles',
      problem: 'P0440 code with rough idle and occasional engine hesitation. Problem seemed worse after fuel fill-ups.',
      diagnosis: 'GeekOBD APP showed EVAP purge valve was not responding to commands. Valve was stuck open, causing continuous purge flow affecting idle quality.',
      solution: 'Replaced EVAP purge valve located near intake manifold. Also cleaned intake manifold area where valve connects.',
      cost: 'EVAP purge valve: $85, Labor: $120, Total: $205',
      result: 'P0440 code cleared and idle quality returned to normal. No more hesitation problems and EVAP system operates properly.'
    }
  ],

  relatedCodes: [
    { code: 'P0441', description: 'EVAP Purge Flow Incorrect - Purge system flow problems', color: '#3498db' },
    { code: 'P0442', description: 'EVAP System Small Leak - Small leak detected in system', color: '#f39c12' },
    { code: 'P0443', description: 'EVAP Purge Valve Circuit - Electrical problems with purge valve', color: '#e74c3c' },
    { code: 'P0446', description: 'EVAP Vent Control Circuit - Vent valve electrical problems', color: '#9b59b6' },
    { code: 'P0455', description: 'EVAP System Large Leak - Large leak detected in system', color: '#e67e22' },
    { code: 'P0456', description: 'EVAP System Very Small Leak - Very small leak detected', color: '#4a90e2' },
    { code: 'P0171', description: 'System Too Lean - Can be affected by EVAP purge problems', color: '#27ae60' }
  ],

  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P0440',
      description: 'Use GeekOBD APP for EVAP system testing!',
      features: [
        'EVAP system monitoring',
        'Purge valve testing',
        'Pressure monitoring',
        'Readiness verification'
      ]
    },
    systemCodes: {
      title: 'EVAP System Codes',
      description: 'Related evaporative emission control codes:'
    },
    diagnosticResources: [
      {
        title: 'EVAP System Testing',
        description: 'Professional procedures for testing EVAP system operation',
        icon: 'cloud',
        url: '#diagnostic-steps'
      },
      {
        title: 'Gas Cap Inspection',
        description: 'Proper gas cap inspection and replacement procedures',
        icon: 'circle',
        url: '../resources/gas-cap-inspection.html'
      },
      {
        title: 'EVAP Leak Testing',
        description: 'Professional techniques for finding EVAP system leaks',
        icon: 'search',
        url: '../resources/evap-leak-testing.html'
      },
      {
        title: 'Emissions System Guide',
        description: 'Understanding automotive emissions control systems',
        icon: 'leaf',
        url: '../resources/emissions-system-guide.html'
      }
    ],
    codeInfo: {
      system: 'Emissions Control',
      severity: 'LOW',
      category: 'EVAP System'
    }
  }
});

module.exports = p0440Data;
