const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0203Data = require('./p0203-data');

console.log('🚀 Generating P0203 - Injector Circuit Malfunction Cylinder 3 Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0203Data);
  const outputPath = path.join(__dirname, '../../p0203.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0203 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0203 page:', error.message);
  process.exit(1);
}
