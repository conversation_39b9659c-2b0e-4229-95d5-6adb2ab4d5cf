const { DTCData } = require('./dtc-template-generator');

// P0133 O2 Sensor Slow Response Bank 1 Sensor 1 的完整数据结构
const p0133Data = new DTCData({
  code: 'P0133',
  title: 'O2 Sensor Slow Response Bank 1 Sensor 1',
  description: 'The Engine Control Module has detected that the upstream oxygen sensor on Bank 1 is responding too slowly to changes in exhaust oxygen content.',
  definition: 'The Engine Control Module has detected that the upstream oxygen sensor (Bank 1, Sensor 1) is responding too slowly to changes in exhaust oxygen content. A healthy O2 sensor should switch rapidly between rich (high voltage) and lean (low voltage) readings as the ECM adjusts fuel mixture. When the sensor takes too long to respond to these changes, it indicates sensor aging, contamination, or other performance issues that affect fuel control accuracy.',
  
  symptoms: [
    'Check engine light illuminated - ECM detected O2 sensor slow response',
    'Poor fuel economy - Delayed fuel mixture corrections',
    'Engine hesitation during acceleration - Sluggish fuel system response',
    'Rough idle or uneven engine operation - Inconsistent fuel mixture control',
    'Failed emissions test - Poor fuel control affects exhaust emissions',
    'Engine surging at cruise speeds - Delayed sensor feedback causes overcorrection',
    'Poor engine performance - Reduced power and responsiveness',
    'Increased exhaust emissions - Inefficient fuel mixture control',
    'Catalytic converter damage - Poor fuel control can damage catalyst over time'
  ],
  
  causes: [
    'Aged O2 sensor - Normal wear causing slower response time',
    'Contaminated O2 sensor - Oil, coolant, or fuel contamination affecting response',
    'Carbon buildup on sensor - Deposits slowing sensor reaction time',
    'Faulty O2 sensor heater - Inadequate heating affecting sensor performance',
    'Exhaust leak before sensor - Diluting exhaust gases affecting readings',
    'Poor fuel quality - Contaminants affecting sensor operation',
    'Engine mechanical problems - Poor combustion affecting exhaust composition',
    'Vacuum leaks - Affecting air/fuel mixture and sensor response'
  ],
  
  performanceImpact: 'P0133 causes delayed fuel mixture corrections, leading to poor fuel economy, reduced performance, increased emissions, and potential catalytic converter damage from prolonged rich or lean conditions.',
  
  quickAnswer: {
    icon: 'clock-o',
    meaning: 'Upstream O2 sensor responding too slowly to exhaust changes - usually aged or contaminated sensor.',
    fix: 'Replace O2 sensor, check sensor heater, inspect for contamination',
    cost: '$140-$380',
    time: '60-90 minutes',
    drivingSafety: 'Safe to drive but expect poor fuel economy and performance. Replace sensor to restore proper fuel control.'
  },
  
  aiQuestions: [
    {
      question: 'What\'s the difference between P0133 and other O2 sensor codes?',
      answer: 'P0133 indicates the O2 sensor is working but responding too slowly, while P0131/P0132 indicate voltage problems and P0134 indicates no activity. P0133 typically means an aging sensor that needs replacement rather than electrical problems.'
    },
    {
      question: 'How fast should an O2 sensor respond?',
      answer: 'A healthy O2 sensor should switch from lean to rich (or vice versa) in less than 100 milliseconds. Sensors with P0133 typically take 300+ milliseconds to respond, which is too slow for proper fuel control and triggers the code.'
    },
    {
      question: 'Can contamination cause P0133?',
      answer: 'Yes, oil, coolant, or fuel contamination can coat the O2 sensor element, slowing its response time. This is common in engines with worn rings, head gasket leaks, or fuel system problems. Clean the sensor if contamination is light, but replacement is usually needed.'
    },
    {
      question: 'How do I test O2 sensor response time?',
      answer: 'Use GeekOBD APP to monitor O2 sensor switching during a snap throttle test. Rev engine quickly and watch how fast the sensor responds - it should switch from lean to rich in under 100ms. Slow switching indicates sensor aging.'
    }
  ],

  costAnalysis: {
    averageCost: '$140-$380 for most P0133 repairs',
    repairOptions: [
      {
        title: 'O2 Sensor Replacement',
        description: 'Most common fix - Replace aged upstream O2 sensor (85% of cases)',
        color: '#4CAF50',
        icon: 'clock-o',
        items: [
          { name: 'Upstream O2 sensor', cost: '$75-$180' },
          { name: 'Labor (45-90 minutes)', cost: '$60-$180' }
        ],
        total: '$135-$360',
        successRate: '95% success rate'
      },
      {
        title: 'Sensor Cleaning',
        description: 'Clean contaminated sensor (10% success rate)',
        color: '#FF9800',
        icon: 'refresh',
        items: [
          { name: 'O2 sensor cleaner', cost: '$15-$25' },
          { name: 'Labor (30-45 minutes)', cost: '$40-$90' }
        ],
        total: '$55-$115',
        successRate: '10% success rate'
      },
      {
        title: 'Heater Circuit Repair',
        description: 'Fix faulty O2 sensor heater (5% of cases)',
        color: '#2196F3',
        icon: 'flash',
        items: [
          { name: 'Wiring repair', cost: '$25-$60' },
          { name: 'Diagnostic time', cost: '$80-$120' },
          { name: 'Labor (1-1.5 hours)', cost: '$100-$180' }
        ],
        total: '$205-$360',
        successRate: '90% success rate'
      }
    ],
    savingTips: [
      'Replace O2 sensor - cleaning rarely fixes slow response issues',
      'Use OEM or high-quality sensors for best response time',
      'Replace sensors in pairs if vehicle has high mileage',
      'O2 sensor replacement is often DIY-friendly, saving $60-180 in labor',
      'Address P0133 promptly to prevent catalytic converter damage'
    ]
  },

  diagnosticSteps: {
    estimatedTime: 'PT60M',
    steps: [
      {
        title: 'Monitor O2 Sensor Response Time',
        icon: 'stopwatch',
        description: 'Connect GeekOBD APP and perform snap throttle test. Rev engine quickly and monitor how fast O2 sensor switches from lean to rich. Should be under 100 milliseconds.',
        geekobdTip: 'GeekOBD APP can measure O2 sensor response time during throttle snap test - slow switching (over 300ms) confirms P0133 diagnosis.'
      },
      {
        title: 'Check O2 Sensor Heater Operation',
        icon: 'fire',
        description: 'Test O2 sensor heater circuit with multimeter. Heater should draw 1-2 amps and reach operating temperature quickly for proper sensor response.',
        geekobdTip: 'Use GeekOBD APP to monitor O2 sensor heater status - proper heating is essential for fast sensor response time.'
      },
      {
        title: 'Visual Inspection for Contamination',
        icon: 'eye',
        description: 'Remove O2 sensor and inspect for contamination, carbon buildup, or physical damage. White, black, or oily deposits indicate contamination affecting response.',
        geekobdTip: 'Monitor O2 readings with GeekOBD APP during inspection - contaminated sensors often show sluggish or erratic readings.'
      },
      {
        title: 'Test Exhaust System Integrity',
        icon: 'search',
        description: 'Check for exhaust leaks before O2 sensor that could dilute exhaust gases and affect sensor response. Listen for hissing sounds and inspect connections.',
        geekobdTip: 'GeekOBD APP can show if O2 readings are affected by exhaust leaks - readings may be erratic or show false lean conditions.'
      },
      {
        title: 'Component Replacement and Verification',
        icon: 'check-circle',
        description: 'Replace aged O2 sensor with new unit. Clear codes and perform road test with snap throttle tests to verify improved response time.',
        geekobdTip: 'Use GeekOBD APP to verify new O2 sensor responds quickly (under 100ms) during throttle snap tests, confirming successful repair.'
      }
    ],
    importantNotes: [
      'O2 sensor should respond in under 100 milliseconds',
      'Slow response usually indicates sensor aging requiring replacement',
      'Cleaning contaminated sensors rarely restores proper response time'
    ]
  },

  caseStudies: [
    {
      title: 'Honda Civic Aged O2 Sensor',
      vehicle: '2014 Honda Civic 1.8L 4-cylinder, 145,000 miles',
      problem: 'Customer reported poor fuel economy (dropped from 35 to 28 MPG) and P0133 code. Engine ran rough during warm-up and had poor throttle response.',
      diagnosis: 'GeekOBD APP snap throttle test showed O2 sensor taking 450 milliseconds to switch from lean to rich (should be under 100ms). Sensor was original and had never been replaced.',
      solution: 'Replaced upstream O2 sensor with OEM part. Sensor was aged and no longer responding quickly enough for proper fuel control.',
      cost: 'O2 sensor: $125, Labor: $85, Total: $210',
      result: 'P0133 code cleared immediately. O2 sensor now responds in 75ms during snap throttle test. Fuel economy returned to 34 MPG and throttle response improved.'
    },
    {
      title: 'Ford Explorer Contaminated Sensor',
      vehicle: '2016 Ford Explorer 3.5L V6, 98,000 miles',
      problem: 'P0133 code with poor performance and black smoke during acceleration. Customer noticed oil consumption had increased recently.',
      diagnosis: 'O2 sensor response time was 380ms, but sensor also showed oil contamination. Engine had worn valve seals allowing oil into exhaust, contaminating O2 sensor.',
      solution: 'Replaced contaminated O2 sensor and repaired valve seals to prevent future contamination. Also replaced spark plugs fouled by oil consumption.',
      cost: 'O2 sensor: $95, Valve seals: $450, Spark plugs: $65, Labor: $380, Total: $990',
      result: 'P0133 code cleared and O2 sensor response improved to 85ms. Oil consumption stopped and no more sensor contamination after 8 months.'
    }
  ],

  relatedCodes: [
    { code: 'P0131', description: 'O2 Sensor Low Voltage Bank 1 Sensor 1 - Voltage problems', color: '#3498db' },
    { code: 'P0132', description: 'O2 Sensor High Voltage Bank 1 Sensor 1 - Voltage problems', color: '#e74c3c' },
    { code: 'P0134', description: 'O2 Sensor No Activity Bank 1 Sensor 1 - No sensor switching', color: '#f39c12' },
    { code: 'P0171', description: 'System Too Lean Bank 1 - Can be caused by slow O2 response', color: '#9b59b6' },
    { code: 'P0172', description: 'System Too Rich Bank 1 - Can be caused by slow O2 response', color: '#9b59b6' },
    { code: 'P0420', description: 'Catalyst Efficiency Bank 1 - Can be affected by poor fuel control', color: '#e67e22' },
    { code: 'P0300', description: 'Random Misfire - Poor fuel control can cause misfires', color: '#27ae60' }
  ],

  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P0133',
      description: 'Use GeekOBD APP for O2 sensor response time testing!',
      features: [
        'Snap throttle test monitoring',
        'Response time measurement',
        'O2 sensor switching analysis',
        'Heater circuit verification'
      ]
    },
    systemCodes: {
      title: 'O2 Sensor Codes',
      description: 'Related oxygen sensor codes:'
    },
    diagnosticResources: [
      {
        title: 'O2 Response Time Testing',
        description: 'Professional procedures for testing sensor response speed',
        icon: 'clock-o',
        url: '#diagnostic-steps'
      },
      {
        title: 'Snap Throttle Test',
        description: 'Performing snap throttle tests for O2 sensor diagnosis',
        icon: 'tachometer',
        url: '../resources/snap-throttle-test.html'
      },
      {
        title: 'O2 Sensor Contamination',
        description: 'Identifying and preventing O2 sensor contamination',
        icon: 'flask',
        url: '../resources/o2-sensor-contamination.html'
      },
      {
        title: 'Fuel Control Systems',
        description: 'Understanding closed-loop fuel control operation',
        icon: 'cogs',
        url: '../resources/fuel-control-systems.html'
      }
    ],
    codeInfo: {
      system: 'Engine Management',
      severity: 'MEDIUM',
      category: 'Oxygen Sensor'
    }
  }
});

module.exports = p0133Data;
