const { DTCData } = require('./dtc-template-generator');

// P0106 MAP Sensor Range/Performance 的完整数据结构
const p0106Data = new DTCData({
  code: 'P0106',
  title: 'MAP Sensor Range/Performance',
  description: 'The Engine Control Module has detected that the Manifold Absolute Pressure sensor signal is outside the expected range or not performing within specifications.',
  definition: 'The Engine Control Module has detected that the Manifold Absolute Pressure (MAP) sensor signal is outside the expected range or not performing within specifications. The MAP sensor measures intake manifold vacuum/pressure to help the ECM calculate engine load and determine proper fuel injection timing and duration. When the MAP sensor reading doesn\'t correlate with throttle position and other engine parameters, P0106 is triggered.',
  
  symptoms: [
    'Check engine light illuminated - ECM detected MAP sensor performance issue',
    'Poor engine performance - Incorrect load calculation affects fuel delivery',
    'Rough idle or stalling - Improper fuel mixture at idle conditions',
    'Black smoke from exhaust - Rich fuel mixture from incorrect MAP readings',
    'Engine hesitation during acceleration - Poor throttle response under load',
    'Hard starting - Incorrect fuel calculation during startup',
    'Poor fuel economy - ECM unable to optimize fuel delivery',
    'Engine surging at cruise - Inconsistent MAP readings causing fuel fluctuations',
    'Failed emissions test - Improper air/fuel mixture affects exhaust emissions'
  ],
  
  causes: [
    'Faulty MAP sensor - Internal sensor failure or contamination',
    'Damaged MAP sensor wiring - Broken, corroded, or shorted wires',
    'Loose MAP sensor connector - Poor electrical connection causing intermittent signals',
    'Vacuum leak at MAP sensor - Affecting pressure readings to sensor',
    'Clogged MAP sensor vacuum line - Restricting pressure signal to sensor',
    'Faulty ECM - Engine control module not processing MAP signals correctly',
    'Intake manifold problems - Cracks or leaks affecting manifold pressure',
    'Throttle body issues - Carbon buildup affecting airflow and pressure readings'
  ],
  
  performanceImpact: 'P0106 causes poor engine performance, fuel economy issues, and emissions problems due to incorrect engine load calculations. The ECM cannot properly determine fuel injection requirements, leading to rich or lean conditions that affect power, efficiency, and catalytic converter operation.',
  
  quickAnswer: {
    icon: 'dashboard',
    meaning: 'MAP sensor not reading manifold pressure correctly - affects fuel delivery calculations.',
    fix: 'Replace MAP sensor, check vacuum lines, inspect wiring',
    cost: '$85-$320',
    time: '30-90 minutes',
    drivingSafety: 'Safe to drive but expect poor performance and fuel economy. Repair soon to prevent emissions issues.'
  },
  
  aiQuestions: [
    {
      question: 'What\'s the difference between MAP and MAF sensors?',
      answer: 'MAP (Manifold Absolute Pressure) sensors measure intake manifold pressure/vacuum to calculate engine load, while MAF (Mass Air Flow) sensors directly measure the amount of air entering the engine. Some engines use MAP sensors (speed-density systems), others use MAF sensors, and some use both for cross-reference.'
    },
    {
      question: 'Can I clean a MAP sensor instead of replacing it?',
      answer: 'Yes, MAP sensors can often be cleaned with MAF sensor cleaner or electrical contact cleaner. Remove the sensor, spray the sensing element gently, and let dry completely. However, if the sensor is internally damaged or the diaphragm is torn, cleaning won\'t help and replacement is necessary.'
    },
    {
      question: 'How do I test a MAP sensor with GeekOBD APP?',
      answer: 'GeekOBD APP can display live MAP sensor readings in kPa or inHg. At idle, expect 20-30 kPa (6-9 inHg). With engine off, key on, readings should be near atmospheric pressure (100 kPa/30 inHg). Rev the engine and watch for smooth changes - erratic readings indicate sensor problems.'
    },
    {
      question: 'Why does P0106 cause black smoke?',
      answer: 'When the MAP sensor reads incorrectly high pressure (indicating high engine load), the ECM adds extra fuel thinking the engine needs more power. This creates a rich fuel mixture that produces black smoke from unburned fuel in the exhaust. The ECM is essentially overfueling based on false load information.'
    }
  ],

  costAnalysis: {
    averageCost: '$85-$320 for most P0106 repairs',
    repairOptions: [
      {
        title: 'MAP Sensor Replacement',
        description: 'Most common fix - Replace faulty MAP sensor (70% of cases)',
        color: '#4CAF50',
        icon: 'dashboard',
        items: [
          { name: 'MAP sensor', cost: '$35-$85' },
          { name: 'Labor (30-60 minutes)', cost: '$50-$120' }
        ],
        total: '$85-$205',
        successRate: '90% success rate'
      },
      {
        title: 'Wiring Repair',
        description: 'Fix damaged MAP sensor wiring or connectors (20% of cases)',
        color: '#2196F3',
        icon: 'plug',
        items: [
          { name: 'Wiring repair kit', cost: '$15-$40' },
          { name: 'Connector (if needed)', cost: '$25-$60' },
          { name: 'Labor (1-2 hours)', cost: '$100-$240' }
        ],
        total: '$140-$340',
        successRate: '85% success rate'
      },
      {
        title: 'Vacuum Line Service',
        description: 'Replace damaged vacuum lines to MAP sensor (10% of cases)',
        color: '#FF9800',
        icon: 'road',
        items: [
          { name: 'Vacuum hose', cost: '$10-$25' },
          { name: 'Fittings', cost: '$5-$15' },
          { name: 'Labor (30-45 minutes)', cost: '$50-$90' }
        ],
        total: '$65-$130',
        successRate: '95% success rate'
      }
    ],
    savingTips: [
      'Try cleaning the MAP sensor first - often fixes intermittent issues for under $10',
      'Check vacuum lines before replacing sensor - simple hose replacement may fix the problem',
      'MAP sensor replacement is usually DIY-friendly, saving $50-120 in labor',
      'Use GeekOBD APP to verify repair - sensor readings should be stable and responsive',
      'Replace vacuum lines when replacing MAP sensor to prevent future issues'
    ]
  },

  diagnosticSteps: {
    estimatedTime: 'PT45M',
    steps: [
      {
        title: 'Monitor MAP Sensor Data',
        icon: 'line-chart',
        description: 'Connect GeekOBD APP and monitor live MAP sensor readings. At idle expect 20-30 kPa, with engine off/key on expect near atmospheric pressure (100 kPa). Rev engine and observe smooth pressure changes.',
        geekobdTip: 'GeekOBD APP can graph MAP sensor data over time - look for erratic readings, stuck values, or readings that don\'t change with throttle input.'
      },
      {
        title: 'Visual Inspection',
        icon: 'eye',
        description: 'Inspect MAP sensor, vacuum lines, and electrical connections. Look for cracked vacuum hoses, loose connectors, damaged wiring, or oil contamination on the sensor.',
        geekobdTip: 'Use GeekOBD APP to monitor readings while wiggling wires and vacuum lines - intermittent changes indicate connection problems.'
      },
      {
        title: 'Vacuum System Test',
        icon: 'road',
        description: 'Check vacuum line from intake manifold to MAP sensor for restrictions or leaks. Apply vacuum with hand pump if available and verify sensor responds correctly.',
        geekobdTip: 'GeekOBD APP should show MAP readings change smoothly when vacuum is applied - stuck or erratic readings indicate sensor failure.'
      },
      {
        title: 'Electrical Testing',
        icon: 'plug',
        description: 'Test MAP sensor power supply (usually 5V reference), ground circuit, and signal wire continuity. Check for proper voltage at sensor connector with key on.',
        geekobdTip: 'GeekOBD APP can show if ECM is receiving MAP sensor signals - no data or fixed values indicate wiring problems.'
      },
      {
        title: 'Component Replacement and Verification',
        icon: 'check-circle',
        description: 'Replace faulty MAP sensor, repair wiring, or fix vacuum leaks as needed. Clear codes and road test while monitoring MAP sensor data for proper operation.',
        geekobdTip: 'Use GeekOBD APP to verify MAP sensor readings are now stable and responsive to throttle changes - proper readings confirm successful repair.'
      }
    ],
    importantNotes: [
      'MAP sensor readings should change smoothly with throttle input',
      'Atmospheric pressure reading with engine off confirms sensor accuracy',
      'Clean MAP sensor before replacement - may resolve intermittent issues'
    ]
  },

  caseStudies: [
    {
      title: 'Honda Civic Intermittent MAP Sensor',
      vehicle: '2015 Honda Civic 1.8L 4-cylinder, 78,000 miles',
      problem: 'Customer reported intermittent rough idle, occasional stalling, and poor fuel economy. P0106 code appeared sporadically, making diagnosis difficult.',
      diagnosis: 'GeekOBD APP monitoring showed MAP sensor readings would occasionally spike to unrealistic values, then return to normal. Visual inspection revealed a cracked vacuum line near the firewall that would flex and seal under certain conditions.',
      solution: 'Replaced the cracked vacuum line section with new hose and secured with proper clamps. The intermittent nature was caused by the crack opening and closing with engine vibration.',
      cost: 'Vacuum hose: $8, Clamps: $3, Labor: $45, Total: $56',
      result: 'P0106 code has not returned after 3 months. MAP sensor readings are now stable and fuel economy improved by 3 MPG.'
    },
    {
      title: 'Ford F-150 Contaminated MAP Sensor',
      vehicle: '2018 Ford F-150 3.5L V6 Turbo, 95,000 miles',
      problem: 'Truck experienced poor acceleration, black smoke under load, and failed emissions test. P0106 code was present along with rich fuel mixture symptoms.',
      diagnosis: 'GeekOBD APP showed MAP sensor reading consistently high pressure values, causing ECM to add excessive fuel. Physical inspection revealed oil contamination on MAP sensor from PCV system problems.',
      solution: 'Cleaned MAP sensor thoroughly with electrical contact cleaner and repaired PCV valve that was allowing oil vapors to contaminate the sensor. Also replaced air filter.',
      cost: 'MAP sensor cleaning: $0, PCV valve: $25, Air filter: $18, Labor: $85, Total: $128',
      result: 'Black smoke eliminated, acceleration improved significantly. Emissions test passed and fuel economy returned to normal levels.'
    }
  ],

  relatedCodes: [
    { code: 'P0105', description: 'MAP Sensor Circuit Malfunction - Electrical circuit problems', color: '#e74c3c' },
    { code: 'P0107', description: 'MAP Sensor Low Input - Sensor reading too low pressure', color: '#3498db' },
    { code: 'P0108', description: 'MAP Sensor High Input - Sensor reading too high pressure', color: '#f39c12' },
    { code: 'P0171', description: 'System Too Lean Bank 1 - Can be caused by MAP sensor problems', color: '#9b59b6' },
    { code: 'P0172', description: 'System Too Rich Bank 1 - Can be caused by MAP sensor problems', color: '#9b59b6' },
    { code: 'P0101', description: 'MAF Sensor Range/Performance - Similar airflow measurement issues', color: '#4a90e2' },
    { code: 'P0300', description: 'Random Misfire - Can be caused by incorrect fuel delivery from MAP issues', color: '#e67e22' }
  ],

  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P0106',
      description: 'Use GeekOBD APP for comprehensive MAP sensor monitoring!',
      features: [
        'Live MAP sensor data graphing',
        'Pressure vs RPM correlation analysis',
        'Vacuum leak detection assistance',
        'Repair verification testing'
      ]
    },
    systemCodes: {
      title: 'MAP Sensor Codes',
      description: 'Related manifold pressure sensor codes:'
    },
    diagnosticResources: [
      {
        title: 'MAP Sensor Testing Guide',
        description: 'Professional procedures for testing MAP sensor operation',
        icon: 'dashboard',
        url: '#diagnostic-steps'
      },
      {
        title: 'Vacuum System Diagnosis',
        description: 'Complete guide to finding and fixing vacuum leaks',
        icon: 'road',
        url: '../resources/vacuum-system-diagnosis.html'
      },
      {
        title: 'Engine Load Calculation',
        description: 'How ECM uses MAP sensor for fuel delivery decisions',
        icon: 'calculator',
        url: '../resources/engine-load-calculation.html'
      },
      {
        title: 'Speed-Density Systems',
        description: 'Understanding MAP-based fuel injection systems',
        icon: 'cogs',
        url: '../resources/speed-density-systems.html'
      }
    ],
    codeInfo: {
      system: 'Engine Management',
      severity: 'MEDIUM',
      category: 'Sensor Performance'
    }
  }
});

module.exports = p0106Data;
