const { DTCData } = require('./dtc-template-generator');

// P0107 MAP Sensor Low Input 的完整数据结构
const p0107Data = new DTCData({
  code: 'P0107',
  title: 'MAP Sensor Low Input',
  description: 'The Engine Control Module has detected that the Manifold Absolute Pressure sensor is reading abnormally low pressure values.',
  definition: 'The Engine Control Module has detected that the Manifold Absolute Pressure (MAP) sensor is reading abnormally low pressure values, typically below 10 kPa (3 inHg) when higher readings are expected. This indicates the sensor is either reading excessive vacuum conditions or has an electrical fault causing low voltage output. The MAP sensor measures intake manifold pressure to help calculate engine load for proper fuel injection.',
  
  symptoms: [
    'Check engine light illuminated - ECM detected MAP sensor low input fault',
    'Engine running rich - ECM thinks engine is under high load due to low pressure reading',
    'Black smoke from exhaust - Excessive fuel delivery from incorrect load calculation',
    'Poor fuel economy - Rich fuel mixture wastes gasoline',
    'Engine flooding during startup - Too much fuel injected based on false readings',
    'Rough idle or stalling - Rich mixture causes unstable combustion',
    'Strong fuel smell - Unburned fuel from rich mixture',
    'Carbon buildup on spark plugs - Rich mixture fouls plugs quickly',
    'Failed emissions test - Rich exhaust conditions exceed limits'
  ],
  
  causes: [
    'Faulty MAP sensor - Internal sensor failure reading constant low pressure',
    'Short circuit in MAP sensor wiring - Wire shorted to ground causing low voltage',
    'Damaged MAP sensor connector - Corrosion or damage causing poor connection',
    'Vacuum leak at MAP sensor - Large leak causing extremely low pressure readings',
    'Blocked MAP sensor vacuum line - Complete blockage preventing pressure signal',
    'ECM internal fault - Control module misreading MAP sensor signals',
    'MAP sensor contamination - Oil or debris affecting sensor operation',
    'Incorrect MAP sensor installation - Wrong sensor type or improper mounting'
  ],
  
  performanceImpact: 'P0107 causes the ECM to deliver excessive fuel based on false high-load readings, resulting in poor fuel economy, emissions problems, carbon buildup, and potential catalytic converter damage from rich exhaust conditions.',
  
  quickAnswer: {
    icon: 'arrow-down',
    meaning: 'MAP sensor reading abnormally low pressure - causes rich fuel mixture and poor performance.',
    fix: 'Replace MAP sensor, check wiring for shorts, inspect vacuum lines',
    cost: '$90-$350',
    time: '45-90 minutes',
    drivingSafety: 'Safe to drive short distances but expect poor fuel economy and black smoke. Repair soon to prevent catalytic converter damage.'
  },
  
  aiQuestions: [
    {
      question: 'What\'s the difference between P0107 and P0108 MAP codes?',
      answer: 'P0107 indicates the MAP sensor is reading too low pressure (high vacuum), while P0108 indicates too high pressure (low vacuum). P0107 typically causes rich fuel mixture because the ECM thinks the engine is under heavy load, while P0108 causes lean mixture.'
    },
    {
      question: 'Can a completely blocked vacuum line cause P0107?',
      answer: 'Yes, if the MAP sensor vacuum line is completely blocked, the sensor will read atmospheric pressure (high) initially, but if there\'s a leak after the blockage or the sensor fails, it can read very low pressure. More commonly, P0107 is caused by sensor failure or electrical problems.'
    },
    {
      question: 'Why does P0107 cause black smoke?',
      answer: 'When the MAP sensor reads abnormally low pressure, the ECM interprets this as high engine load (like full throttle acceleration) and adds extra fuel. This creates a rich mixture that produces black smoke from unburned fuel in the exhaust system.'
    },
    {
      question: 'How do I test MAP sensor voltage for P0107?',
      answer: 'Use GeekOBD APP to monitor MAP sensor voltage - should be 1-4.5V depending on pressure. With P0107, you\'ll typically see voltage stuck near 0.5V or lower. At idle, expect 1-2V; at wide open throttle, expect 4-4.5V. Constant low voltage confirms P0107 diagnosis.'
    }
  ],
  
  costAnalysis: {
    averageCost: '$90-$350 for most P0107 repairs',
    repairOptions: [
      {
        title: 'MAP Sensor Replacement',
        description: 'Most common fix - Replace failed MAP sensor (80% of cases)',
        color: '#4CAF50',
        icon: 'arrow-down',
        items: [
          { name: 'MAP sensor', cost: '$40-$95' },
          { name: 'Labor (45-75 minutes)', cost: '$60-$150' }
        ],
        total: '$100-$245',
        successRate: '95% success rate'
      },
      {
        title: 'Wiring Repair',
        description: 'Fix short circuit in MAP sensor wiring (15% of cases)',
        color: '#FF9800',
        icon: 'plug',
        items: [
          { name: 'Wiring repair materials', cost: '$20-$50' },
          { name: 'Diagnostic time', cost: '$80-$120' },
          { name: 'Labor (1-2 hours)', cost: '$100-$240' }
        ],
        total: '$200-$410',
        successRate: '90% success rate'
      },
      {
        title: 'Connector Service',
        description: 'Clean or replace corroded MAP sensor connector (5% of cases)',
        color: '#2196F3',
        icon: 'wrench',
        items: [
          { name: 'Connector cleaning', cost: '$15-$30' },
          { name: 'New connector (if needed)', cost: '$25-$60' },
          { name: 'Labor (30-60 minutes)', cost: '$50-$120' }
        ],
        total: '$90-$210',
        successRate: '85% success rate'
      }
    ],
    savingTips: [
      'Check MAP sensor voltage first - constant low voltage confirms sensor failure',
      'Inspect connector for corrosion before replacing sensor',
      'MAP sensor replacement is usually DIY-friendly, saving $60-150 in labor',
      'Use GeekOBD APP to verify repair - readings should respond to throttle changes',
      'Address P0107 quickly to prevent catalytic converter damage from rich mixture'
    ]
  },

  diagnosticSteps: {
    estimatedTime: 'PT60M',
    steps: [
      {
        title: 'Monitor MAP Sensor Voltage',
        icon: 'bolt',
        description: 'Connect GeekOBD APP and monitor MAP sensor voltage. With P0107, expect constant low voltage (under 1V) regardless of throttle position. Normal operation shows 1-2V at idle, 4-4.5V at wide open throttle.',
        geekobdTip: 'GeekOBD APP can graph MAP voltage over time - P0107 typically shows flat line at low voltage instead of normal pressure variations.'
      },
      {
        title: 'Visual Inspection',
        icon: 'eye',
        description: 'Inspect MAP sensor, wiring harness, and connector for damage. Look for corroded pins, damaged wires, or signs of water/oil contamination on the sensor.',
        geekobdTip: 'Use GeekOBD APP to monitor voltage while wiggling wires - if readings change, you\'ve found intermittent wiring problems.'
      },
      {
        title: 'Electrical Testing',
        icon: 'plug',
        description: 'Test MAP sensor power supply (5V reference), ground circuit, and signal wire. Check for short circuits to ground that could cause low voltage readings.',
        geekobdTip: 'GeekOBD APP should show stable 5V reference voltage - if missing, check ECM power supply or wiring to MAP sensor.'
      },
      {
        title: 'Vacuum System Check',
        icon: 'road',
        description: 'Inspect vacuum line from intake manifold to MAP sensor. While less common with P0107, severe vacuum leaks can sometimes cause abnormally low pressure readings.',
        geekobdTip: 'Apply vacuum to MAP sensor with hand pump while monitoring GeekOBD APP - sensor should respond with increasing voltage as vacuum increases.'
      },
      {
        title: 'Component Replacement and Verification',
        icon: 'check-circle',
        description: 'Replace faulty MAP sensor or repair wiring as diagnosed. Clear codes and road test while monitoring MAP sensor voltage for proper operation across all throttle positions.',
        geekobdTip: 'GeekOBD APP should now show MAP voltage varying smoothly from 1-2V at idle to 4-4.5V at full throttle, confirming successful repair.'
      }
    ],
    importantNotes: [
      'P0107 typically indicates MAP sensor failure rather than vacuum problems',
      'Constant low voltage reading is key diagnostic indicator',
      'Rich fuel mixture from P0107 can damage catalytic converter quickly'
    ]
  },

  caseStudies: [
    {
      title: 'Chevrolet Malibu Rich Running Condition',
      vehicle: '2016 Chevrolet Malibu 1.5L Turbo, 85,000 miles',
      problem: 'Customer complained of poor fuel economy (dropped from 32 to 18 MPG), black smoke during acceleration, and strong fuel smell. P0107 code was present.',
      diagnosis: 'GeekOBD APP showed MAP sensor voltage stuck at 0.3V regardless of throttle position or engine load. Normal readings should vary from 1-4.5V. Visual inspection revealed no obvious damage to sensor or wiring.',
      solution: 'Replaced MAP sensor with OEM part. Sensor had failed internally, causing constant low voltage output that made ECM think engine was under maximum load.',
      cost: 'MAP sensor: $65, Labor: $75, Total: $140',
      result: 'P0107 code cleared immediately. Fuel economy returned to 31 MPG, black smoke eliminated, and engine runs smoothly across all load conditions.'
    },
    {
      title: 'Ford Escape Intermittent Rich Mixture',
      vehicle: '2018 Ford Escape 1.5L Turbo, 72,000 miles',
      problem: 'Intermittent P0107 code with occasional black smoke and rough idle. Problem seemed worse in wet weather conditions.',
      diagnosis: 'GeekOBD APP monitoring showed MAP voltage would occasionally drop to near zero, then return to normal. Found corroded MAP sensor connector allowing moisture intrusion during rain.',
      solution: 'Cleaned corroded connector pins with electrical contact cleaner and applied dielectric grease. Secured connector boot to prevent future moisture entry.',
      cost: 'Connector cleaning kit: $12, Dielectric grease: $8, Labor: $60, Total: $80',
      result: 'P0107 code has not returned after 6 months including through wet winter weather. MAP sensor readings remain stable in all conditions.'
    }
  ],

  relatedCodes: [
    { code: 'P0106', description: 'MAP Sensor Range/Performance - General MAP sensor performance issues', color: '#4a90e2' },
    { code: 'P0108', description: 'MAP Sensor High Input - Opposite condition (too high pressure)', color: '#e74c3c' },
    { code: 'P0105', description: 'MAP Sensor Circuit Malfunction - Electrical circuit problems', color: '#f39c12' },
    { code: 'P0172', description: 'System Too Rich Bank 1 - Often caused by P0107 MAP sensor issues', color: '#9b59b6' },
    { code: 'P0175', description: 'System Too Rich Bank 2 - Often caused by P0107 MAP sensor issues', color: '#9b59b6' },
    { code: 'P0420', description: 'Catalyst Efficiency - Can be damaged by rich mixture from P0107', color: '#e67e22' },
    { code: 'P0300', description: 'Random Misfire - Rich mixture from P0107 can cause misfires', color: '#27ae60' }
  ],

  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P0107',
      description: 'Use GeekOBD APP for precise MAP sensor voltage monitoring!',
      features: [
        'Real-time MAP voltage graphing',
        'Throttle position correlation',
        'Rich mixture detection',
        'Repair verification testing'
      ]
    },
    systemCodes: {
      title: 'MAP Sensor Codes',
      description: 'Related manifold pressure sensor codes:'
    },
    diagnosticResources: [
      {
        title: 'MAP Voltage Testing',
        description: 'Professional procedures for testing MAP sensor voltage',
        icon: 'bolt',
        url: '#diagnostic-steps'
      },
      {
        title: 'Rich Mixture Diagnosis',
        description: 'Understanding and diagnosing rich fuel conditions',
        icon: 'tint',
        url: '../resources/rich-mixture-diagnosis.html'
      },
      {
        title: 'Electrical Short Testing',
        description: 'Finding and repairing short circuits in sensor wiring',
        icon: 'flash',
        url: '../resources/electrical-short-testing.html'
      },
      {
        title: 'Catalytic Converter Protection',
        description: 'Preventing catalyst damage from rich fuel mixtures',
        icon: 'shield',
        url: '../resources/catalytic-converter-protection.html'
      }
    ],
    codeInfo: {
      system: 'Engine Management',
      severity: 'MEDIUM',
      category: 'Sensor Input'
    }
  }
});

module.exports = p0107Data;
