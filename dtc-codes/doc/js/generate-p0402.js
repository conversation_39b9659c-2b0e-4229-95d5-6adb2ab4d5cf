const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0402Data = require('./p0402-data');

console.log('🚀 Generating P0402 - EGR Flow Excessive Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0402Data);
  const outputPath = path.join(__dirname, '../../p0402.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0402 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0402 page:', error.message);
  process.exit(1);
}
