const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0104Data = require('./p0104-data');

console.log('🚀 Generating P0104 - MAF Sensor Intermittent Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0104Data);
  const outputPath = path.join(__dirname, '../../p0104.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0104 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0104 page:', error.message);
  process.exit(1);
}
