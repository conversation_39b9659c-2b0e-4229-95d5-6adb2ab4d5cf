#!/usr/bin/env node
// 自动更新sitemap.xml，添加所有新创建的故障码页面
const fs = require('fs');
const path = require('path');

// 所有故障码列表 - 基于实际生成的HTML文件
const allDTCCodes = [
    // P系统 - Engine/Powertrain (实际生成的故障码)
    'P0100', 'P0101', 'P0102', 'P0103', 'P0104', 'P0105', 'P0106', 'P0107', 'P0108', 'P0109',
    'P0110', 'P0111', 'P0112', 'P0113', 'P0114', 'P0115', 'P0116', 'P0117', 'P0118', 'P0119',
    'P0120', 'P0121', 'P0122', 'P0123', 'P0124', 'P0125', 'P0126', 'P0127', 'P0128', 'P0129',
    'P0130', 'P0131', 'P0132', 'P0133', 'P0134', 'P0135', 'P0136', 'P0137', 'P0138', 'P0139',
    'P0140', 'P0141', 'P0142', 'P0143', 'P0144', 'P0145', 'P0146', 'P0147', 'P0148', 'P0149',
    'P0150', 'P0151', 'P0152', 'P0153', 'P0154', 'P0155', 'P0156', 'P0157', 'P0158', 'P0159',
    'P0160', 'P0161', 'P0162', 'P0163', 'P0164', 'P0165', 'P0166', 'P0167', 'P0168', 'P0169',
    'P0170', 'P0171', 'P0172', 'P0173', 'P0174', 'P0175', 'P0176', 'P0177', 'P0178', 'P0179',
    'P0180', 'P0181', 'P0182', 'P0183', 'P0184', 'P0185', 'P0186', 'P0187', 'P0188', 'P0190',
    'P0191', 'P0192', 'P0193', 'P0200', 'P0201', 'P0202', 'P0203', 'P0204', 'P0205', 'P0206',
    'P0207', 'P0208', 'P0209', 'P0220', 'P0221', 'P0222', 'P0223', 'P0230', 'P0231', 'P0232',
    'P0233', 'P0234', 'P0300', 'P0301', 'P0302', 'P0303', 'P0304', 'P0305', 'P0306', 'P0307',
    'P0340', 'P0341', 'P0350', 'P0351', 'P0352', 'P0354', 'P0355', 'P0356', 'P0357', 'P0358',
    'P0359', 'P0360', 'P0361', 'P0362', 'P0363', 'P0364', 'P0365', 'P0366', 'P0400', 'P0401',
    'P0402', 'P0403', 'P0404', 'P0405', 'P0406', 'P0420', 'P0430', 'P0440', 'P0441', 'P0442',
    'P0443', 'P0444', 'P0445', 'P0446', 'P0447', 'P0448', 'P0449', 'P0450', 'P0451', 'P0452',
    'P0453', 'P0454', 'P0455', 'P0456', 'P0457', 'P0458', 'P0459', 'P0460', 'P0500', 'P0501',
    'P0502', 'P0503', 'P0504', 'P0505', 'P0506', 'P0507', 'P0508', 'P0600', 'P0601', 'P0700',
    'P0701', 'P0702', 'P0703', 'P0704', 'P0705', 'P0706', 'P0707', 'P0708', 'P0709', 'P0710',
    'P0711', 'P0712', 'P0713', 'P0714', 'P0715', 'P0716', 'P0717', 'P0718', 'P0719', 'P0720',
    'P0721', 'P0722', 'P0741', 'P0750', 'P0755', 'P0760', 'P0800', 'P0900', 'P0A00', 'P0A0F',
    'P1000', 'P1001', 'P1100', 'P1101', 'P1130', 'P1131', 'P1150', 'P1151', 'P1200', 'P1201',
    'P1300', 'P1400', 'P1401', 'P1500', 'P1501', 'P2000', 'P2100', 'P2101', 'P2300', 'P2301',
    'P2500', 'P2600', 'P3000', 'P3100', 'P3400', 'P3500', 'P4000',

    // B系统 - Body (实际生成的故障码)
    'B0001', 'B0002', 'B0003', 'B0004', 'B0005', 'B0006', 'B0007', 'B0008', 'B0009', 'B0010',
    'B0050', 'B0051', 'B0052', 'B0053', 'B0054', 'B0055', 'B0056', 'B0057', 'B0058', 'B0059',
    'B0060', 'B0061', 'B0062', 'B0063', 'B0064', 'B0065', 'B0066', 'B0067', 'B0068', 'B0069',
    'B0070', 'B0071', 'B0072', 'B0073', 'B0074', 'B0075', 'B0076', 'B0077', 'B0078', 'B0079',
    'B0100', 'B0200', 'B0201', 'B0300', 'B0400', 'B0500', 'B0501', 'B0600', 'B0700', 'B0701',
    'B1000', 'B1001', 'B1002', 'B1003', 'B1004', 'B1005', 'B1006', 'B1007', 'B1008', 'B1009',
    'B1010', 'B1011', 'B1012', 'B1013', 'B1014', 'B1015', 'B1016', 'B1017', 'B1018', 'B1019',
    'B1020', 'B1021', 'B1022', 'B1023', 'B1024', 'B1025', 'B1026', 'B1027', 'B1028', 'B1029',
    'B1030', 'B1200', 'B1300', 'B1301', 'B1400', 'B2001', 'B2002', 'B2003', 'B2004', 'B2005',
    'B2006', 'B2007', 'B2008', 'B2009', 'B2010', 'B2011', 'B2012', 'B2013', 'B2014', 'B2015',
    'B2016', 'B2017', 'B2018', 'B2019', 'B2020', 'B3001', 'B3002', 'B3003', 'B3004', 'B3005',
    'B3006', 'B3007', 'B3008', 'B3009', 'B3010', 'B3011', 'B3012', 'B3013', 'B3014', 'B3015',
    'B3016', 'B3017', 'B3018', 'B3019', 'B3020', 'B4000', 'B4001', 'B4002', 'B4003', 'B4004',
    'B4005', 'B4006', 'B4007', 'B4008', 'B4009', 'B4010', 'B4011', 'B4012', 'B4013', 'B4014',
    'B4015', 'B4016', 'B4017', 'B4018', 'B4019',

    // C系统 - Chassis (实际生成的故障码)
    'C0035', 'C0040', 'C0050', 'C0101', 'C0102', 'C0103', 'C0104', 'C0105', 'C0106', 'C0107',
    'C0108', 'C0109', 'C0110', 'C0111', 'C0112', 'C0113', 'C0114', 'C0115', 'C0116', 'C0120',
    'C0121', 'C0122', 'C0123', 'C0124', 'C0125', 'C0126', 'C0127', 'C0128', 'C0129', 'C0130',
    'C0131', 'C0132', 'C0133', 'C0134', 'C0135', 'C0136', 'C0137', 'C0138', 'C0139', 'C0140',
    'C0141', 'C0142', 'C0143', 'C0144', 'C0145', 'C0146', 'C0147', 'C0148', 'C0149', 'C0150',
    'C0200', 'C0201', 'C0300', 'C0301', 'C0302', 'C0400', 'C0401', 'C0402', 'C0403', 'C0404',
    'C0405', 'C0410', 'C0411', 'C0412', 'C0413', 'C0414', 'C0420', 'C0421', 'C0422', 'C0423',
    'C0424', 'C0430', 'C0431', 'C0432', 'C0433', 'C0440', 'C0441', 'C0442', 'C0443', 'C0444',
    'C0445', 'C0500', 'C0501', 'C0600', 'C0601', 'C0602', 'C0603', 'C0604', 'C0605', 'C0610',
    'C0611', 'C0612', 'C0613', 'C0614', 'C0620', 'C0621', 'C0622', 'C0623', 'C0624', 'C0630',
    'C0631', 'C0632', 'C0633', 'C0634', 'C0635', 'C0636', 'C0637', 'C0638', 'C0639', 'C0700',
    'C0701', 'C0800', 'C0801', 'C0802', 'C0803', 'C0804', 'C0805', 'C0810', 'C0811', 'C0812',
    'C0813', 'C0814', 'C0820', 'C0821', 'C0822', 'C0823', 'C0824', 'C0825', 'C0826', 'C0827',
    'C0828', 'C0900', 'C0901', 'C0902', 'C0903', 'C0904', 'C0905', 'C0906', 'C0907', 'C0908',
    'C0909', 'C0910', 'C0911', 'C0912', 'C0913', 'C0914', 'C0915', 'C0916', 'C0917', 'C0918',
    'C0919', 'C0920', 'C0921', 'C1000', 'C1100', 'C1200', 'C1201', 'C1300', 'C1400', 'C1500',

    // U系统 - Network (实际生成的故障码)
    'U0100', 'U0101', 'U0102', 'U0103', 'U0104', 'U0105', 'U0106', 'U0107', 'U0108', 'U0109',
    'U0110', 'U0111', 'U0112', 'U0113', 'U0114', 'U0115', 'U0116', 'U0117', 'U0118', 'U0119',
    'U0120', 'U0121', 'U0122', 'U0123', 'U0124', 'U0125', 'U0126', 'U0127', 'U0128', 'U0129',
    'U0130', 'U0131', 'U0132', 'U0133', 'U0134', 'U0135', 'U0136', 'U0137', 'U0138', 'U0139',
    'U0140', 'U0155', 'U0200', 'U0201', 'U0202', 'U0203', 'U0204', 'U0205', 'U0206', 'U0207',
    'U0208', 'U0209', 'U0210', 'U0211', 'U0212', 'U0213', 'U0214', 'U0215', 'U0216', 'U0217',
    'U0218', 'U0219', 'U0220', 'U0221', 'U0222', 'U0223', 'U0224', 'U0225', 'U0226', 'U0227',
    'U0228', 'U0229', 'U0230', 'U0231', 'U0232', 'U0300', 'U0301', 'U0302', 'U0303', 'U0304',
    'U0305', 'U0306', 'U0307', 'U0308', 'U0309', 'U0310', 'U0311', 'U0312', 'U0313', 'U0314',
    'U0315', 'U0316', 'U0317', 'U0318', 'U0319', 'U0320', 'U0321', 'U0322', 'U0400', 'U0401',
    'U0402', 'U0403', 'U0404', 'U0405', 'U0406', 'U0407', 'U0408', 'U0409', 'U0410', 'U0411',
    'U0412', 'U0413', 'U0414', 'U0415', 'U0416', 'U0417', 'U0418', 'U0419', 'U0500', 'U0501',
    'U0502', 'U0503', 'U0504', 'U0505', 'U0506', 'U0507', 'U0508', 'U0509', 'U0510', 'U0511',
    'U0512', 'U0513', 'U0514', 'U0515', 'U0516', 'U0517', 'U0518', 'U0519', 'U0600', 'U0601',
    'U0700', 'U0800'
];

// 读取当前sitemap.xml
function readCurrentSitemap() {
    try {
        const sitemapPath = path.join(__dirname, '..', 'sitemap.xml');
        return fs.readFileSync(sitemapPath, 'utf8');
    } catch (error) {
        console.error('Error reading sitemap.xml:', error.message);
        return null;
    }
}

// 生成DTC代码的sitemap条目
function generateDTCEntry(code) {
    const today = new Date().toISOString().split('T')[0];
    return `  <url>
    <loc>https://www.geekobd.com/dtc-codes/${code.toLowerCase()}.html</loc>
    <lastmod>${today}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
  </url>`;
}

// 更新sitemap.xml
function updateSitemap() {
    console.log('🚀 开始更新sitemap.xml...\n');
    
    const currentSitemap = readCurrentSitemap();
    if (!currentSitemap) {
        console.error('❌ 无法读取当前sitemap.xml文件');
        return;
    }

    // 查找现有的DTC代码条目
    const existingCodes = [];
    const dtcPattern = /dtc-codes\/([a-z0-9]+)\.html/g;
    let match;
    while ((match = dtcPattern.exec(currentSitemap)) !== null) {
        existingCodes.push(match[1].toUpperCase());
    }

    console.log(`📊 当前sitemap中已有 ${existingCodes.length} 个故障码页面`);

    // 找出需要添加的新代码
    const newCodes = allDTCCodes.filter(code => 
        !existingCodes.includes(code.toUpperCase())
    );

    console.log(`📝 需要添加 ${newCodes.length} 个新的故障码页面`);

    if (newCodes.length === 0) {
        console.log('✅ sitemap.xml已经是最新的，无需更新');
        return;
    }

    // 生成新的DTC条目
    const newEntries = newCodes.map(generateDTCEntry).join('\n\n');

    // 在现有DTC条目后插入新条目
    const insertPosition = currentSitemap.lastIndexOf('  <!-- DTC Category Pages -->');
    if (insertPosition === -1) {
        console.error('❌ 无法找到插入位置');
        return;
    }

    const updatedSitemap = currentSitemap.slice(0, insertPosition) + 
                          newEntries + '\n\n' + 
                          currentSitemap.slice(insertPosition);

    // 写入更新后的sitemap
    try {
        const sitemapPath = path.join(__dirname, '..', '..', '..', 'sitemap.xml');
        fs.writeFileSync(sitemapPath, updatedSitemap, 'utf8');
        console.log(`✅ sitemap.xml更新成功！`);
        console.log(`📈 新增 ${newCodes.length} 个故障码页面到sitemap`);
        console.log(`📊 sitemap现在包含 ${existingCodes.length + newCodes.length} 个故障码页面`);
        
        // 按系统分类统计
        const pCodes = newCodes.filter(code => code.startsWith('P')).length;
        const bCodes = newCodes.filter(code => code.startsWith('B')).length;
        const cCodes = newCodes.filter(code => code.startsWith('C')).length;
        const uCodes = newCodes.filter(code => code.startsWith('U')).length;
        
        console.log(`\n📋 新增故障码分类统计:`);
        console.log(`🔵 P系统 (Engine/Powertrain): ${pCodes} 个`);
        console.log(`🔴 B系统 (Body): ${bCodes} 个`);
        console.log(`🔵 C系统 (Chassis): ${cCodes} 个`);
        console.log(`🟢 U系统 (Network): ${uCodes} 个`);
        
    } catch (error) {
        console.error('❌ 写入sitemap.xml失败:', error.message);
    }
}

// 验证sitemap格式
function validateSitemap() {
    console.log('\n🔍 验证sitemap.xml格式...');
    
    const sitemapPath = path.join(__dirname, '..', '..', '..', 'sitemap.xml');
    const content = fs.readFileSync(sitemapPath, 'utf8');
    
    // 基本XML格式检查
    if (!content.includes('<?xml version="1.0" encoding="UTF-8"?>')) {
        console.error('❌ 缺少XML声明');
        return false;
    }
    
    if (!content.includes('<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"')) {
        console.error('❌ 缺少urlset声明');
        return false;
    }
    
    if (!content.includes('</urlset>')) {
        console.error('❌ 缺少urlset结束标签');
        return false;
    }
    
    // 统计URL数量
    const urlCount = (content.match(/<url>/g) || []).length;
    console.log(`✅ sitemap.xml格式正确，包含 ${urlCount} 个URL`);
    
    return true;
}

// 运行脚本
if (require.main === module) {
    updateSitemap();
    validateSitemap();
    
    console.log('\n📝 下一步建议:');
    console.log('1. 检查sitemap.xml文件内容');
    console.log('2. 提交sitemap到Google Search Console');
    console.log('3. 验证所有新页面都可以正常访问');
    console.log('4. 监控搜索引擎索引状态');
}

module.exports = { updateSitemap, validateSitemap };
