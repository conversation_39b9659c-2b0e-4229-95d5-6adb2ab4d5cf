const fs = require('fs');
const path = require('path');

// 获取当前日期
function getCurrentDate() {
  const now = new Date();
  return now.toISOString().split('T')[0]; // YYYY-MM-DD format
}

// 获取所有DTC文件
function getAllDTCFiles() {
  const files = fs.readdirSync('.');
  const dtcFiles = [];
  
  files.forEach(file => {
    if (file.endsWith('.html') && file.match(/^[pcbu]\d+\.html$/i)) {
      const code = file.replace('.html', '').toUpperCase();
      dtcFiles.push({
        code: code,
        filename: file,
        category: getCategory(code[0])
      });
    }
  });
  
  // 按代码排序
  dtcFiles.sort((a, b) => {
    if (a.code[0] !== b.code[0]) {
      return a.code[0].localeCompare(b.code[0]);
    }
    const numA = parseInt(a.code.substring(1));
    const numB = parseInt(b.code.substring(1));
    return numA - numB;
  });
  
  return dtcFiles;
}

// 获取分类名称
function getCategory(prefix) {
  switch (prefix) {
    case 'P': return 'Engine/Powertrain System';
    case 'C': return 'Chassis System';
    case 'B': return 'Body System';
    case 'U': return 'Network System';
    default: return 'Unknown System';
  }
}

// 生成sitemap XML
function generateSitemap() {
  const currentDate = getCurrentDate();
  const dtcFiles = getAllDTCFiles();
  
  console.log(`Found ${dtcFiles.length} DTC files to include in sitemap`);
  
  let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9
        http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">

  <!-- Homepage -->
  <url>
    <loc>https://www.geekobd.com/</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>

  <!-- Product Purchase Page -->
  <url>
    <loc>https://www.geekobd.com/buy.html</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.9</priority>
  </url>

  <!-- Hardware Products -->
  <url>
    <loc>https://www.geekobd.com/hardware.html</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
  </url>

  <url>
    <loc>https://www.geekobd.com/hardware2.html</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
  </url>

  <!-- About Us -->
  <url>
    <loc>https://www.geekobd.com/about.html</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.7</priority>
  </url>

  <!-- Mobile App Page -->
  <url>
    <loc>https://www.geekobd.com/app.html</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.7</priority>
  </url>

  <!-- Blog -->
  <url>
    <loc>https://www.geekobd.com/blog.html</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.6</priority>
  </url>

  <!-- Support -->
  <url>
    <loc>https://www.geekobd.com/support.html</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.6</priority>
  </url>

  <!-- Vehicle Compatibility -->
  <url>
    <loc>https://www.geekobd.com/vehicle-compatibility.html</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.6</priority>
  </url>

  <!-- OBD Diagnostic Guide -->
  <url>
    <loc>https://www.geekobd.com/obd-diagnostic-guide.html</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.6</priority>
  </url>

  <!-- Fuel Efficiency Monitoring -->
  <url>
    <loc>https://www.geekobd.com/fuel-efficiency-monitoring.html</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.6</priority>
  </url>

  <!-- DTC Codes Database -->
  <url>
    <loc>https://www.geekobd.com/dtc-codes.html</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.9</priority>
  </url>

  <!-- DTC Category Pages -->
  <url>
    <loc>https://www.geekobd.com/dtc-codes/engine/</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>

  <url>
    <loc>https://www.geekobd.com/dtc-codes/chassis/</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>

  <url>
    <loc>https://www.geekobd.com/dtc-codes/body/</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>

  <url>
    <loc>https://www.geekobd.com/dtc-codes/network/</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>

`;

  // 添加所有DTC代码页面
  dtcFiles.forEach(dtc => {
    sitemap += `  <!-- ${dtc.code} - ${dtc.category} -->
  <url>
    <loc>https://www.geekobd.com/dtc-codes/${dtc.filename}</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.7</priority>
  </url>

`;
  });

  sitemap += `</urlset>`;
  
  return sitemap;
}

// 主函数
function main() {
  console.log('🚀 Generating comprehensive sitemap.xml...\n');
  
  try {
    const sitemapContent = generateSitemap();
    
    // 写入sitemap文件到根目录
    const sitemapPath = '../sitemap.xml';
    fs.writeFileSync(sitemapPath, sitemapContent, 'utf8');
    
    console.log(`✅ Sitemap generated successfully!`);
    console.log(`📍 Location: ${path.resolve(sitemapPath)}`);
    console.log(`📊 Total URLs: ${(sitemapContent.match(/<url>/g) || []).length}`);
    console.log(`📅 Last modified: ${getCurrentDate()}`);
    
    // 统计各分类的数量
    const dtcFiles = getAllDTCFiles();
    const stats = {
      P: dtcFiles.filter(f => f.code[0] === 'P').length,
      C: dtcFiles.filter(f => f.code[0] === 'C').length,
      B: dtcFiles.filter(f => f.code[0] === 'B').length,
      U: dtcFiles.filter(f => f.code[0] === 'U').length
    };
    
    console.log('\n📈 DTC Code Statistics:');
    console.log(`🔧 P-codes (Engine): ${stats.P}`);
    console.log(`🚗 C-codes (Chassis): ${stats.C}`);
    console.log(`🏠 B-codes (Body): ${stats.B}`);
    console.log(`🌐 U-codes (Network): ${stats.U}`);
    console.log(`📊 Total DTC pages: ${dtcFiles.length}`);
    
  } catch (error) {
    console.error('❌ Error generating sitemap:', error.message);
    process.exit(1);
  }
}

main();
