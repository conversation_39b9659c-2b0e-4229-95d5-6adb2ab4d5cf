const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0132Data = require('./p0132-data');

console.log('🚀 Generating P0132 - O2 Sensor High Voltage Bank 1 Sensor 1 Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0132Data);
  const outputPath = path.join(__dirname, '../../p0132.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0132 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0132 page:', error.message);
  process.exit(1);
}
