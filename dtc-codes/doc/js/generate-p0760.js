const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0760Data = require('./p0760-data');

console.log('🚀 Generating P0760 - Shift Solenoid C Malfunction Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0760Data);
  const outputPath = path.join(__dirname, '../../p0760.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0760 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0760 page:', error.message);
  process.exit(1);
}
