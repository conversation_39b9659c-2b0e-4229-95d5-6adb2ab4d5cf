const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p1130Data = require('./p1130-data');

console.log('🚀 Generating P1130 - Lack of Upstream Heated Oxygen Sensor Switch - Sensor Indicates Lean Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p1130Data);
  const outputPath = path.join(__dirname, '../../p1130.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P1130 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P1130 page:', error.message);
  process.exit(1);
}
