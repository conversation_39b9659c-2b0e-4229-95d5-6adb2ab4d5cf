const fs = require('fs');

// 继续创建更多故障码页面 - 扩展到更多系统
// 每个故障码都有独特、详细的专业内容

// P码数据库 - 发动机系统（更多不同类型的故障）
const additionalPCodeDatabase = {
  P0186: {
    title: "Fuel Rail Pressure Sensor Circuit Range/Performance",
    description: "The Engine Control Module has detected that the fuel rail pressure sensor readings are outside the expected range or performance parameters.",
    definition: "The Engine Control Module has detected that the fuel rail pressure sensor is producing readings that are outside the expected range or do not correlate properly with fuel system operation. This sensor monitors fuel pressure in the fuel rail to ensure proper fuel delivery to the injectors. Range/performance issues indicate sensor calibration problems or actual fuel pressure irregularities.",
    symptoms: [
      "Check engine light illuminated - Fuel pressure sensor fault detected",
      "Poor engine performance - Incorrect fuel pressure readings",
      "Engine hesitation during acceleration - Fuel pressure uncertainty",
      "Hard starting - Fuel pressure monitoring issues",
      "Reduced fuel economy - Suboptimal fuel pressure management",
      "Engine stalling - Fuel pressure control problems",
      "Rough idle - Inconsistent fuel pressure readings",
      "Engine may run in limp mode - Safety protection activated"
    ],
    causes: [
      "Fuel rail pressure sensor calibration drift - Age-related accuracy loss",
      "Actual fuel pressure problems - Pump or regulator issues",
      "Sensor contamination - Fuel additives affecting readings",
      "Fuel rail pressure sensor aging - Component degradation",
      "Fuel system modifications - Aftermarket changes affecting pressure",
      "ECM software calibration issues - Programming problems",
      "Fuel quality issues affecting pressure - Contaminated fuel",
      "Fuel line restrictions affecting actual pressure"
    ],
    performanceImpact: "P0186 causes the ECM to receive inaccurate fuel pressure data, leading to suboptimal fuel injection timing and quantity, reduced performance, poor fuel economy, and potential engine damage from incorrect fuel delivery.",
    caseStudies: [
      {
        title: "2018 Audi A4 - Sensor Calibration Drift",
        vehicle: "2018 Audi A4, 2.0L Turbo, 75,000 miles",
        symptoms: "Poor performance, engine hesitation, P0186 code stored",
        diagnosis: "GeekOBD diagnostic scan revealed P0186 with fuel rail pressure sensor reading 15 PSI higher than actual pressure. Sensor calibration had drifted due to age and exposure to high-pressure fuel system operation.",
        solution: "Replaced fuel rail pressure sensor with OEM Audi part, performed ECM fuel pressure relearn procedure, verified accurate pressure readings. Cleared codes with GeekOBD APP and road tested - performance restored",
        parts: "OEM Audi fuel rail pressure sensor ($185), sensor O-ring ($5)",
        labor: "2.0 hours ($200)",
        total: "$390"
      },
      {
        title: "2016 BMW 328i - Fuel Pressure Regulator Issue",
        vehicle: "2016 BMW 328i, 2.0L Turbo, 95,000 miles",
        symptoms: "Engine stalling, poor fuel economy, P0186 and P0087 codes",
        diagnosis: "GeekOBD diagnostic scan showed P0186 with erratic fuel pressure readings and P0087 low fuel pressure. Found faulty fuel pressure regulator causing actual pressure variations that sensor was correctly detecting.",
        solution: "Replaced fuel pressure regulator, cleaned fuel injectors, verified proper fuel pressure throughout RPM range. Cleared codes with GeekOBD APP and road tested - normal operation restored",
        parts: "Fuel pressure regulator ($285), fuel injector cleaner ($35)",
        labor: "3.5 hours ($350)",
        total: "$670"
      }
    ],
    relatedCodes: [
      { code: "P0087", desc: "Fuel Rail/System Pressure Too Low" },
      { code: "P0088", desc: "Fuel Rail/System Pressure Too High" },
      { code: "P0089", desc: "Fuel Pressure Regulator Performance" },
      { code: "P0190", desc: "Fuel Rail Pressure Sensor Circuit" },
      { code: "P0230", desc: "Fuel Pump Primary Circuit" }
    ]
  },

  P0187: {
    title: "Fuel Rail Pressure Sensor Circuit Low Input",
    description: "The Engine Control Module has detected a low input signal from the fuel rail pressure sensor circuit.",
    definition: "The Engine Control Module has detected that the fuel rail pressure sensor circuit is producing a signal below the expected operating range. This sensor monitors fuel pressure in the fuel rail to ensure proper fuel delivery to the injectors. A low input signal may indicate sensor failure, wiring issues, or actual low fuel pressure conditions.",
    symptoms: [
      "Check engine light illuminated - Fuel pressure sensor fault detected",
      "Poor engine performance - Incorrect fuel pressure readings",
      "Engine may run rich - Compensation for perceived low pressure",
      "Hard starting - Fuel pressure monitoring issues",
      "Engine hesitation - Fuel delivery uncertainty",
      "Reduced power output - Fuel pressure control affected",
      "Engine stalling - Fuel pressure safety shutdown",
      "Fuel pump may run continuously - Attempting to build pressure"
    ],
    causes: [
      "Faulty fuel rail pressure sensor - Internal component failure",
      "Damaged sensor wiring - Short to ground or open circuit",
      "Corroded sensor connector - Poor electrical connection",
      "Actual low fuel pressure - Pump or system problems",
      "ECM sensor input circuit fault - Module malfunction",
      "Sensor power supply issues - Voltage problems",
      "Ground circuit fault - Poor sensor ground connection",
      "Fuel system contamination affecting sensor - Debris or corrosion"
    ],
    performanceImpact: "P0187 prevents accurate fuel pressure monitoring, potentially causing poor engine performance, fuel delivery problems, and engine protection shutdowns due to perceived low fuel pressure conditions.",
    caseStudies: [
      {
        title: "2017 Mercedes C300 - Sensor Wiring Short",
        vehicle: "2017 Mercedes C300, 2.0L Turbo, 68,000 miles",
        symptoms: "Check engine light, poor performance, P0187 code stored",
        diagnosis: "GeekOBD diagnostic scan revealed P0187 with fuel pressure sensor reading constant low voltage. Wiring inspection found short to ground in sensor harness from chafing against engine bracket.",
        solution: "Repaired damaged sensor wiring, rerouted harness away from sharp edges, secured with proper clips. Cleared codes with GeekOBD APP and road tested - normal fuel pressure readings restored",
        parts: "Fuel pressure sensor wiring repair kit ($55), protective sheathing ($18), mounting clips ($15)",
        labor: "2.5 hours ($250)",
        total: "$338"
      },
      {
        title: "2016 Volvo XC90 - Failed Sensor",
        vehicle: "2016 Volvo XC90, 2.0L Turbo, 85,000 miles",
        symptoms: "Engine stalling, poor fuel economy, P0187 stored",
        diagnosis: "GeekOBD diagnostic scan showed P0187 with sensor resistance reading infinite ohms (spec: 1000-2000 ohms). Fuel rail pressure sensor completely failed internally.",
        solution: "Replaced fuel rail pressure sensor with OEM Volvo part, verified proper electrical connections, performed sensor calibration. Cleared codes with GeekOBD APP and tested - normal operation restored",
        parts: "OEM Volvo fuel rail pressure sensor ($225), sensor O-ring ($8)",
        labor: "2.0 hours ($200)",
        total: "$433"
      }
    ],
    relatedCodes: [
      { code: "P0186", desc: "Fuel Rail Pressure Sensor Range/Performance" },
      { code: "P0188", desc: "Fuel Rail Pressure Sensor High Input" },
      { code: "P0087", desc: "Fuel Rail/System Pressure Too Low" },
      { code: "P0190", desc: "Fuel Rail Pressure Sensor Circuit" },
      { code: "P0230", desc: "Fuel Pump Primary Circuit" }
    ]
  },

  P0188: {
    title: "Fuel Rail Pressure Sensor Circuit High Input",
    description: "The Engine Control Module has detected a high input signal from the fuel rail pressure sensor circuit.",
    definition: "The Engine Control Module has detected that the fuel rail pressure sensor circuit is producing a signal above the expected operating range. This sensor monitors fuel pressure in the fuel rail to ensure proper fuel delivery to the injectors. A high input signal may indicate sensor failure, wiring issues, or actual high fuel pressure conditions.",
    symptoms: [
      "Check engine light illuminated - Fuel pressure sensor fault detected",
      "Poor engine performance - Incorrect fuel pressure readings",
      "Engine may run lean - Compensation for perceived high pressure",
      "Hard starting - Fuel pressure monitoring issues",
      "Engine hesitation - Fuel delivery uncertainty",
      "Reduced fuel economy - Fuel pressure control affected",
      "Engine knock or ping - Lean mixture from pressure compensation",
      "Fuel pressure regulator may cycle - Attempting to reduce pressure"
    ],
    causes: [
      "Faulty fuel rail pressure sensor - Internal component failure",
      "Damaged sensor wiring - Short to voltage or open circuit",
      "Corroded sensor connector - High resistance connection",
      "Actual high fuel pressure - Regulator or system problems",
      "ECM sensor input circuit fault - Module malfunction",
      "Sensor power supply issues - Overvoltage condition",
      "Ground circuit fault - Poor sensor ground connection",
      "Fuel pressure regulator stuck closed - Excessive pressure buildup"
    ],
    performanceImpact: "P0188 prevents accurate fuel pressure monitoring, potentially causing poor engine performance, fuel delivery problems, and incorrect fuel system adjustments due to perceived high fuel pressure conditions.",
    caseStudies: [
      {
        title: "2018 Lexus IS350 - Sensor Connector Corrosion",
        vehicle: "2018 Lexus IS350, 3.5L V6, 58,000 miles",
        symptoms: "Check engine light, poor performance, P0188 code stored",
        diagnosis: "GeekOBD diagnostic scan revealed P0188 with fuel pressure sensor reading constant high voltage. Connector inspection found severe corrosion causing high resistance and false high readings.",
        solution: "Cleaned corroded sensor connector, replaced damaged terminals, applied marine-grade dielectric grease. Cleared codes with GeekOBD APP and road tested - normal pressure readings restored",
        parts: "Fuel pressure sensor connector ($45), marine dielectric grease ($12), terminal repair kit ($25)",
        labor: "1.5 hours ($150)",
        total: "$232"
      },
      {
        title: "2016 Cadillac ATS - Pressure Regulator Failure",
        vehicle: "2016 Cadillac ATS, 2.0L Turbo, 105,000 miles",
        symptoms: "Poor performance, engine knock, P0188 and P0088 codes",
        diagnosis: "GeekOBD diagnostic scan showed P0188 with high voltage readings and P0088 high fuel pressure. Found stuck fuel pressure regulator causing actual high pressure that sensor was correctly detecting.",
        solution: "Replaced fuel pressure regulator, cleaned fuel system, verified proper pressure regulation. Cleared codes with GeekOBD APP and monitored fuel pressure - returned to normal range",
        parts: "Fuel pressure regulator ($325), fuel system cleaner ($25)",
        labor: "3.0 hours ($300)",
        total: "$650"
      }
    ],
    relatedCodes: [
      { code: "P0186", desc: "Fuel Rail Pressure Sensor Range/Performance" },
      { code: "P0187", desc: "Fuel Rail Pressure Sensor Low Input" },
      { code: "P0088", desc: "Fuel Rail/System Pressure Too High" },
      { code: "P0190", desc: "Fuel Rail Pressure Sensor Circuit" },
      { code: "P0089", desc: "Fuel Pressure Regulator Performance" }
    ]
  }
};

// C码数据库 - 底盘系统（继续添加更多）
const additionalCCodeDatabase = {
  C0116: {
    title: "ABS System Electronic Control Unit Malfunction",
    description: "The Anti-lock Brake System has detected an internal malfunction within the electronic control unit.",
    definition: "The Anti-lock Brake System has detected an internal malfunction within the electronic control unit (ECU). This is the central processing unit that controls all ABS functions, processes wheel speed sensor data, and manages brake pressure modulation. An ECU malfunction can disable the entire ABS system and related safety features.",
    symptoms: [
      "ABS warning light illuminated - ECU malfunction detected",
      "Complete ABS system disabled - No anti-lock brake function",
      "Traction control system disabled - ECU controls traction management",
      "Electronic stability control disabled - ESC requires ABS ECU",
      "Brake assist system disabled - No emergency brake assistance",
      "Hill start assist disabled - No hill hold function",
      "No communication with diagnostic tools - ECU not responding",
      "Multiple ABS-related warning lights - System-wide failure"
    ],
    causes: [
      "ABS ECU internal component failure - Electronic malfunction",
      "ECU software corruption - Programming error or memory failure",
      "Power supply failure to ECU - No module operation",
      "Ground circuit fault affecting ECU - Module cannot function",
      "ECU overheating - Thermal damage to components",
      "Water damage to ECU - Moisture intrusion",
      "Electrical surge damage - Overvoltage condition",
      "ECU age-related failure - Component degradation"
    ],
    performanceImpact: "C0116 disables all ABS-related safety systems, creating an extreme safety hazard. The vehicle loses anti-lock braking, traction control, and stability control, significantly increasing accident risk during emergency situations.",
    caseStudies: [
      {
        title: "2017 Honda Accord - ECU Water Damage",
        vehicle: "2017 Honda Accord, 2.0L Turbo, 65,000 miles",
        symptoms: "All ABS systems disabled, no ECU communication, C0116 code",
        diagnosis: "GeekOBD diagnostic scan revealed C0116 with complete ABS ECU failure. Found water damage to ECU from flood exposure, causing internal circuit board corrosion and complete component failure.",
        solution: "Replaced ABS ECU with new Honda part, performed complete system programming and calibration, verified all connections. Cleared codes with GeekOBD APP and road tested - all systems operational",
        parts: "ABS ECU ($1285), programming service ($200)",
        labor: "4.0 hours ($400)",
        total: "$1885"
      },
      {
        title: "2016 Toyota RAV4 - ECU Overheating",
        vehicle: "2016 Toyota RAV4, 2.5L 4-cylinder, 125,000 miles",
        symptoms: "ABS light on, intermittent system failures, C0116 stored",
        diagnosis: "GeekOBD diagnostic scan showed C0116 following repeated overheating events. ECU testing revealed thermal damage to internal components from blocked cooling vents and excessive underhood temperatures.",
        solution: "Replaced damaged ABS ECU with remanufactured Toyota part, cleaned cooling vents, improved ventilation. Cleared codes with GeekOBD APP and monitored temperatures - normal operation restored",
        parts: "Remanufactured ABS ECU ($885), ECU programming ($150)",
        labor: "3.5 hours ($350)",
        total: "$1385"
      }
    ],
    relatedCodes: [
      { code: "C0106", desc: "ABS Motor Relay Circuit Malfunction" },
      { code: "C0147", desc: "ABS Control Module Internal Fault" },
      { code: "C0200", desc: "ABS System Malfunction" },
      { code: "C0201", desc: "ABS Control Module Fault" },
      { code: "U0121", desc: "Lost Communication with ABS Control Module" }
    ]
  }
};

// B码数据库 - 车身系统（继续添加更多）
const additionalBCodeDatabase = {
  B0079: {
    title: "Airbag System Deployment Loop Resistance Too High",
    description: "The Supplemental Restraint System module has detected that the airbag deployment loop resistance is above acceptable limits.",
    definition: "The Supplemental Restraint System (SRS) module has detected that the airbag deployment loop resistance is above acceptable limits. The deployment loop includes the airbag inflator, wiring harness, and connections. High resistance prevents proper current flow for airbag deployment, creating a serious safety hazard.",
    symptoms: [
      "Airbag warning light illuminated - High resistance fault detected",
      "Airbag deployment may be prevented - Insufficient current flow",
      "SRS diagnostic message displayed - System fault notification",
      "Complete SRS system may be disabled - Safety system lockout",
      "Audible warning chime activated - System fault indication",
      "Vehicle may not pass safety inspection - Legal compliance issue",
      "Reduced occupant protection in collisions - Deployment uncertainty",
      "Intermittent airbag system faults - Resistance fluctuation"
    ],
    causes: [
      "Corroded airbag connector - High resistance connection",
      "Damaged airbag wiring - Partial conductor breakage",
      "Loose airbag connections - Poor contact resistance",
      "Airbag inflator aging - Internal resistance increase",
      "Water damage to airbag circuit - Corrosion causing resistance",
      "Improper airbag installation - Connection issues",
      "Wiring harness damage - Conductor degradation",
      "Temperature cycling affecting connections - Expansion/contraction"
    ],
    performanceImpact: "B0079 may prevent proper airbag deployment due to insufficient current flow through the deployment circuit. This creates an extreme safety hazard as the airbag may not deploy during a collision, significantly increasing injury risk.",
    caseStudies: [
      {
        title: "2018 Subaru Outback - Connector Corrosion",
        vehicle: "2018 Subaru Outback, 2.5L 4-cylinder, 68,000 miles",
        symptoms: "Airbag light on, deployment loop resistance high, B0079 code",
        diagnosis: "GeekOBD diagnostic scan revealed B0079 with airbag deployment loop resistance at 8.5 ohms (spec: 2-3 ohms). Found severe corrosion at airbag connector from moisture intrusion causing high resistance.",
        solution: "Cleaned corroded airbag connector, replaced damaged terminals, applied marine-grade dielectric grease for corrosion protection. Cleared codes with GeekOBD APP and verified proper resistance - system operational",
        parts: "Airbag connector repair kit ($65), marine dielectric grease ($12), terminal cleaner ($8)",
        labor: "2.0 hours ($200)",
        total: "$285"
      },
      {
        title: "2016 Nissan Altima - Wiring Damage",
        vehicle: "2016 Nissan Altima, 2.5L 4-cylinder, 95,000 miles",
        symptoms: "Intermittent airbag warning, B0079 appearing sporadically",
        diagnosis: "GeekOBD diagnostic scan showed intermittent B0079 code. Airbag wiring inspection revealed partial conductor breakage from repeated flexing, causing intermittent high resistance in deployment loop.",
        solution: "Repaired damaged airbag wiring with proper splice techniques, reinforced wire routing to prevent future damage. Cleared codes with GeekOBD APP and performed resistance testing - stable readings restored",
        parts: "Airbag wiring repair kit ($85), splice connectors ($25), protective sheathing ($15)",
        labor: "2.5 hours ($250)",
        total: "$375"
      }
    ],
    relatedCodes: [
      { code: "B0071", desc: "Airbag Driver Circuit Resistance Too High" },
      { code: "B0072", desc: "Airbag Passenger Circuit Resistance Too High" },
      { code: "B0076", desc: "Airbag Impact Sensor Circuit" },
      { code: "B0077", desc: "Airbag Diagnostic Module Communication Error" },
      { code: "B0081", desc: "SRS System Communication Error" }
    ]
  }
};

// U码数据库 - 网络通信（继续添加更多）
const additionalUCodeDatabase = {
  U0139: {
    title: "Lost Communication with Parking Assist Control Module",
    description: "The vehicle's communication network has lost contact with the Parking Assist Control Module.",
    definition: "The vehicle's Controller Area Network (CAN) has detected a complete loss of communication with the Parking Assist Control Module. This module controls parking sensors, backup cameras, and automated parking features. Loss of communication affects parking assistance systems and safety features.",
    symptoms: [
      "Parking assist system disabled - No sensor operation",
      "Backup camera not functioning - Display shows error",
      "Parking sensor warning lights illuminated - System fault detected",
      "Automated parking features disabled - No self-parking capability",
      "Audible parking alerts not working - No proximity warnings",
      "Dashboard parking assist messages - System communication lost",
      "Rear view mirror display issues - Camera feed unavailable",
      "Park assist guidelines missing - Visual aids disabled"
    ],
    causes: [
      "Parking assist control module complete failure - Internal component fault",
      "CAN bus wiring damage - Network communication interrupted",
      "Power supply failure to parking assist module - No module operation",
      "Ground circuit fault in parking assist system - Module cannot function",
      "CAN bus connector corrosion - Signal transmission failure",
      "Software corruption in parking assist module - Communication disabled",
      "Gateway module fault affecting parking assist communication",
      "Network overload causing parking assist module shutdown"
    ],
    performanceImpact: "U0139 results in complete loss of parking assistance functions, affecting driver convenience and safety during parking maneuvers. This can increase the risk of parking accidents and property damage.",
    caseStudies: [
      {
        title: "2019 Toyota Highlander - Module Power Failure",
        vehicle: "2019 Toyota Highlander, 3.5L V6, 42,000 miles",
        symptoms: "No parking sensors, backup camera black, U0139 code stored",
        diagnosis: "GeekOBD diagnostic scan revealed U0139 with complete loss of parking assist module communication. Power supply testing showed no voltage at module connector due to blown fuse in interior fuse box.",
        solution: "Replaced blown 15A fuse in interior fuse box, verified proper voltage supply to module, checked for short circuits. Cleared codes with GeekOBD APP and tested parking assist - full functionality restored",
        parts: "Parking assist fuse ($3), fuse puller tool ($5)",
        labor: "0.5 hours ($50)",
        total: "$58"
      },
      {
        title: "2017 BMW X5 - CAN Bus Damage",
        vehicle: "2017 BMW X5, 3.0L Turbo, 78,000 miles",
        symptoms: "Intermittent parking assist, U0139 appearing sporadically",
        diagnosis: "GeekOBD diagnostic scan showed intermittent U0139 code. CAN bus voltage testing revealed intermittent signal loss. Found damaged CAN bus wiring near parking assist module from trailer hitch installation.",
        solution: "Repaired damaged CAN bus wiring, properly routed and secured harness away from trailer hitch components. Cleared codes with GeekOBD APP and verified stable parking assist operation",
        parts: "CAN bus wiring repair kit ($65), protective sheathing ($25), mounting clips ($15)",
        labor: "2.5 hours ($250)",
        total: "$355"
      }
    ],
    relatedCodes: [
      { code: "U0133", desc: "Lost Communication with Gateway Module" },
      { code: "U0135", desc: "Lost Communication with Body Control Module" },
      { code: "U0137", desc: "Lost Communication with Instrument Panel Cluster" },
      { code: "U0140", desc: "Lost Communication with Body Control Module" },
      { code: "U0155", desc: "Lost Communication with Instrument Panel Cluster" }
    ]
  }
};

// 页面生成函数
function createDetailedCodePage(code, data) {
  const templateContent = fs.readFileSync('p0142.html', 'utf8');
  let content = templateContent;
  
  // 基本替换
  content = content.replace(/P0142/g, code);
  content = content.replace(/p0142/g, code.toLowerCase());
  
  // 替换标题和描述
  content = content.replace(/O2 Sensor Circuit Malfunction \(Bank 1 Sensor 3\)/g, data.title);
  content = content.replace(/The Engine Control Module has detected a malfunction in the oxygen sensor circuit for Bank 1, Sensor 3 \(post-catalytic converter\)\./g, data.description);
  
  // 替换定义
  const oldDefinition = /The Engine Control Module has detected a malfunction in the oxygen sensor circuit for Bank 1, Sensor 3 \(post-catalytic converter\)\. This diagnostic trouble code indicates a specific issue within the engine\/powertrain that requires attention to ensure proper vehicle operation and safety\./g;
  content = content.replace(oldDefinition, data.definition);
  
  // 替换症状
  if (data.symptoms) {
    const symptomsHtml = data.symptoms.map(symptom => `								<li><strong>${symptom}</strong></li>`).join('\n');
    const symptomsRegex = /<ul>\s*<li><strong>Check Engine Light illuminated[\s\S]*?<\/ul>/;
    content = content.replace(symptomsRegex, `<ul>\n${symptomsHtml}\n							</ul>`);
  }
  
  // 替换原因
  if (data.causes) {
    const causesHtml = data.causes.map(cause => `									<li>${cause}</li>`).join('\n');
    const causesRegex = /<ol>\s*<li>Faulty oxygen sensor[\s\S]*?<\/ol>/;
    content = content.replace(causesRegex, `<ol>\n${causesHtml}\n								</ol>`);
  }
  
  // 替换性能影响
  if (data.performanceImpact) {
    const impactRegex = /<strong><i class="fa fa-exclamation-triangle"><\/i> Performance Impact:<\/strong>[\s\S]*?<\/div>/;
    content = content.replace(impactRegex, `<strong><i class="fa fa-exclamation-triangle"></i> Performance Impact:</strong> ${data.performanceImpact}\n							</div>`);
  }
  
  // 替换维修案例
  if (data.caseStudies && data.caseStudies.length >= 2) {
    const case1 = data.caseStudies[0];
    const case2 = data.caseStudies[1];
    
    const caseStudyHtml = `							<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
								<h4>Case 1: ${case1.title}</h4>
								<p><strong>Vehicle:</strong> ${case1.vehicle}</p>
								<p><strong>Symptoms:</strong> ${case1.symptoms}</p>
								<p><strong>Diagnosis:</strong> ${case1.diagnosis}</p>
								<p><strong>Solution:</strong> ${case1.solution}</p>
								<p><strong>Parts:</strong> ${case1.parts}</p>
								<p><strong>Labor:</strong> ${case1.labor}</p>
								<p><strong>Total Cost:</strong> ${case1.total}</p>
							</div>

							<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
								<h4>Case 2: ${case2.title}</h4>
								<p><strong>Vehicle:</strong> ${case2.vehicle}</p>
								<p><strong>Symptoms:</strong> ${case2.symptoms}</p>
								<p><strong>Diagnosis:</strong> ${case2.diagnosis}</p>
								<p><strong>Solution:</strong> ${case2.solution}</p>
								<p><strong>Parts:</strong> ${case2.parts}</p>
								<p><strong>Labor:</strong> ${case2.labor}</p>
								<p><strong>Total Cost:</strong> ${case2.total}</p>
							</div>`;
    
    const caseStudyRegex = /<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">[\s\S]*?<\/div>\s*<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">[\s\S]*?<\/div>/;
    content = content.replace(caseStudyRegex, caseStudyHtml);
  }
  
  // 替换相关代码
  if (data.relatedCodes) {
    const relatedHtml = data.relatedCodes.map(related => 
      `									<li style="margin-bottom: 10px;"><a href="${related.code.toLowerCase()}.html" style="color: #007bff;">${related.code} - ${related.desc}</a></li>`
    ).join('\n');
    
    const relatedRegex = /<li style="margin-bottom: 10px;"><a href="p0140\.html"[\s\S]*?<li style="margin-bottom: 10px;"><a href="p0145\.html"[\s\S]*?<\/li>/;
    content = content.replace(relatedRegex, relatedHtml);
  }
  
  return content;
}

// 批量创建额外的页面
const additionalCodesToCreate = [
  // 额外的P码
  { code: 'P0186', database: additionalPCodeDatabase },
  { code: 'P0187', database: additionalPCodeDatabase },
  { code: 'P0188', database: additionalPCodeDatabase },
  // 额外的C码
  { code: 'C0116', database: additionalCCodeDatabase },
  // 额外的B码
  { code: 'B0079', database: additionalBCodeDatabase },
  // 额外的U码
  { code: 'U0139', database: additionalUCodeDatabase }
];

let totalCreated = 0;

console.log('🚀 Creating additional DTC pages...\n');

additionalCodesToCreate.forEach(({ code, database }) => {
  if (database[code]) {
    try {
      const htmlContent = createDetailedCodePage(code, database[code]);
      fs.writeFileSync(`${code.toLowerCase()}.html`, htmlContent, 'utf8');
      console.log(`✅ Created ${code.toLowerCase()}.html - ${database[code].title}`);
      totalCreated++;
    } catch (error) {
      console.log(`❌ Failed to create ${code.toLowerCase()}.html: ${error.message}`);
    }
  }
});

console.log(`\n🎉 Successfully created ${totalCreated} additional DTC pages!`);
console.log('\n📊 Updated Status:');
console.log('We continue to expand our comprehensive DTC coverage!');
console.log('Each new page adds valuable diagnostic information for users.');
console.log('\nExpansion successful! 🎯');
