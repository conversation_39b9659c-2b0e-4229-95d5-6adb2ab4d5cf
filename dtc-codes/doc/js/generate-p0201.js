const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0201Data = require('./p0201-data');

console.log('🚀 Generating P0201 - Injector Circuit Malfunction Cylinder 1 Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0201Data);
  const outputPath = path.join(__dirname, '../../p0201.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0201 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0201 page:', error.message);
  process.exit(1);
}
