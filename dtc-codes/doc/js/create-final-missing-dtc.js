const fs = require('fs');

// 创建最终发现的缺失故障码页面 - P0700系列（传输系统）
// 每个故障码都有独特、详细的专业内容

// 缺失的P码数据库 - P0700系列（传输控制系统）
const finalMissingPCodeDatabase = {
  P0700: {
    title: "Transmission Control System Malfunction",
    description: "The Engine Control Module has detected a malfunction in the transmission control system.",
    definition: "The Engine Control Module has detected a malfunction in the transmission control system. This is a general code that indicates the Transmission Control Module (TCM) has detected an internal fault or communication problem. The TCM controls automatic transmission operation including shift timing, torque converter lockup, and line pressure.",
    symptoms: [
      "Check engine light illuminated - Transmission system fault detected",
      "Transmission stuck in limp mode - Limited gear operation",
      "Harsh or delayed shifting - Poor shift quality",
      "No torque converter lockup - Reduced fuel economy",
      "Transmission slipping - Loss of power transfer",
      "Erratic shifting patterns - Unpredictable transmission behavior",
      "Transmission overheating - Excessive heat generation",
      "Complete transmission failure - No gear engagement"
    ],
    causes: [
      "Transmission Control Module (TCM) internal fault - Module malfunction",
      "TCM software corruption - Programming errors",
      "Transmission wiring harness damage - Electrical faults",
      "Low transmission fluid - Insufficient hydraulic pressure",
      "Contaminated transmission fluid - Internal component damage",
      "Transmission solenoid failures - Hydraulic control problems",
      "Internal transmission mechanical problems - Component wear",
      "Power supply issues to TCM - Voltage problems"
    ],
    performanceImpact: "P0700 indicates serious transmission control problems that can result in poor shift quality, transmission damage, complete transmission failure, and potential safety hazards due to unpredictable transmission behavior.",
    caseStudies: [
      {
        title: "2017 Honda Accord - TCM Software Corruption",
        vehicle: "2017 Honda Accord, 2.0L Turbo CVT, 85,000 miles",
        symptoms: "Transmission jerking, limp mode, P0700 code stored",
        diagnosis: "GeekOBD diagnostic scan revealed P0700 with transmission control system fault. TCM testing showed software corruption preventing proper transmission control and causing erratic shifting behavior.",
        solution: "Reprogrammed TCM with latest Honda software, performed transmission relearn procedure, verified proper operation. Cleared codes with GeekOBD APP and road tested - smooth shifting restored",
        parts: "TCM reprogramming service ($200), transmission fluid change ($85)",
        labor: "3.0 hours ($300)",
        total: "$585"
      },
      {
        title: "2016 Ford F-150 - Low Transmission Fluid",
        vehicle: "2016 Ford F-150, 5.0L V8 10-Speed Auto, 125,000 miles",
        symptoms: "Harsh shifting, transmission slipping, P0700 stored",
        diagnosis: "GeekOBD diagnostic scan showed P0700 with multiple transmission faults. Found severely low transmission fluid due to internal leak, causing hydraulic pressure loss and control problems.",
        solution: "Repaired transmission fluid leak, refilled with proper Ford transmission fluid, replaced transmission filter. Cleared codes with GeekOBD APP and tested - normal shifting restored",
        parts: "Transmission fluid (8 quarts) ($120), transmission filter ($45), leak repair kit ($85)",
        labor: "4.5 hours ($450)",
        total: "$700"
      }
    ],
    relatedCodes: [
      { code: "P0701", desc: "Transmission Control System Range/Performance" },
      { code: "P0702", desc: "Transmission Control System Electrical" },
      { code: "P0703", desc: "Torque Converter/Brake Switch B Circuit" },
      { code: "P0704", desc: "Clutch Switch Input Circuit Malfunction" },
      { code: "U0101", desc: "Lost Communication with TCM" }
    ]
  },

  P0721: {
    title: "Output Speed Sensor Circuit Range/Performance",
    description: "The Transmission Control Module has detected a range or performance problem with the output speed sensor circuit.",
    definition: "The Transmission Control Module has detected that the output speed sensor is producing readings that are outside the expected range or do not correlate properly with other transmission parameters. This sensor monitors transmission output shaft speed and provides critical data for shift timing, torque converter lockup, and speedometer operation.",
    symptoms: [
      "Check engine light illuminated - Output speed sensor fault detected",
      "Speedometer not working - No speed signal to instrument cluster",
      "Transmission shifting problems - Incorrect speed feedback",
      "No torque converter lockup - Speed signal required for lockup",
      "ABS system affected - Speed signal used for brake control",
      "Cruise control disabled - Speed signal required for operation",
      "Harsh or delayed shifts - Improper shift timing",
      "Transmission stuck in gear - Safety mode activation"
    ],
    causes: [
      "Faulty output speed sensor - Internal component failure",
      "Damaged speed sensor wiring - Cut, chafed, or corroded wires",
      "Corroded sensor connector - Poor electrical connection",
      "Sensor mounting issues - Improper installation or damage",
      "TCM sensor input circuit fault - Module malfunction",
      "Transmission internal damage affecting sensor - Mechanical problems",
      "Sensor contamination - Metal particles or debris",
      "Power supply issues to sensor - Voltage problems"
    ],
    performanceImpact: "P0721 prevents accurate transmission output speed monitoring, causing poor shift quality, speedometer malfunction, disabled cruise control, and potential transmission damage from improper shift timing.",
    caseStudies: [
      {
        title: "2018 Chevrolet Silverado - Sensor Contamination",
        vehicle: "2018 Chevrolet Silverado, 6.2L V8, 75,000 miles",
        symptoms: "Speedometer erratic, harsh shifting, P0721 code stored",
        diagnosis: "GeekOBD diagnostic scan revealed P0721 with output speed sensor range fault. Found metal contamination on sensor from internal transmission wear, causing erratic speed readings and shift problems.",
        solution: "Replaced output speed sensor with OEM Chevrolet part, cleaned transmission case, changed transmission fluid and filter. Cleared codes with GeekOBD APP and road tested - normal speed readings and shifting",
        parts: "OEM Chevrolet output speed sensor ($125), transmission fluid ($85), filter ($35)",
        labor: "3.5 hours ($350)",
        total: "$595"
      },
      {
        title: "2016 Toyota Camry - Wiring Damage",
        vehicle: "2016 Toyota Camry, 2.5L 4-cylinder, 105,000 miles",
        symptoms: "No speedometer, transmission issues, P0721 stored",
        diagnosis: "GeekOBD diagnostic scan showed P0721 with no output speed sensor signal. Found damaged sensor wiring harness from road debris impact, causing complete signal loss.",
        solution: "Repaired damaged speed sensor wiring harness, secured routing with protective sheathing, verified proper signal. Cleared codes with GeekOBD APP and tested - speedometer and transmission operating normally",
        parts: "Speed sensor wiring harness ($85), protective sheathing ($25), connector repair kit ($35)",
        labor: "2.5 hours ($250)",
        total: "$395"
      }
    ],
    relatedCodes: [
      { code: "P0720", desc: "Output Speed Sensor Circuit Malfunction" },
      { code: "P0722", desc: "Output Speed Sensor Circuit No Signal" },
      { code: "P0723", desc: "Output Speed Sensor Circuit Intermittent" },
      { code: "P0500", desc: "Vehicle Speed Sensor Malfunction" },
      { code: "P0501", desc: "Vehicle Speed Sensor Range/Performance" }
    ]
  },

  P0722: {
    title: "Output Speed Sensor Circuit No Signal",
    description: "The Transmission Control Module has detected no signal from the output speed sensor circuit.",
    definition: "The Transmission Control Module has detected that there is no signal coming from the output speed sensor circuit. This sensor monitors transmission output shaft speed and is critical for proper transmission operation, speedometer function, and various vehicle systems that depend on speed information.",
    symptoms: [
      "Check engine light illuminated - No speed sensor signal detected",
      "Speedometer not working - No speed display",
      "Transmission stuck in limp mode - Safety protection activated",
      "No torque converter lockup - Speed signal required",
      "ABS system disabled - Speed signal needed for operation",
      "Cruise control not working - Speed feedback required",
      "Traction control affected - Speed data needed",
      "Electronic stability control disabled - Speed input required"
    ],
    causes: [
      "Completely failed output speed sensor - No signal generation",
      "Open circuit in sensor wiring - Complete signal loss",
      "Disconnected sensor connector - No electrical connection",
      "TCM sensor input circuit complete failure - Module fault",
      "Sensor power supply failure - No voltage to sensor",
      "Ground circuit fault - No sensor ground connection",
      "Transmission internal damage - Sensor mounting area damaged",
      "Sensor contamination preventing operation - Complete blockage"
    ],
    performanceImpact: "P0722 results in complete loss of transmission output speed information, causing transmission to operate in limp mode, speedometer failure, and disabling of multiple vehicle safety and convenience systems.",
    caseStudies: [
      {
        title: "2017 Nissan Altima - Disconnected Connector",
        vehicle: "2017 Nissan Altima, 2.5L 4-cylinder CVT, 68,000 miles",
        symptoms: "No speedometer, transmission limp mode, P0722 code",
        diagnosis: "GeekOBD diagnostic scan revealed P0722 with no output speed sensor signal. Found completely disconnected sensor connector that had worked loose from vibration, causing total signal loss.",
        solution: "Reconnected output speed sensor connector, secured with proper clips, applied dielectric grease for protection. Cleared codes with GeekOBD APP and road tested - speedometer and transmission operating normally",
        parts: "Sensor connector clips ($15), dielectric grease ($8)",
        labor: "1.0 hour ($100)",
        total: "$123"
      },
      {
        title: "2016 Jeep Grand Cherokee - Sensor Failure",
        vehicle: "2016 Jeep Grand Cherokee, 3.6L V6, 115,000 miles",
        symptoms: "Multiple system failures, no speed reading, P0722 stored",
        diagnosis: "GeekOBD diagnostic scan showed P0722 with complete sensor failure. Output speed sensor testing revealed internal failure with no signal generation despite proper power and ground connections.",
        solution: "Replaced failed output speed sensor with OEM Jeep part, verified proper installation and signal generation. Cleared codes with GeekOBD APP and tested all affected systems - full functionality restored",
        parts: "OEM Jeep output speed sensor ($165), sensor gasket ($8)",
        labor: "2.5 hours ($250)",
        total: "$423"
      }
    ],
    relatedCodes: [
      { code: "P0720", desc: "Output Speed Sensor Circuit Malfunction" },
      { code: "P0721", desc: "Output Speed Sensor Circuit Range/Performance" },
      { code: "P0723", desc: "Output Speed Sensor Circuit Intermittent" },
      { code: "P0500", desc: "Vehicle Speed Sensor Malfunction" },
      { code: "U0101", desc: "Lost Communication with TCM" }
    ]
  }
};

// 页面生成函数
function createDetailedCodePage(code, data) {
  const templateContent = fs.readFileSync('p0142.html', 'utf8');
  let content = templateContent;
  
  // 基本替换
  content = content.replace(/P0142/g, code);
  content = content.replace(/p0142/g, code.toLowerCase());
  
  // 替换标题和描述
  content = content.replace(/O2 Sensor Circuit Malfunction \(Bank 1 Sensor 3\)/g, data.title);
  content = content.replace(/The Engine Control Module has detected a malfunction in the oxygen sensor circuit for Bank 1, Sensor 3 \(post-catalytic converter\)\./g, data.description);
  
  // 替换定义
  const oldDefinition = /The Engine Control Module has detected a malfunction in the oxygen sensor circuit for Bank 1, Sensor 3 \(post-catalytic converter\)\. This diagnostic trouble code indicates a specific issue within the engine\/powertrain that requires attention to ensure proper vehicle operation and safety\./g;
  content = content.replace(oldDefinition, data.definition);
  
  // 替换症状
  if (data.symptoms) {
    const symptomsHtml = data.symptoms.map(symptom => `								<li><strong>${symptom}</strong></li>`).join('\n');
    const symptomsRegex = /<ul>\s*<li><strong>Check Engine Light illuminated[\s\S]*?<\/ul>/;
    content = content.replace(symptomsRegex, `<ul>\n${symptomsHtml}\n							</ul>`);
  }
  
  // 替换原因
  if (data.causes) {
    const causesHtml = data.causes.map(cause => `									<li>${cause}</li>`).join('\n');
    const causesRegex = /<ol>\s*<li>Faulty oxygen sensor[\s\S]*?<\/ol>/;
    content = content.replace(causesRegex, `<ol>\n${causesHtml}\n								</ol>`);
  }
  
  // 替换性能影响
  if (data.performanceImpact) {
    const impactRegex = /<strong><i class="fa fa-exclamation-triangle"><\/i> Performance Impact:<\/strong>[\s\S]*?<\/div>/;
    content = content.replace(impactRegex, `<strong><i class="fa fa-exclamation-triangle"></i> Performance Impact:</strong> ${data.performanceImpact}\n							</div>`);
  }
  
  // 替换维修案例
  if (data.caseStudies && data.caseStudies.length >= 2) {
    const case1 = data.caseStudies[0];
    const case2 = data.caseStudies[1];
    
    const caseStudyHtml = `							<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
								<h4>Case 1: ${case1.title}</h4>
								<p><strong>Vehicle:</strong> ${case1.vehicle}</p>
								<p><strong>Symptoms:</strong> ${case1.symptoms}</p>
								<p><strong>Diagnosis:</strong> ${case1.diagnosis}</p>
								<p><strong>Solution:</strong> ${case1.solution}</p>
								<p><strong>Parts:</strong> ${case1.parts}</p>
								<p><strong>Labor:</strong> ${case1.labor}</p>
								<p><strong>Total Cost:</strong> ${case1.total}</p>
							</div>

							<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
								<h4>Case 2: ${case2.title}</h4>
								<p><strong>Vehicle:</strong> ${case2.vehicle}</p>
								<p><strong>Symptoms:</strong> ${case2.symptoms}</p>
								<p><strong>Diagnosis:</strong> ${case2.diagnosis}</p>
								<p><strong>Solution:</strong> ${case2.solution}</p>
								<p><strong>Parts:</strong> ${case2.parts}</p>
								<p><strong>Labor:</strong> ${case2.labor}</p>
								<p><strong>Total Cost:</strong> ${case2.total}</p>
							</div>`;
    
    const caseStudyRegex = /<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">[\s\S]*?<\/div>\s*<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">[\s\S]*?<\/div>/;
    content = content.replace(caseStudyRegex, caseStudyHtml);
  }
  
  // 替换相关代码
  if (data.relatedCodes) {
    const relatedHtml = data.relatedCodes.map(related => 
      `									<li style="margin-bottom: 10px;"><a href="${related.code.toLowerCase()}.html" style="color: #007bff;">${related.code} - ${related.desc}</a></li>`
    ).join('\n');
    
    const relatedRegex = /<li style="margin-bottom: 10px;"><a href="p0140\.html"[\s\S]*?<li style="margin-bottom: 10px;"><a href="p0145\.html"[\s\S]*?<\/li>/;
    content = content.replace(relatedRegex, relatedHtml);
  }
  
  return content;
}

// 批量创建最终发现的缺失页面
const finalMissingCodesToCreate = [
  // 最终发现的缺失P码 - 传输控制系统
  { code: 'P0700', database: finalMissingPCodeDatabase },
  { code: 'P0721', database: finalMissingPCodeDatabase },
  { code: 'P0722', database: finalMissingPCodeDatabase }
];

let totalCreated = 0;

console.log('🚀 Creating final batch of missing DTC pages...\n');

finalMissingCodesToCreate.forEach(({ code, database }) => {
  if (database[code]) {
    try {
      const htmlContent = createDetailedCodePage(code, database[code]);
      fs.writeFileSync(`${code.toLowerCase()}.html`, htmlContent, 'utf8');
      console.log(`✅ Created ${code.toLowerCase()}.html - ${database[code].title}`);
      totalCreated++;
    } catch (error) {
      console.log(`❌ Failed to create ${code.toLowerCase()}.html: ${error.message}`);
    }
  }
});

console.log(`\n🎉 Successfully created ${totalCreated} final missing DTC pages!`);
console.log('\n📊 Final Status:');
console.log('We have systematically filled gaps across all major automotive systems!');
console.log('Our DTC coverage now includes transmission control systems.');
console.log('\nFinal expansion complete! 🎯');
