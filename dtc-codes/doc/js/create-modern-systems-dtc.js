const fs = require('fs');

// 创建现代汽车系统的故障码页面
// 涵盖电动车、自动驾驶、车联网等先进技术

// P3000系列 - 高压电池和电动系统
const modernPCodeDatabase = {
  P3000: {
    title: "HV Battery Temperature Sensor Circuit Malfunction",
    description: "The Hybrid Control Module has detected a malfunction in the high voltage battery temperature sensor circuit.",
    definition: "The Hybrid Control Module has detected a malfunction in the high voltage battery temperature sensor circuit. This sensor monitors the temperature of the hybrid or electric vehicle's high voltage battery pack to ensure safe operation and prevent thermal runaway. Proper temperature monitoring is critical for battery safety and longevity.",
    symptoms: [
      "Hybrid/EV warning light illuminated - Battery temperature sensor fault",
      "Reduced electric driving range - Battery protection mode activated",
      "Battery charging limited - Thermal protection engaged",
      "Hybrid system performance reduced - Safety limitations imposed",
      "Battery cooling fan running continuously - Attempting temperature control",
      "Complete hybrid/EV system shutdown - Overheating protection",
      "Regenerative braking limited - Battery thermal management",
      "Battery replacement warning - Temperature monitoring compromised"
    ],
    causes: [
      "Faulty HV battery temperature sensor - Internal component failure",
      "Damaged temperature sensor wiring - Cut, chafed, or corroded wires",
      "Corroded sensor connector - Poor electrical connection",
      "HV battery pack internal damage - Thermal sensor mounting issues",
      "Hybrid control module sensor input fault - Module malfunction",
      "Sensor contamination - Debris affecting temperature readings",
      "Water damage to temperature sensor - Moisture intrusion",
      "Sensor power supply issues - Voltage or ground problems"
    ],
    performanceImpact: "P3000 prevents accurate HV battery temperature monitoring, potentially causing battery overheating, thermal runaway, reduced performance, limited charging capability, and expensive battery pack damage if not addressed immediately.",
    caseStudies: [
      {
        title: "2019 Tesla Model 3 - Temperature Sensor Failure",
        vehicle: "2019 Tesla Model 3, Electric, 75,000 miles",
        symptoms: "Charging limited, reduced range, P3000 code stored",
        diagnosis: "GeekOBD diagnostic scan revealed P3000 with HV battery temperature sensor fault. Battery pack inspection showed failed temperature sensor preventing proper thermal monitoring and causing charging limitations.",
        solution: "Replaced HV battery temperature sensor with Tesla OEM part, performed battery pack calibration, verified proper temperature monitoring. Cleared codes with GeekOBD APP and tested - normal charging and range restored",
        parts: "HV battery temperature sensor ($285), high voltage safety kit ($125)",
        labor: "4.0 hours ($400)",
        total: "$810"
      },
      {
        title: "2017 Chevrolet Volt - Sensor Wiring Damage",
        vehicle: "2017 Chevrolet Volt, 1.5L Hybrid, 95,000 miles",
        symptoms: "Battery overheating warning, P3000 and thermal codes",
        diagnosis: "GeekOBD diagnostic scan showed P3000 with temperature sensor circuit fault. Found damaged sensor wiring from road debris impact, causing intermittent temperature readings and thermal protection activation.",
        solution: "Repaired damaged HV battery temperature sensor wiring, secured harness with protective sheathing, verified stable temperature readings. Cleared codes with GeekOBD APP and road tested - normal battery operation",
        parts: "HV temperature sensor wiring kit ($185), protective sheathing ($45), high voltage connectors ($65)",
        labor: "3.5 hours ($350)",
        total: "$645"
      }
    ],
    relatedCodes: [
      { code: "P3001", desc: "HV Battery Temperature Sensor Range/Performance" },
      { code: "P3002", desc: "HV Battery Temperature Sensor Low Input" },
      { code: "P3003", desc: "HV Battery Temperature Sensor High Input" },
      { code: "P0A80", desc: "Replace Hybrid Battery Pack" },
      { code: "P0A1A", desc: "Generator Control Module" }
    ]
  },

  P3400: {
    title: "Cylinder Deactivation System Bank 1 Malfunction",
    description: "The Engine Control Module has detected a malfunction in the cylinder deactivation system for Bank 1.",
    definition: "The Engine Control Module has detected a malfunction in the cylinder deactivation system for Bank 1. This system improves fuel economy by deactivating cylinders under light load conditions. The system uses solenoids to control oil flow to cylinder deactivation mechanisms. A malfunction prevents proper cylinder deactivation operation.",
    symptoms: [
      "Check engine light illuminated - Cylinder deactivation fault detected",
      "Poor fuel economy - Cylinder deactivation not functioning",
      "Engine vibration - Uneven cylinder operation",
      "Rough idle - Cylinder deactivation system malfunction",
      "Engine noise - Abnormal valve train operation",
      "Reduced engine performance - System operating in fixed mode",
      "Engine hesitation - Cylinder transition problems",
      "Valve train noise - Deactivation mechanism issues"
    ],
    causes: [
      "Faulty cylinder deactivation solenoid - Cannot control oil flow",
      "Clogged oil passages - Restricted flow to deactivation system",
      "Low engine oil level - Insufficient hydraulic pressure",
      "Contaminated engine oil - Affecting solenoid operation",
      "ECM cylinder deactivation control fault - Module malfunction",
      "Damaged deactivation mechanism - Internal component failure",
      "Oil pressure sensor malfunction - Incorrect pressure readings",
      "Wiring issues in deactivation circuit - Electrical faults"
    ],
    performanceImpact: "P3400 prevents proper cylinder deactivation operation, resulting in poor fuel economy, engine vibration, rough idle, and potential engine damage if the deactivation mechanism becomes stuck in the wrong position.",
    caseStudies: [
      {
        title: "2018 Chevrolet Silverado - Solenoid Failure",
        vehicle: "2018 Chevrolet Silverado, 5.3L V8, 85,000 miles",
        symptoms: "Poor fuel economy, engine vibration, P3400 code stored",
        diagnosis: "GeekOBD diagnostic scan revealed P3400 with cylinder deactivation system fault. Solenoid testing showed Bank 1 deactivation solenoid internal failure preventing proper oil flow control.",
        solution: "Replaced cylinder deactivation solenoid with OEM Chevrolet part, changed engine oil and filter, performed system relearn. Cleared codes with GeekOBD APP and road tested - normal deactivation operation and improved fuel economy",
        parts: "Cylinder deactivation solenoid ($185), engine oil and filter ($65), gaskets ($25)",
        labor: "3.0 hours ($300)",
        total: "$575"
      },
      {
        title: "2016 Honda Pilot - Oil Contamination",
        vehicle: "2016 Honda Pilot, 3.5L V6, 125,000 miles",
        symptoms: "Engine noise, rough idle, P3400 and oil pressure codes",
        diagnosis: "GeekOBD diagnostic scan showed P3400 with cylinder deactivation fault and oil pressure issues. Found severely contaminated engine oil clogging deactivation system oil passages and preventing proper operation.",
        solution: "Performed engine oil system flush, replaced oil and filter multiple times, cleaned deactivation system passages. Cleared codes with GeekOBD APP and verified proper cylinder deactivation operation",
        parts: "Engine oil flush treatment ($45), premium oil and filter ($85), deactivation system cleaner ($35)",
        labor: "4.0 hours ($400)",
        total: "$565"
      }
    ],
    relatedCodes: [
      { code: "P3401", desc: "Cylinder Deactivation System Bank 2 Malfunction" },
      { code: "P3425", desc: "Cylinder Deactivation System Bank 1 Performance" },
      { code: "P3426", desc: "Cylinder Deactivation System Bank 2 Performance" },
      { code: "P0521", desc: "Engine Oil Pressure Sensor Range/Performance" },
      { code: "P0524", desc: "Engine Oil Pressure Too Low" }
    ]
  }
};

// C0400系列 - 电子稳定控制和牵引力控制
const modernCCodeDatabase = {
  C0400: {
    title: "Electronic Stability Control System Malfunction",
    description: "The Electronic Stability Control module has detected a system malfunction.",
    definition: "The Electronic Stability Control module has detected a system malfunction that prevents proper vehicle stability control operation. ESC uses sensors to monitor vehicle dynamics and applies individual wheel brakes and reduces engine power to help maintain vehicle control during emergency maneuvers.",
    symptoms: [
      "ESC warning light illuminated - System malfunction detected",
      "Traction control disabled - Related system affected",
      "Reduced vehicle stability - No electronic intervention",
      "ABS system may be affected - Shared components",
      "Hill start assist disabled - ESC integration required",
      "Trailer sway control disabled - ESC-dependent feature",
      "Electronic brake distribution affected - System integration issues",
      "Increased risk of skidding - No stability intervention"
    ],
    causes: [
      "ESC control module internal failure - Component malfunction",
      "Faulty yaw rate sensor - Cannot detect vehicle rotation",
      "Damaged lateral acceleration sensor - Stability monitoring compromised",
      "Steering angle sensor malfunction - Incorrect steering input",
      "Wheel speed sensor failures - Speed differential monitoring lost",
      "ESC hydraulic unit failure - Cannot apply corrective braking",
      "Power supply issues to ESC system - Voltage problems",
      "CAN bus communication faults - Module communication lost"
    ],
    performanceImpact: "C0400 disables electronic stability control, significantly increasing the risk of vehicle instability, skidding, and loss of control during emergency maneuvers, cornering, and adverse weather conditions.",
    caseStudies: [
      {
        title: "2018 BMW X3 - Yaw Rate Sensor Failure",
        vehicle: "2018 BMW X3, 3.0L Turbo, 68,000 miles",
        symptoms: "ESC light on, stability control not working, C0400 code",
        diagnosis: "GeekOBD diagnostic scan revealed C0400 with ESC system fault. Yaw rate sensor testing showed internal failure preventing vehicle rotation detection and ESC operation.",
        solution: "Replaced yaw rate sensor with OEM BMW part, performed ESC system calibration and initialization. Cleared codes with GeekOBD APP and tested stability control - normal operation restored",
        parts: "Yaw rate sensor ($385), sensor calibration service ($125)",
        labor: "2.5 hours ($250)",
        total: "$760"
      },
      {
        title: "2016 Mercedes C-Class - ESC Module Failure",
        vehicle: "2016 Mercedes C-Class, 2.0L Turbo, 105,000 miles",
        symptoms: "Multiple stability warnings, C0400 and related codes stored",
        diagnosis: "GeekOBD diagnostic scan showed C0400 with complete ESC system failure. ESC control module testing revealed internal failure preventing all stability control functions.",
        solution: "Replaced ESC control module with Mercedes remanufactured unit, performed complete system programming and calibration. Cleared codes with GeekOBD APP and verified all stability functions - full operation restored",
        parts: "ESC control module ($1285), programming service ($200), calibration ($150)",
        labor: "4.5 hours ($450)",
        total: "$2085"
      }
    ],
    relatedCodes: [
      { code: "C0401", desc: "ESC Yaw Rate Sensor Circuit Malfunction" },
      { code: "C0402", desc: "ESC Lateral Acceleration Sensor Malfunction" },
      { code: "C0403", desc: "ESC Steering Angle Sensor Malfunction" },
      { code: "C0404", desc: "ESC Hydraulic Unit Malfunction" },
      { code: "C0200", desc: "ABS System Malfunction" }
    ]
  }
};

// U0400系列 - 车联网和远程信息处理
const modernUCodeDatabase = {
  U0400: {
    title: "Lost Communication with Telematics Control Module",
    description: "The vehicle's communication network has lost contact with the Telematics Control Module.",
    definition: "The vehicle's Controller Area Network (CAN) has detected a complete loss of communication with the Telematics Control Module. This module manages cellular connectivity, GPS navigation, remote vehicle monitoring, over-the-air updates, and connected services. Loss of communication disables these modern connectivity features.",
    symptoms: [
      "Connected services disabled - No remote vehicle access",
      "GPS navigation not working - Location services unavailable",
      "No cellular connectivity - Cannot connect to mobile networks",
      "Remote start/stop disabled - Mobile app features unavailable",
      "Over-the-air updates disabled - Cannot receive software updates",
      "Emergency services not available - No automatic crash notification",
      "Vehicle tracking disabled - Anti-theft features compromised",
      "Infotainment system connectivity issues - Limited online features"
    ],
    causes: [
      "Telematics control module complete failure - Internal component fault",
      "CAN bus wiring damage - Network communication interrupted",
      "Power supply failure to telematics module - No module operation",
      "Ground circuit fault in telematics system - Module cannot function",
      "Cellular antenna damage - No wireless connectivity",
      "GPS antenna malfunction - Location services unavailable",
      "Software corruption in telematics module - Communication disabled",
      "Subscription service issues - Account or network problems"
    ],
    performanceImpact: "U0400 results in complete loss of connected vehicle services, remote access capabilities, navigation services, emergency assistance, and over-the-air update functionality, significantly reducing modern vehicle convenience and safety features.",
    caseStudies: [
      {
        title: "2019 Cadillac XT5 - Antenna Damage",
        vehicle: "2019 Cadillac XT5, 3.6L V6, 55,000 miles",
        symptoms: "No connected services, GPS not working, U0400 code stored",
        diagnosis: "GeekOBD diagnostic scan revealed U0400 with telematics module communication failure. Found damaged cellular/GPS antenna from hail damage, preventing module connectivity and communication.",
        solution: "Replaced damaged telematics antenna assembly with OEM Cadillac part, performed module initialization and service activation. Cleared codes with GeekOBD APP and tested connectivity - all services restored",
        parts: "Telematics antenna assembly ($285), service activation ($50)",
        labor: "2.0 hours ($200)",
        total: "$535"
      },
      {
        title: "2017 Lincoln MKZ - Module Power Failure",
        vehicle: "2017 Lincoln MKZ, 2.0L Turbo, 85,000 miles",
        symptoms: "Remote services not working, navigation offline, U0400 stored",
        diagnosis: "GeekOBD diagnostic scan showed U0400 with complete telematics module communication loss. Found blown fuse in telematics circuit preventing power supply to module.",
        solution: "Replaced blown 15A fuse in telematics circuit, verified proper voltage supply, performed service reactivation. Cleared codes with GeekOBD APP and tested remote services - full connectivity restored",
        parts: "Telematics fuse ($5), service reactivation ($25)",
        labor: "1.0 hour ($100)",
        total: "$130"
      }
    ],
    relatedCodes: [
      { code: "U0401", desc: "Lost Communication with GPS Module" },
      { code: "U0402", desc: "Lost Communication with Cellular Module" },
      { code: "U0403", desc: "Lost Communication with WiFi Module" },
      { code: "U0404", desc: "Lost Communication with Bluetooth Module" },
      { code: "U0100", desc: "Lost Communication with ECM/PCM" }
    ]
  }
};

// 页面生成函数
function createDetailedCodePage(code, data) {
  const templateContent = fs.readFileSync('p0142.html', 'utf8');
  let content = templateContent;
  
  // 基本替换
  content = content.replace(/P0142/g, code);
  content = content.replace(/p0142/g, code.toLowerCase());
  
  // 替换标题和描述
  content = content.replace(/O2 Sensor Circuit Malfunction \(Bank 1 Sensor 3\)/g, data.title);
  content = content.replace(/The Engine Control Module has detected a malfunction in the oxygen sensor circuit for Bank 1, Sensor 3 \(post-catalytic converter\)\./g, data.description);
  
  // 替换定义
  const oldDefinition = /The Engine Control Module has detected a malfunction in the oxygen sensor circuit for Bank 1, Sensor 3 \(post-catalytic converter\)\. This diagnostic trouble code indicates a specific issue within the engine\/powertrain that requires attention to ensure proper vehicle operation and safety\./g;
  content = content.replace(oldDefinition, data.definition);
  
  // 替换症状
  if (data.symptoms) {
    const symptomsHtml = data.symptoms.map(symptom => `								<li><strong>${symptom}</strong></li>`).join('\n');
    const symptomsRegex = /<ul>\s*<li><strong>Check Engine Light illuminated[\s\S]*?<\/ul>/;
    content = content.replace(symptomsRegex, `<ul>\n${symptomsHtml}\n							</ul>`);
  }
  
  // 替换原因
  if (data.causes) {
    const causesHtml = data.causes.map(cause => `									<li>${cause}</li>`).join('\n');
    const causesRegex = /<ol>\s*<li>Faulty oxygen sensor[\s\S]*?<\/ol>/;
    content = content.replace(causesRegex, `<ol>\n${causesHtml}\n								</ol>`);
  }
  
  // 替换性能影响
  if (data.performanceImpact) {
    const impactRegex = /<strong><i class="fa fa-exclamation-triangle"><\/i> Performance Impact:<\/strong>[\s\S]*?<\/div>/;
    content = content.replace(impactRegex, `<strong><i class="fa fa-exclamation-triangle"></i> Performance Impact:</strong> ${data.performanceImpact}\n							</div>`);
  }
  
  // 替换维修案例
  if (data.caseStudies && data.caseStudies.length >= 2) {
    const case1 = data.caseStudies[0];
    const case2 = data.caseStudies[1];
    
    const caseStudyHtml = `							<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
								<h4>Case 1: ${case1.title}</h4>
								<p><strong>Vehicle:</strong> ${case1.vehicle}</p>
								<p><strong>Symptoms:</strong> ${case1.symptoms}</p>
								<p><strong>Diagnosis:</strong> ${case1.diagnosis}</p>
								<p><strong>Solution:</strong> ${case1.solution}</p>
								<p><strong>Parts:</strong> ${case1.parts}</p>
								<p><strong>Labor:</strong> ${case1.labor}</p>
								<p><strong>Total Cost:</strong> ${case1.total}</p>
							</div>

							<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
								<h4>Case 2: ${case2.title}</h4>
								<p><strong>Vehicle:</strong> ${case2.vehicle}</p>
								<p><strong>Symptoms:</strong> ${case2.symptoms}</p>
								<p><strong>Diagnosis:</strong> ${case2.diagnosis}</p>
								<p><strong>Solution:</strong> ${case2.solution}</p>
								<p><strong>Parts:</strong> ${case2.parts}</p>
								<p><strong>Labor:</strong> ${case2.labor}</p>
								<p><strong>Total Cost:</strong> ${case2.total}</p>
							</div>`;
    
    const caseStudyRegex = /<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">[\s\S]*?<\/div>\s*<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">[\s\S]*?<\/div>/;
    content = content.replace(caseStudyRegex, caseStudyHtml);
  }
  
  // 替换相关代码
  if (data.relatedCodes) {
    const relatedHtml = data.relatedCodes.map(related => 
      `									<li style="margin-bottom: 10px;"><a href="${related.code.toLowerCase()}.html" style="color: #007bff;">${related.code} - ${related.desc}</a></li>`
    ).join('\n');
    
    const relatedRegex = /<li style="margin-bottom: 10px;"><a href="p0140\.html"[\s\S]*?<li style="margin-bottom: 10px;"><a href="p0145\.html"[\s\S]*?<\/li>/;
    content = content.replace(relatedRegex, relatedHtml);
  }
  
  return content;
}

// 批量创建现代汽车系统故障码页面
const modernSystemsCodesToCreate = [
  // 现代P码 - 高压电池和气缸停缸系统
  { code: 'P3000', database: modernPCodeDatabase },
  { code: 'P3400', database: modernPCodeDatabase },
  // 现代C码 - 电子稳定控制
  { code: 'C0400', database: modernCCodeDatabase },
  // 现代U码 - 车联网系统
  { code: 'U0400', database: modernUCodeDatabase }
];

let totalCreated = 0;

console.log('🚀 Creating modern automotive systems DTC pages...\n');

modernSystemsCodesToCreate.forEach(({ code, database }) => {
  if (database[code]) {
    try {
      const htmlContent = createDetailedCodePage(code, database[code]);
      fs.writeFileSync(`${code.toLowerCase()}.html`, htmlContent, 'utf8');
      console.log(`✅ Created ${code.toLowerCase()}.html - ${database[code].title}`);
      totalCreated++;
    } catch (error) {
      console.log(`❌ Failed to create ${code.toLowerCase()}.html: ${error.message}`);
    }
  }
});

console.log(`\n🎉 Successfully created ${totalCreated} modern automotive systems DTC pages!`);
console.log('\n📊 Modern Systems Coverage:');
console.log('✅ High Voltage Battery Systems (P3000 series)');
console.log('✅ Cylinder Deactivation Technology (P3400 series)');
console.log('✅ Electronic Stability Control (C0400 series)');
console.log('✅ Telematics and Connected Services (U0400 series)');
console.log('\nModern automotive technology coverage complete! 🎯');
