const fs = require('fs');

// 创建扩展的真实汽车技术故障码页面
// 涵盖EGR系统、柴油系统、高级网络通信等

// P1400系列 - EGR和排放控制系统
const extendedRealPCodeDatabase = {
  P1400: {
    title: "EGR Valve Position Sensor Circuit Malfunction",
    description: "The Engine Control Module has detected a malfunction in the EGR valve position sensor circuit.",
    definition: "The Engine Control Module has detected a malfunction in the EGR valve position sensor circuit that monitors the position of the exhaust gas recirculation valve. This sensor provides feedback to the ECM about valve position for precise EGR flow control and emissions management.",
    symptoms: [
      "Check engine light illuminated - EGR position sensor circuit fault detected",
      "Poor engine performance - EGR flow not properly controlled",
      "Engine knock or ping - Incorrect EGR valve positioning",
      "Rough idle - EGR valve position affecting idle stability",
      "Increased NOx emissions - EGR system not functioning optimally",
      "Failed emissions test - NOx levels above acceptable limits",
      "Engine hesitation - EGR position sensor affecting air/fuel mixture",
      "EGR system diagnostic trouble - Position feedback unavailable"
    ],
    causes: [
      "Faulty EGR valve position sensor - Internal sensor element failure",
      "Damaged EGR position sensor wiring - Cut, chafed, or corroded wires",
      "Corroded sensor connector - Poor electrical connection",
      "EGR valve mechanical binding - Preventing accurate position sensing",
      "ECM EGR position sensor input fault - Module malfunction",
      "Carbon buildup on EGR valve - Affecting sensor operation",
      "Power supply issues to sensor - Voltage or ground problems",
      "EGR valve actuator malfunction - Affecting position feedback"
    ],
    performanceImpact: "P1400 prevents accurate EGR valve position monitoring, compromising emissions control, potentially causing engine knock, poor performance, and emissions test failure.",
    caseStudies: [
      {
        title: "2018 Volkswagen Passat - EGR Position Sensor Failure",
        vehicle: "2018 Volkswagen Passat, 2.0L TDI Diesel, 115,000 miles",
        symptoms: "Engine knock, poor performance, P1400 code stored",
        diagnosis: "GeekOBD diagnostic scan revealed P1400 with EGR position sensor circuit fault. Sensor testing showed internal failure preventing accurate valve position feedback.",
        solution: "Replaced EGR valve position sensor with OEM Volkswagen part, cleaned EGR valve, performed EGR system adaptation. Cleared codes with GeekOBD APP and road tested - normal EGR operation restored",
        parts: "EGR position sensor ($165), EGR valve cleaner ($25), sensor gasket ($8)",
        labor: "2.5 hours ($250)",
        total: "$448"
      },
      {
        title: "2017 BMW X3 - Sensor Wiring Corrosion",
        vehicle: "2017 BMW X3, 2.0L Diesel, 125,000 miles",
        symptoms: "Intermittent engine issues, P1400 appearing sporadically",
        diagnosis: "GeekOBD diagnostic scan showed intermittent P1400 with EGR position sensor circuit problems. Found corroded sensor wiring from moisture intrusion, causing intermittent signal loss.",
        solution: "Repaired corroded EGR position sensor wiring, applied marine-grade protection, secured routing. Cleared codes with GeekOBD APP and verified stable sensor operation",
        parts: "EGR sensor wiring repair kit ($85), marine protection coating ($25), connector repair kit ($35)",
        labor: "2.0 hours ($200)",
        total: "$345"
      }
    ],
    relatedCodes: [
      { code: "P1401", desc: "EGR Valve Position Sensor Circuit Range/Performance" },
      { code: "P1402", desc: "EGR Valve Position Sensor Circuit Low" },
      { code: "P1403", desc: "EGR Valve Position Sensor Circuit High" },
      { code: "P0401", desc: "Exhaust Gas Recirculation Flow Insufficient" },
      { code: "P0402", desc: "Exhaust Gas Recirculation Flow Excessive" }
    ]
  },

  P1401: {
    title: "EGR Valve Position Sensor Circuit Range/Performance",
    description: "The Engine Control Module has detected a range or performance problem with the EGR valve position sensor circuit.",
    definition: "The Engine Control Module has detected that the EGR valve position sensor is not performing within the expected range or specifications. The sensor signal is present but indicates values outside normal operating parameters, affecting EGR system control accuracy.",
    symptoms: [
      "Check engine light illuminated - EGR position sensor performance fault detected",
      "EGR system not responding properly - Position feedback inaccurate",
      "Engine performance issues - EGR flow control compromised",
      "Increased emissions - EGR system not optimizing combustion",
      "Engine knock under load - EGR position control inaccurate",
      "Poor fuel economy - EGR system not operating efficiently",
      "Failed emissions test - NOx control compromised",
      "Engine hesitation - EGR position affecting engine operation"
    ],
    causes: [
      "EGR valve position sensor drift - Calibration out of specification",
      "Carbon buildup affecting sensor - Mechanical interference with operation",
      "EGR valve binding - Preventing accurate position sensing",
      "Sensor mounting issues - Improper installation affecting readings",
      "Temperature effects on sensor - Thermal drift affecting accuracy",
      "EGR valve actuator wear - Inconsistent position control",
      "Electrical interference - Affecting sensor signal accuracy",
      "ECM calibration issues - Software parameters incorrect"
    ],
    performanceImpact: "P1401 indicates EGR position sensor performance issues that can cause inaccurate EGR flow control, increased emissions, engine knock, and reduced fuel economy.",
    caseStudies: [
      {
        title: "2019 Audi A4 - EGR Valve Carbon Buildup",
        vehicle: "2019 Audi A4, 2.0L TDI Diesel, 95,000 miles",
        symptoms: "Engine knock, emissions test failure, P1401 code stored",
        diagnosis: "GeekOBD diagnostic scan revealed P1401 with EGR position sensor performance fault. Found severe carbon buildup on EGR valve affecting sensor accuracy and valve operation.",
        solution: "Cleaned EGR valve and position sensor, performed EGR system decarbonization, calibrated position sensor. Cleared codes with GeekOBD APP and retested emissions - passed with normal NOx levels",
        parts: "EGR cleaning service ($125), decarbonization chemicals ($45), sensor calibration ($75)",
        labor: "3.5 hours ($350)",
        total: "$595"
      },
      {
        title: "2018 Mercedes Sprinter - Sensor Calibration Drift",
        vehicle: "2018 Mercedes Sprinter, 2.1L Diesel, 135,000 miles",
        symptoms: "Poor performance, increased fuel consumption, P1401 stored",
        diagnosis: "GeekOBD diagnostic scan showed P1401 with EGR position sensor range issues. Found sensor calibration drift causing inaccurate position readings and improper EGR control.",
        solution: "Recalibrated EGR position sensor with Mercedes diagnostic equipment, updated ECM software, performed EGR system adaptation. Cleared codes with GeekOBD APP and road tested - normal EGR operation restored",
        parts: "EGR sensor calibration service ($185), ECM software update ($125)",
        labor: "2.5 hours ($250)",
        total: "$560"
      }
    ],
    relatedCodes: [
      { code: "P1400", desc: "EGR Valve Position Sensor Circuit Malfunction" },
      { code: "P1402", desc: "EGR Valve Position Sensor Circuit Low" },
      { code: "P1403", desc: "EGR Valve Position Sensor Circuit High" },
      { code: "P0404", desc: "Exhaust Gas Recirculation Circuit Range/Performance" },
      { code: "P0405", desc: "Exhaust Gas Recirculation Sensor A Circuit Low" }
    ]
  }
};

// U0800系列 - 高级网络通信系统
const extendedRealUCodeDatabase = {
  U0800: {
    title: "Lost Communication with Transmission Control Module",
    description: "The vehicle's communication network has lost contact with the Transmission Control Module (TCM).",
    definition: "The vehicle's Controller Area Network (CAN) has detected a complete loss of communication with the Transmission Control Module (TCM). This module controls automatic transmission operation, shift timing, torque converter lockup, and transmission diagnostic functions.",
    symptoms: [
      "Transmission warning light illuminated - TCM communication failure detected",
      "Transmission stuck in one gear - No shift control available",
      "No transmission shifting - Manual valve body operation only",
      "Torque converter not locking up - Fuel economy reduced",
      "Transmission diagnostic functions disabled - No system monitoring",
      "Shift quality poor - Electronic control unavailable",
      "Engine may not start - Transmission interlock affected",
      "Speedometer not working - Speed signal from TCM unavailable"
    ],
    causes: [
      "Transmission control module complete failure - Internal component fault",
      "CAN bus wiring damage - Network communication interrupted",
      "Power supply failure to TCM - No module operation",
      "Ground circuit fault in transmission system - Module cannot function",
      "CAN bus connector corrosion - Signal transmission failure",
      "Software corruption in TCM - Communication disabled",
      "Gateway module fault affecting TCM communication",
      "Electromagnetic interference affecting transmission system communication"
    ],
    performanceImpact: "U0800 results in complete loss of transmission control, eliminating automatic shifting, torque converter lockup, and electronic transmission functions, potentially causing transmission damage.",
    caseStudies: [
      {
        title: "2018 Ford Explorer - TCM Power Supply Failure",
        vehicle: "2018 Ford Explorer, 3.5L V6, 105,000 miles",
        symptoms: "Transmission not shifting, warning lights, U0800 code",
        diagnosis: "GeekOBD diagnostic scan revealed U0800 with TCM communication failure. Found blown fuse in transmission control circuit preventing power supply to module.",
        solution: "Replaced blown 30A fuse in transmission control fuse box, verified proper voltage supply to TCM, performed transmission adaptation. Cleared codes with GeekOBD APP and tested shifting - normal operation restored",
        parts: "Transmission control fuse ($12), fuse puller tool ($5)",
        labor: "1.5 hours ($150)",
        total: "$167"
      },
      {
        title: "2017 Honda Pilot - CAN Bus Damage",
        vehicle: "2017 Honda Pilot, 3.5L V6, 125,000 miles",
        symptoms: "Intermittent transmission issues, U0800 appearing sporadically",
        diagnosis: "GeekOBD diagnostic scan showed intermittent U0800 code. Found damaged CAN bus wiring near transmission from road debris impact, causing communication disruption.",
        solution: "Repaired damaged CAN bus wiring, properly routed and secured harness with protective sheathing. Cleared codes with GeekOBD APP and verified stable TCM communication",
        parts: "CAN bus wiring repair kit ($145), protective sheathing ($35), mounting clips ($25)",
        labor: "4.0 hours ($400)",
        total: "$605"
      }
    ],
    relatedCodes: [
      { code: "U0801", desc: "Lost Communication with Transfer Case Control Module" },
      { code: "U0802", desc: "Lost Communication with Transmission Shift Control Module" },
      { code: "U0803", desc: "Lost Communication with Gear Shift Module" },
      { code: "P0700", desc: "Transmission Control System Malfunction" },
      { code: "U0100", desc: "Lost Communication with ECM/PCM" }
    ]
  }
};

// C1300系列 - 高级悬挂控制系统
const extendedRealCCodeDatabase = {
  C1300: {
    title: "Suspension Height Sensor Circuit Malfunction",
    description: "The Suspension Control Module has detected a malfunction in the suspension height sensor circuit.",
    definition: "The Suspension Control Module has detected a malfunction in the suspension height sensor circuit that monitors vehicle ride height for automatic leveling systems. This sensor provides feedback for air suspension, electronic ride control, and load leveling functions.",
    symptoms: [
      "Suspension warning light illuminated - Height sensor circuit fault detected",
      "Vehicle ride height incorrect - Automatic leveling not functioning",
      "Suspension not adjusting to load - Load leveling system disabled",
      "Uneven vehicle stance - Height sensor feedback unavailable",
      "Air suspension compressor running continuously - No height feedback",
      "Suspension diagnostic functions disabled - System monitoring offline",
      "Ride quality affected - Suspension not maintaining proper height",
      "Headlight aim affected - Vehicle height changes affecting beam pattern"
    ],
    causes: [
      "Faulty suspension height sensor - Internal sensor element failure",
      "Damaged height sensor wiring - Cut, chafed, or corroded wires",
      "Corroded sensor connector - Poor electrical connection",
      "Sensor mounting damage - Mechanical failure affecting operation",
      "Suspension control module sensor input fault - Module malfunction",
      "Sensor linkage binding - Mechanical obstruction preventing operation",
      "Power supply issues to sensor - Voltage or ground problems",
      "Environmental damage to sensor - Water or debris affecting operation"
    ],
    performanceImpact: "C1300 prevents accurate ride height monitoring, disabling automatic leveling, load compensation, and suspension height control functions, affecting ride quality and vehicle stability.",
    caseStudies: [
      {
        title: "2019 Lincoln Navigator - Height Sensor Linkage Failure",
        vehicle: "2019 Lincoln Navigator, 3.5L EcoBoost, 85,000 miles",
        symptoms: "Air suspension not leveling, C1300 code stored",
        diagnosis: "GeekOBD diagnostic scan revealed C1300 with suspension height sensor circuit fault. Found broken sensor linkage preventing accurate height measurement and feedback.",
        solution: "Replaced suspension height sensor linkage assembly with OEM Lincoln part, calibrated suspension system, performed height sensor adaptation. Cleared codes with GeekOBD APP and tested leveling - normal operation restored",
        parts: "Height sensor linkage assembly ($185), suspension calibration service ($125)",
        labor: "3.0 hours ($300)",
        total: "$610"
      },
      {
        title: "2018 Mercedes S-Class - Sensor Water Damage",
        vehicle: "2018 Mercedes S-Class, 4.0L V8, 95,000 miles",
        symptoms: "Suspension height erratic, warning messages, C1300 stored",
        diagnosis: "GeekOBD diagnostic scan showed C1300 with height sensor circuit malfunction. Found water damage to sensor from road splash causing internal component failure.",
        solution: "Replaced suspension height sensor with OEM Mercedes part, applied protective coating, performed suspension system calibration. Cleared codes with GeekOBD APP and verified height control - normal leveling restored",
        parts: "Suspension height sensor ($285), protective coating ($25), sensor mounting hardware ($15)",
        labor: "2.5 hours ($250)",
        total: "$575"
      }
    ],
    relatedCodes: [
      { code: "C1301", desc: "Suspension Height Sensor Circuit Range/Performance" },
      { code: "C1302", desc: "Suspension Height Sensor Circuit Low" },
      { code: "C1303", desc: "Suspension Height Sensor Circuit High" },
      { code: "C0500", desc: "Vehicle Dynamics Control System Malfunction" },
      { code: "U0155", desc: "Lost Communication with Suspension Control Module" }
    ]
  }
};

// 页面生成函数
function createDetailedCodePage(code, data) {
  const templateContent = fs.readFileSync('p0142.html', 'utf8');
  let content = templateContent;
  
  // 基本替换
  content = content.replace(/P0142/g, code);
  content = content.replace(/p0142/g, code.toLowerCase());
  
  // 替换标题和描述
  content = content.replace(/O2 Sensor Circuit Malfunction \(Bank 1 Sensor 3\)/g, data.title);
  content = content.replace(/The Engine Control Module has detected a malfunction in the oxygen sensor circuit for Bank 1, Sensor 3 \(post-catalytic converter\)\./g, data.description);
  
  // 替换定义
  const oldDefinition = /The Engine Control Module has detected a malfunction in the oxygen sensor circuit for Bank 1, Sensor 3 \(post-catalytic converter\)\. This diagnostic trouble code indicates a specific issue within the engine\/powertrain that requires attention to ensure proper vehicle operation and safety\./g;
  content = content.replace(oldDefinition, data.definition);
  
  // 替换症状
  if (data.symptoms) {
    const symptomsHtml = data.symptoms.map(symptom => `								<li><strong>${symptom}</strong></li>`).join('\n');
    const symptomsRegex = /<ul>\s*<li><strong>Check Engine Light illuminated[\s\S]*?<\/ul>/;
    content = content.replace(symptomsRegex, `<ul>\n${symptomsHtml}\n							</ul>`);
  }
  
  // 替换原因
  if (data.causes) {
    const causesHtml = data.causes.map(cause => `									<li>${cause}</li>`).join('\n');
    const causesRegex = /<ol>\s*<li>Faulty oxygen sensor[\s\S]*?<\/ol>/;
    content = content.replace(causesRegex, `<ol>\n${causesHtml}\n								</ol>`);
  }
  
  // 替换性能影响
  if (data.performanceImpact) {
    const impactRegex = /<strong><i class="fa fa-exclamation-triangle"><\/i> Performance Impact:<\/strong>[\s\S]*?<\/div>/;
    content = content.replace(impactRegex, `<strong><i class="fa fa-exclamation-triangle"></i> Performance Impact:</strong> ${data.performanceImpact}\n							</div>`);
  }
  
  // 替换维修案例
  if (data.caseStudies && data.caseStudies.length >= 2) {
    const case1 = data.caseStudies[0];
    const case2 = data.caseStudies[1];
    
    const caseStudyHtml = `							<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
								<h4>Case 1: ${case1.title}</h4>
								<p><strong>Vehicle:</strong> ${case1.vehicle}</p>
								<p><strong>Symptoms:</strong> ${case1.symptoms}</p>
								<p><strong>Diagnosis:</strong> ${case1.diagnosis}</p>
								<p><strong>Solution:</strong> ${case1.solution}</p>
								<p><strong>Parts:</strong> ${case1.parts}</p>
								<p><strong>Labor:</strong> ${case1.labor}</p>
								<p><strong>Total Cost:</strong> ${case1.total}</p>
							</div>

							<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
								<h4>Case 2: ${case2.title}</h4>
								<p><strong>Vehicle:</strong> ${case2.vehicle}</p>
								<p><strong>Symptoms:</strong> ${case2.symptoms}</p>
								<p><strong>Diagnosis:</strong> ${case2.diagnosis}</p>
								<p><strong>Solution:</strong> ${case2.solution}</p>
								<p><strong>Parts:</strong> ${case2.parts}</p>
								<p><strong>Labor:</strong> ${case2.labor}</p>
								<p><strong>Total Cost:</strong> ${case2.total}</p>
							</div>`;
    
    const caseStudyRegex = /<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">[\s\S]*?<\/div>\s*<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">[\s\S]*?<\/div>/;
    content = content.replace(caseStudyRegex, caseStudyHtml);
  }
  
  // 替换相关代码
  if (data.relatedCodes) {
    const relatedHtml = data.relatedCodes.map(related => 
      `									<li style="margin-bottom: 10px;"><a href="${related.code.toLowerCase()}.html" style="color: #007bff;">${related.code} - ${related.desc}</a></li>`
    ).join('\n');
    
    const relatedRegex = /<li style="margin-bottom: 10px;"><a href="p0140\.html"[\s\S]*?<li style="margin-bottom: 10px;"><a href="p0145\.html"[\s\S]*?<\/li>/;
    content = content.replace(relatedRegex, relatedHtml);
  }
  
  return content;
}

// 批量创建扩展的真实汽车技术故障码页面
const extendedRealCodesToCreate = [
  // EGR和排放控制系统
  { code: 'P1400', database: extendedRealPCodeDatabase },
  { code: 'P1401', database: extendedRealPCodeDatabase },
  // 高级网络通信系统
  { code: 'U0800', database: extendedRealUCodeDatabase },
  // 高级悬挂控制系统
  { code: 'C1300', database: extendedRealCCodeDatabase }
];

let totalCreated = 0;

console.log('🚀 Creating extended real automotive technology DTC pages...\n');

extendedRealCodesToCreate.forEach(({ code, database }) => {
  if (database[code]) {
    try {
      const htmlContent = createDetailedCodePage(code, database[code]);
      fs.writeFileSync(`${code.toLowerCase()}.html`, htmlContent, 'utf8');
      console.log(`✅ Created ${code.toLowerCase()}.html - ${database[code].title}`);
      totalCreated++;
    } catch (error) {
      console.log(`❌ Failed to create ${code.toLowerCase()}.html: ${error.message}`);
    }
  }
});

console.log(`\n🎉 Successfully created ${totalCreated} extended real automotive technology DTC pages!`);
console.log('\n📊 Extended Real Technology Coverage:');
console.log('✅ EGR and Emissions Control Systems (P1400 series)');
console.log('✅ Advanced Network Communication Systems (U0800 series)');
console.log('✅ Advanced Suspension Control Systems (C1300 series)');
console.log('\nExtended real automotive technology coverage complete! 🎯');
