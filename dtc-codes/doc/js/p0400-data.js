const { DTCData } = require('./dtc-template-generator');

// P0400 EGR Flow Malfunction 的完整数据结构
const p0400Data = new DTCData({
  code: 'P0400',
  title: 'EGR Flow Malfunction',
  description: 'The Engine Control Module has detected a malfunction in the Exhaust Gas Recirculation (EGR) system flow.',
  definition: 'The Engine Control Module has detected a malfunction in the Exhaust Gas Recirculation (EGR) system flow. The EGR system recirculates a portion of exhaust gases back into the intake manifold to reduce combustion temperatures and NOx emissions. P0400 indicates the ECM has detected insufficient EGR flow, excessive EGR flow, or no EGR flow when commanded. This can be caused by mechanical problems with the EGR valve, blocked passages, or control system issues.',
  
  symptoms: [
    'Check engine light illuminated - ECM detected EGR system flow problem',
    'Failed emissions test - Increased NOx emissions from poor EGR operation',
    'Engine knocking or pinging - Higher combustion temperatures without EGR',
    'Rough idle - Incorrect EGR flow affecting idle stability',
    'Engine hesitation - Poor EGR control affecting performance',
    'Increased fuel consumption - Engine working harder due to higher combustion temperatures',
    'Engine overheating - Reduced cooling effect from EGR system',
    'Poor acceleration - EGR problems affecting engine performance',
    'Engine stalling - Excessive or insufficient EGR flow disrupting combustion'
  ],
  
  causes: [
    'Stuck EGR valve - Valve stuck open or closed preventing proper flow control',
    'Clogged EGR passages - Carbon buildup blocking exhaust gas flow',
    'Faulty EGR valve position sensor - Incorrect feedback to ECM',
    'Vacuum leak in EGR system - Affecting vacuum-operated EGR valves',
    'Faulty EGR solenoid - Electronic control valve not operating properly',
    'Blocked EGR cooler - Restricted flow through EGR cooling system',
    'Damaged EGR pipe - Cracked or blocked EGR pipe preventing flow',
    'ECM software issues - Control module not properly commanding EGR operation'
  ],
  
  performanceImpact: 'P0400 causes increased NOx emissions, potential engine knocking, poor fuel economy, and failed emissions tests. The engine may run hotter and experience reduced performance due to improper EGR system operation.',
  
  quickAnswer: {
    icon: 'recycle',
    meaning: 'EGR system not flowing exhaust gases properly - usually stuck valve or clogged passages.',
    fix: 'Clean EGR valve and passages, replace EGR valve if needed, check vacuum lines',
    cost: '$180-$650',
    time: '90-180 minutes',
    drivingSafety: 'Safe to drive but may fail emissions test and experience poor performance. Clean EGR system to restore proper operation.'
  },
  
  aiQuestions: [
    {
      question: 'What does the EGR system do?',
      answer: 'The EGR system recirculates exhaust gases back into the intake to reduce combustion temperatures and NOx emissions. Lower combustion temperatures reduce the formation of nitrogen oxides, which are harmful pollutants.'
    },
    {
      question: 'Can I drive with P0400?',
      answer: 'Yes, you can drive with P0400, but the engine may knock, run hot, and have poor performance. You\'ll likely fail emissions testing. The main concern is increased NOx emissions and potential engine damage from higher combustion temperatures.'
    },
    {
      question: 'How do I clean the EGR system?',
      answer: 'Remove the EGR valve and clean carbon deposits with carburetor cleaner. Clean EGR passages in the intake manifold. Use GeekOBD APP to command EGR valve operation and verify it moves freely after cleaning.'
    },
    {
      question: 'Why does the EGR valve get clogged?',
      answer: 'EGR valves get clogged with carbon deposits from exhaust gases. This is normal wear, but poor fuel quality, short trips, and infrequent highway driving can accelerate carbon buildup. Regular EGR cleaning every 60-80k miles helps prevent problems.'
    }
  ],

  costAnalysis: {
    averageCost: '$180-$650 for most P0400 repairs',
    repairOptions: [
      {
        title: 'EGR System Cleaning',
        description: 'Clean EGR valve and passages (60% success rate)',
        color: '#4CAF50',
        icon: 'refresh',
        items: [
          { name: 'EGR cleaner and gaskets', cost: '$25-$50' },
          { name: 'Labor (1.5-2.5 hours)', cost: '$150-$300' }
        ],
        total: '$175-$350',
        successRate: '60% success rate'
      },
      {
        title: 'EGR Valve Replacement',
        description: 'Replace faulty EGR valve (35% of cases)',
        color: '#2196F3',
        icon: 'recycle',
        items: [
          { name: 'EGR valve', cost: '$150-$400' },
          { name: 'Labor (1-2 hours)', cost: '$100-$240' }
        ],
        total: '$250-$640',
        successRate: '90% success rate'
      },
      {
        title: 'EGR System Overhaul',
        description: 'Replace valve, clean passages, repair vacuum lines (5% of cases)',
        color: '#FF9800',
        icon: 'wrench',
        items: [
          { name: 'EGR valve and components', cost: '$200-$500' },
          { name: 'Labor (2-4 hours)', cost: '$200-$480' }
        ],
        total: '$400-$980',
        successRate: '95% success rate'
      }
    ],
    savingTips: [
      'Try EGR cleaning first - fixes 60% of P0400 cases for under $200',
      'Use GeekOBD APP to test EGR valve operation before replacement',
      'Check vacuum lines and connections before replacing expensive components',
      'Regular EGR cleaning every 60-80k miles prevents major problems',
      'Address P0400 before emissions testing to avoid test failure'
    ]
  },

  diagnosticSteps: {
    estimatedTime: 'PT120M',
    steps: [
      {
        title: 'Test EGR Valve Operation',
        icon: 'play',
        description: 'Connect GeekOBD APP and command EGR valve operation. Monitor EGR valve position sensor to verify valve moves when commanded.',
        geekobdTip: 'GeekOBD APP can command EGR valve open/closed and show position feedback - valve should move smoothly and reach commanded positions.'
      },
      {
        title: 'Check EGR Flow',
        icon: 'tachometer',
        description: 'Monitor engine RPM while commanding EGR valve open at idle. RPM should drop or engine should stall if EGR is flowing properly.',
        geekobdTip: 'Use GeekOBD APP to monitor RPM during EGR test - significant RPM drop indicates good EGR flow, no change suggests blocked passages.'
      },
      {
        title: 'Visual Inspection',
        icon: 'eye',
        description: 'Remove EGR valve and inspect for carbon buildup, stuck valve, or damaged components. Check EGR passages in intake manifold for blockage.',
        geekobdTip: 'Monitor EGR position with GeekOBD APP while manually moving valve - should show position changes if sensor is working properly.'
      },
      {
        title: 'Check Vacuum System',
        icon: 'compress',
        description: 'Test vacuum lines, EGR solenoid, and vacuum-operated components for proper operation. Check for vacuum leaks affecting EGR control.',
        geekobdTip: 'GeekOBD APP can show EGR solenoid commands - compare commanded vs actual operation to identify control problems.'
      },
      {
        title: 'Clean or Replace Components',
        icon: 'check-circle',
        description: 'Clean EGR valve and passages or replace faulty components as needed. Clear codes and verify EGR system operates properly.',
        geekobdTip: 'Use GeekOBD APP to verify EGR valve responds properly to commands and position sensor provides accurate feedback after service.'
      }
    ],
    importantNotes: [
      'EGR cleaning often resolves P0400 without expensive parts replacement',
      'Test EGR valve operation with scan tool before replacement',
      'Check for vacuum leaks that can affect EGR operation'
    ]
  },

  caseStudies: [
    {
      title: 'Honda Accord EGR Valve Cleaning',
      vehicle: '2016 Honda Accord 2.4L 4-cylinder, 135,000 miles',
      problem: 'Customer failed emissions test with P0400 code. Engine had occasional knocking during acceleration and poor fuel economy.',
      diagnosis: 'GeekOBD APP showed EGR valve would not open when commanded. Visual inspection revealed EGR valve was stuck closed due to heavy carbon buildup.',
      solution: 'Removed and thoroughly cleaned EGR valve with carburetor cleaner. Also cleaned EGR passages in intake manifold and replaced EGR valve gasket.',
      cost: 'EGR cleaning kit: $35, Gasket: $15, Labor: $180, Total: $230',
      result: 'P0400 code cleared and EGR valve now operates properly. Engine passed emissions test and knocking eliminated. Fuel economy improved by 2 MPG.'
    },
    {
      title: 'Ford F-150 EGR Valve Replacement',
      vehicle: '2017 Ford F-150 3.5L V6, 98,000 miles',
      problem: 'P0400 code with rough idle and engine hesitation. EGR valve had been cleaned previously but code returned.',
      diagnosis: 'EGR valve moved when commanded but GeekOBD APP showed position sensor readings were erratic. EGR valve position sensor had failed internally.',
      solution: 'Replaced complete EGR valve assembly with integrated position sensor. Previous cleaning had not addressed the faulty position sensor.',
      cost: 'EGR valve assembly: $285, Labor: $150, Total: $435',
      result: 'P0400 code cleared permanently. EGR position sensor now provides accurate feedback and engine idle is smooth. No more hesitation issues.'
    }
  ],

  relatedCodes: [
    { code: 'P0401', description: 'EGR Flow Insufficient - Not enough EGR flow detected', color: '#3498db' },
    { code: 'P0402', description: 'EGR Flow Excessive - Too much EGR flow detected', color: '#e74c3c' },
    { code: 'P0403', description: 'EGR Circuit Malfunction - Electrical control problems', color: '#f39c12' },
    { code: 'P0404', description: 'EGR Position Sensor Range/Performance - Position sensor issues', color: '#9b59b6' },
    { code: 'P0405', description: 'EGR Position Sensor Low - Position sensor voltage too low', color: '#4a90e2' },
    { code: 'P0406', description: 'EGR Position Sensor High - Position sensor voltage too high', color: '#e67e22' },
    { code: 'P0171', description: 'System Too Lean - Can be affected by EGR problems', color: '#27ae60' }
  ],

  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P0400',
      description: 'Use GeekOBD APP for comprehensive EGR system testing!',
      features: [
        'EGR valve command testing',
        'Position sensor monitoring',
        'Flow verification',
        'System performance analysis'
      ]
    },
    systemCodes: {
      title: 'EGR System Codes',
      description: 'Related exhaust gas recirculation codes:'
    },
    diagnosticResources: [
      {
        title: 'EGR System Testing',
        description: 'Professional procedures for testing EGR system operation',
        icon: 'recycle',
        url: '#diagnostic-steps'
      },
      {
        title: 'EGR Valve Cleaning',
        description: 'Step-by-step EGR valve cleaning procedures',
        icon: 'refresh',
        url: '../resources/egr-valve-cleaning.html'
      },
      {
        title: 'Emissions System Guide',
        description: 'Understanding automotive emissions control systems',
        icon: 'leaf',
        url: '../resources/emissions-system-guide.html'
      },
      {
        title: 'NOx Reduction Systems',
        description: 'How EGR and other systems reduce nitrogen oxide emissions',
        icon: 'filter',
        url: '../resources/nox-reduction-systems.html'
      }
    ],
    codeInfo: {
      system: 'Emissions Control',
      severity: 'MEDIUM',
      category: 'EGR System'
    }
  }
});

module.exports = p0400Data;
