const { DTCData } = require('./dtc-template-generator');

// P0113 IAT Sensor High Input 的完整数据结构
const p0113Data = new DTCData({
  code: 'P0113',
  title: 'IAT Sensor High Input',
  description: 'The Engine Control Module has detected that the Intake Air Temperature sensor is reading extremely cold temperatures when actual air temperature is warmer.',
  definition: 'The Engine Control Module has detected that the Intake Air Temperature (IAT) sensor is producing readings that indicate extremely cold air temperatures (typically -40°F or lower) when the actual intake air temperature should be much warmer. This sensor measures the temperature of air entering the engine to help the ECM calculate proper fuel injection timing and quantity. When the sensor reads too cold, it can cause rich fuel mixture, poor performance, and increased emissions.',
  
  symptoms: [
    'Check engine light illuminated - IAT sensor fault detected',
    'Poor engine performance - Rich fuel mixture from cold air reading',
    'Increased fuel consumption - ECM compensating for perceived cold air',
    'Rough idle - Incorrect air/fuel mixture calculations',
    'Black smoke from exhaust - Rich fuel condition',
    'Engine hesitation during acceleration - Fuel delivery issues',
    'Hard starting in warm weather - Incorrect fuel mixture',
    'Failed emissions test - Rich exhaust conditions'
  ],
  
  causes: [
    'Faulty IAT sensor - Internal component failure causing high resistance',
    'Open circuit in IAT sensor wiring - Broken wire or poor connection',
    'Corroded IAT sensor connector - Poor electrical contact',
    'IAT sensor contamination - Oil, dirt, or debris affecting readings',
    'Damaged IAT sensor housing - Physical damage to sensor element',
    'ECM internal fault - Module misreading sensor signal',
    'Wiring harness damage - Chafing or rodent damage to wires',
    'Poor ground connection - Inadequate sensor ground circuit'
  ],
  
  performanceImpact: 'P0113 causes the ECM to receive incorrect air temperature data, leading to overly rich fuel mixture, reduced fuel economy, poor engine performance, increased emissions, and potential engine damage from running too rich.',
  
  quickAnswer: {
    icon: 'thermometer-empty',
    meaning: 'IAT sensor reading extremely cold temperatures (-40°F) when actual air is warmer - usually sensor failure.',
    fix: 'Replace IAT sensor or repair wiring',
    cost: '$85-$320',
    time: '30-60 minutes',
    drivingSafety: 'Safe to drive short distances, but expect poor performance and increased fuel consumption. Repair soon to prevent engine damage.'
  },
  
  aiQuestions: [
    {
      question: 'What\'s the difference between P0113 and P0112 IAT sensor codes?',
      answer: 'P0113 indicates the IAT sensor is reading too cold (high input voltage), while P0112 indicates too hot readings (low input voltage). P0113 typically means an open circuit or failed sensor, while P0112 usually indicates a short circuit or sensor reading actual high temperatures.'
    },
    {
      question: 'What causes an IAT sensor to read -40°F constantly?',
      answer: 'A constant -40°F reading typically indicates an open circuit in the sensor or wiring. When the ECM loses signal from the IAT sensor, it defaults to -40°F as a failsafe value. This is usually caused by a failed sensor, broken wire, or corroded connector.'
    },
    {
      question: 'Can a dirty IAT sensor cause P0113?',
      answer: 'Yes, but it\'s less common. Heavy contamination with oil, dirt, or carbon deposits can insulate the sensor element and cause erratic readings. However, P0113 more commonly results from complete sensor failure or wiring issues rather than contamination.'
    },
    {
      question: 'How do I test an IAT sensor with a multimeter?',
      answer: 'Disconnect the sensor and measure resistance across the sensor terminals. At 68°F, resistance should be around 2,500 ohms. As temperature increases, resistance decreases. If you get infinite resistance or no reading, the sensor has failed. GeekOBD APP can also monitor live IAT readings for easier diagnosis.'
    }
  ],
  
  costAnalysis: {
    averageCost: '200',
    repairOptions: [
      {
        title: 'IAT Sensor Replacement',
        description: 'Most common and effective repair for P0113. Replace the faulty sensor with OEM or quality aftermarket part.',
        color: '#4CAF50',
        icon: 'thermometer-empty',
        items: [
          { name: 'IAT Sensor', cost: '$45-$120' },
          { name: 'Labor (0.5-1 hr)', cost: '$75-$100' }
        ],
        total: '$120-$220',
        successRate: '95'
      },
      {
        title: 'Wiring Repair',
        description: 'When the sensor is good but wiring is damaged. Requires locating and repairing the damaged wire.',
        color: '#FF9800',
        icon: 'wrench',
        items: [
          { name: 'Wiring repair kit', cost: '$25-$45' },
          { name: 'Labor (1-2 hrs)', cost: '$60-$135' }
        ],
        total: '$85-$180',
        successRate: '90'
      },
      {
        title: 'Harness Replacement',
        description: 'When multiple wires are damaged or connector is severely corroded. Most comprehensive but expensive option.',
        color: '#f44336',
        icon: 'exchange',
        items: [
          { name: 'Wiring harness', cost: '$150-$320' },
          { name: 'Labor (2-3 hrs)', cost: '$100-$200' }
        ],
        total: '$250-$520',
        successRate: '98'
      }
    ],
    savingTips: [
      'Clean IAT sensor with MAF cleaner before replacing - may resolve contamination issues',
      'Check connector for corrosion first - cleaning may fix the problem',
      'Use GeekOBD APP to verify sensor readings before and after repair',
      'Consider aftermarket sensors for older vehicles to save 30-50% on parts',
      'Inspect entire air intake system while sensor is removed'
    ]
  },
  
  diagnosticSteps: {
    estimatedTime: 'PT30M',
    steps: [
      {
        title: 'Initial Code Verification',
        icon: 'search',
        description: 'Connect GeekOBD APP and verify P0113 code is present. Check for additional codes that may indicate related issues. Clear codes and test drive to see if P0113 returns immediately.',
        geekobdTip: 'Use GeekOBD APP\'s live data feature to monitor IAT sensor readings in real-time. A constant -40°F reading confirms the diagnosis.'
      },
      {
        title: 'Visual Inspection',
        icon: 'eye',
        description: 'Locate IAT sensor (usually in air intake tube or MAF housing). Inspect sensor, connector, and wiring for obvious damage, corrosion, or contamination. Check for loose connections.',
        geekobdTip: 'Take photos with GeekOBD APP to document sensor condition and location for future reference.'
      },
      {
        title: 'Sensor Resistance Test',
        icon: 'bolt',
        description: 'Disconnect IAT sensor connector and measure resistance across sensor terminals with multimeter. At 68°F, resistance should be approximately 2,500 ohms. Infinite resistance indicates sensor failure.',
        geekobdTip: 'Compare your readings with GeekOBD APP\'s sensor specifications database for your specific vehicle.'
      },
      {
        title: 'Wiring Circuit Test',
        icon: 'random',
        description: 'If sensor tests good, check wiring continuity from sensor connector to ECM. Test for proper voltage supply (usually 5V) and ground circuit integrity.',
        geekobdTip: 'GeekOBD APP can display expected voltage values for your vehicle\'s IAT circuit during testing.'
      },
      {
        title: 'Repair and Verification',
        icon: 'check',
        description: 'Replace faulty sensor or repair damaged wiring. Clear codes with GeekOBD APP and test drive vehicle. Monitor IAT readings to ensure proper operation and verify code does not return.',
        geekobdTip: 'Use GeekOBD APP\'s post-repair monitoring feature to track IAT sensor performance over several drive cycles.'
      }
    ],
    importantNotes: [
      'Always disconnect battery before working on sensor connections',
      'Use only specified torque when installing new sensor to avoid damage',
      'Apply dielectric grease to connector to prevent future corrosion',
      'Verify code does not return after several drive cycles'
    ]
  },
  
  caseStudies: [
    {
      title: '2017 Honda Civic - Faulty IAT Sensor',
      vehicle: '2017 Honda Civic LX 1.5L Turbo, 89,000 miles',
      problem: 'Customer complained of hard starting in cold weather and poor fuel economy. GeekOBD scan revealed P0113 code with IAT sensor reading constant -40°F even when engine bay was warm.',
      diagnosis: 'GeekOBD APP monitoring showed IAT sensor stuck at -40°F regardless of actual temperature. Resistance test confirmed sensor internal failure - infinite resistance instead of expected 2,500 ohms at 68°F.',
      solution: 'Replaced IAT sensor with OEM Honda part. Sensor was integrated into air intake tube, requiring tube replacement. Cleared codes and verified proper temperature tracking.',
      cost: '$185 (IAT sensor/tube: $95, labor: $90)',
      result: 'IAT sensor now reads accurate temperatures. Cold starting improved immediately and fuel economy returned to normal. Customer saved $200+ by avoiding unnecessary MAF sensor replacement.'
    },
    {
      title: '2018 Toyota Camry - Wiring Issue',
      vehicle: '2018 Toyota Camry LE 2.5L 4-cylinder, 65,000 miles',
      problem: 'Intermittent P0113 code with occasional rough idle. IAT readings would jump between normal and -40°F during driving.',
      diagnosis: 'GeekOBD APP data logging revealed intermittent signal loss. IAT sensor tested good with proper resistance. Found damaged signal wire in harness near battery tray from acid corrosion.',
      solution: 'Repaired damaged signal wire with proper splice and heat shrink protection. Applied dielectric grease to connector to prevent future corrosion.',
      cost: '$125 (wire repair kit: $25, labor: $100)',
      result: 'IAT sensor now provides accurate readings. Engine performance returned to normal and customer saved $150+ by avoiding unnecessary sensor replacement.'
    }
  ],
  
  relatedCodes: [
    { code: 'P0112', description: 'IAT Sensor Low Input', color: '#4a90e2' },
    { code: 'P0114', description: 'IAT Sensor Intermittent', color: '#28a745' },
    { code: 'P0110', description: 'IAT Sensor Circuit', color: '#ffc107' },
    { code: 'P0101', description: 'MAF Sensor Range/Performance', color: '#dc3545' },
    { code: 'P0102', description: 'MAF Sensor Low Input', color: '#6f42c1' },
    { code: 'P0103', description: 'MAF Sensor High Input', color: '#17a2b8' },
    { code: 'P0171', description: 'System Too Lean Bank 1', color: '#fd7e14' },
    { code: 'P0172', description: 'System Too Rich Bank 1', color: '#20c997' }
  ],
  
  sidebarData: {
    appPromo: {
      icon: 'thermometer-empty',
      title: 'Monitor IAT Sensor',
      description: 'Track intake air temperature with our GeekOBD APP!',
      features: [
        'Real-time IAT readings',
        'Temperature trend monitoring',
        'Sensor response testing',
        'Verify repair success'
      ]
    },
    systemCodes: {
      title: 'IAT System Codes',
      description: 'Related intake air temperature sensor diagnostic codes:'
    },
    diagnosticResources: [
      {
        title: 'IAT Sensor Testing Guide',
        description: 'Step-by-step sensor testing procedures',
        icon: 'book',
        url: '#diagnostic-steps'
      },
      {
        title: 'Wiring Diagrams',
        description: 'IAT sensor circuit diagrams and pinouts',
        icon: 'sitemap',
        url: '../resources/wiring-diagrams.html'
      },
      {
        title: 'Repair Procedures',
        description: 'Professional repair techniques and tips',
        icon: 'wrench',
        url: '../resources/repair-procedures.html'
      }
    ],
    codeInfo: {
      system: 'Engine Management',
      severity: 'MEDIUM',
      category: 'Sensor Circuit'
    }
  }
});

module.exports = p0113Data;
