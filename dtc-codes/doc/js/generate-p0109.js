const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0109Data = require('./p0109-data');

console.log('🚀 Generating P0109 - MAP Sensor Intermittent Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0109Data);
  const outputPath = path.join(__dirname, '../../p0109.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0109 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0109 page:', error.message);
  process.exit(1);
}
