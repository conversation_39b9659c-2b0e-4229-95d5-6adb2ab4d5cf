const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p1150Data = require('./p1150-data');

console.log('🚀 Generating P1150 - Lack of Downstream Heated Oxygen Sensor Switch - Sensor Indicates Lean Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p1150Data);
  const outputPath = path.join(__dirname, '../../p1150.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P1150 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P1150 page:', error.message);
  process.exit(1);
}
