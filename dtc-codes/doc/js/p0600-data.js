const { DTCData } = require('./dtc-template-generator');

// P0600 Serial Communication Link Malfunction 的完整数据结构
const p0600Data = new DTCData({
  code: 'P0600',
  title: 'Serial Communication Link Malfunction',
  description: 'The Engine Control Module has detected a malfunction in the serial communication link.',
  definition: 'The Engine Control Module has detected a malfunction in the serial communication link. Modern vehicles use various communication networks (CAN bus, LIN bus, etc.) to allow different control modules to communicate with each other. When the ECM detects problems with these communication links, such as missing messages, corrupted data, or network failures, P0600 is triggered. This can affect multiple vehicle systems that rely on inter-module communication.',
  
  symptoms: [
    'Check engine light illuminated - ECM detected communication link malfunction',
    'Multiple warning lights - Various systems affected by communication failure',
    'Intermittent electrical problems - Systems randomly not working properly',
    'Scan tool communication issues - Difficulty connecting to vehicle modules',
    'Transmission shifting problems - Communication issues affecting shift control',
    'ABS/traction control problems - Safety systems affected by network issues',
    'Instrument cluster malfunctions - Gauges or displays not working correctly',
    'Engine performance issues - ECM not receiving data from other modules',
    'Air conditioning problems - HVAC system communication affected'
  ],
  
  causes: [
    'Faulty ECM - Internal communication circuit failure in control module',
    'Damaged CAN bus wiring - Broken or shorted communication wires',
    'Corroded communication connectors - Poor electrical contact affecting data transmission',
    'Failed communication module - Other control modules not responding properly',
    'Low system voltage - Insufficient power affecting communication circuits',
    'Electromagnetic interference - External interference disrupting communication',
    'Software corruption - ECM software problems affecting communication protocols',
    'Ground circuit problems - Poor grounds affecting communication networks'
  ],
  
  performanceImpact: 'P0600 can cause widespread vehicle system malfunctions, intermittent electrical problems, poor engine performance, and potential safety system failures due to communication network disruption.',
  
  quickAnswer: {
    icon: 'wifi',
    meaning: 'Communication network problem between vehicle control modules - usually wiring or ECM issue.',
    fix: 'Check communication wiring, test system voltage, scan all modules, may need ECM replacement',
    cost: '$200-$1200',
    time: '120-300 minutes',
    drivingSafety: 'May be unsafe to drive if safety systems affected. Have diagnosed immediately as multiple systems may malfunction.'
  },
  
  aiQuestions: [
    {
      question: 'What is a serial communication link in cars?',
      answer: 'Serial communication links are data networks (like CAN bus) that allow different control modules in the vehicle to share information. The ECM, transmission control module, ABS module, and others communicate through these networks to coordinate vehicle operation.'
    },
    {
      question: 'Can P0600 cause other systems to malfunction?',
      answer: 'Yes, P0600 can cause widespread system malfunctions because many vehicle systems depend on communication between modules. Transmission, ABS, air conditioning, instrument cluster, and other systems may not work properly without reliable communication.'
    },
    {
      question: 'How do I diagnose communication problems?',
      answer: 'Use GeekOBD APP to scan all vehicle modules and check for communication errors. Professional diagnosis often requires specialized equipment to test communication networks and identify which modules or wiring sections have failed.'
    },
    {
      question: 'Can low battery voltage cause P0600?',
      answer: 'Yes, low system voltage can cause P0600 because communication networks require stable power to operate properly. Weak batteries, failing alternators, or poor electrical connections can disrupt communication and trigger this code.'
    }
  ],

  costAnalysis: {
    averageCost: '$200-$1200 for most P0600 repairs',
    repairOptions: [
      {
        title: 'Communication Wiring Repair',
        description: 'Fix damaged CAN bus or communication wiring (40% of cases)',
        color: '#4CAF50',
        icon: 'flash',
        items: [
          { name: 'Communication wiring repair', cost: '$50-$150' },
          { name: 'Diagnostic time', cost: '$150-$300' },
          { name: 'Labor (2-4 hours)', cost: '$200-$480' }
        ],
        total: '$400-$930',
        successRate: '85% success rate'
      },
      {
        title: 'ECM Replacement',
        description: 'Replace failed Engine Control Module (35% of cases)',
        color: '#FF9800',
        icon: 'microchip',
        items: [
          { name: 'ECM (remanufactured)', cost: '$400-$800' },
          { name: 'Programming', cost: '$100-$200' },
          { name: 'Labor (2-3 hours)', cost: '$200-$360' }
        ],
        total: '$700-$1360',
        successRate: '95% success rate'
      },
      {
        title: 'Module Replacement',
        description: 'Replace other failed communication modules (25% of cases)',
        color: '#2196F3',
        icon: 'wifi',
        items: [
          { name: 'Control module', cost: '$200-$600' },
          { name: 'Programming', cost: '$50-$150' },
          { name: 'Labor (1-2 hours)', cost: '$100-$240' }
        ],
        total: '$350-$990',
        successRate: '90% success rate'
      }
    ],
    savingTips: [
      'Check battery and charging system first - low voltage can cause communication issues',
      'Scan all modules to identify which systems are affected',
      'Communication problems often require professional diagnosis with specialized equipment',
      'Consider remanufactured ECM to save costs if replacement needed',
      'Address P0600 promptly as multiple vehicle systems may be affected'
    ]
  },

  diagnosticSteps: {
    estimatedTime: 'PT180M',
    steps: [
      {
        title: 'Scan All Vehicle Modules',
        icon: 'search',
        description: 'Connect GeekOBD APP and scan all available vehicle modules to identify which systems are affected by communication problems.',
        geekobdTip: 'GeekOBD APP can show which modules are communicating properly - missing or non-responsive modules indicate communication network problems.'
      },
      {
        title: 'Check System Voltage',
        icon: 'battery',
        description: 'Test battery voltage, charging system, and power supply to communication networks. Low voltage can cause communication failures.',
        geekobdTip: 'Monitor system voltage with GeekOBD APP during testing - voltage should remain stable above 12V with engine running above 13.5V.'
      },
      {
        title: 'Test Communication Networks',
        icon: 'wifi',
        description: 'Use specialized equipment to test CAN bus and other communication networks for proper signal levels and data integrity.',
        geekobdTip: 'GeekOBD APP communication status can indicate network health - frequent communication errors suggest wiring or module problems.'
      },
      {
        title: 'Inspect Communication Wiring',
        icon: 'eye',
        description: 'Visual inspection of communication wiring harnesses, connectors, and modules for damage, corrosion, or loose connections.',
        geekobdTip: 'Monitor communication status with GeekOBD APP while wiggling wires - intermittent communication indicates wiring problems.'
      },
      {
        title: 'Module Replacement and Programming',
        icon: 'check-circle',
        description: 'Replace faulty modules or repair wiring as diagnosed. Program new modules and verify all systems communicate properly.',
        geekobdTip: 'Use GeekOBD APP to verify all modules now communicate properly and no communication error codes remain after repairs.'
      }
    ],
    importantNotes: [
      'P0600 often requires professional diagnosis with specialized equipment',
      'Multiple vehicle systems may be affected by communication problems',
      'Check system voltage first as low voltage commonly causes communication issues'
    ]
  },

  caseStudies: [
    {
      title: 'Toyota Prius Communication Network Failure',
      vehicle: '2016 Toyota Prius 1.8L Hybrid, 125,000 miles',
      problem: 'Customer reported multiple warning lights, intermittent electrical problems, and P0600 code. Scan tool had difficulty communicating with some modules.',
      diagnosis: 'GeekOBD APP showed several modules were not responding. Found CAN bus wiring had been damaged by rodents, causing network communication failure.',
      solution: 'Repaired chewed CAN bus wiring and installed protective conduit. Also cleaned corroded connectors that had been exposed to moisture.',
      cost: 'CAN bus wiring repair: $85, Protective conduit: $35, Labor: $280, Total: $400',
      result: 'P0600 code cleared and all modules now communicate properly. All warning lights turned off and electrical systems function normally.'
    },
    {
      title: 'Ford F-150 ECM Communication Failure',
      vehicle: '2017 Ford F-150 3.5L V6, 89,000 miles',
      problem: 'P0600 code with transmission shifting problems and instrument cluster malfunctions. Multiple systems not working properly.',
      diagnosis: 'All wiring tested good, but ECM was not communicating properly with other modules. ECM had internal communication circuit failure.',
      solution: 'Replaced ECM with remanufactured unit and programmed with vehicle-specific software. All communication networks restored.',
      cost: 'Remanufactured ECM: $650, Programming: $150, Labor: $240, Total: $1040',
      result: 'P0600 code cleared permanently. All vehicle systems now function properly and communication between modules is restored.'
    }
  ],

  relatedCodes: [
    { code: 'P0601', description: 'Internal Control Module Memory Check Sum Error - ECM memory problems', color: '#e74c3c' },
    { code: 'P0602', description: 'Control Module Programming Error - ECM programming issues', color: '#3498db' },
    { code: 'P0603', description: 'Internal Control Module Keep Alive Memory Error - ECM memory problems', color: '#f39c12' },
    { code: 'P0604', description: 'Internal Control Module Random Access Memory Error - ECM RAM problems', color: '#9b59b6' },
    { code: 'U0100', description: 'Lost Communication with ECM/PCM - Communication network problems', color: '#4a90e2' },
    { code: 'U0101', description: 'Lost Communication with TCM - Transmission communication problems', color: '#e67e22' },
    { code: 'B1000', description: 'ECM/PCM Malfunction - General ECM problems', color: '#27ae60' }
  ],

  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P0600',
      description: 'Use GeekOBD APP for communication network testing!',
      features: [
        'Multi-module scanning',
        'Communication status monitoring',
        'Network health analysis',
        'Module response verification'
      ]
    },
    systemCodes: {
      title: 'Communication Codes',
      description: 'Related vehicle communication network codes:'
    },
    diagnosticResources: [
      {
        title: 'Communication Network Testing',
        description: 'Professional procedures for testing vehicle communication networks',
        icon: 'wifi',
        url: '#diagnostic-steps'
      },
      {
        title: 'ECM Diagnostics',
        description: 'Advanced ECM testing and replacement procedures',
        icon: 'microchip',
        url: '../resources/ecm-diagnostics.html'
      },
      {
        title: 'CAN Bus Systems',
        description: 'Understanding Controller Area Network systems',
        icon: 'sitemap',
        url: '../resources/can-bus-systems.html'
      },
      {
        title: 'Electrical System Diagnosis',
        description: 'Advanced electrical system diagnostic techniques',
        icon: 'flash',
        url: '../resources/electrical-system-diagnosis.html'
      }
    ],
    codeInfo: {
      system: 'Engine Management',
      severity: 'HIGH',
      category: 'Communication Network'
    }
  }
});

module.exports = p0600Data;
