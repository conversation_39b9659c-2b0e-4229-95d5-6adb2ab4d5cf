const { DTCData } = require('./dtc-template-generator');

// P0500 Vehicle Speed Sensor Malfunction 的完整数据结构
const p0500Data = new DTCData({
  code: 'P0500',
  title: 'Vehicle Speed Sensor Malfunction',
  description: 'The Engine Control Module has detected a malfunction in the vehicle speed sensor circuit.',
  definition: 'The Engine Control Module has detected a malfunction in the vehicle speed sensor (VSS) circuit. The vehicle speed sensor provides the ECM with information about vehicle speed, which is used for transmission control, cruise control, speedometer operation, and various engine management functions. When the ECM detects no signal, an erratic signal, or an implausible signal from the VSS, P0500 is triggered.',
  
  symptoms: [
    'Check engine light illuminated - ECM detected vehicle speed sensor malfunction',
    'Speedometer not working - No speed signal to instrument cluster',
    'Transmission shifting problems - Harsh shifts or staying in lower gears',
    'Cruise control not working - System requires accurate speed signal',
    'ABS warning light - Anti-lock brake system affected by speed sensor',
    'Traction control problems - System needs speed information to function',
    'Poor fuel economy - Transmission not shifting optimally',
    'Engine stalling at stops - Idle control affected by speed sensor',
    'Odometer not working - No distance calculation without speed signal'
  ],
  
  causes: [
    'Faulty vehicle speed sensor - Internal sensor failure preventing signal generation',
    'Damaged VSS wiring - Broken or corroded wires affecting signal transmission',
    'Corroded VSS connector - Poor electrical contact affecting signal quality',
    'Faulty transmission output shaft - Mechanical problems affecting sensor operation',
    'ECM internal fault - Control module unable to process speed sensor signals',
    'Damaged reluctor ring - Missing or damaged teeth affecting sensor reading',
    'Contamination on sensor - Oil, debris, or metal particles affecting operation',
    'Incorrect VSS installation - Improper air gap or mounting affecting signal'
  ],
  
  performanceImpact: 'P0500 affects transmission operation, cruise control, speedometer function, and various safety systems that rely on vehicle speed information, potentially causing poor shifting, reduced fuel economy, and loss of important vehicle functions.',
  
  quickAnswer: {
    icon: 'tachometer',
    meaning: 'Vehicle speed sensor not providing proper speed signal - usually failed sensor or wiring problem.',
    fix: 'Check VSS wiring, test sensor signal, replace speed sensor if needed',
    cost: '$150-$450',
    time: '60-150 minutes',
    drivingSafety: 'Safe to drive but transmission may shift poorly and speedometer won\'t work. Repair promptly for proper vehicle operation.'
  },
  
  aiQuestions: [
    {
      question: 'Can I drive without a working vehicle speed sensor?',
      answer: 'You can drive with P0500, but the transmission may shift harshly or stay in lower gears, cruise control won\'t work, and the speedometer will be inaccurate. Drive carefully and repair promptly to restore normal operation.'
    },
    {
      question: 'Why does my transmission shift hard with P0500?',
      answer: 'The transmission uses vehicle speed information to determine proper shift points and shift firmness. Without accurate speed data, the transmission may shift at incorrect times or with inappropriate firmness, causing harsh or delayed shifts.'
    },
    {
      question: 'How do I test the vehicle speed sensor?',
      answer: 'Use GeekOBD APP to monitor vehicle speed sensor signal while driving. You can also test sensor resistance and check for proper voltage signal. The sensor should generate a pulsing signal that increases with vehicle speed.'
    },
    {
      question: 'Can a bad speed sensor affect fuel economy?',
      answer: 'Yes, P0500 can affect fuel economy because the transmission may not shift optimally without proper speed information. The engine may run at higher RPMs than necessary, and various engine management systems may not operate efficiently.'
    }
  ],

  costAnalysis: {
    averageCost: '$150-$450 for most P0500 repairs',
    repairOptions: [
      {
        title: 'Vehicle Speed Sensor Replacement',
        description: 'Replace faulty speed sensor (60% of cases)',
        color: '#4CAF50',
        icon: 'tachometer',
        items: [
          { name: 'Vehicle speed sensor', cost: '$50-$150' },
          { name: 'Labor (1-2 hours)', cost: '$100-$240' }
        ],
        total: '$150-$390',
        successRate: '90% success rate'
      },
      {
        title: 'Wiring Harness Repair',
        description: 'Fix damaged VSS wiring (30% of cases)',
        color: '#FF9800',
        icon: 'flash',
        items: [
          { name: 'Wiring repair materials', cost: '$25-$60' },
          { name: 'Diagnostic time', cost: '$80-$120' },
          { name: 'Labor (1-2 hours)', cost: '$100-$240' }
        ],
        total: '$205-$420',
        successRate: '85% success rate'
      },
      {
        title: 'Connector Service',
        description: 'Clean or replace corroded VSS connector (10% of cases)',
        color: '#2196F3',
        icon: 'plug',
        items: [
          { name: 'Connector cleaning/replacement', cost: '$20-$50' },
          { name: 'Dielectric grease', cost: '$8-$15' },
          { name: 'Labor (30-60 minutes)', cost: '$50-$120' }
        ],
        total: '$78-$185',
        successRate: '80% success rate'
      }
    ],
    savingTips: [
      'Check VSS connector and wiring first - may save expensive sensor replacement',
      'Use GeekOBD APP to verify speed signal before replacing sensor',
      'Some VSS locations are accessible for DIY replacement, saving $100-240 in labor',
      'Clean sensor mounting area to ensure proper operation',
      'Address P0500 promptly to restore transmission and speedometer operation'
    ]
  },

  diagnosticSteps: {
    estimatedTime: 'PT90M',
    steps: [
      {
        title: 'Check Vehicle Speed Signal',
        icon: 'search',
        description: 'Connect GeekOBD APP and monitor vehicle speed sensor signal while driving. Speed reading should match actual vehicle speed.',
        geekobdTip: 'GeekOBD APP can show VSS signal in real-time - compare displayed speed with GPS or known speed to verify sensor accuracy.'
      },
      {
        title: 'Test VSS Electrical Circuit',
        icon: 'bolt',
        description: 'Check VSS power supply, ground, and signal circuits with multimeter. Verify proper voltage and signal generation.',
        geekobdTip: 'Use GeekOBD APP to monitor speed signal while testing circuits - signal should be present and change with wheel rotation.'
      },
      {
        title: 'Visual Inspection',
        icon: 'eye',
        description: 'Inspect VSS sensor, wiring, and connector for damage, corrosion, or contamination. Check sensor mounting and air gap.',
        geekobdTip: 'Monitor VSS signal with GeekOBD APP while wiggling wires - intermittent readings indicate wiring problems.'
      },
      {
        title: 'Check Reluctor Ring',
        icon: 'cog',
        description: 'Inspect transmission output shaft reluctor ring for missing or damaged teeth that could affect sensor reading.',
        geekobdTip: 'GeekOBD APP can show VSS signal pattern - irregular or missing pulses indicate reluctor ring damage.'
      },
      {
        title: 'Component Replacement and Verification',
        icon: 'check-circle',
        description: 'Replace faulty VSS sensor or repair wiring as diagnosed. Clear codes and verify speed signal is accurate.',
        geekobdTip: 'Use GeekOBD APP to verify VSS now provides accurate speed readings that match actual vehicle speed during test drive.'
      }
    ],
    importantNotes: [
      'VSS affects transmission operation, speedometer, and cruise control',
      'Test speed signal accuracy with GeekOBD APP before replacement',
      'Check reluctor ring condition if sensor tests good electrically'
    ]
  },

  caseStudies: [
    {
      title: 'Honda Civic Transmission Issues',
      vehicle: '2016 Honda Civic 1.5L CVT, 118,000 miles',
      problem: 'Customer reported harsh transmission operation, non-working speedometer, and P0500 code. Cruise control also not functioning.',
      diagnosis: 'GeekOBD APP showed no vehicle speed signal. Visual inspection revealed VSS connector was corroded and had poor electrical contact.',
      solution: 'Cleaned corroded VSS connector pins and applied dielectric grease. Also tightened loose connector to ensure proper contact.',
      cost: 'Connector cleaning kit: $18, Dielectric grease: $10, Labor: $75, Total: $103',
      result: 'P0500 code cleared immediately. Speedometer works normally, transmission shifts smoothly, and cruise control restored.'
    },
    {
      title: 'Ford F-150 Speed Sensor Failure',
      vehicle: '2017 Ford F-150 3.5L V6, 95,000 miles',
      problem: 'P0500 code with erratic speedometer readings and transmission staying in lower gears. ABS warning light also present.',
      diagnosis: 'VSS wiring tested good, but GeekOBD APP showed intermittent speed signal. Speed sensor was generating weak and inconsistent signal.',
      solution: 'Replaced vehicle speed sensor located on transmission. Sensor had internal failure causing weak signal generation.',
      cost: 'Vehicle speed sensor: $95, Labor: $125, Total: $220',
      result: 'P0500 code cleared and speedometer now reads accurately. Transmission shifts normally and ABS warning light turned off.'
    }
  ],

  relatedCodes: [
    { code: 'P0501', description: 'Vehicle Speed Sensor Range/Performance - Speed signal out of range', color: '#4a90e2' },
    { code: 'P0502', description: 'Vehicle Speed Sensor Low Input - Speed signal too low', color: '#3498db' },
    { code: 'P0503', description: 'Vehicle Speed Sensor Intermittent - Intermittent speed signal', color: '#f39c12' },
    { code: 'P0700', description: 'Transmission Control System - Related transmission problems', color: '#e74c3c' },
    { code: 'P0715', description: 'Input/Turbine Speed Sensor - Related transmission speed sensor', color: '#9b59b6' },
    { code: 'P0720', description: 'Output Speed Sensor Circuit - Related transmission speed sensor', color: '#e67e22' },
    { code: 'C1200', description: 'ABS Speed Sensor - Related wheel speed sensor problems', color: '#27ae60' }
  ],

  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P0500',
      description: 'Use GeekOBD APP for vehicle speed sensor testing!',
      features: [
        'Real-time speed monitoring',
        'VSS signal verification',
        'Transmission data analysis',
        'Speed accuracy testing'
      ]
    },
    systemCodes: {
      title: 'Speed Sensor Codes',
      description: 'Related vehicle speed sensor codes:'
    },
    diagnosticResources: [
      {
        title: 'VSS Testing Procedures',
        description: 'Professional procedures for testing vehicle speed sensors',
        icon: 'tachometer',
        url: '#diagnostic-steps'
      },
      {
        title: 'Transmission Diagnostics',
        description: 'Understanding transmission control and speed sensors',
        icon: 'cogs',
        url: '../resources/transmission-diagnostics.html'
      },
      {
        title: 'Speedometer Problems',
        description: 'Diagnosing and fixing speedometer issues',
        icon: 'dashboard',
        url: '../resources/speedometer-problems.html'
      },
      {
        title: 'Cruise Control Systems',
        description: 'Understanding cruise control operation and diagnosis',
        icon: 'road',
        url: '../resources/cruise-control-systems.html'
      }
    ],
    codeInfo: {
      system: 'Transmission/Engine Management',
      severity: 'MEDIUM',
      category: 'Speed Sensor'
    }
  }
});

module.exports = p0500Data;
