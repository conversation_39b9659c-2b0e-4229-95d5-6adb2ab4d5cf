const { DTCData } = require('./dtc-template-generator');

// P0105 MAP Sensor Circuit Malfunction 的完整数据结构
const p0105Data = new DTCData({
  code: 'P0105',
  title: 'MAP Sensor Circuit Malfunction',
  description: 'The Engine Control Module has detected an electrical malfunction in the Manifold Absolute Pressure sensor circuit.',
  definition: 'The Engine Control Module has detected an electrical malfunction in the Manifold Absolute Pressure (MAP) sensor circuit. This indicates a problem with the electrical components of the MAP sensor system rather than the sensor readings themselves. The MAP sensor circuit includes the sensor, wiring harness, connector, and ECM connections. P0105 is triggered when there are voltage irregularities, open circuits, short circuits, or other electrical faults in the MAP sensor circuit.',
  
  symptoms: [
    'Check engine light illuminated - ECM detected MAP sensor circuit electrical fault',
    'Engine running in default mode - ECM using backup fuel calculations',
    'Poor engine performance - Reduced power and acceleration',
    'Rough idle or stalling - Incorrect fuel mixture from backup calculations',
    'Hard starting - ECM unable to calculate proper fuel delivery',
    'Poor fuel economy - Non-optimized fuel delivery without MAP data',
    'Engine hesitation - Inconsistent performance under load',
    'Black or white smoke from exhaust - Rich or lean mixture from default settings',
    'Failed emissions test - Engine not operating in optimal parameters'
  ],
  
  causes: [
    'Open circuit in MAP sensor wiring - Broken wire preventing signal transmission',
    'Short circuit in MAP sensor harness - Wire touching ground or power',
    'Faulty MAP sensor connector - Corroded, damaged, or loose connection',
    'Failed MAP sensor - Internal electrical failure in sensor components',
    'ECM internal fault - Control module unable to process MAP signals',
    'Damaged wiring harness - Physical damage from heat, vibration, or rodents',
    'Poor ground connection - Inadequate ground circuit for MAP sensor',
    'Water damage to electrical components - Moisture causing circuit problems'
  ],
  
  performanceImpact: 'P0105 forces the ECM to operate without MAP sensor data, using default fuel and timing maps that are not optimized for current conditions, resulting in poor performance, fuel economy, and emissions.',
  
  quickAnswer: {
    icon: 'flash',
    meaning: 'Electrical problem in MAP sensor circuit - wiring, connector, or sensor electrical failure.',
    fix: 'Check wiring, test connections, replace MAP sensor if needed',
    cost: '$95-$380',
    time: '60-150 minutes',
    drivingSafety: 'Safe to drive but expect reduced performance. Repair soon as engine runs in non-optimal default mode.'
  },
  
  aiQuestions: [
    {
      question: 'What\'s the difference between P0105 and P0106 MAP codes?',
      answer: 'P0105 indicates an electrical circuit problem (wiring, connector, or sensor electrical failure), while P0106 indicates the sensor is working electrically but providing readings outside expected range. P0105 is typically easier to diagnose with electrical testing.'
    },
    {
      question: 'Can water damage cause P0105?',
      answer: 'Yes, water intrusion into the MAP sensor connector or wiring harness can cause P0105 by creating short circuits or corrosion. This is common after driving through deep water, flooding, or in areas with poor drainage where water can reach engine bay components.'
    },
    {
      question: 'How do I test MAP sensor circuit for P0105?',
      answer: 'Use a multimeter to check for 5V reference voltage, good ground, and signal wire continuity. GeekOBD APP can show if the ECM is receiving any signal from the MAP sensor - no data or fixed values indicate circuit problems rather than sensor range issues.'
    },
    {
      question: 'Why does P0105 cause poor fuel economy?',
      answer: 'Without MAP sensor data, the ECM cannot accurately calculate engine load and must use default fuel maps. These default settings are conservative and not optimized for current driving conditions, typically resulting in richer fuel mixture and poor economy.'
    }
  ],

  costAnalysis: {
    averageCost: '$95-$380 for most P0105 repairs',
    repairOptions: [
      {
        title: 'Wiring Harness Repair',
        description: 'Most common fix - Repair damaged MAP sensor wiring (60% of cases)',
        color: '#4CAF50',
        icon: 'flash',
        items: [
          { name: 'Wiring repair materials', cost: '$25-$60' },
          { name: 'Diagnostic time', cost: '$80-$120' },
          { name: 'Labor (1-2 hours)', cost: '$100-$240' }
        ],
        total: '$205-$420',
        successRate: '90% success rate'
      },
      {
        title: 'MAP Sensor Replacement',
        description: 'Replace sensor with internal electrical failure (30% of cases)',
        color: '#2196F3',
        icon: 'exchange',
        items: [
          { name: 'MAP sensor', cost: '$40-$95' },
          { name: 'Labor (45-75 minutes)', cost: '$60-$150' }
        ],
        total: '$100-$245',
        successRate: '95% success rate'
      },
      {
        title: 'Connector Service',
        description: 'Clean or replace corroded MAP sensor connector (10% of cases)',
        color: '#FF9800',
        icon: 'plug',
        items: [
          { name: 'Connector cleaning/replacement', cost: '$20-$65' },
          { name: 'Dielectric grease', cost: '$8-$15' },
          { name: 'Labor (30-60 minutes)', cost: '$50-$120' }
        ],
        total: '$78-$200',
        successRate: '85% success rate'
      }
    ],
    savingTips: [
      'Check connector first - 20% of P0105 cases are just corroded connections',
      'Use multimeter to test circuits before replacing expensive components',
      'MAP sensor replacement is usually DIY-friendly if wiring tests good',
      'GeekOBD APP can help identify if problem is sensor or wiring related',
      'Address water damage source to prevent recurrence of circuit problems'
    ]
  },

  diagnosticSteps: {
    estimatedTime: 'PT90M',
    steps: [
      {
        title: 'Check for MAP Sensor Data',
        icon: 'search',
        description: 'Connect GeekOBD APP and check if MAP sensor data is available. With P0105, you may see no data, fixed values, or erratic electrical readings rather than sensor range problems.',
        geekobdTip: 'GeekOBD APP will show if ECM is receiving MAP sensor signals - complete absence of data indicates circuit failure rather than sensor range issues.'
      },
      {
        title: 'Visual Inspection',
        icon: 'eye',
        description: 'Inspect MAP sensor, wiring harness, and connector for obvious damage. Look for corroded pins, damaged wires, water damage, or signs of rodent damage to harness.',
        geekobdTip: 'Use GeekOBD APP to monitor for any signal while wiggling wires - intermittent data indicates wiring problems.'
      },
      {
        title: 'Electrical Circuit Testing',
        icon: 'bolt',
        description: 'Test MAP sensor power supply (5V reference), ground circuit, and signal wire continuity with multimeter. Check for proper voltage at sensor connector with key on.',
        geekobdTip: 'GeekOBD APP should show stable readings when circuits are good - use multimeter to verify actual voltages match expected values.'
      },
      {
        title: 'Connector and Ground Testing',
        icon: 'plug',
        description: 'Clean MAP sensor connector and test for proper connection. Verify ground circuit integrity and check for corrosion or loose connections.',
        geekobdTip: 'Monitor GeekOBD APP while cleaning connections - MAP data should appear or stabilize when good connection is restored.'
      },
      {
        title: 'Component Replacement and Verification',
        icon: 'check-circle',
        description: 'Replace faulty wiring, connector, or MAP sensor as diagnosed. Clear codes and verify MAP sensor data is now available and responding properly.',
        geekobdTip: 'GeekOBD APP should now show stable MAP sensor readings that respond appropriately to throttle changes, confirming circuit repair.'
      }
    ],
    importantNotes: [
      'P0105 is electrical circuit problem, not sensor range issue',
      'Test circuits with multimeter before replacing components',
      'Water damage often affects multiple circuits - check related sensors'
    ]
  },

  caseStudies: [
    {
      title: 'Jeep Grand Cherokee Water Damage',
      vehicle: '2017 Jeep Grand Cherokee 3.6L V6, 85,000 miles',
      problem: 'Customer drove through deep water during flooding. Engine ran poorly afterward with P0105 code and several other electrical codes.',
      diagnosis: 'GeekOBD APP showed no MAP sensor data available. Visual inspection revealed water had entered MAP sensor connector, causing corrosion on all three pins and creating open circuits.',
      solution: 'Cleaned corroded connector pins with electrical contact cleaner, applied dielectric grease, and ensured proper seal. Also checked other sensors affected by water intrusion.',
      cost: 'Connector cleaning kit: $15, Dielectric grease: $8, Labor: $120, Total: $143',
      result: 'P0105 code cleared immediately. MAP sensor data now available and engine performance fully restored. No recurrence after 8 months.'
    },
    {
      title: 'Ford Escape Rodent Damage',
      vehicle: '2016 Ford Escape 1.6L Turbo, 92,000 miles',
      problem: 'Intermittent P0105 code with poor engine performance that seemed to worsen in cold weather. Customer noticed evidence of rodent activity in engine bay.',
      diagnosis: 'Found MAP sensor wiring harness had been chewed by rodents, creating intermittent open circuit. GeekOBD APP showed MAP data would disappear randomly, especially when wires moved with temperature changes.',
      solution: 'Repaired damaged section of MAP sensor wiring harness and installed protective conduit to prevent future rodent damage. Used proper automotive wire and connectors.',
      cost: 'Wiring repair kit: $35, Protective conduit: $20, Labor: $180, Total: $235',
      result: 'P0105 code has not returned after 6 months. Installed rodent deterrent measures and MAP sensor data remains stable in all conditions.'
    }
  ],

  relatedCodes: [
    { code: 'P0106', description: 'MAP Sensor Range/Performance - Sensor working but readings out of range', color: '#4a90e2' },
    { code: 'P0107', description: 'MAP Sensor Low Input - Sensor reading too low pressure', color: '#3498db' },
    { code: 'P0108', description: 'MAP Sensor High Input - Sensor reading too high pressure', color: '#e74c3c' },
    { code: 'P0109', description: 'MAP Sensor Intermittent - Intermittent sensor circuit problems', color: '#f39c12' },
    { code: 'P0172', description: 'System Too Rich Bank 1 - Can result from default fuel maps', color: '#9b59b6' },
    { code: 'P0175', description: 'System Too Rich Bank 2 - Can result from default fuel maps', color: '#9b59b6' },
    { code: 'P0300', description: 'Random Misfire - Poor performance from non-optimal fuel delivery', color: '#e67e22' }
  ],

  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P0105',
      description: 'Use GeekOBD APP for MAP sensor circuit diagnosis!',
      features: [
        'Circuit connectivity testing',
        'Real-time electrical monitoring',
        'Wiring problem identification',
        'Repair verification'
      ]
    },
    systemCodes: {
      title: 'MAP Sensor Codes',
      description: 'Related manifold pressure sensor codes:'
    },
    diagnosticResources: [
      {
        title: 'Electrical Circuit Testing',
        description: 'Professional procedures for testing MAP sensor circuits',
        icon: 'flash',
        url: '#diagnostic-steps'
      },
      {
        title: 'Wiring Repair Guide',
        description: 'Proper techniques for automotive wiring repair',
        icon: 'wrench',
        url: '../resources/wiring-repair-guide.html'
      },
      {
        title: 'Water Damage Recovery',
        description: 'Procedures for repairing water-damaged electrical systems',
        icon: 'tint',
        url: '../resources/water-damage-recovery.html'
      },
      {
        title: 'Connector Maintenance',
        description: 'Cleaning and protecting automotive electrical connectors',
        icon: 'plug',
        url: '../resources/connector-maintenance.html'
      }
    ],
    codeInfo: {
      system: 'Engine Management',
      severity: 'MEDIUM',
      category: 'Electrical Circuit'
    }
  }
});

module.exports = p0105Data;
