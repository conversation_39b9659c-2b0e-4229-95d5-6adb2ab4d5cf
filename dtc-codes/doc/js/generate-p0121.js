const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0121Data = require('./p0121-data');

console.log('🚀 Generating P0121 - TPS Range/Performance Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0121Data);
  const outputPath = path.join(__dirname, '../../p0121.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0121 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0121 page:', error.message);
  process.exit(1);
}
