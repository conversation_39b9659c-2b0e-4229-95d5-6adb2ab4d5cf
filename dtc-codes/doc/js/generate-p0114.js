const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0114Data = require('./p0114-data');

console.log('🚀 Generating P0114 - IAT Sensor Intermittent Page\n');

try {
  // 创建模板生成器实例
  const generator = new DTCTemplateGenerator();
  
  // 生成HTML内容
  console.log('📝 Generating HTML content...');
  const htmlContent = generator.generatePage(p0114Data);
  
  // 保存到文件
  const outputPath = path.join(__dirname, '../../p0114.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0114 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
  // 统计信息
  console.log('\n📊 Content Statistics:');
  console.log(`   • Total file size: ${Math.round(htmlContent.length / 1024)}KB`);
  console.log(`   • Symptoms: ${p0114Data.symptoms.length} items`);
  console.log(`   • Causes: ${p0114Data.causes.length} items`);
  console.log(`   • AI Questions: ${p0114Data.aiQuestions.length} items`);
  console.log(`   • Repair Options: ${p0114Data.costAnalysis.repairOptions.length} options`);
  console.log(`   • Diagnostic Steps: ${p0114Data.diagnosticSteps.steps.length} steps`);
  console.log(`   • Case Studies: ${p0114Data.caseStudies.length} studies`);
  console.log(`   • Related Codes: ${p0114Data.relatedCodes.length} codes`);
  
  // 验证关键组件
  console.log('\n🔍 Component Verification:');
  console.log(`   ✅ Quick Answer: ${p0114Data.quickAnswer ? 'Present' : 'Missing'}`);
  console.log(`   ✅ Cost Analysis: ${p0114Data.costAnalysis ? 'Present' : 'Missing'}`);
  console.log(`   ✅ Diagnostic Steps: ${p0114Data.diagnosticSteps ? 'Present' : 'Missing'}`);
  console.log(`   ✅ Case Studies: ${p0114Data.caseStudies.length > 0 ? 'Present' : 'Missing'}`);
  console.log(`   ✅ Sidebar: ${p0114Data.sidebarData ? 'Present' : 'Missing'}`);
  
  console.log('\n🎉 P0114 optimization completed successfully!');
  console.log('\n💡 Key Features:');
  console.log('   • Specialized intermittent fault diagnosis procedures');
  console.log('   • Wire wiggle testing methodology');
  console.log('   • Real-world Jeep Wrangler and Toyota Camry case studies');
  console.log('   • Cost-effective repair strategies ($65-$350 range)');
  console.log('   • GeekOBD APP integration for intermittent monitoring');
  console.log('   • Complete IAT sensor network linking');
  
  console.log('\n🔄 Next steps:');
  console.log('   1. Review generated p0114.html for accuracy');
  console.log('   2. Test page layout and functionality');
  console.log('   3. Continue with P0117 (ECT Sensor Low Input)');
  console.log('   4. Build complete temperature sensor diagnostic series');
  
} catch (error) {
  console.error('❌ Error generating P0114 page:', error.message);
  console.error(error.stack);
  process.exit(1);
}
