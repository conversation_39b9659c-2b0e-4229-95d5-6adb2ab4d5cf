const { DTCData } = require('./dtc-template-generator');

// P1101 Mass Air Flow Sensor Out of Self-Test Range 的完整数据结构
const p1101Data = new DTCData({
  code: 'P1101',
  title: 'Mass Air Flow Sensor Out of Self-Test Range',
  description: 'The Mass Air Flow sensor reading is outside the expected self-test range.',
  definition: 'P1101 indicates that the Mass Air Flow (MAF) sensor reading is outside the expected self-test range. This is primarily a Ford-specific code that appears when the ECM performs a self-test on the MAF sensor and finds that the sensor readings are not within the acceptable parameters. The MAF sensor should provide specific voltage or frequency readings under controlled test conditions, and when these readings fall outside the expected range, P1101 is triggered.',
  
  symptoms: [
    'Check engine light illuminated - ECM detected MAF sensor out of range',
    'Poor engine performance - Incorrect air flow readings affecting fuel mixture',
    'Rough idle - MAF sensor not providing accurate idle air flow data',
    'Engine hesitation - Inconsistent air flow measurements affecting acceleration',
    'Poor fuel economy - Incorrect air/fuel mixture from faulty MAF readings',
    'Engine stalling - Complete MAF sensor failure causing shutdown',
    'Black smoke from exhaust - Rich mixture from incorrect air flow readings',
    'Hard starting - MAF sensor not providing accurate startup air flow data',
    'Engine surging - Fluctuating power from incorrect air flow measurements'
  ],
  
  causes: [
    'Faulty MAF sensor - Internal sensor failure providing out-of-range readings',
    'Contaminated MAF sensor - Dirt, oil, or debris affecting sensor accuracy',
    'Damaged MAF sensor wiring - Electrical problems affecting sensor signal',
    'Air intake leaks - Unmetered air affecting MAF sensor accuracy',
    'Clogged air filter - Restricted airflow affecting sensor readings',
    'Faulty ECM - Control module incorrectly interpreting MAF sensor signals',
    'Incorrect MAF sensor installation - Improper mounting affecting readings',
    'Aftermarket air intake modifications - Non-OEM components affecting sensor operation'
  ],
  
  performanceImpact: 'P1101 causes poor engine performance, reduced fuel economy, rough running, and potential stalling due to incorrect air flow measurement affecting fuel mixture calculations.',
  
  quickAnswer: {
    icon: 'exclamation-circle',
    meaning: 'MAF sensor readings outside normal test range - usually faulty or contaminated sensor.',
    fix: 'Clean MAF sensor, check air filter, test sensor readings, replace MAF sensor if needed',
    cost: '$150-$400',
    time: '60-120 minutes',
    drivingSafety: 'Safe to drive but expect poor performance. Clean MAF sensor first as common fix.'
  },
  
  aiQuestions: [
    {
      question: 'What does "out of self-test range" mean?',
      answer: 'The ECM performs self-tests on the MAF sensor under specific conditions and expects certain voltage or frequency readings. "Out of range" means the sensor readings are either too high, too low, or inconsistent compared to what the ECM expects for proper operation.'
    },
    {
      question: 'Can a dirty air filter cause P1101?',
      answer: 'Yes, a severely clogged air filter can restrict airflow enough to cause the MAF sensor readings to fall outside the expected range. The sensor may read lower airflow than expected, triggering P1101.'
    },
    {
      question: 'How do I test if my MAF sensor is working correctly?',
      answer: 'Use GeekOBD APP to monitor MAF sensor readings at idle and during acceleration. Compare readings to specifications for your engine. The sensor should show smooth, consistent increases with throttle opening.'
    },
    {
      question: 'Can I drive with P1101?',
      answer: 'You can drive with P1101, but expect poor performance, rough running, and reduced fuel economy. The ECM may use backup strategies, but these don\'t provide optimal performance. Repair promptly for best operation.'
    }
  ],

  costAnalysis: {
    averageCost: '$150-$400 for most P1101 repairs',
    repairOptions: [
      {
        title: 'MAF Sensor Cleaning',
        description: 'Clean contaminated MAF sensor (40% success rate)',
        color: '#4CAF50',
        icon: 'spray-can',
        items: [
          { name: 'MAF sensor cleaner', cost: '$15-$25' },
          { name: 'Labor (30-60 minutes)', cost: '$50-$120' }
        ],
        total: '$65-$145',
        successRate: '40% success rate'
      },
      {
        title: 'Air Filter Replacement',
        description: 'Replace clogged air filter affecting MAF readings (20% of cases)',
        color: '#FF9800',
        icon: 'filter',
        items: [
          { name: 'Air filter', cost: '$15-$45' },
          { name: 'Labor (15-30 minutes)', cost: '$25-$60' }
        ],
        total: '$40-$105',
        successRate: '60% success rate'
      },
      {
        title: 'MAF Sensor Replacement',
        description: 'Replace faulty MAF sensor (40% of cases)',
        color: '#2196F3',
        icon: 'exclamation-circle',
        items: [
          { name: 'MAF sensor', cost: '$120-$280' },
          { name: 'Labor (30-60 minutes)', cost: '$50-$120' }
        ],
        total: '$170-$400',
        successRate: '95% success rate'
      }
    ],
    savingTips: [
      'Try MAF sensor cleaning and air filter replacement first',
      'Use GeekOBD APP to verify MAF readings are within specification',
      'Check for air intake leaks that can affect MAF accuracy',
      'Compare MAF readings to engine specifications before replacement',
      'Address P1101 promptly to restore proper engine performance'
    ]
  },

  diagnosticSteps: {
    estimatedTime: 'PT90M',
    steps: [
      {
        title: 'Check MAF Sensor Readings',
        icon: 'search',
        description: 'Connect GeekOBD APP and monitor MAF sensor readings at idle and during acceleration. Compare to specifications for your engine.',
        geekobdTip: 'GeekOBD APP can show MAF sensor voltage and airflow readings - compare to normal ranges for your specific engine size and type.'
      },
      {
        title: 'Inspect Air Filter',
        icon: 'filter',
        description: 'Remove and inspect air filter for dirt, debris, or damage. A severely clogged filter can cause out-of-range MAF readings.',
        geekobdTip: 'Monitor MAF readings with GeekOBD APP before and after air filter replacement - readings should improve with new filter.'
      },
      {
        title: 'Clean MAF Sensor',
        icon: 'spray-can',
        description: 'Remove MAF sensor and clean sensing elements with specialized MAF cleaner. Allow to dry completely before reinstalling.',
        geekobdTip: 'Use GeekOBD APP to compare MAF readings before and after cleaning - readings should be more accurate and within specification.'
      },
      {
        title: 'Check for Air Leaks',
        icon: 'search-plus',
        description: 'Inspect air intake system for leaks between MAF sensor and throttle body. Unmetered air can cause incorrect MAF readings.',
        geekobdTip: 'Monitor fuel trims with GeekOBD APP while checking for leaks - positive fuel trims may indicate unmetered air affecting MAF accuracy.'
      },
      {
        title: 'Component Replacement and Verification',
        icon: 'check-circle',
        description: 'Replace MAF sensor if cleaning doesn\'t resolve the issue. Clear codes and verify MAF readings are within specification.',
        geekobdTip: 'Use GeekOBD APP to verify new MAF sensor provides readings within specification range and engine performance is restored.'
      }
    ],
    importantNotes: [
      'P1101 indicates MAF sensor readings are outside acceptable test parameters',
      'Check air filter and clean MAF sensor before replacement',
      'Verify MAF readings match specifications for your specific engine'
    ]
  },

  caseStudies: [
    {
      title: 'Ford Mustang Contaminated MAF Sensor',
      vehicle: '2016 Ford Mustang 2.3L Turbo, 85,000 miles',
      problem: 'Customer reported poor acceleration, rough idle, and P1101 code. Engine felt sluggish and fuel economy was poor.',
      diagnosis: 'GeekOBD APP showed MAF sensor readings were consistently low compared to specifications. MAF sensor was contaminated with oil residue.',
      solution: 'Cleaned MAF sensor with specialized cleaner and replaced dirty air filter. Also checked for oil leaks that caused contamination.',
      cost: 'MAF cleaner: $18, Air filter: $35, Labor: $85, Total: $138',
      result: 'P1101 code cleared and MAF readings now within specification. Engine performance and fuel economy fully restored.'
    },
    {
      title: 'Ford F-150 Failed MAF Sensor',
      vehicle: '2017 Ford F-150 5.0L V8, 125,000 miles',
      problem: 'P1101 code with engine hesitation and black smoke from exhaust. MAF sensor cleaning did not resolve the issue.',
      diagnosis: 'MAF sensor readings were erratic and outside specification range even after cleaning. Sensor had failed internally.',
      solution: 'Replaced MAF sensor with OEM part. Sensor was providing incorrect readings that could not be corrected by cleaning.',
      cost: 'MAF sensor: $195, Labor: $75, Total: $270',
      result: 'P1101 code cleared permanently. MAF readings now accurate and within specification. Engine runs smoothly with normal exhaust.'
    }
  ],

  relatedCodes: [
    { code: 'P1100', description: 'Mass Air Flow Sensor Intermittent - Related MAF sensor problem', color: '#e74c3c' },
    { code: 'P0100', description: 'MAF Sensor Circuit Malfunction - General MAF circuit problem', color: '#3498db' },
    { code: 'P0101', description: 'MAF Sensor Range/Performance - MAF sensor reading issues', color: '#f39c12' },
    { code: 'P0102', description: 'MAF Sensor Low Input - MAF sensor signal too low', color: '#9b59b6' },
    { code: 'P0103', description: 'MAF Sensor High Input - MAF sensor signal too high', color: '#4a90e2' },
    { code: 'P0171', description: 'System Too Lean Bank 1 - Can result from MAF problems', color: '#e67e22' },
    { code: 'P0174', description: 'System Too Lean Bank 2 - Can result from MAF problems', color: '#27ae60' }
  ],

  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P1101',
      description: 'Use GeekOBD APP for MAF sensor range testing!',
      features: [
        'MAF sensor reading verification',
        'Specification comparison',
        'Real-time monitoring',
        'Performance analysis'
      ]
    },
    systemCodes: {
      title: 'MAF Sensor Codes',
      description: 'Related mass air flow sensor codes:'
    },
    diagnosticResources: [
      {
        title: 'MAF Sensor Range Testing',
        description: 'Professional procedures for testing MAF sensor accuracy',
        icon: 'exclamation-circle',
        url: '#diagnostic-steps'
      },
      {
        title: 'MAF Sensor Specifications',
        description: 'Understanding MAF sensor reading specifications',
        icon: 'chart-line',
        url: '../resources/maf-sensor-specifications.html'
      },
      {
        title: 'Air Intake System Diagnosis',
        description: 'Complete air intake system diagnostic procedures',
        icon: 'wind',
        url: '../resources/air-intake-system-diagnosis.html'
      },
      {
        title: 'Engine Performance Testing',
        description: 'Testing engine performance and air flow systems',
        icon: 'tachometer-alt',
        url: '../resources/engine-performance-testing.html'
      }
    ],
    codeInfo: {
      system: 'Engine Management',
      severity: 'MEDIUM',
      category: 'Air Flow Sensor'
    }
  }
});

module.exports = p1101Data;
