const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0507Data = require('./p0507-data');

console.log('🚀 Generating P0507 - Idle Air Control System RPM Higher Than Expected Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0507Data);
  const outputPath = path.join(__dirname, '../../p0507.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0507 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0507 page:', error.message);
  process.exit(1);
}
