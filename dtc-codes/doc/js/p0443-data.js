const { DTCData } = require('./dtc-template-generator');

// P0443 EVAP Purge Valve Circuit 的完整数据结构
const p0443Data = new DTCData({
  code: 'P0443',
  title: 'EVAP Purge Valve Circuit',
  description: 'The Engine Control Module has detected an electrical malfunction in the EVAP purge valve circuit.',
  definition: 'The Engine Control Module has detected an electrical malfunction in the EVAP purge valve circuit. The EVAP purge valve is an electrically controlled solenoid that regulates the flow of fuel vapors from the charcoal canister into the intake manifold. When there are electrical problems with the purge valve circuit, including open circuits, short circuits, or control signal issues, P0443 is triggered. This indicates electrical problems rather than mechanical valve operation issues.',
  
  symptoms: [
    'Check engine light illuminated - ECM detected purge valve circuit electrical fault',
    'EVAP system not purging - No fuel vapor flow from canister to engine',
    'Failed emissions test - EVAP system not controlling fuel vapor emissions',
    'Fuel smell around vehicle - Vapors not being purged into engine for combustion',
    'Poor fuel economy - EVAP system not recovering fuel vapors efficiently',
    'Rough idle (if valve stuck open) - Continuous vapor flow affecting mixture',
    'Engine hesitation - Purge system problems affecting air/fuel mixture',
    'Fuel tank pressure buildup - System not purging vapors properly',
    'Difficulty filling fuel tank - EVAP system not operating correctly'
  ],
  
  causes: [
    'Faulty EVAP purge valve solenoid - Internal electrical failure in valve',
    'Open circuit in purge valve wiring - Broken wire preventing valve operation',
    'Short circuit in valve harness - Wire touching ground or power',
    'Corroded purge valve connector - Poor electrical contact affecting operation',
    'ECM driver circuit failure - Control module unable to control purge valve',
    'Damaged wiring harness - Physical damage affecting purge valve circuit',
    'Faulty purge valve power supply - Inadequate voltage to valve circuit',
    'Ground circuit problems - Poor ground connection for purge valve'
  ],
  
  performanceImpact: 'P0443 prevents proper EVAP system operation, leading to increased fuel vapor emissions, failed emissions testing, and potential fuel odors. The engine may experience minor drivability issues if the valve is stuck in an abnormal position.',
  
  quickAnswer: {
    icon: 'flash',
    meaning: 'Electrical problem in EVAP purge valve circuit - wiring, connector, or valve electrical failure.',
    fix: 'Check purge valve wiring, test valve resistance, replace purge valve if needed',
    cost: '$120-$380',
    time: '60-120 minutes',
    drivingSafety: 'Safe to drive but EVAP system won\'t work properly. May smell fuel vapors and fail emissions test.'
  },
  
  aiQuestions: [
    {
      question: 'What\'s the difference between P0441 and P0443?',
      answer: 'P0441 indicates incorrect purge flow (mechanical/flow problem), while P0443 indicates electrical circuit problems with the purge valve. P0443 means the ECM cannot electrically control the valve, while P0441 means the valve may work electrically but flow is incorrect.'
    },
    {
      question: 'Can I drive with P0443?',
      answer: 'Yes, you can drive with P0443. The main issues are increased fuel vapor emissions and potential fuel odors. The EVAP system won\'t purge vapors properly, but this doesn\'t affect basic engine operation or safety.'
    },
    {
      question: 'How do I test the EVAP purge valve circuit?',
      answer: 'Use a multimeter to test purge valve resistance (usually 20-40 ohms) and check for power and ground at the connector. GeekOBD APP can show if the ECM is sending control signals to the valve.'
    },
    {
      question: 'Can a bad purge valve cause other EVAP codes?',
      answer: 'Yes, a faulty purge valve can cause other EVAP codes like P0440, P0441, or leak detection codes because the EVAP system cannot operate properly without functional purge valve control.'
    }
  ],

  costAnalysis: {
    averageCost: '$120-$380 for most P0443 repairs',
    repairOptions: [
      {
        title: 'EVAP Purge Valve Replacement',
        description: 'Replace electrically failed purge valve (75% of cases)',
        color: '#4CAF50',
        icon: 'flash',
        items: [
          { name: 'EVAP purge valve', cost: '$60-$150' },
          { name: 'Labor (1-1.5 hours)', cost: '$100-$180' }
        ],
        total: '$160-$330',
        successRate: '95% success rate'
      },
      {
        title: 'Wiring Harness Repair',
        description: 'Fix damaged purge valve wiring (20% of cases)',
        color: '#FF9800',
        icon: 'wrench',
        items: [
          { name: 'Wiring repair materials', cost: '$25-$60' },
          { name: 'Diagnostic time', cost: '$80-$120' },
          { name: 'Labor (1-2 hours)', cost: '$100-$240' }
        ],
        total: '$205-$420',
        successRate: '90% success rate'
      },
      {
        title: 'Connector Service',
        description: 'Clean or replace corroded purge valve connector (5% of cases)',
        color: '#2196F3',
        icon: 'plug',
        items: [
          { name: 'Connector cleaning/replacement', cost: '$20-$50' },
          { name: 'Dielectric grease', cost: '$8-$15' },
          { name: 'Labor (30-60 minutes)', cost: '$50-$120' }
        ],
        total: '$78-$185',
        successRate: '85% success rate'
      }
    ],
    savingTips: [
      'Test purge valve resistance before replacement - may be wiring issue',
      'Check connector for corrosion before replacing expensive valve',
      'Purge valve replacement is often DIY-friendly, saving $100-180 in labor',
      'Use GeekOBD APP to verify ECM control signals before assuming valve failure',
      'Address P0443 before emissions testing to ensure EVAP system operation'
    ]
  },

  diagnosticSteps: {
    estimatedTime: 'PT75M',
    steps: [
      {
        title: 'Check ECM Control Signals',
        icon: 'search',
        description: 'Connect GeekOBD APP and monitor ECM commands to purge valve. Verify ECM is attempting to control valve operation.',
        geekobdTip: 'GeekOBD APP can show purge valve duty cycle and control commands - no commands indicate ECM problems, commands present suggest valve circuit issues.'
      },
      {
        title: 'Test Purge Valve Resistance',
        icon: 'bolt',
        description: 'Disconnect purge valve connector and test resistance across valve terminals. Normal resistance is typically 20-40 ohms.',
        geekobdTip: 'Use GeekOBD APP to command valve operation while testing - infinite resistance indicates open valve, zero resistance indicates shorted valve.'
      },
      {
        title: 'Check Power and Ground Circuits',
        icon: 'flash',
        description: 'Test for proper voltage and ground at purge valve connector. Verify ECM is providing control signal to valve.',
        geekobdTip: 'GeekOBD APP can show valve commands while testing circuits - voltage should change when ECM commands valve operation.'
      },
      {
        title: 'Visual Inspection',
        icon: 'eye',
        description: 'Inspect purge valve wiring, connector, and mounting for damage, corrosion, or signs of electrical problems.',
        geekobdTip: 'Monitor valve operation with GeekOBD APP while wiggling wires - intermittent commands indicate wiring problems.'
      },
      {
        title: 'Component Replacement and Verification',
        icon: 'check-circle',
        description: 'Replace faulty purge valve or repair wiring as diagnosed. Clear codes and verify valve responds to ECM commands.',
        geekobdTip: 'Use GeekOBD APP to verify purge valve now responds properly to commands and EVAP system operates correctly.'
      }
    ],
    importantNotes: [
      'P0443 indicates electrical problems, not mechanical valve issues',
      'Test valve resistance and circuits before replacement',
      'ECM must be able to control valve for proper EVAP operation'
    ]
  },

  caseStudies: [
    {
      title: 'Chevrolet Cruze Purge Valve Failure',
      vehicle: '2016 Chevrolet Cruze 1.4L Turbo, 105,000 miles',
      problem: 'Customer reported P0443 code and fuel smell around vehicle. EVAP system was not operating during emissions test.',
      diagnosis: 'GeekOBD APP showed ECM was commanding purge valve operation but valve was not responding. Resistance test revealed purge valve had infinite resistance.',
      solution: 'Replaced EVAP purge valve with OEM part. Valve had failed internally with open coil preventing electrical operation.',
      cost: 'EVAP purge valve: $85, Labor: $110, Total: $195',
      result: 'P0443 code cleared immediately. EVAP system now operates properly and fuel odors eliminated. Vehicle passed emissions test.'
    },
    {
      title: 'Subaru Outback Wiring Damage',
      vehicle: '2017 Subaru Outback 2.5L 4-cylinder, 89,000 miles',
      problem: 'Intermittent P0443 code with occasional fuel smell. Problem seemed worse in wet weather conditions.',
      diagnosis: 'Purge valve tested good resistance, but GeekOBD APP showed intermittent loss of control signal. Found purge valve wire damaged by rodents.',
      solution: 'Repaired chewed section of purge valve wiring and installed protective conduit to prevent future rodent damage.',
      cost: 'Wiring repair kit: $30, Protective conduit: $20, Labor: $95, Total: $145',
      result: 'P0443 code has not returned after 8 months. Purge valve operates properly and no more intermittent fuel odors.'
    }
  ],

  relatedCodes: [
    { code: 'P0440', description: 'EVAP System Malfunction - General EVAP system problem', color: '#e74c3c' },
    { code: 'P0441', description: 'EVAP Purge Flow Incorrect - Purge flow problems', color: '#3498db' },
    { code: 'P0442', description: 'EVAP System Small Leak - Small leak detected in system', color: '#f39c12' },
    { code: 'P0446', description: 'EVAP Vent Control Circuit - Vent valve electrical problems', color: '#9b59b6' },
    { code: 'P0455', description: 'EVAP System Large Leak - Large leak detected in system', color: '#e67e22' },
    { code: 'P0456', description: 'EVAP System Very Small Leak - Very small leak detected', color: '#4a90e2' },
    { code: 'P0171', description: 'System Too Lean - Can be affected by purge system problems', color: '#27ae60' }
  ],

  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P0443',
      description: 'Use GeekOBD APP for EVAP purge valve circuit testing!',
      features: [
        'Purge valve command monitoring',
        'Circuit electrical testing',
        'Control signal verification',
        'Resistance measurement'
      ]
    },
    systemCodes: {
      title: 'EVAP System Codes',
      description: 'Related evaporative emission control codes:'
    },
    diagnosticResources: [
      {
        title: 'EVAP Electrical Testing',
        description: 'Professional procedures for testing EVAP electrical circuits',
        icon: 'flash',
        url: '#diagnostic-steps'
      },
      {
        title: 'Purge Valve Replacement',
        description: 'Step-by-step purge valve replacement procedures',
        icon: 'exchange',
        url: '../resources/purge-valve-replacement.html'
      },
      {
        title: 'Electrical Diagnostics',
        description: 'Advanced electrical testing for automotive systems',
        icon: 'bolt',
        url: '../resources/electrical-diagnostics.html'
      },
      {
        title: 'EVAP System Operation',
        description: 'Understanding how EVAP systems control fuel vapors',
        icon: 'info-circle',
        url: '../resources/evap-system-operation.html'
      }
    ],
    codeInfo: {
      system: 'Emissions Control',
      severity: 'MEDIUM',
      category: 'Electrical Circuit'
    }
  }
});

module.exports = p0443Data;
