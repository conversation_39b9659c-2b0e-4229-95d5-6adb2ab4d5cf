const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0300Data = require('./p0300-data');

console.log('🚀 Generating P0300 - Random Misfire Detected Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0300Data);
  const outputPath = path.join(__dirname, '../../p0300.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0300 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0300 page:', error.message);
  process.exit(1);
}
