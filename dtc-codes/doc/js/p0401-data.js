const { DTCData } = require('./dtc-template-generator');

// P0401 EGR Flow Insufficient 的完整数据结构
const p0401Data = new DTCData({
  code: 'P0401',
  title: 'EGR Flow Insufficient',
  description: 'The Engine Control Module has detected insufficient exhaust gas recirculation flow.',
  definition: 'The Engine Control Module has detected insufficient exhaust gas recirculation (EGR) flow. The EGR system is designed to recirculate a specific amount of exhaust gases back into the intake manifold to reduce combustion temperatures and NOx emissions. When the ECM commands EGR flow but detects less flow than expected, P0401 is triggered. This typically indicates blocked EGR passages, a stuck EGR valve, or problems with the EGR control system.',
  
  symptoms: [
    'Check engine light illuminated - ECM detected insufficient EGR flow',
    'Failed emissions test - Increased NOx emissions from inadequate EGR',
    'Engine knocking or pinging - Higher combustion temperatures without sufficient EGR',
    'Increased fuel consumption - Engine working harder due to higher combustion temperatures',
    'Engine overheating - Reduced cooling effect from insufficient EGR',
    'Poor performance under load - Higher combustion temperatures affecting power',
    'Carbon knock during acceleration - Insufficient EGR allowing higher peak pressures',
    'Rough idle (if EGR operates at idle) - Inconsistent EGR flow affecting idle quality',
    'Pre-ignition problems - Higher combustion temperatures causing timing issues'
  ],
  
  causes: [
    'Clogged EGR valve - Carbon buildup preventing valve from opening fully',
    'Blocked EGR passages - Carbon deposits restricting exhaust gas flow',
    'Stuck EGR valve - Valve mechanically stuck in closed or partially closed position',
    'Faulty EGR vacuum solenoid - Not providing adequate vacuum to operate valve',
    'Vacuum leak in EGR system - Reducing vacuum available to operate EGR valve',
    'Clogged EGR cooler - Restricted flow through EGR cooling system',
    'Faulty EGR position sensor - Providing incorrect feedback to ECM',
    'Blocked EGR pipe - Restricted exhaust gas flow to EGR valve'
  ],
  
  performanceImpact: 'P0401 causes increased NOx emissions, engine knocking, reduced fuel economy, and potential engine damage from higher combustion temperatures. The vehicle will likely fail emissions testing.',
  
  quickAnswer: {
    icon: 'arrow-down',
    meaning: 'Not enough exhaust gas flowing through EGR system - usually clogged valve or passages.',
    fix: 'Clean EGR valve and passages, check vacuum system, replace EGR valve if needed',
    cost: '$150-$550',
    time: '90-180 minutes',
    drivingSafety: 'Safe to drive but may experience knocking and poor performance. Clean EGR system to prevent engine damage from high combustion temperatures.'
  },
  
  aiQuestions: [
    {
      question: 'What\'s the difference between P0400 and P0401?',
      answer: 'P0400 is a general EGR flow malfunction, while P0401 specifically indicates insufficient EGR flow. P0401 means the ECM is commanding EGR flow but not getting enough, typically due to clogged passages or a stuck valve.'
    },
    {
      question: 'Can insufficient EGR flow damage my engine?',
      answer: 'Yes, insufficient EGR flow can cause engine damage over time. Higher combustion temperatures can lead to engine knocking, pre-ignition, overheating, and accelerated wear of pistons, valves, and other components. Address P0401 promptly.'
    },
    {
      question: 'How do I know if my EGR passages are blocked?',
      answer: 'Use GeekOBD APP to command EGR valve operation while monitoring engine RPM. If the valve moves but RPM doesn\'t drop significantly, passages are likely blocked. Visual inspection during EGR valve removal will confirm blockage.'
    },
    {
      question: 'Can I just disconnect the EGR system?',
      answer: 'No, disconnecting the EGR system is illegal in most areas and will cause the vehicle to fail emissions testing. It can also cause engine knocking and damage from higher combustion temperatures. Proper repair is necessary.'
    }
  ],

  costAnalysis: {
    averageCost: '$150-$550 for most P0401 repairs',
    repairOptions: [
      {
        title: 'EGR System Cleaning',
        description: 'Clean EGR valve and passages (70% success rate)',
        color: '#4CAF50',
        icon: 'refresh',
        items: [
          { name: 'EGR cleaner and gaskets', cost: '$25-$50' },
          { name: 'Labor (1.5-2.5 hours)', cost: '$150-$300' }
        ],
        total: '$175-$350',
        successRate: '70% success rate'
      },
      {
        title: 'EGR Valve Replacement',
        description: 'Replace stuck or damaged EGR valve (25% of cases)',
        color: '#2196F3',
        icon: 'arrow-down',
        items: [
          { name: 'EGR valve', cost: '$120-$350' },
          { name: 'Labor (1-2 hours)', cost: '$100-$240' }
        ],
        total: '$220-$590',
        successRate: '95% success rate'
      },
      {
        title: 'Vacuum System Repair',
        description: 'Fix vacuum leaks or faulty solenoid (5% of cases)',
        color: '#FF9800',
        icon: 'compress',
        items: [
          { name: 'Vacuum lines/solenoid', cost: '$30-$120' },
          { name: 'Labor (1-1.5 hours)', cost: '$100-$180' }
        ],
        total: '$130-$300',
        successRate: '90% success rate'
      }
    ],
    savingTips: [
      'Try EGR cleaning first - fixes 70% of P0401 cases',
      'Use GeekOBD APP to test EGR valve movement before replacement',
      'Check vacuum lines and connections - simple fixes may resolve issue',
      'Regular EGR maintenance every 60-80k miles prevents major problems',
      'Address P0401 before emissions testing to avoid failure'
    ]
  },

  diagnosticSteps: {
    estimatedTime: 'PT120M',
    steps: [
      {
        title: 'Test EGR Valve Operation',
        icon: 'play',
        description: 'Connect GeekOBD APP and command EGR valve operation. Monitor EGR valve position to verify valve attempts to open when commanded.',
        geekobdTip: 'GeekOBD APP can command EGR valve open and show position feedback - valve should move but may not reach full open position if passages are blocked.'
      },
      {
        title: 'Check EGR Flow Response',
        icon: 'tachometer',
        description: 'Monitor engine RPM while commanding EGR valve open at idle. Insufficient RPM drop indicates blocked passages even if valve moves.',
        geekobdTip: 'Use GeekOBD APP to monitor RPM during EGR test - small or no RPM drop with P0401 indicates insufficient flow despite valve movement.'
      },
      {
        title: 'Visual Inspection of EGR Components',
        icon: 'eye',
        description: 'Remove EGR valve and inspect for carbon buildup. Check EGR passages in intake manifold and exhaust side for blockage.',
        geekobdTip: 'Monitor EGR position with GeekOBD APP while manually operating valve - should show smooth movement if valve mechanism is not stuck.'
      },
      {
        title: 'Check Vacuum System Operation',
        icon: 'compress',
        description: 'Test vacuum lines, EGR solenoid, and vacuum supply for proper operation. Verify adequate vacuum reaches EGR valve.',
        geekobdTip: 'GeekOBD APP can show EGR solenoid commands - compare commanded vs actual vacuum operation to identify control problems.'
      },
      {
        title: 'Clean EGR System and Verify',
        icon: 'check-circle',
        description: 'Clean EGR valve and passages thoroughly. Clear codes and verify EGR system now provides adequate flow when commanded.',
        geekobdTip: 'Use GeekOBD APP to verify EGR valve now causes significant RPM drop when opened, confirming adequate flow restoration.'
      }
    ],
    importantNotes: [
      'P0401 specifically indicates insufficient flow, not complete blockage',
      'EGR cleaning is usually successful for P0401',
      'Check both valve operation and passage cleanliness'
    ]
  },

  caseStudies: [
    {
      title: 'Toyota Prius EGR Passage Blockage',
      vehicle: '2015 Toyota Prius 1.8L Hybrid, 145,000 miles',
      problem: 'Customer failed emissions test with P0401 code. Engine had occasional knocking during highway acceleration.',
      diagnosis: 'GeekOBD APP showed EGR valve moved when commanded but engine RPM barely dropped. EGR passages in intake manifold were severely clogged with carbon.',
      solution: 'Removed intake manifold and thoroughly cleaned EGR passages with carburetor cleaner and wire brushes. Also cleaned EGR valve and replaced gaskets.',
      cost: 'Cleaning supplies: $40, Gaskets: $25, Labor: $220, Total: $285',
      result: 'P0401 code cleared and EGR flow test now shows proper RPM drop. Vehicle passed emissions test and no more knocking during acceleration.'
    },
    {
      title: 'Chevrolet Malibu Stuck EGR Valve',
      vehicle: '2016 Chevrolet Malibu 1.5L Turbo, 95,000 miles',
      problem: 'P0401 code with poor fuel economy and engine knocking under load. EGR valve had been cleaned previously but code returned.',
      diagnosis: 'EGR valve appeared clean but GeekOBD APP showed valve position sensor indicated only partial opening. Valve was mechanically stuck due to warped valve seat.',
      solution: 'Replaced EGR valve assembly. Previous cleaning had not addressed the warped valve seat that prevented full opening.',
      cost: 'EGR valve: $195, Labor: $140, Total: $335',
      result: 'P0401 code cleared permanently. EGR valve now opens fully and engine knocking eliminated. Fuel economy improved by 3 MPG.'
    }
  ],

  relatedCodes: [
    { code: 'P0400', description: 'EGR Flow Malfunction - General EGR flow problem', color: '#e74c3c' },
    { code: 'P0402', description: 'EGR Flow Excessive - Too much EGR flow detected', color: '#f39c12' },
    { code: 'P0403', description: 'EGR Circuit Malfunction - Electrical control problems', color: '#9b59b6' },
    { code: 'P0404', description: 'EGR Position Sensor Range/Performance - Position sensor issues', color: '#4a90e2' },
    { code: 'P0405', description: 'EGR Position Sensor Low - Position sensor voltage too low', color: '#3498db' },
    { code: 'P0406', description: 'EGR Position Sensor High - Position sensor voltage too high', color: '#e67e22' },
    { code: 'P0171', description: 'System Too Lean - Can be affected by EGR problems', color: '#27ae60' }
  ],

  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P0401',
      description: 'Use GeekOBD APP for EGR flow testing!',
      features: [
        'EGR valve command testing',
        'Flow verification testing',
        'Position sensor monitoring',
        'RPM response analysis'
      ]
    },
    systemCodes: {
      title: 'EGR System Codes',
      description: 'Related exhaust gas recirculation codes:'
    },
    diagnosticResources: [
      {
        title: 'EGR Flow Testing',
        description: 'Professional procedures for testing EGR system flow',
        icon: 'arrow-down',
        url: '#diagnostic-steps'
      },
      {
        title: 'EGR Cleaning Procedures',
        description: 'Step-by-step EGR system cleaning guide',
        icon: 'refresh',
        url: '../resources/egr-cleaning-procedures.html'
      },
      {
        title: 'Emissions Testing Prep',
        description: 'Preparing your vehicle for emissions testing',
        icon: 'check-circle',
        url: '../resources/emissions-testing-prep.html'
      },
      {
        title: 'Engine Knock Diagnosis',
        description: 'Understanding and diagnosing engine knock problems',
        icon: 'exclamation-triangle',
        url: '../resources/engine-knock-diagnosis.html'
      }
    ],
    codeInfo: {
      system: 'Emissions Control',
      severity: 'MEDIUM',
      category: 'EGR System'
    }
  }
});

module.exports = p0401Data;
