const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p1101Data = require('./p1101-data');

console.log('🚀 Generating P1101 - Mass Air Flow Sensor Out of Self-Test Range Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p1101Data);
  const outputPath = path.join(__dirname, '../../p1101.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P1101 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P1101 page:', error.message);
  process.exit(1);
}
