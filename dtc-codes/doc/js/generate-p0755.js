const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0755Data = require('./p0755-data');

console.log('🚀 Generating P0755 - Shift Solenoid B Malfunction Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0755Data);
  const outputPath = path.join(__dirname, '../../p0755.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0755 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0755 page:', error.message);
  process.exit(1);
}
