const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p1001Data = require('./p1001-data');

console.log('🚀 Generating P1001 - Key On Engine Running (KOER) Test Not Able to Complete Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p1001Data);
  const outputPath = path.join(__dirname, '../../p1001.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P1001 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P1001 page:', error.message);
  process.exit(1);
}
