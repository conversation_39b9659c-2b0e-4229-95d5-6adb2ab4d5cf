const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0105Data = require('./p0105-data');

console.log('🚀 Generating P0105 - MAP Sensor Circuit Malfunction Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0105Data);
  const outputPath = path.join(__dirname, '../../p0105.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0105 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0105 page:', error.message);
  process.exit(1);
}
