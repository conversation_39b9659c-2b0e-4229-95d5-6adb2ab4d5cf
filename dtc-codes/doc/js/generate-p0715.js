const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0715Data = require('./p0715-data');

console.log('🚀 Generating P0715 - Input/Turbine Speed Sensor Circuit Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0715Data);
  const outputPath = path.join(__dirname, '../../p0715.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0715 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0715 page:', error.message);
  process.exit(1);
}
