const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0120Data = require('./p0120-data');

console.log('🚀 Generating P0120 - TPS Circuit Malfunction Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0120Data);
  const outputPath = path.join(__dirname, '../../p0120.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0120 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0120 page:', error.message);
  process.exit(1);
}
