const fs = require('fs');

// 创建新发现的缺失故障码页面
// 每个故障码都有独特、详细的专业内容

// 缺失的P码数据库 - P0400系列（排放控制系统）
const newMissingPCodeDatabase = {
  P0401: {
    title: "Exhaust Gas Recirculation Flow Insufficient Detected",
    description: "The Engine Control Module has detected insufficient exhaust gas recirculation (EGR) flow.",
    definition: "The Engine Control Module has detected that the exhaust gas recirculation (EGR) system is not providing sufficient flow of exhaust gases back into the intake manifold. The EGR system reduces NOx emissions by recirculating a portion of exhaust gases back into the combustion chamber to lower combustion temperatures.",
    symptoms: [
      "Check engine light illuminated - EGR flow insufficient detected",
      "Engine knock or ping - Higher combustion temperatures",
      "Increased NOx emissions - Insufficient exhaust gas recirculation",
      "Failed emissions test - NOx levels above acceptable limits",
      "Engine hesitation during acceleration - EGR flow issues",
      "Rough idle - Inconsistent EGR operation",
      "Reduced fuel economy - Suboptimal combustion temperatures",
      "Engine overheating - Higher combustion chamber temperatures"
    ],
    causes: [
      "Clogged EGR valve - Carbon buildup restricting flow",
      "Faulty EGR valve - Valve not opening properly",
      "Blocked EGR passages - Carbon deposits in intake manifold",
      "Damaged EGR vacuum lines - Vacuum leaks affecting operation",
      "Faulty EGR position sensor - Incorrect flow feedback",
      "Clogged EGR cooler - Restricted exhaust gas flow",
      "ECM EGR control circuit fault - Module malfunction",
      "Intake manifold gasket leaks - Affecting EGR flow measurement"
    ],
    performanceImpact: "P0401 results in insufficient EGR flow, leading to higher combustion temperatures, increased NOx emissions, potential engine knock, and failed emissions testing. This can also cause engine overheating and reduced fuel economy.",
    caseStudies: [
      {
        title: "2017 Honda Accord - Clogged EGR Valve",
        vehicle: "2017 Honda Accord, 2.0L Turbo, 85,000 miles",
        symptoms: "Engine knock, failed emissions test, P0401 code stored",
        diagnosis: "GeekOBD diagnostic scan revealed P0401 with insufficient EGR flow. EGR valve inspection showed severe carbon buildup preventing proper valve opening and exhaust gas recirculation.",
        solution: "Cleaned EGR valve and passages, replaced EGR gasket, performed EGR system relearn procedure. Cleared codes with GeekOBD APP and retested emissions - passed with normal NOx levels",
        parts: "EGR valve cleaning kit ($35), EGR gasket ($15), intake cleaner ($25)",
        labor: "3.0 hours ($300)",
        total: "$375"
      },
      {
        title: "2016 Toyota Camry - EGR Cooler Blockage",
        vehicle: "2016 Toyota Camry, 2.5L 4-cylinder, 125,000 miles",
        symptoms: "Poor performance, engine overheating, P0401 and P0217 codes",
        diagnosis: "GeekOBD diagnostic scan showed P0401 insufficient EGR flow and P0217 engine overheating. Found completely blocked EGR cooler preventing exhaust gas recirculation and causing overheating.",
        solution: "Replaced blocked EGR cooler with OEM Toyota part, cleaned EGR system, flushed cooling system. Cleared codes with GeekOBD APP and road tested - normal temperatures and EGR operation",
        parts: "EGR cooler ($285), coolant ($25), EGR cleaning service ($85)",
        labor: "4.5 hours ($450)",
        total: "$845"
      }
    ],
    relatedCodes: [
      { code: "P0400", desc: "Exhaust Gas Recirculation Flow Malfunction" },
      { code: "P0402", desc: "Exhaust Gas Recirculation Flow Excessive" },
      { code: "P0403", desc: "Exhaust Gas Recirculation Circuit Malfunction" },
      { code: "P0404", desc: "Exhaust Gas Recirculation Circuit Range/Performance" },
      { code: "P0405", desc: "Exhaust Gas Recirculation Sensor Circuit Low" }
    ]
  },

  P0504: {
    title: "Brake Switch A/B Correlation",
    description: "The Engine Control Module has detected a correlation problem between brake switch A and brake switch B signals.",
    definition: "The Engine Control Module has detected that the brake switch A and brake switch B signals do not correlate properly. Modern vehicles use multiple brake switches for redundancy and safety. When these switches provide conflicting signals, it indicates a malfunction that can affect cruise control, transmission shift points, and other brake-dependent systems.",
    symptoms: [
      "Check engine light illuminated - Brake switch correlation fault",
      "Cruise control disabled - Safety system shutdown",
      "Transmission shift issues - Brake signal affects shift points",
      "Brake lights may not work properly - Switch malfunction",
      "Engine may not start - Brake switch required for starting",
      "Traction control affected - Brake signal integration issues",
      "Electronic stability control problems - Brake input required",
      "Push-button start issues - Brake switch verification needed"
    ],
    causes: [
      "Faulty brake switch A - Internal switch failure",
      "Faulty brake switch B - Internal switch failure",
      "Damaged brake switch wiring - Cut, chafed, or corroded wires",
      "Corroded brake switch connectors - Poor electrical connections",
      "Brake pedal adjustment issues - Switches not properly aligned",
      "ECM brake switch input circuit fault - Module malfunction",
      "Brake switch mounting problems - Improper installation",
      "Brake pedal bushing wear - Affecting switch operation"
    ],
    performanceImpact: "P0504 can disable cruise control, affect transmission operation, cause starting problems, and compromise various safety systems that depend on accurate brake switch signals for proper operation.",
    caseStudies: [
      {
        title: "2018 Ford F-150 - Brake Switch Failure",
        vehicle: "2018 Ford F-150, 5.0L V8, 75,000 miles",
        symptoms: "Cruise control not working, transmission issues, P0504 code",
        diagnosis: "GeekOBD diagnostic scan revealed P0504 with brake switch correlation fault. Testing showed brake switch A providing correct signal while brake switch B remained stuck in 'on' position due to internal failure.",
        solution: "Replaced faulty brake switch B with OEM Ford part, adjusted brake pedal position, verified proper switch operation. Cleared codes with GeekOBD APP and tested systems - cruise control and transmission operating normally",
        parts: "OEM Ford brake switch B ($45), brake switch adjustment tool ($15)",
        labor: "1.5 hours ($150)",
        total: "$210"
      },
      {
        title: "2016 Honda Pilot - Wiring Corrosion",
        vehicle: "2016 Honda Pilot, 3.5L V6, 95,000 miles",
        symptoms: "Engine won't start, brake lights intermittent, P0504 stored",
        diagnosis: "GeekOBD diagnostic scan showed P0504 with conflicting brake switch signals. Found severe corrosion at brake switch A connector causing intermittent signal loss and correlation problems.",
        solution: "Cleaned corroded brake switch connector, replaced damaged terminals, applied dielectric grease for protection. Cleared codes with GeekOBD APP and verified proper brake switch correlation",
        parts: "Brake switch connector repair kit ($35), dielectric grease ($8), terminal cleaner ($5)",
        labor: "2.0 hours ($200)",
        total: "$248"
      }
    ],
    relatedCodes: [
      { code: "P0571", desc: "Cruise Control/Brake Switch A Circuit Malfunction" },
      { code: "P0572", desc: "Cruise Control/Brake Switch A Circuit Low" },
      { code: "P0573", desc: "Cruise Control/Brake Switch A Circuit High" },
      { code: "P0574", desc: "Cruise Control System - Vehicle Speed Too High" },
      { code: "P0575", desc: "Cruise Control Input Circuit Malfunction" }
    ]
  },

  P0506: {
    title: "Idle Air Control System RPM Lower Than Expected",
    description: "The Engine Control Module has detected that the idle speed is lower than the expected target RPM.",
    definition: "The Engine Control Module has detected that the engine idle speed is consistently lower than the programmed target RPM. The idle air control (IAC) system manages engine idle speed by controlling the amount of air bypassing the throttle plate. When idle RPM is too low, it can cause rough idle, stalling, and poor drivability.",
    symptoms: [
      "Check engine light illuminated - Idle speed too low detected",
      "Engine idle speed too low - Below target RPM",
      "Rough idle - Unstable engine operation",
      "Engine stalling - Particularly when coming to a stop",
      "Poor cold start performance - Difficulty maintaining idle",
      "Engine dies when air conditioning engaged - Additional load causes stalling",
      "Hesitation when shifting to drive - Low idle affecting transmission",
      "Engine vibration at idle - Irregular combustion from low RPM"
    ],
    causes: [
      "Faulty idle air control valve - Not providing enough air",
      "Clogged idle air control passages - Restricted airflow",
      "Vacuum leaks - Unmetered air affecting idle control",
      "Dirty throttle body - Carbon buildup restricting airflow",
      "Faulty mass airflow sensor - Incorrect air measurement",
      "ECM idle control circuit fault - Module malfunction",
      "Carbon buildup in intake manifold - Restricted air passages",
      "Faulty throttle position sensor - Incorrect throttle position feedback"
    ],
    performanceImpact: "P0506 causes engine idle speed to be too low, resulting in rough idle, stalling, poor drivability, and potential engine damage from irregular combustion at excessively low RPM.",
    caseStudies: [
      {
        title: "2017 Nissan Altima - Dirty Throttle Body",
        vehicle: "2017 Nissan Altima, 2.5L 4-cylinder, 85,000 miles",
        symptoms: "Rough idle, engine stalling, P0506 code stored",
        diagnosis: "GeekOBD diagnostic scan revealed P0506 with idle RPM at 450 (target: 650 RPM). Throttle body inspection showed severe carbon buildup restricting airflow and preventing proper idle speed control.",
        solution: "Cleaned throttle body and idle air control passages, performed throttle body relearn procedure, verified proper idle speed. Cleared codes with GeekOBD APP and road tested - stable idle at target RPM",
        parts: "Throttle body cleaner ($15), idle air control cleaner ($12), new air filter ($25)",
        labor: "2.0 hours ($200)",
        total: "$252"
      },
      {
        title: "2016 Chevrolet Malibu - IAC Valve Failure",
        vehicle: "2016 Chevrolet Malibu, 2.0L Turbo, 105,000 miles",
        symptoms: "Engine dies when stopping, poor cold start, P0506 stored",
        diagnosis: "GeekOBD diagnostic scan showed P0506 with consistently low idle speed. IAC valve testing revealed internal failure preventing proper air control, causing idle speed to drop below acceptable range.",
        solution: "Replaced faulty idle air control valve with OEM Chevrolet part, cleaned intake passages, performed idle relearn. Cleared codes with GeekOBD APP and tested - normal idle speed maintained",
        parts: "OEM Chevrolet IAC valve ($125), intake cleaner ($18), gasket ($8)",
        labor: "2.5 hours ($250)",
        total: "$401"
      }
    ],
    relatedCodes: [
      { code: "P0505", desc: "Idle Air Control System Malfunction" },
      { code: "P0507", desc: "Idle Air Control System RPM Higher Than Expected" },
      { code: "P0508", desc: "Idle Air Control System Circuit Low" },
      { code: "P0509", desc: "Idle Air Control System Circuit High" },
      { code: "P0510", desc: "Closed Throttle Position Switch Malfunction" }
    ]
  }
};

// 缺失的C码数据库 - C0300系列（悬挂和转向系统）
const newMissingCCodeDatabase = {
  C0301: {
    title: "Electronic Suspension Control System Malfunction",
    description: "The Electronic Suspension Control module has detected a malfunction in the suspension control system.",
    definition: "The Electronic Suspension Control module has detected a malfunction in the electronic suspension control system. This system automatically adjusts suspension damping and ride height based on driving conditions, vehicle load, and driver preferences. A malfunction can affect ride quality, handling, and vehicle stability.",
    symptoms: [
      "Suspension warning light illuminated - System malfunction detected",
      "Harsh ride quality - Suspension not adjusting properly",
      "Vehicle sagging - Suspension not maintaining proper height",
      "Poor handling - Suspension damping not optimized",
      "Uneven tire wear - Improper suspension geometry",
      "Suspension noise - System components malfunctioning",
      "Reduced vehicle stability - Electronic control compromised",
      "Suspension stuck in one mode - No adaptive adjustment"
    ],
    causes: [
      "Faulty suspension control module - Internal component failure",
      "Damaged suspension sensors - Height or position sensor failure",
      "Air suspension leaks - Loss of system pressure",
      "Faulty suspension actuators - Damper adjustment failure",
      "Damaged suspension wiring - Cut, chafed, or corroded wires",
      "Power supply issues to suspension system - Voltage problems",
      "Suspension compressor failure - No air pressure generation",
      "Clogged suspension air lines - Restricted airflow"
    ],
    performanceImpact: "C0301 compromises electronic suspension control, resulting in poor ride quality, reduced handling performance, uneven tire wear, and potential safety issues due to compromised vehicle stability and control.",
    caseStudies: [
      {
        title: "2018 Mercedes S-Class - Air Suspension Leak",
        vehicle: "2018 Mercedes S-Class, 4.0L V8 Turbo, 65,000 miles",
        symptoms: "Vehicle sagging, harsh ride, C0301 code stored",
        diagnosis: "GeekOBD diagnostic scan revealed C0301 with suspension system fault. Found air leak at rear left air spring causing system pressure loss and inability to maintain proper ride height.",
        solution: "Replaced leaking air spring with OEM Mercedes part, tested system pressure, performed suspension calibration. Cleared codes with GeekOBD APP and road tested - normal ride height and quality restored",
        parts: "OEM Mercedes air spring ($485), suspension sealant ($25)",
        labor: "3.5 hours ($350)",
        total: "$860"
      },
      {
        title: "2016 Audi A8 - Suspension Control Module Failure",
        vehicle: "2016 Audi A8, 3.0L V6 Supercharged, 95,000 miles",
        symptoms: "Suspension stuck in comfort mode, warning light on, C0301 stored",
        diagnosis: "GeekOBD diagnostic scan showed C0301 with suspension control module communication failure. Module testing revealed internal failure preventing adaptive suspension adjustment and mode selection.",
        solution: "Replaced suspension control module with new Audi part, performed complete system programming and calibration. Cleared codes with GeekOBD APP and tested all suspension modes - full functionality restored",
        parts: "Suspension control module ($1285), programming service ($200)",
        labor: "4.0 hours ($400)",
        total: "$1885"
      }
    ],
    relatedCodes: [
      { code: "C0300", desc: "Electronic Suspension System Malfunction" },
      { code: "C0302", desc: "Suspension Height Sensor Circuit Malfunction" },
      { code: "C0303", desc: "Suspension Actuator Circuit Malfunction" },
      { code: "C0304", desc: "Suspension Compressor Circuit Malfunction" },
      { code: "C0305", desc: "Suspension Air Line Malfunction" }
    ]
  },

  P0507: {
    title: "Idle Air Control System RPM Higher Than Expected",
    description: "The Engine Control Module has detected that the idle speed is higher than the expected target RPM.",
    definition: "The Engine Control Module has detected that the engine idle speed is consistently higher than the programmed target RPM. The idle air control (IAC) system manages engine idle speed by controlling the amount of air bypassing the throttle plate. When idle RPM is too high, it can cause rough idle, increased fuel consumption, and poor drivability.",
    symptoms: [
      "Check engine light illuminated - Idle speed too high detected",
      "Engine idle speed too high - Above target RPM",
      "Rough idle - Unstable engine operation at high RPM",
      "Increased fuel consumption - Engine running at higher than necessary RPM",
      "Engine racing at idle - Excessive idle speed",
      "Difficulty engaging transmission - High idle affecting shift quality",
      "Engine noise at idle - Higher RPM creating more noise",
      "Poor cold start behavior - Idle speed not reducing as engine warms"
    ],
    causes: [
      "Faulty idle air control valve - Providing too much air",
      "Vacuum leaks - Unmetered air entering intake system",
      "Dirty or sticking throttle plate - Not closing properly",
      "Faulty throttle position sensor - Incorrect position feedback",
      "Carbon buildup preventing throttle closure - Mechanical restriction",
      "ECM idle control circuit fault - Module malfunction",
      "Intake manifold gasket leaks - Additional air entering system",
      "PCV system malfunction - Excessive crankcase ventilation"
    ],
    performanceImpact: "P0507 causes engine idle speed to be too high, resulting in increased fuel consumption, rough idle, poor drivability, and potential engine damage from excessive RPM during idle conditions.",
    caseStudies: [
      {
        title: "2017 Ford Escape - Vacuum Leak",
        vehicle: "2017 Ford Escape, 2.0L Turbo, 68,000 miles",
        symptoms: "High idle speed, rough idle, P0507 code stored",
        diagnosis: "GeekOBD diagnostic scan revealed P0507 with idle RPM at 1200 (target: 750 RPM). Smoke test revealed large vacuum leak at intake manifold gasket allowing unmetered air into system.",
        solution: "Replaced intake manifold gasket, cleaned throttle body, verified no additional vacuum leaks. Cleared codes with GeekOBD APP and road tested - stable idle at target RPM",
        parts: "Intake manifold gasket set ($85), throttle body cleaner ($15), vacuum hoses ($25)",
        labor: "3.5 hours ($350)",
        total: "$475"
      },
      {
        title: "2016 Honda CR-V - Sticking Throttle Plate",
        vehicle: "2016 Honda CR-V, 2.4L 4-cylinder, 95,000 miles",
        symptoms: "Engine racing at idle, poor fuel economy, P0507 stored",
        diagnosis: "GeekOBD diagnostic scan showed P0507 with consistently high idle speed. Throttle body inspection revealed severe carbon buildup causing throttle plate to stick in partially open position.",
        solution: "Cleaned throttle body and throttle plate, performed throttle body relearn procedure, verified proper throttle closure. Cleared codes with GeekOBD APP and tested - normal idle speed maintained",
        parts: "Throttle body cleaner ($15), throttle plate cleaning kit ($25), new air filter ($28)",
        labor: "2.0 hours ($200)",
        total: "$268"
      }
    ],
    relatedCodes: [
      { code: "P0505", desc: "Idle Air Control System Malfunction" },
      { code: "P0506", desc: "Idle Air Control System RPM Lower Than Expected" },
      { code: "P0508", desc: "Idle Air Control System Circuit Low" },
      { code: "P0509", desc: "Idle Air Control System Circuit High" },
      { code: "P0171", desc: "System Too Lean (Bank 1)" }
    ]
  },

  P0600: {
    title: "Serial Communication Link Malfunction",
    description: "The Engine Control Module has detected a malfunction in the serial communication link.",
    definition: "The Engine Control Module has detected a malfunction in the serial communication link between the ECM and other vehicle control modules. This communication link allows various control modules to share information and coordinate vehicle operations. A malfunction can affect multiple vehicle systems and diagnostic capabilities.",
    symptoms: [
      "Check engine light illuminated - Communication link fault detected",
      "Multiple system malfunctions - Various modules affected",
      "Diagnostic scanner communication issues - Limited access to vehicle data",
      "Transmission shifting problems - ECM-TCM communication lost",
      "ABS system issues - Communication with brake control module affected",
      "Instrument cluster malfunctions - Display and gauge problems",
      "Climate control issues - HVAC module communication problems",
      "Reduced vehicle functionality - Multiple systems operating independently"
    ],
    causes: [
      "ECM internal communication circuit failure - Module malfunction",
      "CAN bus wiring damage - Network communication interrupted",
      "Corroded communication connectors - Signal degradation",
      "Gateway module failure - Central communication hub malfunction",
      "Power supply issues to communication network - Voltage problems",
      "Ground circuit faults - Poor electrical connections",
      "Software corruption in ECM - Communication protocol errors",
      "Electromagnetic interference - Signal disruption"
    ],
    performanceImpact: "P0600 disrupts communication between vehicle control modules, potentially causing multiple system malfunctions, reduced functionality, and difficulty diagnosing other vehicle problems due to limited communication access.",
    caseStudies: [
      {
        title: "2018 Toyota Prius - Gateway Module Failure",
        vehicle: "2018 Toyota Prius, 1.8L Hybrid, 75,000 miles",
        symptoms: "Multiple warning lights, communication errors, P0600 code",
        diagnosis: "GeekOBD diagnostic scan revealed P0600 with limited module communication. Found gateway module failure preventing proper communication between ECM and other vehicle control modules.",
        solution: "Replaced gateway communication module with OEM Toyota part, performed complete system programming and initialization. Cleared codes with GeekOBD APP and verified all module communication - normal operation restored",
        parts: "Gateway communication module ($485), programming service ($150)",
        labor: "3.0 hours ($300)",
        total: "$935"
      },
      {
        title: "2016 BMW 328i - CAN Bus Wiring Damage",
        vehicle: "2016 BMW 328i, 2.0L Turbo, 85,000 miles",
        symptoms: "Intermittent system failures, diagnostic issues, P0600 stored",
        diagnosis: "GeekOBD diagnostic scan showed P0600 with intermittent communication faults. Found damaged CAN bus wiring from aftermarket accessory installation causing signal interference and communication disruption.",
        solution: "Repaired damaged CAN bus wiring, properly routed and shielded harness, removed interfering aftermarket components. Cleared codes with GeekOBD APP and verified stable communication",
        parts: "CAN bus wiring repair kit ($125), shielded harness ($85), proper connectors ($35)",
        labor: "4.0 hours ($400)",
        total: "$645"
      }
    ],
    relatedCodes: [
      { code: "U0100", desc: "Lost Communication with ECM/PCM" },
      { code: "U0101", desc: "Lost Communication with TCM" },
      { code: "U0121", desc: "Lost Communication with ABS Control Module" },
      { code: "U0140", desc: "Lost Communication with Body Control Module" },
      { code: "P0601", desc: "Internal Control Module Memory Check Sum Error" }
    ]
  }
};

// 更多缺失的C码数据库
const additionalMissingCCodeDatabase = {
  C0302: {
    title: "Suspension Height Sensor Circuit Malfunction",
    description: "The Electronic Suspension Control module has detected a malfunction in the suspension height sensor circuit.",
    definition: "The Electronic Suspension Control module has detected a malfunction in the suspension height sensor circuit. These sensors monitor vehicle ride height and provide feedback to the suspension control system for automatic leveling and ride height adjustment. A sensor malfunction prevents proper suspension control.",
    symptoms: [
      "Suspension warning light illuminated - Height sensor fault detected",
      "Vehicle ride height incorrect - Suspension not leveling properly",
      "Uneven vehicle stance - One corner higher or lower than others",
      "Poor ride quality - Suspension not adjusting to load changes",
      "Suspension system disabled - Safety shutdown due to sensor fault",
      "Load leveling not working - No automatic height adjustment",
      "Headlight aim affected - Vehicle attitude changes affecting beam pattern",
      "Handling problems - Improper suspension geometry"
    ],
    causes: [
      "Faulty suspension height sensor - Internal component failure",
      "Damaged height sensor wiring - Cut, chafed, or corroded wires",
      "Corroded sensor connector - Poor electrical connection",
      "Sensor mounting damage - Mechanical failure or misalignment",
      "Suspension control module fault - Sensor input circuit failure",
      "Sensor calibration drift - Age-related accuracy loss",
      "Water damage to sensor - Moisture affecting electronics",
      "Sensor power supply issues - Voltage or ground problems"
    ],
    performanceImpact: "C0302 prevents accurate ride height monitoring, resulting in improper suspension leveling, poor ride quality, uneven tire wear, and potential safety issues due to incorrect vehicle attitude and handling characteristics.",
    caseStudies: [
      {
        title: "2018 Land Rover Range Rover - Sensor Water Damage",
        vehicle: "2018 Land Rover Range Rover, 5.0L V8 Supercharged, 55,000 miles",
        symptoms: "Vehicle sagging on one side, suspension warning, C0302 code",
        diagnosis: "GeekOBD diagnostic scan revealed C0302 with rear left height sensor fault. Found water damage to sensor from off-road use, causing internal circuit corrosion and sensor failure.",
        solution: "Replaced water-damaged height sensor with OEM Land Rover part, sealed sensor connections, performed suspension calibration. Cleared codes with GeekOBD APP and tested - proper ride height restored",
        parts: "OEM Land Rover height sensor ($285), sensor sealant ($15), mounting hardware ($25)",
        labor: "2.5 hours ($250)",
        total: "$575"
      },
      {
        title: "2016 Cadillac Escalade - Sensor Mounting Damage",
        vehicle: "2016 Cadillac Escalade, 6.2L V8, 105,000 miles",
        symptoms: "Uneven ride height, poor handling, C0302 stored",
        diagnosis: "GeekOBD diagnostic scan showed C0302 with front right height sensor fault. Found damaged sensor mounting bracket from road impact, causing sensor misalignment and incorrect readings.",
        solution: "Replaced damaged sensor mounting bracket, realigned height sensor, performed suspension system calibration. Cleared codes with GeekOBD APP and road tested - even ride height and normal handling restored",
        parts: "Height sensor mounting bracket ($125), sensor alignment tool ($35)",
        labor: "3.0 hours ($300)",
        total: "$460"
      }
    ],
    relatedCodes: [
      { code: "C0301", desc: "Electronic Suspension Control System Malfunction" },
      { code: "C0303", desc: "Suspension Actuator Circuit Malfunction" },
      { code: "C0304", desc: "Suspension Compressor Circuit Malfunction" },
      { code: "C0305", desc: "Suspension Air Line Malfunction" },
      { code: "C0306", desc: "Suspension Control Module Communication Error" }
    ]
  }
};

// 页面生成函数
function createDetailedCodePage(code, data) {
  const templateContent = fs.readFileSync('p0142.html', 'utf8');
  let content = templateContent;
  
  // 基本替换
  content = content.replace(/P0142/g, code);
  content = content.replace(/p0142/g, code.toLowerCase());
  
  // 替换标题和描述
  content = content.replace(/O2 Sensor Circuit Malfunction \(Bank 1 Sensor 3\)/g, data.title);
  content = content.replace(/The Engine Control Module has detected a malfunction in the oxygen sensor circuit for Bank 1, Sensor 3 \(post-catalytic converter\)\./g, data.description);
  
  // 替换定义
  const oldDefinition = /The Engine Control Module has detected a malfunction in the oxygen sensor circuit for Bank 1, Sensor 3 \(post-catalytic converter\)\. This diagnostic trouble code indicates a specific issue within the engine\/powertrain that requires attention to ensure proper vehicle operation and safety\./g;
  content = content.replace(oldDefinition, data.definition);
  
  // 替换症状
  if (data.symptoms) {
    const symptomsHtml = data.symptoms.map(symptom => `								<li><strong>${symptom}</strong></li>`).join('\n');
    const symptomsRegex = /<ul>\s*<li><strong>Check Engine Light illuminated[\s\S]*?<\/ul>/;
    content = content.replace(symptomsRegex, `<ul>\n${symptomsHtml}\n							</ul>`);
  }
  
  // 替换原因
  if (data.causes) {
    const causesHtml = data.causes.map(cause => `									<li>${cause}</li>`).join('\n');
    const causesRegex = /<ol>\s*<li>Faulty oxygen sensor[\s\S]*?<\/ol>/;
    content = content.replace(causesRegex, `<ol>\n${causesHtml}\n								</ol>`);
  }
  
  // 替换性能影响
  if (data.performanceImpact) {
    const impactRegex = /<strong><i class="fa fa-exclamation-triangle"><\/i> Performance Impact:<\/strong>[\s\S]*?<\/div>/;
    content = content.replace(impactRegex, `<strong><i class="fa fa-exclamation-triangle"></i> Performance Impact:</strong> ${data.performanceImpact}\n							</div>`);
  }
  
  // 替换维修案例
  if (data.caseStudies && data.caseStudies.length >= 2) {
    const case1 = data.caseStudies[0];
    const case2 = data.caseStudies[1];
    
    const caseStudyHtml = `							<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
								<h4>Case 1: ${case1.title}</h4>
								<p><strong>Vehicle:</strong> ${case1.vehicle}</p>
								<p><strong>Symptoms:</strong> ${case1.symptoms}</p>
								<p><strong>Diagnosis:</strong> ${case1.diagnosis}</p>
								<p><strong>Solution:</strong> ${case1.solution}</p>
								<p><strong>Parts:</strong> ${case1.parts}</p>
								<p><strong>Labor:</strong> ${case1.labor}</p>
								<p><strong>Total Cost:</strong> ${case1.total}</p>
							</div>

							<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
								<h4>Case 2: ${case2.title}</h4>
								<p><strong>Vehicle:</strong> ${case2.vehicle}</p>
								<p><strong>Symptoms:</strong> ${case2.symptoms}</p>
								<p><strong>Diagnosis:</strong> ${case2.diagnosis}</p>
								<p><strong>Solution:</strong> ${case2.solution}</p>
								<p><strong>Parts:</strong> ${case2.parts}</p>
								<p><strong>Labor:</strong> ${case2.labor}</p>
								<p><strong>Total Cost:</strong> ${case2.total}</p>
							</div>`;
    
    const caseStudyRegex = /<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">[\s\S]*?<\/div>\s*<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">[\s\S]*?<\/div>/;
    content = content.replace(caseStudyRegex, caseStudyHtml);
  }
  
  // 替换相关代码
  if (data.relatedCodes) {
    const relatedHtml = data.relatedCodes.map(related => 
      `									<li style="margin-bottom: 10px;"><a href="${related.code.toLowerCase()}.html" style="color: #007bff;">${related.code} - ${related.desc}</a></li>`
    ).join('\n');
    
    const relatedRegex = /<li style="margin-bottom: 10px;"><a href="p0140\.html"[\s\S]*?<li style="margin-bottom: 10px;"><a href="p0145\.html"[\s\S]*?<\/li>/;
    content = content.replace(relatedRegex, relatedHtml);
  }
  
  return content;
}

// 批量创建新发现的缺失页面
const newMissingCodesToCreate = [
  // 新发现的缺失P码 - 排放控制和怠速控制系列
  { code: 'P0401', database: newMissingPCodeDatabase },
  { code: 'P0504', database: newMissingPCodeDatabase },
  { code: 'P0506', database: newMissingPCodeDatabase },
  { code: 'P0507', database: newMissingPCodeDatabase },
  { code: 'P0600', database: newMissingPCodeDatabase },
  // 新发现的缺失C码 - 悬挂系统
  { code: 'C0301', database: newMissingCCodeDatabase },
  { code: 'C0302', database: additionalMissingCCodeDatabase }
];

let totalCreated = 0;

console.log('🚀 Creating newly discovered missing DTC pages...\n');

newMissingCodesToCreate.forEach(({ code, database }) => {
  if (database[code]) {
    try {
      const htmlContent = createDetailedCodePage(code, database[code]);
      fs.writeFileSync(`${code.toLowerCase()}.html`, htmlContent, 'utf8');
      console.log(`✅ Created ${code.toLowerCase()}.html - ${database[code].title}`);
      totalCreated++;
    } catch (error) {
      console.log(`❌ Failed to create ${code.toLowerCase()}.html: ${error.message}`);
    }
  }
});

console.log(`\n🎉 Successfully created ${totalCreated} newly discovered missing DTC pages!`);
console.log('\n📊 Updated Status:');
console.log('We continue to systematically fill gaps in our DTC coverage!');
console.log('Each new page covers important automotive systems and diagnostic scenarios.');
console.log('\nSystematic expansion continues! 🎯');
