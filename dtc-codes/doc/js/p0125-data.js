const { DTCData } = require('./dtc-template-generator');

// P0125 Insufficient Coolant Temperature 的完整数据结构
const p0125Data = new DTCData({
  code: 'P0125',
  title: 'Insufficient Coolant Temperature',
  description: 'The Engine Control Module has determined that the engine coolant temperature is not reaching normal operating temperature within the expected time frame.',
  definition: 'The Engine Control Module has determined that the engine coolant temperature is not reaching normal operating temperature within the expected time frame (typically 5-15 minutes depending on ambient conditions). The ECM expects coolant temperature to reach approximately 160-180°F for proper closed-loop fuel control, emissions system operation, and optimal engine performance. When this temperature threshold is not met, the engine continues running in open-loop mode with richer fuel mixture and may not meet emissions standards.',

  symptoms: [
    'Check engine light illuminated - Insufficient coolant temperature detected',
    'Poor fuel economy - Engine running in open-loop mode longer than normal',
    'Engine runs rough when cold - Extended rich fuel mixture operation',
    'Slow or no cabin heat - Insufficient coolant temperature for heater operation',
    'Engine takes longer to warm up - Thermostat stuck open or missing',
    'White exhaust smoke when cold - Rich fuel mixture from extended warm-up',
    'Failed emissions test - Engine not reaching closed-loop operation temperature',
    'Engine hesitation during warm-up - Incorrect fuel mixture calculations',
    'Cooling fans may not cycle properly - ECM waiting for normal operating temperature'
  ],

  causes: [
    'Stuck open thermostat - Most common cause, prevents engine from reaching operating temperature',
    'Missing thermostat - Removed during previous repair, causing continuous cooling',
    'Low coolant level - Insufficient coolant to maintain proper temperature readings',
    'Faulty ECT sensor - Sensor reading incorrectly low temperatures',
    'Cooling system air pockets - Preventing proper coolant circulation and temperature',
    'Defective radiator cap - Allowing coolant to boil at lower temperatures',
    'External coolant leaks - Radiator, hoses, or water pump leaking coolant',
    'Faulty water pump - Poor coolant circulation preventing proper heating'
  ],
  
  performanceImpact: 'P0125 causes extended open-loop operation with rich fuel mixture, leading to poor fuel economy, increased emissions, potential catalyst damage from unburned fuel, and reduced engine efficiency until proper operating temperature is reached.',

  quickAnswer: {
    icon: 'thermometer-quarter',
    meaning: 'Engine not reaching normal operating temperature (160-180°F) within expected time - usually stuck open thermostat.',
    fix: 'Replace thermostat, check coolant level, repair leaks',
    cost: '$120-$450',
    time: '1-3 hours',
    drivingSafety: 'Safe to drive, but expect poor fuel economy and slow cabin heating. Repair soon to prevent emissions issues.'
  },

  aiQuestions: [
    {
      question: 'What\'s the difference between P0125 and other coolant temperature codes?',
      answer: 'P0125 indicates the engine isn\'t getting hot enough (insufficient temperature), while P0117 shows sensor reading too hot and P0118 shows too cold sensor readings. P0125 is about actual engine temperature, not sensor electrical problems.'
    },
    {
      question: 'Why does P0125 cause poor fuel economy?',
      answer: 'When coolant temperature is insufficient, the ECM keeps the engine in "open-loop" mode with a richer fuel mixture designed for cold operation. This continues much longer than normal, wasting fuel and potentially fouling spark plugs and catalytic converters.'
    },
    {
      question: 'Can I remove the thermostat to fix overheating?',
      answer: 'Never remove the thermostat permanently - this will cause P0125. The thermostat is essential for proper engine temperature regulation. If overheating, find and fix the root cause (radiator, water pump, etc.) rather than removing the thermostat.'
    },
    {
      question: 'How long should it take for my engine to warm up?',
      answer: 'Most engines should reach operating temperature (160-180°F) within 5-15 minutes depending on ambient temperature. Use GeekOBD APP to monitor actual coolant temperature - if it takes longer than 20 minutes or never reaches 160°F, you likely have P0125.'
    }
  ],
  
  costAnalysis: {
    averageCost: '$120-$450 for most P0125 repairs',
    repairOptions: [
      {
        title: 'Thermostat Replacement',
        description: 'Most common fix - Replace stuck open thermostat (70% of cases)',
        color: '#4CAF50',
        icon: 'thermometer-quarter',
        items: [
          { name: 'Thermostat', cost: '$25-$65' },
          { name: 'Gasket/O-ring', cost: '$8-$20' },
          { name: 'Coolant', cost: '$20-$40' },
          { name: 'Labor (1-2 hours)', cost: '$100-$240' }
        ],
        total: '$153-$365',
        successRate: '95% success rate'
      },
      {
        title: 'Coolant System Service',
        description: 'Address low coolant and air pockets (20% of cases)',
        color: '#2196F3',
        icon: 'tint',
        items: [
          { name: 'Coolant flush', cost: '$80-$150' },
          { name: 'System bleeding', cost: '$40-$80' },
          { name: 'Pressure test', cost: '$50-$100' }
        ],
        total: '$170-$330',
        successRate: '85% success rate'
      },
      {
        title: 'Cooling System Repair',
        description: 'Fix leaks or replace water pump (10% of cases)',
        color: '#FF9800',
        icon: 'wrench',
        items: [
          { name: 'Water pump (if needed)', cost: '$150-$350' },
          { name: 'Hoses/gaskets', cost: '$50-$120' },
          { name: 'Labor (2-4 hours)', cost: '$200-$480' }
        ],
        total: '$400-$950',
        successRate: '90% success rate'
      }
    ],
    savingTips: [
      'Start with thermostat replacement - fixes 70% of P0125 cases for under $200',
      'Check coolant level first - low coolant can mimic thermostat problems',
      'Thermostat replacement is often DIY-friendly, saving $100-240 in labor',
      'Combine thermostat replacement with scheduled coolant service to save money',
      'Use GeekOBD APP to monitor temperature before and after repair to confirm fix'
    ]
  },
  
  diagnosticSteps: {
    estimatedTime: 'PT45M',
    steps: [
      {
        title: 'Monitor Engine Warm-Up Time',
        icon: 'clock-o',
        description: 'Start cold engine and monitor how long it takes to reach operating temperature (160-180°F). Time should be 5-15 minutes depending on ambient temperature. Note if temperature plateaus below normal range.',
        geekobdTip: 'Use GeekOBD APP to log coolant temperature over time. Create a temperature vs. time graph to visualize warm-up pattern and identify if engine reaches proper operating temperature.'
      },
      {
        title: 'Check Coolant Level and Condition',
        icon: 'tint',
        description: 'Inspect coolant level in reservoir and radiator (when cool). Check for proper 50/50 mixture, contamination, or air pockets. Low coolant can prevent proper temperature readings.',
        geekobdTip: 'GeekOBD APP can show if ECT readings are erratic, which may indicate air pockets or low coolant affecting sensor accuracy.'
      },
      {
        title: 'Test Thermostat Operation',
        icon: 'thermometer-quarter',
        description: 'Feel upper radiator hose - should remain cool until thermostat opens (around 180°F), then become hot quickly. If hose warms up immediately, thermostat is stuck open.',
        geekobdTip: 'Monitor upper and lower radiator hose temperatures with GeekOBD APP if available, or use infrared thermometer to verify thermostat opening point.'
      },
      {
        title: 'Inspect for External Leaks',
        icon: 'eye',
        description: 'Check radiator, hoses, water pump, and heater core for external coolant leaks. Even small leaks can cause insufficient coolant temperature by reducing system pressure.',
        geekobdTip: 'Use GeekOBD APP to monitor coolant temperature stability - frequent temperature drops may indicate leaks causing coolant loss.'
      },
      {
        title: 'Verify Repair and Road Test',
        icon: 'check-circle',
        description: 'After repair, clear codes and perform extended road test. Monitor warm-up time and verify engine reaches and maintains proper operating temperature under various driving conditions.',
        geekobdTip: 'GeekOBD APP provides continuous monitoring during road test - verify engine reaches 160-180°F within 15 minutes and maintains temperature during highway and city driving.'
      }
    ],
    importantNotes: [
      'P0125 indicates actual temperature problem, not sensor electrical issue',
      'Stuck open thermostat is the most common cause - check this first',
      'Never remove thermostat permanently - this will worsen the problem'
    ]
  },
  
  caseStudies: [
    {
      title: 'Toyota Camry Stuck Open Thermostat',
      vehicle: '2016 Toyota Camry 2.5L 4-cylinder, 89,000 miles',
      problem: 'Customer complained of poor fuel economy (dropped from 32 to 24 MPG), slow cabin heating, and check engine light. Engine seemed to take forever to warm up, especially in winter.',
      diagnosis: 'GeekOBD APP monitoring showed engine taking 25+ minutes to reach 160°F, and never exceeding 165°F even after highway driving. Upper radiator hose was warm immediately after startup, indicating thermostat stuck open.',
      solution: 'Replaced thermostat and housing gasket. Thermostat was completely stuck in open position, allowing continuous coolant flow through radiator.',
      cost: 'Thermostat: $32, Gasket: $12, Coolant: $25, Labor: $140, Total: $209',
      result: 'P0125 code cleared immediately. Engine now reaches 185°F within 8 minutes. Fuel economy returned to 31 MPG, cabin heat works normally.'
    },
    {
      title: 'Honda Civic Low Coolant Issue',
      vehicle: '2018 Honda Civic 1.5L Turbo, 65,000 miles',
      problem: 'Intermittent P0125 code with occasional poor fuel economy. Problem seemed worse in cold weather and after sitting overnight.',
      diagnosis: 'Initial thermostat test seemed normal, but GeekOBD APP showed erratic temperature readings. Found coolant level was 2 quarts low due to small leak in radiator. Air pockets were preventing proper ECT sensor operation.',
      solution: 'Repaired small radiator leak, refilled cooling system, and properly bled air pockets. Performed cooling system pressure test to verify no other leaks.',
      cost: 'Radiator repair: $85, Coolant: $30, System flush/bleed: $95, Total: $210',
      result: 'P0125 code has not returned after 6 months. ECT readings are now stable and engine reaches operating temperature consistently within 10 minutes.'
    }
  ],

  relatedCodes: [
    { code: 'P0117', description: 'ECT Sensor Low Input - Sensor reading too hot temperatures', color: '#e74c3c' },
    { code: 'P0118', description: 'ECT Sensor High Input - Sensor reading too cold temperatures', color: '#3498db' },
    { code: 'P0128', description: 'Coolant Thermostat Rationality - Related thermostat malfunction', color: '#f39c12' },
    { code: 'P0115', description: 'ECT Sensor Circuit Malfunction - General ECT circuit problem', color: '#4a90e2' },
    { code: 'P0172', description: 'System Too Rich Bank 1 - Can be caused by extended open-loop operation', color: '#9b59b6' },
    { code: 'P0175', description: 'System Too Rich Bank 2 - Can be caused by extended open-loop operation', color: '#9b59b6' },
    { code: 'P0420', description: 'Catalyst Efficiency - Can be damaged by rich mixture from P0125', color: '#e67e22' }
  ],
  
  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P0125',
      description: 'Use GeekOBD APP for comprehensive coolant temperature monitoring!',
      features: [
        'Engine warm-up time tracking',
        'Temperature vs. time graphing',
        'Thermostat operation analysis',
        'Cooling system performance monitoring'
      ]
    },
    systemCodes: {
      title: 'Cooling System Codes',
      description: 'Related coolant temperature and thermostat codes:'
    },
    diagnosticResources: [
      {
        title: 'Thermostat Testing Guide',
        description: 'Professional procedures for testing thermostat operation',
        icon: 'thermometer-quarter',
        url: '#diagnostic-steps'
      },
      {
        title: 'Cooling System Bleeding',
        description: 'Proper procedures for removing air pockets',
        icon: 'tint',
        url: '../resources/cooling-system-bleeding.html'
      },
      {
        title: 'Temperature Specifications',
        description: 'Normal operating temperature ranges by vehicle',
        icon: 'line-chart',
        url: '../resources/temperature-specifications.html'
      },
      {
        title: 'Thermostat Replacement',
        description: 'Step-by-step thermostat replacement procedures',
        icon: 'wrench',
        url: '../resources/thermostat-replacement.html'
      }
    ],
    codeInfo: {
      system: 'Engine Management',
      severity: 'MEDIUM',
      category: 'Coolant Temperature'
    }
  }
});

module.exports = p0125Data;