const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0401Data = require('./p0401-data');

console.log('🚀 Generating P0401 - EGR Flow Insufficient Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0401Data);
  const outputPath = path.join(__dirname, '../../p0401.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0401 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0401 page:', error.message);
  process.exit(1);
}
