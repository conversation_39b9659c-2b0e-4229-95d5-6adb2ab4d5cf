const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p1151Data = require('./p1151-data');

console.log('🚀 Generating P1151 - Lack of Downstream Heated Oxygen Sensor Switch - Sensor Indicates Rich Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p1151Data);
  const outputPath = path.join(__dirname, '../../p1151.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P1151 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P1151 page:', error.message);
  process.exit(1);
}
