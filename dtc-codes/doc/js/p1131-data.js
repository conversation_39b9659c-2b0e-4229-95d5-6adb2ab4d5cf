const { DTCData } = require('./dtc-template-generator');

// P1131 Lack of Upstream Heated Oxygen Sensor Switch - Sensor Indicates Rich 的完整数据结构
const p1131Data = new DTCData({
  code: 'P1131',
  title: 'Lack of Upstream Heated Oxygen Sensor Switch - Sensor Indicates Rich',
  description: 'The upstream heated oxygen sensor is not switching properly and indicates a rich condition.',
  definition: 'P1131 indicates that the upstream heated oxygen sensor (HO2S) is not switching properly between rich and lean readings and is stuck indicating a rich condition. This is primarily a Ford-specific code. The upstream oxygen sensor should rapidly switch between approximately 0.1V (lean) and 0.9V (rich) as the ECM adjusts the fuel mixture. When the sensor remains at high voltage (rich indication) and doesn\'t switch properly, P1131 is triggered.',
  
  symptoms: [
    'Check engine light illuminated - ECM detected oxygen sensor switching problem',
    'Poor fuel economy - ECM compensating for perceived rich condition',
    'Engine hesitation - Incorrect fuel mixture affecting performance',
    'Rough idle - Unstable fuel mixture causing idle problems',
    'Engine surging - Fluctuating power from incorrect fuel control',
    'White or light smoke from exhaust - Lean mixture from ECM overcompensation',
    'Engine stalling - Severe fuel mixture problems causing shutdown',
    'Poor acceleration - Incorrect air/fuel ratio affecting power delivery',
    'Failed emissions test - Improper fuel control affecting emissions'
  ],
  
  causes: [
    'Faulty upstream oxygen sensor - Sensor not switching properly between rich/lean',
    'Contaminated oxygen sensor - Fuel additives or oil affecting sensor readings',
    'Fuel system problems - High fuel pressure or leaking injectors causing rich mixture',
    'Clogged air filter - Restricted airflow causing actual rich condition',
    'Faulty fuel pressure regulator - Excessive fuel pressure causing rich mixture',
    'Damaged oxygen sensor wiring - Electrical problems affecting sensor signal',
    'ECM internal fault - Control module not properly interpreting sensor signals',
    'Mass airflow sensor problems - Incorrect air measurement affecting fuel control'
  ],
  
  performanceImpact: 'P1131 causes poor fuel economy, rough running, engine hesitation, and potential stalling due to incorrect fuel mixture control based on faulty oxygen sensor feedback.',
  
  quickAnswer: {
    icon: 'thermometer-full',
    meaning: 'Upstream oxygen sensor stuck reading rich - usually faulty sensor or actual rich condition.',
    fix: 'Check fuel pressure, test oxygen sensor operation, replace O2 sensor if needed',
    cost: '$180-$520',
    time: '90-180 minutes',
    drivingSafety: 'Safe to drive but expect poor performance and fuel economy. Check fuel system first.'
  },
  
  aiQuestions: [
    {
      question: 'What\'s the difference between P1130 and P1131?',
      answer: 'P1130 indicates the oxygen sensor is stuck reading lean (low voltage), while P1131 indicates it\'s stuck reading rich (high voltage). Both indicate the sensor is not switching properly between rich and lean readings.'
    },
    {
      question: 'Can high fuel pressure cause P1131?',
      answer: 'Yes, high fuel pressure can cause P1131 by creating an actual rich condition. The oxygen sensor correctly reads rich, but doesn\'t switch to lean because the engine is actually running rich due to excessive fuel delivery.'
    },
    {
      question: 'Why would the ECM make the mixture lean when the sensor reads rich?',
      answer: 'The ECM relies on oxygen sensor feedback to control fuel mixture. If the sensor is stuck reading rich, the ECM will reduce fuel delivery thinking the mixture is rich, potentially creating a lean condition despite the rich reading.'
    },
    {
      question: 'Can a clogged air filter cause P1131?',
      answer: 'Yes, a severely clogged air filter can restrict airflow enough to create an actual rich condition. With less air entering the engine, the fuel mixture becomes richer, and the oxygen sensor correctly reads this rich condition.'
    }
  ],

  costAnalysis: {
    averageCost: '$180-$520 for most P1131 repairs',
    repairOptions: [
      {
        title: 'Air Filter Replacement',
        description: 'Replace clogged air filter causing rich condition (30% of cases)',
        color: '#4CAF50',
        icon: 'filter',
        items: [
          { name: 'Air filter', cost: '$15-$45' },
          { name: 'Labor (15-30 minutes)', cost: '$25-$60' }
        ],
        total: '$40-$105',
        successRate: '70% success rate'
      },
      {
        title: 'Upstream O2 Sensor Replacement',
        description: 'Replace faulty upstream oxygen sensor (50% of cases)',
        color: '#2196F3',
        icon: 'thermometer-full',
        items: [
          { name: 'Upstream O2 sensor', cost: '$80-$200' },
          { name: 'Labor (1-1.5 hours)', cost: '$100-$180' }
        ],
        total: '$180-$380',
        successRate: '90% success rate'
      },
      {
        title: 'Fuel System Repair',
        description: 'Fix fuel pressure or injector problems (20% of cases)',
        color: '#FF9800',
        icon: 'gas-pump',
        items: [
          { name: 'Fuel system components', cost: '$150-$400' },
          { name: 'Labor (2-4 hours)', cost: '$200-$480' }
        ],
        total: '$350-$880',
        successRate: '95% success rate'
      }
    ],
    savingTips: [
      'Check air filter first - simple and inexpensive potential fix',
      'Use GeekOBD APP to monitor O2 sensor switching before replacement',
      'Test fuel pressure if air filter is clean',
      'Address P1131 promptly to prevent catalytic converter damage',
      'Monitor fuel trims to help identify root cause'
    ]
  },

  diagnosticSteps: {
    estimatedTime: 'PT120M',
    steps: [
      {
        title: 'Monitor O2 Sensor Operation',
        icon: 'search',
        description: 'Connect GeekOBD APP and monitor upstream oxygen sensor voltage. Sensor should switch between 0.1V and 0.9V, not stay at high voltage.',
        geekobdTip: 'GeekOBD APP can show O2 sensor voltage in real-time - healthy sensor switches rapidly, stuck sensor shows constant high voltage.'
      },
      {
        title: 'Check Air Filter Condition',
        icon: 'filter',
        description: 'Remove and inspect air filter for dirt, debris, or severe clogging that could restrict airflow and cause rich condition.',
        geekobdTip: 'Monitor fuel trims with GeekOBD APP before and after air filter replacement - negative trims may indicate rich conditions.'
      },
      {
        title: 'Test Fuel System Pressure',
        icon: 'gas-pump',
        description: 'Check fuel pressure and pressure regulator operation. High fuel pressure can cause rich conditions.',
        geekobdTip: 'Use GeekOBD APP to monitor fuel trims - high negative trims may indicate fuel system problems causing rich operation.'
      },
      {
        title: 'Check for Fuel Injector Problems',
        icon: 'syringe',
        description: 'Test fuel injectors for leaking or excessive flow that could cause rich mixture conditions.',
        geekobdTip: 'Monitor individual cylinder fuel trims with GeekOBD APP if available - identify specific cylinders with rich conditions.'
      },
      {
        title: 'Component Replacement and Verification',
        icon: 'check-circle',
        description: 'Replace oxygen sensor or repair identified problems. Clear codes and verify proper O2 sensor switching.',
        geekobdTip: 'Use GeekOBD APP to verify new O2 sensor switches properly between rich and lean readings during normal operation.'
      }
    ],
    importantNotes: [
      'P1131 can indicate faulty sensor or actual rich condition',
      'Check air filter and fuel system before replacing oxygen sensor',
      'Monitor fuel trims to help identify rich condition causes'
    ]
  },

  caseStudies: [
    {
      title: 'Ford Mustang Clogged Air Filter',
      vehicle: '2016 Ford Mustang 2.3L Turbo, 95,000 miles',
      problem: 'Customer reported poor fuel economy, sluggish acceleration, and P1131 code. Engine seemed to lack power.',
      diagnosis: 'GeekOBD APP showed upstream O2 sensor stuck at high voltage. Found air filter was severely clogged, restricting airflow.',
      solution: 'Replaced clogged air filter with OEM part. Air filter was so dirty it was restricting airflow and causing rich mixture.',
      cost: 'Air filter: $28, Labor: $35, Total: $63',
      result: 'P1131 code cleared after air filter replacement. O2 sensor now switches properly and fuel economy improved by 3 MPG.'
    },
    {
      title: 'Ford F-150 High Fuel Pressure',
      vehicle: '2017 Ford F-150 3.5L V6, 115,000 miles',
      problem: 'P1131 code with black smoke from exhaust and poor performance. Air filter was clean.',
      diagnosis: 'Fuel pressure was 65 PSI instead of normal 45 PSI. Faulty fuel pressure regulator was causing excessive fuel pressure.',
      solution: 'Replaced fuel pressure regulator and cleaned fuel injectors. High pressure was causing rich mixture condition.',
      cost: 'Fuel pressure regulator: $85, Injector cleaning: $120, Labor: $180, Total: $385',
      result: 'P1131 code cleared and fuel pressure now normal. Black smoke eliminated and engine performance restored.'
    }
  ],

  relatedCodes: [
    { code: 'P1130', description: 'Lack of Upstream HO2S Switch - Sensor Indicates Lean - Opposite condition', color: '#e74c3c' },
    { code: 'P0131', description: 'O2 Sensor Circuit Low Voltage Bank 1 Sensor 1 - Related upstream O2 sensor', color: '#3498db' },
    { code: 'P0132', description: 'O2 Sensor Circuit High Voltage Bank 1 Sensor 1 - Related upstream O2 sensor', color: '#f39c12' },
    { code: 'P0133', description: 'O2 Sensor Circuit Slow Response Bank 1 Sensor 1 - Related O2 sensor response', color: '#9b59b6' },
    { code: 'P0172', description: 'System Too Rich Bank 1 - Related rich condition', color: '#4a90e2' },
    { code: 'P0175', description: 'System Too Rich Bank 2 - Related rich condition', color: '#e67e22' },
    { code: 'P0420', description: 'Catalyst System Efficiency Below Threshold - Can result from O2 sensor problems', color: '#27ae60' }
  ],

  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P1131',
      description: 'Use GeekOBD APP for oxygen sensor testing!',
      features: [
        'Real-time O2 sensor monitoring',
        'Switching pattern analysis',
        'Fuel trim monitoring',
        'Rich condition detection'
      ]
    },
    systemCodes: {
      title: 'Oxygen Sensor Codes',
      description: 'Related oxygen sensor and fuel system codes:'
    },
    diagnosticResources: [
      {
        title: 'O2 Sensor Testing',
        description: 'Professional procedures for testing oxygen sensors',
        icon: 'thermometer-full',
        url: '#diagnostic-steps'
      },
      {
        title: 'Rich Condition Diagnosis',
        description: 'Diagnosing and fixing rich running conditions',
        icon: 'balance-scale',
        url: '../resources/rich-condition-diagnosis.html'
      },
      {
        title: 'Fuel Pressure Testing',
        description: 'Testing fuel pressure and pressure regulator systems',
        icon: 'gas-pump',
        url: '../resources/fuel-pressure-testing.html'
      },
      {
        title: 'Air Filter Service',
        description: 'Air filter inspection and replacement procedures',
        icon: 'filter',
        url: '../resources/air-filter-service.html'
      }
    ],
    codeInfo: {
      system: 'Fuel System',
      severity: 'MEDIUM',
      category: 'Oxygen Sensor'
    }
  }
});

module.exports = p1131Data;
