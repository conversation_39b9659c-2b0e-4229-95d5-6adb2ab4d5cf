const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p1131Data = require('./p1131-data');

console.log('🚀 Generating P1131 - Lack of Upstream Heated Oxygen Sensor Switch - Sensor Indicates Rich Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p1131Data);
  const outputPath = path.join(__dirname, '../../p1131.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P1131 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P1131 page:', error.message);
  process.exit(1);
}
