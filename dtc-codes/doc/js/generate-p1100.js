const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p1100Data = require('./p1100-data');

console.log('🚀 Generating P1100 - Mass Air Flow Sensor Intermittent Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p1100Data);
  const outputPath = path.join(__dirname, '../../p1100.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P1100 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P1100 page:', error.message);
  process.exit(1);
}
