const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0301Data = require('./p0301-data');

console.log('🚀 Generating P0301 - Cylinder 1 Misfire Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0301Data);
  const outputPath = path.join(__dirname, '../../p0301.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0301 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0301 page:', error.message);
  process.exit(1);
}
