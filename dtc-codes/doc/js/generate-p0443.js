const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0443Data = require('./p0443-data');

console.log('🚀 Generating P0443 - EVAP Purge Valve Circuit Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0443Data);
  const outputPath = path.join(__dirname, '../../p0443.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0443 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0443 page:', error.message);
  process.exit(1);
}
