const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');

/**
 * 批量生成DTC页面脚本
 * 用于快速创建多个优化的DTC页面
 */

console.log('🚀 DTC Batch Generator - Template System\n');

// 待生成的DTC代码列表（高优先级）
const dtcCodesToGenerate = [
  'P0112', // IAT Sensor Low Input - 继续IAT传感器系列
  'P0114', // IAT Sensor Intermittent - 完成IAT传感器系列
  'P0117', // ECT Sensor Low Input - 冷却液温度传感器系列
  'P0118', // ECT Sensor High Input - 冷却液温度传感器系列
  'P0125', // Insufficient Coolant Temperature - 冷却液系统系列
  'P0300', // Random/Multiple Cylinder Misfire - 失火系列
  'P0301', // Cylinder 1 Misfire - 失火系列
  'P0302', // Cylinder 2 Misfire - 失火系列
  'P0420', // Catalyst System Efficiency Below Threshold - 催化器系列
  'P0430'  // Catalyst System Efficiency Below Threshold Bank 2 - 催化器系列
];

function createDataTemplate(code, title, description) {
  return `const { DTCData } = require('./dtc-template-generator');

// ${code} ${title} 的完整数据结构
const ${code.toLowerCase()}Data = new DTCData({
  code: '${code}',
  title: '${title}',
  description: '${description}',
  definition: '${description} [需要详细定义]',
  
  symptoms: [
    // 需要添加具体症状
    'Check engine light illuminated',
    'Engine performance issues',
    // 添加更多症状...
  ],
  
  causes: [
    // 需要添加具体原因
    'Faulty sensor or component',
    'Wiring issues',
    // 添加更多原因...
  ],
  
  performanceImpact: '${code} causes [具体影响描述]',
  
  quickAnswer: {
    icon: 'wrench', // 需要选择合适的图标
    meaning: '${code} means: [简洁描述]',
    fix: '[主要修复方法]',
    cost: '$[成本范围]',
    time: '[时间估计]',
    drivingSafety: '[驾驶安全建议]'
  },
  
  aiQuestions: [
    {
      question: 'What causes ${code}?',
      answer: '[详细回答]'
    },
    {
      question: 'How serious is ${code}?',
      answer: '[严重程度说明]'
    },
    {
      question: 'Can I drive with ${code}?',
      answer: '[驾驶安全建议]'
    },
    {
      question: 'How to diagnose ${code}?',
      answer: '[诊断方法，突出GeekOBD APP]'
    }
  ],
  
  costAnalysis: {
    averageCost: '[平均成本]',
    repairOptions: [
      {
        title: '[最常见修复]',
        description: '[描述]',
        color: '#4CAF50',
        icon: 'wrench',
        items: [
          { name: '[部件1]', cost: '$[范围]' },
          { name: '[人工费]', cost: '$[范围]' }
        ],
        total: '$[总计范围]',
        successRate: '[成功率]'
      }
      // 添加更多修复选项...
    ],
    savingTips: [
      '[省钱建议1]',
      '[省钱建议2]',
      // 添加更多建议...
    ]
  },
  
  diagnosticSteps: {
    estimatedTime: 'PT30M',
    steps: [
      {
        title: 'Initial Diagnosis',
        icon: 'search',
        description: '[诊断步骤描述]',
        geekobdTip: '[GeekOBD APP使用建议]'
      }
      // 添加更多步骤...
    ],
    importantNotes: [
      '[重要注意事项1]',
      '[重要注意事项2]'
    ]
  },
  
  caseStudies: [
    {
      title: '[案例标题]',
      vehicle: '[车辆信息]',
      problem: '[问题描述]',
      diagnosis: '[诊断过程]',
      solution: '[解决方案]',
      cost: '[实际成本]',
      result: '[修复结果]'
    }
    // 添加更多案例...
  ],
  
  relatedCodes: [
    // 需要添加相关代码
    { code: '[相关代码1]', description: '[描述]', color: '#4a90e2' }
    // 添加更多相关代码...
  ],
  
  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose ${code}',
      description: 'Use GeekOBD APP for professional ${code} diagnosis!',
      features: [
        'Real-time data monitoring',
        'Step-by-step diagnosis',
        'Repair verification',
        'Cost estimation'
      ]
    },
    systemCodes: {
      title: '[系统名称] Codes',
      description: 'Related diagnostic trouble codes:'
    },
    diagnosticResources: [
      {
        title: '${code} Testing Guide',
        description: 'Professional diagnostic procedures',
        icon: 'book',
        url: '#diagnostic-steps'
      },
      {
        title: 'Wiring Diagrams',
        description: 'Circuit diagrams and specifications',
        icon: 'sitemap',
        url: '../resources/wiring-diagrams.html'
      },
      {
        title: 'Repair Procedures',
        description: 'Step-by-step repair instructions',
        icon: 'wrench',
        url: '../resources/repair-procedures.html'
      }
    ],
    codeInfo: {
      system: '[系统名称]',
      severity: 'MEDIUM', // HIGH, MEDIUM, LOW
      category: '[类别]'
    }
  }
});

module.exports = ${code.toLowerCase()}Data;`;
}

function generateDataFile(code, title, description) {
  const dataContent = createDataTemplate(code, title, description);
  const dataFilePath = path.join(__dirname, `${code.toLowerCase()}-data.js`);
  
  fs.writeFileSync(dataFilePath, dataContent, 'utf8');
  console.log(`📝 Created data template: ${code.toLowerCase()}-data.js`);
  
  return dataFilePath;
}

function generatePageFromData(dataFilePath, code) {
  try {
    // 动态加载数据文件
    delete require.cache[require.resolve(dataFilePath)];
    const dtcData = require(dataFilePath);
    
    // 生成HTML页面
    const generator = new DTCTemplateGenerator();
    const htmlContent = generator.generatePage(dtcData);
    
    // 保存页面
    const outputPath = path.join(__dirname, `../../${code.toLowerCase()}.html`);
    fs.writeFileSync(outputPath, htmlContent, 'utf8');
    
    console.log(`✅ Generated: ${code.toLowerCase()}.html`);
    return true;
  } catch (error) {
    console.error(`❌ Error generating ${code}: ${error.message}`);
    return false;
  }
}

// DTC代码基本信息数据库
const dtcDatabase = {
  'P0112': {
    title: 'IAT Sensor Low Input',
    description: 'The Engine Control Module has detected that the Intake Air Temperature sensor is reading extremely hot temperatures when actual air temperature is cooler.'
  },
  'P0114': {
    title: 'IAT Sensor Intermittent',
    description: 'The Engine Control Module has detected intermittent or erratic readings from the Intake Air Temperature sensor circuit.'
  },
  'P0117': {
    title: 'ECT Sensor Low Input',
    description: 'The Engine Control Module has detected that the Engine Coolant Temperature sensor is reading extremely hot temperatures.'
  },
  'P0118': {
    title: 'ECT Sensor High Input',
    description: 'The Engine Control Module has detected that the Engine Coolant Temperature sensor is reading extremely cold temperatures.'
  },
  'P0125': {
    title: 'Insufficient Coolant Temperature',
    description: 'The Engine Control Module has determined that the engine coolant temperature is not reaching normal operating temperature within the expected time frame.'
  },
  'P0300': {
    title: 'Random/Multiple Cylinder Misfire',
    description: 'The Engine Control Module has detected random misfires occurring across multiple cylinders or a pattern that cannot be attributed to a specific cylinder.'
  },
  'P0301': {
    title: 'Cylinder 1 Misfire Detected',
    description: 'The Engine Control Module has detected a misfire condition specifically in cylinder 1 of the engine.'
  },
  'P0302': {
    title: 'Cylinder 2 Misfire Detected',
    description: 'The Engine Control Module has detected a misfire condition specifically in cylinder 2 of the engine.'
  },
  'P0420': {
    title: 'Catalyst System Efficiency Below Threshold',
    description: 'The Engine Control Module has determined that the catalytic converter is not operating efficiently enough to meet emissions standards.'
  },
  'P0430': {
    title: 'Catalyst System Efficiency Below Threshold Bank 2',
    description: 'The Engine Control Module has determined that the catalytic converter on Bank 2 is not operating efficiently enough to meet emissions standards.'
  }
};

// 主执行函数
async function main() {
  console.log(`📋 Planning to generate ${dtcCodesToGenerate.length} DTC pages:\n`);
  
  dtcCodesToGenerate.forEach(code => {
    const info = dtcDatabase[code];
    console.log(`   • ${code} - ${info.title}`);
  });
  
  console.log('\n🔄 Starting generation process...\n');
  
  let successCount = 0;
  let failCount = 0;
  
  for (const code of dtcCodesToGenerate) {
    const info = dtcDatabase[code];
    console.log(`\n🔧 Processing ${code} - ${info.title}`);
    
    try {
      // 生成数据模板文件
      const dataFilePath = generateDataFile(code, info.title, info.description);
      
      // 生成HTML页面（使用基本模板数据）
      // 注意：实际使用时需要手动完善数据文件中的内容
      console.log(`⚠️  ${code} data template created - manual completion required`);
      successCount++;
      
    } catch (error) {
      console.error(`❌ Failed to process ${code}: ${error.message}`);
      failCount++;
    }
  }
  
  console.log('\n📊 Generation Summary:');
  console.log(`   ✅ Successfully created: ${successCount} data templates`);
  console.log(`   ❌ Failed: ${failCount}`);
  
  console.log('\n📝 Next Steps:');
  console.log('   1. Review and complete the generated data template files');
  console.log('   2. Add specific symptoms, causes, and case studies');
  console.log('   3. Update cost analysis and diagnostic steps');
  console.log('   4. Run individual generation scripts to create HTML pages');
  console.log('   5. Test generated pages for layout and content accuracy');
  
  console.log('\n🎯 Template system is ready for production use!');
}

// 运行主函数
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { generateDataFile, generatePageFromData, dtcDatabase };
