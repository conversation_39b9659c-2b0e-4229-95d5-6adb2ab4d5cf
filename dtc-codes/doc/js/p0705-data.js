const { DTCData } = require('./dtc-template-generator');

// P0705 Transmission Range Sensor Circuit Malfunction 的完整数据结构
const p0705Data = new DTCData({
  code: 'P0705',
  title: 'Transmission Range Sensor Circuit Malfunction',
  description: 'The Engine Control Module has detected a malfunction in the transmission range sensor circuit.',
  definition: 'The Engine Control Module has detected a malfunction in the transmission range sensor (TRS) circuit, also known as the Park/Neutral Position (PNP) switch or Transmission Position Sensor. This sensor tells the ECM and TCM which gear position the transmission is in (Park, Reverse, Neutral, Drive, etc.). When there are electrical problems with the TRS circuit, such as open circuits, short circuits, or implausible signals, P0705 is triggered.',
  
  symptoms: [
    'Check engine light illuminated - ECM detected TRS circuit malfunction',
    'Engine won\'t start - Starter safety circuit affected by TRS problems',
    'Starts in any gear - Safety interlock not working properly',
    'Backup lights not working - Reverse signal not being sent',
    'Transmission shifting problems - TCM not receiving proper gear position',
    'Speedometer not working - Some vehicles use TRS for speed calculation',
    'Cruise control not working - System needs gear position information',
    'Harsh shifting - TCM using incorrect gear position data',
    'No Park position recognition - Vehicle may roll when in Park'
  ],
  
  causes: [
    'Faulty transmission range sensor - Internal sensor failure',
    'Damaged TRS wiring - Broken or corroded wires affecting signal',
    'Corroded TRS connector - Poor electrical contact affecting operation',
    'Misadjusted transmission range sensor - Sensor not aligned properly',
    'Faulty shift linkage - Mechanical problems affecting sensor position',
    'ECM/TCM internal fault - Control modules unable to process TRS signals',
    'Damaged transmission case - Physical damage affecting sensor mounting',
    'Contamination on sensor - Fluid or debris affecting sensor operation'
  ],
  
  performanceImpact: 'P0705 can prevent engine starting, cause unsafe starting in gear, affect transmission shifting quality, and disable various safety and convenience systems that rely on gear position information.',
  
  quickAnswer: {
    icon: 'exchange-alt',
    meaning: 'Transmission range sensor not telling ECM which gear is selected - usually sensor or wiring problem.',
    fix: 'Test TRS wiring, adjust sensor position, replace transmission range sensor',
    cost: '$180-$520',
    time: '90-180 minutes',
    drivingSafety: 'May be unsafe - engine might start in gear or vehicle may not recognize Park. Have diagnosed immediately.'
  },
  
  aiQuestions: [
    {
      question: 'What does the transmission range sensor do?',
      answer: 'The transmission range sensor tells the ECM and TCM which gear position is selected (Park, Reverse, Neutral, Drive, etc.). This information is used for starter safety, backup lights, transmission control, and various other systems.'
    },
    {
      question: 'Why won\'t my engine start with P0705?',
      answer: 'The transmission range sensor is part of the starter safety circuit. If the ECM can\'t determine that the transmission is in Park or Neutral, it won\'t allow the starter to engage for safety reasons.'
    },
    {
      question: 'Can P0705 cause transmission shifting problems?',
      answer: 'Yes, the TCM uses transmission range sensor information to determine proper shift patterns and timing. Without accurate gear position data, the transmission may shift harshly or at incorrect times.'
    },
    {
      question: 'How do I test the transmission range sensor?',
      answer: 'Use GeekOBD APP to monitor TRS signal while moving the gear selector through different positions. The signal should change predictably with each gear position. You can also test sensor resistance and wiring continuity.'
    }
  ],

  costAnalysis: {
    averageCost: '$180-$520 for most P0705 repairs',
    repairOptions: [
      {
        title: 'TRS Adjustment',
        description: 'Adjust misaligned transmission range sensor (25% of cases)',
        color: '#4CAF50',
        icon: 'wrench',
        items: [
          { name: 'Adjustment procedure', cost: '$0' },
          { name: 'Labor (1-1.5 hours)', cost: '$100-$180' }
        ],
        total: '$100-$180',
        successRate: '80% success rate'
      },
      {
        title: 'TRS Replacement',
        description: 'Replace faulty transmission range sensor (60% of cases)',
        color: '#2196F3',
        icon: 'exchange-alt',
        items: [
          { name: 'Transmission range sensor', cost: '$80-$200' },
          { name: 'Labor (1.5-2.5 hours)', cost: '$150-$300' }
        ],
        total: '$230-$500',
        successRate: '95% success rate'
      },
      {
        title: 'Wiring Repair',
        description: 'Fix damaged TRS wiring or connector (15% of cases)',
        color: '#FF9800',
        icon: 'flash',
        items: [
          { name: 'Wiring repair materials', cost: '$30-$80' },
          { name: 'Labor (1-2 hours)', cost: '$100-$240' }
        ],
        total: '$130-$320',
        successRate: '90% success rate'
      }
    ],
    savingTips: [
      'Try TRS adjustment first - may fix problem without parts replacement',
      'Check TRS connector for corrosion before replacing sensor',
      'Use GeekOBD APP to verify TRS signal before replacement',
      'Some TRS sensors are accessible for DIY replacement',
      'Address P0705 promptly for safety - affects starter interlock system'
    ]
  },

  diagnosticSteps: {
    estimatedTime: 'PT120M',
    steps: [
      {
        title: 'Test TRS Signal Operation',
        icon: 'search',
        description: 'Connect GeekOBD APP and monitor transmission range sensor signal while moving gear selector through all positions.',
        geekobdTip: 'GeekOBD APP can show TRS position in real-time - signal should change predictably with each gear position (P, R, N, D, etc.).'
      },
      {
        title: 'Check TRS Electrical Circuit',
        icon: 'bolt',
        description: 'Test TRS power supply, ground, and signal circuits with multimeter. Verify proper voltage and continuity.',
        geekobdTip: 'Use GeekOBD APP to monitor TRS voltage while testing circuits - voltage should be stable and within specifications.'
      },
      {
        title: 'Inspect TRS Mounting and Adjustment',
        icon: 'eye',
        description: 'Visual inspection of TRS sensor mounting, alignment, and mechanical linkage. Check for proper sensor positioning.',
        geekobdTip: 'Monitor TRS signal with GeekOBD APP while checking alignment - signal should be clean and consistent when properly adjusted.'
      },
      {
        title: 'Test Shift Linkage Operation',
        icon: 'link',
        description: 'Check transmission shift linkage for proper operation and adjustment. Ensure linkage moves TRS through full range.',
        geekobdTip: 'GeekOBD APP can show if TRS reaches all expected positions - incomplete range indicates linkage problems.'
      },
      {
        title: 'Component Replacement and Verification',
        icon: 'check-circle',
        description: 'Adjust TRS position or replace sensor as diagnosed. Clear codes and verify proper operation in all gear positions.',
        geekobdTip: 'Use GeekOBD APP to verify TRS now provides correct signals for all gear positions and starter safety system works properly.'
      }
    ],
    importantNotes: [
      'TRS affects starter safety system - engine should only start in Park/Neutral',
      'Check TRS adjustment before replacement - may just need alignment',
      'Verify backup lights work properly after TRS service'
    ]
  },

  caseStudies: [
    {
      title: 'Honda Accord TRS Adjustment',
      vehicle: '2016 Honda Accord 2.4L CVT, 108,000 miles',
      problem: 'Customer reported engine would not start and P0705 code. Backup lights were also not working when in reverse.',
      diagnosis: 'GeekOBD APP showed TRS was not reaching proper Park position signal. Visual inspection revealed TRS was slightly misaligned after recent transmission service.',
      solution: 'Adjusted transmission range sensor position according to Honda specifications. Sensor alignment was off by approximately 2 degrees.',
      cost: 'TRS adjustment: $0, Labor: $120, Total: $120',
      result: 'P0705 code cleared and engine now starts properly in Park/Neutral only. Backup lights work correctly in reverse.'
    },
    {
      title: 'Ford Explorer TRS Failure',
      vehicle: '2017 Ford Explorer 3.5L V6, 95,000 miles',
      problem: 'P0705 code with intermittent no-start condition. Sometimes engine would start in Drive position, creating safety concern.',
      diagnosis: 'TRS adjustment was correct, but GeekOBD APP showed erratic TRS signals. Sensor was providing inconsistent position data due to internal failure.',
      solution: 'Replaced transmission range sensor with OEM part. Sensor had failed internally and could not provide reliable position signals.',
      cost: 'TRS sensor: $145, Labor: $180, Total: $325',
      result: 'P0705 code cleared permanently. Engine now starts only in Park/Neutral and all gear position signals are accurate.'
    }
  ],

  relatedCodes: [
    { code: 'P0700', description: 'Transmission Control System Malfunction - General transmission problem', color: '#e74c3c' },
    { code: 'P0706', description: 'Transmission Range Sensor Range/Performance - TRS signal out of range', color: '#3498db' },
    { code: 'P0707', description: 'Transmission Range Sensor Low Input - TRS signal too low', color: '#f39c12' },
    { code: 'P0708', description: 'Transmission Range Sensor High Input - TRS signal too high', color: '#9b59b6' },
    { code: 'P0709', description: 'Transmission Range Sensor Intermittent - Intermittent TRS signal', color: '#4a90e2' },
    { code: 'P0500', description: 'Vehicle Speed Sensor Malfunction - Related speed sensor problems', color: '#e67e22' },
    { code: 'P0720', description: 'Output Speed Sensor Circuit - Related transmission sensor', color: '#27ae60' }
  ],

  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P0705',
      description: 'Use GeekOBD APP for transmission range sensor testing!',
      features: [
        'Real-time TRS monitoring',
        'Gear position verification',
        'Signal quality analysis',
        'Safety system testing'
      ]
    },
    systemCodes: {
      title: 'Transmission Sensor Codes',
      description: 'Related transmission range sensor codes:'
    },
    diagnosticResources: [
      {
        title: 'TRS Testing Procedures',
        description: 'Professional procedures for testing transmission range sensors',
        icon: 'exchange-alt',
        url: '#diagnostic-steps'
      },
      {
        title: 'TRS Adjustment Guide',
        description: 'Proper transmission range sensor adjustment procedures',
        icon: 'wrench',
        url: '../resources/trs-adjustment-guide.html'
      },
      {
        title: 'Starter Safety Systems',
        description: 'Understanding Park/Neutral safety interlock systems',
        icon: 'shield-alt',
        url: '../resources/starter-safety-systems.html'
      },
      {
        title: 'Transmission Linkage',
        description: 'Diagnosing and adjusting transmission shift linkage',
        icon: 'link',
        url: '../resources/transmission-linkage.html'
      }
    ],
    codeInfo: {
      system: 'Transmission Control',
      severity: 'HIGH',
      category: 'Position Sensor'
    }
  }
});

module.exports = p0705Data;
