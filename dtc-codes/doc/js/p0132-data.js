const { DTCData } = require('./dtc-template-generator');

// P0132 O2 Sensor High Voltage Bank 1 Sensor 1 的完整数据结构
const p0132Data = new DTCData({
  code: 'P0132',
  title: 'O2 Sensor High Voltage Bank 1 Sensor 1',
  description: 'The Engine Control Module has detected that the upstream oxygen sensor on Bank 1 is reading abnormally high voltage.',
  definition: 'The Engine Control Module has detected that the upstream oxygen sensor (Bank 1, Sensor 1) is reading abnormally high voltage, typically above 0.9 volts for extended periods. This sensor monitors exhaust oxygen content before the catalytic converter to help the ECM maintain proper air/fuel mixture. High voltage indicates a rich fuel condition, but when voltage stays high constantly, it suggests sensor failure rather than actual rich mixture.',
  
  symptoms: [
    'Check engine light illuminated - ECM detected O2 sensor high voltage fault',
    'Poor fuel economy - ECM may be running rich mixture based on false readings',
    'Engine running rough - Incorrect fuel mixture adjustments',
    'Black smoke from exhaust - Rich fuel mixture from sensor misreading',
    'Strong fuel smell - Unburned fuel from rich conditions',
    'Engine hesitation or stumbling - Inconsistent fuel delivery',
    'Failed emissions test - Rich exhaust conditions exceed limits',
    'Carbon buildup on spark plugs - Rich mixture fouls plugs quickly',
    'Catalytic converter damage - Rich mixture can overheat catalyst'
  ],
  
  causes: [
    'Faulty O2 sensor - Internal sensor failure reading constant high voltage',
    'Contaminated O2 sensor - Oil, coolant, or fuel contamination affecting readings',
    'Short circuit in O2 sensor wiring - Wire shorted to power causing high voltage',
    'Exhaust leak before O2 sensor - Allowing outside air to affect readings',
    'Rich fuel mixture - Actual rich condition causing legitimate high readings',
    'Faulty fuel injectors - Leaking injectors causing rich mixture',
    'High fuel pressure - Excessive pressure causing over-fueling',
    'Faulty ECM - Control module misreading O2 sensor signals'
  ],
  
  performanceImpact: 'P0132 can cause the ECM to incorrectly adjust fuel mixture based on false rich readings, potentially leading to lean conditions, poor performance, increased emissions, and possible engine damage from incorrect air/fuel ratios.',
  
  quickAnswer: {
    icon: 'flask',
    meaning: 'Upstream O2 sensor reading constant high voltage - usually failed sensor or rich fuel condition.',
    fix: 'Replace O2 sensor, check for fuel system problems, inspect wiring',
    cost: '$150-$420',
    time: '60-120 minutes',
    drivingSafety: 'Safe to drive but expect poor fuel economy and emissions issues. Replace sensor soon to prevent catalytic converter damage.'
  },
  
  aiQuestions: [
    {
      question: 'What\'s the difference between P0132 and P0131 O2 sensor codes?',
      answer: 'P0132 indicates the O2 sensor is reading too high voltage (rich condition), while P0131 indicates too low voltage (lean condition). P0132 typically suggests sensor failure or actual rich fuel mixture, while P0131 usually indicates sensor failure or lean mixture conditions.'
    },
    {
      question: 'Can a rich fuel mixture cause P0132?',
      answer: 'Yes, an actual rich fuel mixture can cause P0132 if the condition is severe enough. However, P0132 more commonly indicates a failed O2 sensor that\'s stuck reading high voltage. Check fuel pressure, injectors, and other fuel system components if sensor replacement doesn\'t fix the code.'
    },
    {
      question: 'How do I test an O2 sensor for P0132?',
      answer: 'Use GeekOBD APP to monitor O2 sensor voltage - it should fluctuate between 0.1-0.9V during normal operation. With P0132, you\'ll see voltage stuck above 0.9V. A good sensor should switch rapidly between high and low voltage as fuel mixture changes.'
    },
    {
      question: 'Why does P0132 affect fuel economy?',
      answer: 'When the O2 sensor reads constant high voltage, the ECM thinks the mixture is always rich and may lean out the fuel delivery. This can cause poor performance and the ECM may overcompensate, leading to inconsistent fuel delivery and poor economy.'
    }
  ],

  costAnalysis: {
    averageCost: '$150-$420 for most P0132 repairs',
    repairOptions: [
      {
        title: 'O2 Sensor Replacement',
        description: 'Most common fix - Replace failed upstream O2 sensor (80% of cases)',
        color: '#4CAF50',
        icon: 'flask',
        items: [
          { name: 'Upstream O2 sensor', cost: '$80-$180' },
          { name: 'Labor (45-90 minutes)', cost: '$60-$180' }
        ],
        total: '$140-$360',
        successRate: '90% success rate'
      },
      {
        title: 'Fuel System Service',
        description: 'Address rich fuel condition causing high O2 readings (15% of cases)',
        color: '#2196F3',
        icon: 'tint',
        items: [
          { name: 'Fuel pressure test', cost: '$50-$100' },
          { name: 'Injector cleaning/replacement', cost: '$120-$400' },
          { name: 'Labor (1-2 hours)', cost: '$100-$240' }
        ],
        total: '$270-$740',
        successRate: '85% success rate'
      },
      {
        title: 'Wiring Repair',
        description: 'Fix short circuit in O2 sensor wiring (5% of cases)',
        color: '#FF9800',
        icon: 'flash',
        items: [
          { name: 'Wiring repair materials', cost: '$20-$50' },
          { name: 'Diagnostic time', cost: '$100-$150' },
          { name: 'Labor (1-2 hours)', cost: '$100-$240' }
        ],
        total: '$220-$440',
        successRate: '95% success rate'
      }
    ],
    savingTips: [
      'Replace O2 sensor first - fixes 80% of P0132 cases',
      'Use OEM or high-quality aftermarket sensors for best performance',
      'Check fuel pressure before expensive fuel system repairs',
      'O2 sensor replacement is often DIY-friendly, saving $60-180 in labor',
      'Address P0132 quickly to prevent catalytic converter damage'
    ]
  },

  diagnosticSteps: {
    estimatedTime: 'PT75M',
    steps: [
      {
        title: 'Monitor O2 Sensor Voltage',
        icon: 'line-chart',
        description: 'Connect GeekOBD APP and monitor upstream O2 sensor voltage. Normal operation shows fluctuation between 0.1-0.9V. P0132 typically shows voltage stuck above 0.9V.',
        geekobdTip: 'GeekOBD APP can graph O2 sensor voltage over time - look for lack of switching or voltage constantly above 0.9V indicating sensor failure.'
      },
      {
        title: 'Check Fuel System Operation',
        icon: 'tint',
        description: 'Test fuel pressure and check for leaking injectors that could cause rich mixture. Monitor fuel trims to see if ECM is trying to compensate for rich condition.',
        geekobdTip: 'Use GeekOBD APP to monitor fuel trims - large negative values indicate ECM is trying to lean out mixture due to rich condition.'
      },
      {
        title: 'Visual Inspection',
        icon: 'eye',
        description: 'Inspect O2 sensor for contamination, damage, or signs of rich mixture (black sooty deposits). Check exhaust system for leaks before sensor.',
        geekobdTip: 'Monitor O2 readings with GeekOBD APP while inspecting - readings should remain stable if sensor and wiring are good.'
      },
      {
        title: 'Electrical Testing',
        icon: 'bolt',
        description: 'Test O2 sensor heater circuit and signal wire for proper operation. Check for short circuits that could cause high voltage readings.',
        geekobdTip: 'GeekOBD APP can show O2 sensor heater status - proper heating is essential for accurate sensor operation.'
      },
      {
        title: 'Component Replacement and Verification',
        icon: 'check-circle',
        description: 'Replace faulty O2 sensor or repair fuel system issues as diagnosed. Clear codes and monitor O2 sensor operation during test drive.',
        geekobdTip: 'Use GeekOBD APP to verify O2 sensor now switches properly between 0.1-0.9V and responds to fuel mixture changes.'
      }
    ],
    importantNotes: [
      'O2 sensor should fluctuate between 0.1-0.9V during normal operation',
      'Constant high voltage usually indicates sensor failure',
      'Check for actual rich fuel condition before replacing sensor'
    ]
  },

  caseStudies: [
    {
      title: 'Toyota Prius Failed O2 Sensor',
      vehicle: '2016 Toyota Prius 1.8L Hybrid, 125,000 miles',
      problem: 'Customer reported poor fuel economy and P0132 code. Fuel economy dropped from 50 to 38 MPG with occasional rough running.',
      diagnosis: 'GeekOBD APP showed upstream O2 sensor voltage stuck at 0.95V with no switching activity. Fuel trims were at -25%, indicating ECM was trying to lean mixture based on false rich reading.',
      solution: 'Replaced upstream O2 sensor (Bank 1, Sensor 1) with OEM part. Sensor had failed internally and was no longer responding to exhaust oxygen content.',
      cost: 'O2 sensor: $165, Labor: $95, Total: $260',
      result: 'P0132 code cleared immediately. O2 sensor now switches properly between 0.2-0.8V. Fuel economy returned to 48 MPG and engine runs smoothly.'
    },
    {
      title: 'Ford F-150 Rich Fuel Mixture',
      vehicle: '2017 Ford F-150 3.5L V6, 89,000 miles',
      problem: 'P0132 code with black smoke from exhaust and strong fuel smell. Customer noticed poor acceleration and rough idle.',
      diagnosis: 'O2 sensor was reading high voltage, but GeekOBD APP showed it was switching properly. Found fuel pressure was 65 PSI (should be 45 PSI), causing rich mixture and legitimate high O2 readings.',
      solution: 'Replaced faulty fuel pressure regulator that was allowing excessive fuel pressure. Also cleaned carbon-fouled spark plugs caused by rich mixture.',
      cost: 'Fuel pressure regulator: $85, Spark plugs: $45, Labor: $150, Total: $280',
      result: 'P0132 code cleared after fuel pressure correction. O2 sensor readings now normal and black smoke eliminated. Engine performance fully restored.'
    }
  ],

  relatedCodes: [
    { code: 'P0131', description: 'O2 Sensor Low Voltage Bank 1 Sensor 1 - Opposite condition', color: '#3498db' },
    { code: 'P0133', description: 'O2 Sensor Slow Response Bank 1 Sensor 1 - Sluggish sensor response', color: '#f39c12' },
    { code: 'P0134', description: 'O2 Sensor No Activity Bank 1 Sensor 1 - No sensor switching', color: '#e74c3c' },
    { code: 'P0172', description: 'System Too Rich Bank 1 - Related rich fuel condition', color: '#9b59b6' },
    { code: 'P0420', description: 'Catalyst Efficiency Bank 1 - Can be damaged by rich mixture', color: '#e67e22' },
    { code: 'P0300', description: 'Random Misfire - Rich mixture can cause misfires', color: '#27ae60' },
    { code: 'P0171', description: 'System Too Lean Bank 1 - ECM overcompensating for false rich reading', color: '#4a90e2' }
  ],

  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P0132',
      description: 'Use GeekOBD APP for comprehensive O2 sensor testing!',
      features: [
        'Real-time O2 voltage monitoring',
        'Fuel trim analysis',
        'Sensor switching verification',
        'Rich mixture detection'
      ]
    },
    systemCodes: {
      title: 'O2 Sensor Codes',
      description: 'Related oxygen sensor codes:'
    },
    diagnosticResources: [
      {
        title: 'O2 Sensor Testing Guide',
        description: 'Professional procedures for testing oxygen sensor operation',
        icon: 'flask',
        url: '#diagnostic-steps'
      },
      {
        title: 'Rich Mixture Diagnosis',
        description: 'Identifying and fixing rich fuel conditions',
        icon: 'tint',
        url: '../resources/rich-mixture-diagnosis.html'
      },
      {
        title: 'Fuel System Testing',
        description: 'Complete fuel system pressure and injector testing',
        icon: 'wrench',
        url: '../resources/fuel-system-testing.html'
      },
      {
        title: 'Catalytic Converter Protection',
        description: 'Preventing catalyst damage from rich fuel mixtures',
        icon: 'shield',
        url: '../resources/catalytic-converter-protection.html'
      }
    ],
    codeInfo: {
      system: 'Engine Management',
      severity: 'MEDIUM',
      category: 'Oxygen Sensor'
    }
  }
});

module.exports = p0132Data;
