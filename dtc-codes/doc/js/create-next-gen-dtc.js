const fs = require('fs');

// 创建下一代汽车技术的故障码页面
// 涵盖电子节气门、高级制动系统、智能车身控制等

// P2100系列 - 电子节气门控制系统
const nextGenPCodeDatabase = {
  P2100: {
    title: "Throttle Actuator Control Motor Circuit/Open",
    description: "The Engine Control Module has detected an open circuit in the throttle actuator control motor circuit.",
    definition: "The Engine Control Module has detected an open circuit in the throttle actuator control motor circuit. The electronic throttle control system uses an electric motor to position the throttle plate instead of a traditional cable connection. An open circuit prevents the motor from receiving power and controlling throttle position.",
    symptoms: [
      "Check engine light illuminated - Throttle actuator circuit fault detected",
      "Throttle not responding to accelerator pedal - No throttle plate movement",
      "Engine stuck at idle speed - Throttle plate cannot open",
      "Reduced engine power mode activated - Safety limitation engaged",
      "Engine may not start - Throttle position required for starting",
      "Cruise control disabled - Throttle control required for operation",
      "Traction control affected - Throttle intervention unavailable",
      "Electronic stability control limited - Throttle modulation disabled"
    ],
    causes: [
      "Faulty throttle actuator motor - Internal motor failure",
      "Open circuit in throttle motor wiring - Cut, damaged, or corroded wires",
      "Blown fuse in throttle control circuit - Overcurrent protection",
      "Corroded throttle motor connector - Poor electrical connection",
      "ECM throttle driver circuit fault - Module output failure",
      "Power supply issues to throttle motor - Voltage problems",
      "Ground circuit fault - Poor electrical connection",
      "Throttle body mechanical binding - Motor cannot move throttle plate"
    ],
    performanceImpact: "P2100 prevents electronic throttle control operation, potentially causing no-start conditions, engine stuck at idle, complete loss of throttle response, and disabling of systems that depend on throttle control.",
    caseStudies: [
      {
        title: "2019 Ford F-150 - Throttle Motor Failure",
        vehicle: "2019 Ford F-150, 5.0L V8, 85,000 miles",
        symptoms: "No throttle response, engine stuck at idle, P2100 code stored",
        diagnosis: "GeekOBD diagnostic scan revealed P2100 with throttle actuator motor circuit fault. Motor testing showed internal failure with infinite resistance, preventing throttle plate movement.",
        solution: "Replaced electronic throttle body assembly with OEM Ford part, performed throttle relearn procedure, verified proper operation. Cleared codes with GeekOBD APP and road tested - normal throttle response restored",
        parts: "Electronic throttle body assembly ($485), throttle body gasket ($15), intake cleaner ($12)",
        labor: "2.5 hours ($250)",
        total: "$762"
      },
      {
        title: "2017 Chevrolet Silverado - Wiring Damage",
        vehicle: "2017 Chevrolet Silverado, 6.2L V8, 105,000 miles",
        symptoms: "Intermittent throttle issues, reduced power mode, P2100 stored",
        diagnosis: "GeekOBD diagnostic scan showed P2100 with intermittent throttle motor circuit fault. Found damaged wiring harness from engine heat exposure, causing intermittent open circuit.",
        solution: "Repaired damaged throttle motor wiring harness, applied heat-resistant sheathing, secured routing. Cleared codes with GeekOBD APP and verified consistent throttle operation",
        parts: "Throttle motor wiring harness ($125), heat-resistant sheathing ($35), connector repair kit ($25)",
        labor: "3.0 hours ($300)",
        total: "$485"
      }
    ],
    relatedCodes: [
      { code: "P2101", desc: "Throttle Actuator Control Motor Circuit Range/Performance" },
      { code: "P2102", desc: "Throttle Actuator Control Motor Circuit Low" },
      { code: "P2103", desc: "Throttle Actuator Control Motor Circuit High" },
      { code: "P2104", desc: "Throttle Actuator Control System - Forced Idle" },
      { code: "P2105", desc: "Throttle Actuator Control System - Forced Engine Shutdown" }
    ]
  },

  P2101: {
    title: "Throttle Actuator Control Motor Circuit Range/Performance",
    description: "The Engine Control Module has detected a range or performance problem with the throttle actuator control motor circuit.",
    definition: "The Engine Control Module has detected that the throttle actuator control motor is not performing within the expected range or specifications. This indicates the motor is receiving power but not positioning the throttle plate correctly, affecting engine performance and throttle response.",
    symptoms: [
      "Check engine light illuminated - Throttle actuator performance fault detected",
      "Poor throttle response - Delayed or incorrect throttle plate positioning",
      "Engine hesitation during acceleration - Throttle not opening properly",
      "Rough idle - Throttle plate not maintaining proper idle position",
      "Reduced engine power - Throttle opening limited by ECM",
      "Engine surging - Inconsistent throttle plate positioning",
      "Poor fuel economy - Incorrect throttle position affecting air/fuel mixture",
      "Failed emissions test - Improper throttle control affecting emissions"
    ],
    causes: [
      "Throttle actuator motor degradation - Reduced motor performance",
      "Carbon buildup on throttle plate - Mechanical restriction affecting movement",
      "Throttle position sensor malfunction - Incorrect position feedback",
      "Throttle body mechanical wear - Binding or sticking throttle shaft",
      "ECM throttle control calibration drift - Software issues",
      "Voltage fluctuations to throttle motor - Power supply instability",
      "Throttle return spring issues - Mechanical problems affecting positioning",
      "Intake air leaks - Affecting throttle control accuracy"
    ],
    performanceImpact: "P2101 causes poor throttle response, reduced engine performance, rough idle, and potential emissions issues due to incorrect throttle plate positioning and inadequate air flow control.",
    caseStudies: [
      {
        title: "2018 Toyota Camry - Carbon Buildup",
        vehicle: "2018 Toyota Camry, 2.5L 4-cylinder, 125,000 miles",
        symptoms: "Poor acceleration, rough idle, P2101 code stored",
        diagnosis: "GeekOBD diagnostic scan revealed P2101 with throttle actuator performance fault. Throttle body inspection showed severe carbon buildup preventing smooth throttle plate movement.",
        solution: "Cleaned throttle body and throttle plate, performed throttle relearn procedure, verified smooth operation. Cleared codes with GeekOBD APP and road tested - normal throttle response restored",
        parts: "Throttle body cleaner ($15), throttle plate cleaning kit ($25), new air filter ($28)",
        labor: "2.0 hours ($200)",
        total: "$268"
      },
      {
        title: "2016 Honda Accord - TPS Sensor Failure",
        vehicle: "2016 Honda Accord, 2.0L Turbo, 95,000 miles",
        symptoms: "Engine hesitation, throttle response poor, P2101 and TPS codes",
        diagnosis: "GeekOBD diagnostic scan showed P2101 with throttle performance fault and related TPS codes. Found faulty throttle position sensor providing incorrect feedback, causing poor throttle control.",
        solution: "Replaced throttle position sensor with OEM Honda part, performed throttle adaptation and calibration. Cleared codes with GeekOBD APP and tested - precise throttle control restored",
        parts: "Throttle position sensor ($185), sensor gasket ($8), throttle body cleaner ($12)",
        labor: "2.5 hours ($250)",
        total: "$455"
      }
    ],
    relatedCodes: [
      { code: "P2100", desc: "Throttle Actuator Control Motor Circuit/Open" },
      { code: "P2102", desc: "Throttle Actuator Control Motor Circuit Low" },
      { code: "P2103", desc: "Throttle Actuator Control Motor Circuit High" },
      { code: "P0121", desc: "Throttle Position Sensor Circuit Range/Performance" },
      { code: "P0507", desc: "Idle Air Control System RPM Higher Than Expected" }
    ]
  }
};

// C0700系列 - 高级制动系统
const nextGenCCodeDatabase = {
  C0700: {
    title: "Electronic Brake Distribution System Malfunction",
    description: "The Electronic Brake Distribution control module has detected a system malfunction.",
    definition: "The Electronic Brake Distribution (EBD) control module has detected a system malfunction that affects the vehicle's ability to distribute braking force optimally among all wheels. EBD works with ABS to ensure maximum braking efficiency and stability by adjusting brake pressure to each wheel based on load distribution and road conditions.",
    symptoms: [
      "EBD warning light illuminated - System malfunction detected",
      "ABS warning light may also be on - Integrated system affected",
      "Uneven braking performance - Brake force distribution compromised",
      "Longer stopping distances - Reduced braking efficiency",
      "Vehicle pulling during braking - Unequal brake force application",
      "Rear wheel lockup during hard braking - EBD not preventing wheel lock",
      "Brake pedal feel changes - System not optimizing brake pressure",
      "Electronic stability control affected - Brake intervention compromised"
    ],
    causes: [
      "EBD control module internal failure - Processing unit malfunction",
      "Brake pressure sensors malfunction - Incorrect pressure readings",
      "Wheel speed sensor failures - Cannot determine individual wheel speeds",
      "Hydraulic control unit failure - Cannot modulate brake pressure",
      "EBD system wiring damage - Communication or power supply issues",
      "Brake fluid contamination - Affecting hydraulic system operation",
      "ABS pump motor failure - Cannot generate required brake pressure",
      "Load sensing system malfunction - Cannot determine vehicle load distribution"
    ],
    performanceImpact: "C0700 compromises brake force distribution, potentially causing uneven braking, longer stopping distances, vehicle instability during braking, and increased risk of wheel lockup, especially under heavy braking conditions.",
    caseStudies: [
      {
        title: "2019 BMW X5 - Brake Pressure Sensor Failure",
        vehicle: "2019 BMW X5, 3.0L Turbo, 75,000 miles",
        symptoms: "EBD warning light, uneven braking, C0700 code stored",
        diagnosis: "GeekOBD diagnostic scan revealed C0700 with EBD system fault. Brake pressure sensor testing showed rear axle pressure sensor failure, preventing proper brake force distribution calculation.",
        solution: "Replaced faulty brake pressure sensor with OEM BMW part, bled brake system, performed EBD calibration. Cleared codes with GeekOBD APP and tested braking - even brake force distribution restored",
        parts: "Brake pressure sensor ($285), brake fluid ($25), sensor gasket ($8)",
        labor: "3.0 hours ($300)",
        total: "$618"
      },
      {
        title: "2017 Mercedes E-Class - Hydraulic Control Unit Failure",
        vehicle: "2017 Mercedes E-Class, 3.0L V6, 105,000 miles",
        symptoms: "Poor braking performance, EBD and ABS lights on, C0700 stored",
        diagnosis: "GeekOBD diagnostic scan showed C0700 with EBD system malfunction. Found hydraulic control unit internal failure preventing proper brake pressure modulation and force distribution.",
        solution: "Replaced EBD/ABS hydraulic control unit with Mercedes remanufactured part, performed complete system bleeding and calibration. Cleared codes with GeekOBD APP and verified proper brake distribution",
        parts: "EBD/ABS hydraulic unit ($1485), brake fluid ($35), system programming ($200)",
        labor: "5.5 hours ($550)",
        total: "$2270"
      }
    ],
    relatedCodes: [
      { code: "C0701", desc: "EBD Brake Pressure Sensor Circuit Malfunction" },
      { code: "C0702", desc: "EBD Load Sensing System Malfunction" },
      { code: "C0703", desc: "EBD Hydraulic Control Unit Malfunction" },
      { code: "C0200", desc: "ABS System Malfunction" },
      { code: "C0400", desc: "Electronic Stability Control System Malfunction" }
    ]
  }
};

// B0400系列 - 智能车身控制系统
const nextGenBCodeDatabase = {
  B0400: {
    title: "Climate Control System Communication Error",
    description: "The Body Control Module has detected a communication error with the climate control system.",
    definition: "The Body Control Module has detected a communication error with the climate control system that prevents proper coordination between body functions and HVAC operation. This affects automatic climate control, defrosting systems, and integration with other vehicle comfort and safety systems.",
    symptoms: [
      "Climate control not responding - HVAC system communication lost",
      "Automatic climate control disabled - Manual operation only",
      "Defrost system not working properly - Integration with body control lost",
      "Heated seats/steering wheel not working - Climate system integration affected",
      "Remote start climate pre-conditioning disabled - Communication required",
      "Climate control display showing errors - System fault messages",
      "Inconsistent temperature control - System coordination problems",
      "Ventilation system operating independently - No body control integration"
    ],
    causes: [
      "Climate control module communication failure - Internal module fault",
      "CAN bus wiring damage affecting climate system - Network communication interrupted",
      "Body control module climate interface fault - Module communication circuit failure",
      "Power supply issues to climate control module - Voltage problems",
      "Ground circuit fault in climate system - Poor electrical connection",
      "Software corruption in climate control module - Communication protocol errors",
      "Climate control module overheating - Thermal protection shutting down communication",
      "Electromagnetic interference affecting climate system communication"
    ],
    performanceImpact: "B0400 prevents proper climate control system integration with body functions, disabling automatic climate features, remote climate control, and coordination with other comfort and safety systems.",
    caseStudies: [
      {
        title: "2018 Cadillac Escalade - Climate Module Failure",
        vehicle: "2018 Cadillac Escalade, 6.2L V8, 85,000 miles",
        symptoms: "Climate control not responding, B0400 code stored",
        diagnosis: "GeekOBD diagnostic scan revealed B0400 with climate control communication error. Climate control module testing showed internal communication circuit failure preventing body control integration.",
        solution: "Replaced climate control module with OEM Cadillac part, performed module programming and body control integration. Cleared codes with GeekOBD APP and tested all climate functions - full integration restored",
        parts: "Climate control module ($685), programming service ($150), module calibration ($75)",
        labor: "3.5 hours ($350)",
        total: "$1260"
      },
      {
        title: "2016 Lincoln Navigator - CAN Bus Damage",
        vehicle: "2016 Lincoln Navigator, 3.5L EcoBoost, 115,000 miles",
        symptoms: "Intermittent climate issues, remote start problems, B0400 stored",
        diagnosis: "GeekOBD diagnostic scan showed B0400 with intermittent climate communication fault. Found damaged CAN bus wiring near climate control module from aftermarket installation, causing communication disruption.",
        solution: "Repaired damaged CAN bus wiring, properly routed and secured harness, verified stable communication. Cleared codes with GeekOBD APP and tested climate system integration - normal operation restored",
        parts: "CAN bus wiring repair kit ($125), protective sheathing ($35), proper connectors ($25)",
        labor: "4.0 hours ($400)",
        total: "$585"
      }
    ],
    relatedCodes: [
      { code: "B0401", desc: "HVAC Actuator Control Circuit Malfunction" },
      { code: "B0402", desc: "Climate Control Temperature Sensor Circuit Malfunction" },
      { code: "B0403", desc: "Climate Control Blower Motor Circuit Malfunction" },
      { code: "U0140", desc: "Lost Communication with Body Control Module" },
      { code: "B0404", desc: "Climate Control Refrigerant Pressure Sensor Malfunction" }
    ]
  },

  P2102: {
    title: "Throttle Actuator Control Motor Circuit Low",
    description: "The Engine Control Module has detected a low voltage condition in the throttle actuator control motor circuit.",
    definition: "The Engine Control Module has detected a low voltage condition in the throttle actuator control motor circuit. This indicates the throttle motor is receiving insufficient voltage to operate properly, which can cause poor throttle response, reduced engine performance, and potential safety issues.",
    symptoms: [
      "Check engine light illuminated - Throttle actuator low voltage detected",
      "Reduced throttle response - Motor not receiving adequate power",
      "Engine power limited - ECM restricting throttle opening",
      "Poor acceleration performance - Insufficient throttle control",
      "Engine may enter limp mode - Safety protection activated",
      "Throttle position erratic - Inconsistent motor operation",
      "Idle speed fluctuations - Throttle control instability",
      "Electronic stability control affected - Throttle intervention limited"
    ],
    causes: [
      "Low battery voltage - Insufficient power supply to throttle motor",
      "Faulty alternator - Not providing adequate charging voltage",
      "Corroded power supply connections - High resistance reducing voltage",
      "Damaged throttle motor power wiring - Voltage drop in circuit",
      "ECM throttle driver circuit degradation - Reduced output voltage",
      "Poor ground connections - Affecting circuit voltage",
      "Throttle motor internal resistance increase - Age-related degradation",
      "Power distribution issues - Fuse box or relay problems"
    ],
    performanceImpact: "P2102 causes reduced throttle motor performance due to low voltage, resulting in poor throttle response, limited engine power, potential limp mode operation, and compromised vehicle drivability and safety.",
    caseStudies: [
      {
        title: "2018 Nissan Altima - Weak Battery",
        vehicle: "2018 Nissan Altima, 2.5L 4-cylinder, 95,000 miles",
        symptoms: "Poor acceleration, throttle response sluggish, P2102 code",
        diagnosis: "GeekOBD diagnostic scan revealed P2102 with throttle motor low voltage. Battery testing showed weak battery (11.2V) unable to provide adequate voltage to throttle motor during operation.",
        solution: "Replaced weak battery with OEM Nissan battery, tested charging system, verified proper throttle motor voltage. Cleared codes with GeekOBD APP and road tested - normal throttle response restored",
        parts: "Battery ($185), battery terminals ($25), terminal cleaner ($8)",
        labor: "1.5 hours ($150)",
        total: "$368"
      },
      {
        title: "2016 Hyundai Sonata - Corroded Connections",
        vehicle: "2016 Hyundai Sonata, 2.0L Turbo, 105,000 miles",
        symptoms: "Intermittent throttle issues, P2102 appearing sporadically",
        diagnosis: "GeekOBD diagnostic scan showed intermittent P2102 with throttle motor voltage issues. Found severely corroded throttle motor power connections causing voltage drop and intermittent low voltage conditions.",
        solution: "Cleaned corroded throttle motor power connections, replaced damaged terminals, applied protective coating. Cleared codes with GeekOBD APP and verified stable throttle motor voltage",
        parts: "Terminal repair kit ($35), contact cleaner ($8), protective coating ($12)",
        labor: "2.0 hours ($200)",
        total: "$255"
      }
    ],
    relatedCodes: [
      { code: "P2100", desc: "Throttle Actuator Control Motor Circuit/Open" },
      { code: "P2101", desc: "Throttle Actuator Control Motor Circuit Range/Performance" },
      { code: "P2103", desc: "Throttle Actuator Control Motor Circuit High" },
      { code: "P0562", desc: "System Voltage Low" },
      { code: "P0563", desc: "System Voltage High" }
    ]
  },

  P2200: {
    title: "NOx Sensor Circuit Range/Performance Bank 1 Sensor 1",
    description: "The Engine Control Module has detected a range or performance problem with the NOx sensor circuit for Bank 1 Sensor 1.",
    definition: "The Engine Control Module has detected that the nitrogen oxide (NOx) sensor for Bank 1 Sensor 1 is not performing within the expected range or specifications. This sensor monitors NOx levels in the exhaust to ensure proper operation of NOx reduction systems and emissions compliance.",
    symptoms: [
      "Check engine light illuminated - NOx sensor performance fault detected",
      "Failed emissions test - NOx levels not properly monitored",
      "DEF (Diesel Exhaust Fluid) consumption changes - SCR system affected",
      "Reduced engine power - Emissions compliance limiting performance",
      "DPF regeneration frequency changes - Exhaust aftertreatment affected",
      "Engine derate mode possible - Emissions protection activated",
      "Exhaust aftertreatment warnings - NOx monitoring compromised",
      "Fuel economy changes - Engine operating in different emission modes"
    ],
    causes: [
      "Faulty NOx sensor - Internal sensor element failure",
      "NOx sensor contamination - Soot or chemical deposits affecting operation",
      "Damaged NOx sensor wiring - Cut, chafed, or corroded wires",
      "Corroded NOx sensor connector - Poor electrical connection",
      "ECM NOx sensor input circuit fault - Module malfunction",
      "Exhaust leaks affecting NOx sensor - Unmetered air affecting readings",
      "NOx sensor heating element failure - Sensor not reaching operating temperature",
      "Diesel exhaust fluid system problems - Affecting NOx reduction and sensor readings"
    ],
    performanceImpact: "P2200 prevents accurate NOx monitoring, potentially causing emissions test failure, reduced engine performance, DEF system issues, and environmental compliance problems requiring immediate attention.",
    caseStudies: [
      {
        title: "2019 Ram 2500 - NOx Sensor Contamination",
        vehicle: "2019 Ram 2500, 6.7L Cummins Diesel, 125,000 miles",
        symptoms: "Check engine light, failed emissions test, P2200 code stored",
        diagnosis: "GeekOBD diagnostic scan revealed P2200 with NOx sensor performance fault. Sensor inspection showed severe soot contamination affecting sensor element and causing inaccurate NOx readings.",
        solution: "Replaced contaminated NOx sensor with OEM Cummins part, cleaned exhaust system, performed DPF regeneration. Cleared codes with GeekOBD APP and retested emissions - passed with normal NOx levels",
        parts: "NOx sensor ($485), exhaust system cleaner ($35), DEF fluid ($25)",
        labor: "3.5 hours ($350)",
        total: "$895"
      },
      {
        title: "2017 Mercedes Sprinter - Sensor Wiring Damage",
        vehicle: "2017 Mercedes Sprinter 2500, 3.0L Diesel, 155,000 miles",
        symptoms: "DEF system warnings, P2200 and related codes stored",
        diagnosis: "GeekOBD diagnostic scan showed P2200 with NOx sensor circuit fault. Found damaged sensor wiring from exhaust heat exposure, causing intermittent sensor readings and SCR system problems.",
        solution: "Repaired damaged NOx sensor wiring with heat-resistant harness, applied thermal protection, secured routing. Cleared codes with GeekOBD APP and verified stable NOx sensor operation",
        parts: "NOx sensor wiring harness ($185), heat-resistant sheathing ($45), thermal protection ($25)",
        labor: "4.0 hours ($400)",
        total: "$655"
      }
    ],
    relatedCodes: [
      { code: "P2201", desc: "NOx Sensor Circuit Range/Performance Bank 1 Sensor 2" },
      { code: "P2202", desc: "NOx Sensor Circuit Range/Performance Bank 2 Sensor 1" },
      { code: "P2203", desc: "NOx Sensor Circuit Range/Performance Bank 2 Sensor 2" },
      { code: "P2000", desc: "NOx Trap Efficiency Below Threshold (Bank 1)" },
      { code: "P20EE", desc: "SCR NOx Catalyst Efficiency Below Threshold" }
    ]
  }
};

// 更多高级C码
const additionalNextGenCCodeDatabase = {
  C0701: {
    title: "EBD Brake Pressure Sensor Circuit Malfunction",
    description: "The Electronic Brake Distribution control module has detected a malfunction in the brake pressure sensor circuit.",
    definition: "The Electronic Brake Distribution control module has detected a malfunction in the brake pressure sensor circuit that monitors hydraulic pressure in the brake system. This sensor provides critical data for EBD to distribute brake force optimally among all wheels based on braking conditions and vehicle load.",
    symptoms: [
      "EBD warning light illuminated - Brake pressure sensor fault detected",
      "Brake pressure monitoring disabled - No pressure feedback available",
      "Uneven brake force distribution - EBD cannot optimize braking",
      "ABS system may be affected - Shared pressure monitoring",
      "Electronic brake assist limited - Pressure data required for operation",
      "Brake pedal feel changes - System not compensating for pressure variations",
      "Hill start assist affected - Brake pressure monitoring required",
      "Electronic stability control brake intervention limited - Pressure control compromised"
    ],
    causes: [
      "Faulty brake pressure sensor - Internal sensor element failure",
      "Damaged brake pressure sensor wiring - Cut, chafed, or corroded wires",
      "Corroded pressure sensor connector - Poor electrical connection",
      "Brake fluid contamination - Affecting sensor operation",
      "EBD module pressure sensor input fault - Module malfunction",
      "Sensor mounting issues - Improper installation or damage",
      "Brake system air contamination - Affecting pressure readings",
      "Sensor power supply issues - Voltage or ground problems"
    ],
    performanceImpact: "C0701 prevents accurate brake pressure monitoring, compromising EBD's ability to optimize brake force distribution, potentially causing uneven braking, reduced braking efficiency, and compromised vehicle stability during braking.",
    caseStudies: [
      {
        title: "2018 Audi Q7 - Brake Pressure Sensor Failure",
        vehicle: "2018 Audi Q7, 3.0L Supercharged, 85,000 miles",
        symptoms: "EBD warning light, brake feel changes, C0701 code stored",
        diagnosis: "GeekOBD diagnostic scan revealed C0701 with brake pressure sensor circuit fault. Sensor testing showed internal failure preventing accurate brake pressure monitoring and EBD operation.",
        solution: "Replaced brake pressure sensor with OEM Audi part, bled brake system, performed EBD calibration and adaptation. Cleared codes with GeekOBD APP and tested braking - normal pressure monitoring restored",
        parts: "Brake pressure sensor ($385), brake fluid ($35), sensor gasket ($12)",
        labor: "3.5 hours ($350)",
        total: "$782"
      },
      {
        title: "2016 Volvo XC90 - Sensor Wiring Corrosion",
        vehicle: "2016 Volvo XC90, 2.0L Turbo, 105,000 miles",
        symptoms: "Intermittent EBD warnings, C0701 appearing sporadically",
        diagnosis: "GeekOBD diagnostic scan showed intermittent C0701 with brake pressure sensor circuit issues. Found corroded sensor wiring from road salt exposure, causing intermittent signal loss.",
        solution: "Repaired corroded brake pressure sensor wiring, applied marine-grade protection, secured routing. Cleared codes with GeekOBD APP and verified stable pressure sensor operation",
        parts: "Brake pressure sensor wiring kit ($125), marine protection coating ($25), connector repair kit ($35)",
        labor: "2.5 hours ($250)",
        total: "$435"
      }
    ],
    relatedCodes: [
      { code: "C0700", desc: "Electronic Brake Distribution System Malfunction" },
      { code: "C0702", desc: "EBD Load Sensing System Malfunction" },
      { code: "C0703", desc: "EBD Hydraulic Control Unit Malfunction" },
      { code: "C0200", desc: "ABS System Malfunction" },
      { code: "C0704", desc: "EBD Brake Force Distribution Actuator Malfunction" }
    ]
  }
};

// 页面生成函数
function createDetailedCodePage(code, data) {
  const templateContent = fs.readFileSync('p0142.html', 'utf8');
  let content = templateContent;
  
  // 基本替换
  content = content.replace(/P0142/g, code);
  content = content.replace(/p0142/g, code.toLowerCase());
  
  // 替换标题和描述
  content = content.replace(/O2 Sensor Circuit Malfunction \(Bank 1 Sensor 3\)/g, data.title);
  content = content.replace(/The Engine Control Module has detected a malfunction in the oxygen sensor circuit for Bank 1, Sensor 3 \(post-catalytic converter\)\./g, data.description);
  
  // 替换定义
  const oldDefinition = /The Engine Control Module has detected a malfunction in the oxygen sensor circuit for Bank 1, Sensor 3 \(post-catalytic converter\)\. This diagnostic trouble code indicates a specific issue within the engine\/powertrain that requires attention to ensure proper vehicle operation and safety\./g;
  content = content.replace(oldDefinition, data.definition);
  
  // 替换症状
  if (data.symptoms) {
    const symptomsHtml = data.symptoms.map(symptom => `								<li><strong>${symptom}</strong></li>`).join('\n');
    const symptomsRegex = /<ul>\s*<li><strong>Check Engine Light illuminated[\s\S]*?<\/ul>/;
    content = content.replace(symptomsRegex, `<ul>\n${symptomsHtml}\n							</ul>`);
  }
  
  // 替换原因
  if (data.causes) {
    const causesHtml = data.causes.map(cause => `									<li>${cause}</li>`).join('\n');
    const causesRegex = /<ol>\s*<li>Faulty oxygen sensor[\s\S]*?<\/ol>/;
    content = content.replace(causesRegex, `<ol>\n${causesHtml}\n								</ol>`);
  }
  
  // 替换性能影响
  if (data.performanceImpact) {
    const impactRegex = /<strong><i class="fa fa-exclamation-triangle"><\/i> Performance Impact:<\/strong>[\s\S]*?<\/div>/;
    content = content.replace(impactRegex, `<strong><i class="fa fa-exclamation-triangle"></i> Performance Impact:</strong> ${data.performanceImpact}\n							</div>`);
  }
  
  // 替换维修案例
  if (data.caseStudies && data.caseStudies.length >= 2) {
    const case1 = data.caseStudies[0];
    const case2 = data.caseStudies[1];
    
    const caseStudyHtml = `							<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
								<h4>Case 1: ${case1.title}</h4>
								<p><strong>Vehicle:</strong> ${case1.vehicle}</p>
								<p><strong>Symptoms:</strong> ${case1.symptoms}</p>
								<p><strong>Diagnosis:</strong> ${case1.diagnosis}</p>
								<p><strong>Solution:</strong> ${case1.solution}</p>
								<p><strong>Parts:</strong> ${case1.parts}</p>
								<p><strong>Labor:</strong> ${case1.labor}</p>
								<p><strong>Total Cost:</strong> ${case1.total}</p>
							</div>

							<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
								<h4>Case 2: ${case2.title}</h4>
								<p><strong>Vehicle:</strong> ${case2.vehicle}</p>
								<p><strong>Symptoms:</strong> ${case2.symptoms}</p>
								<p><strong>Diagnosis:</strong> ${case2.diagnosis}</p>
								<p><strong>Solution:</strong> ${case2.solution}</p>
								<p><strong>Parts:</strong> ${case2.parts}</p>
								<p><strong>Labor:</strong> ${case2.labor}</p>
								<p><strong>Total Cost:</strong> ${case2.total}</p>
							</div>`;
    
    const caseStudyRegex = /<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">[\s\S]*?<\/div>\s*<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">[\s\S]*?<\/div>/;
    content = content.replace(caseStudyRegex, caseStudyHtml);
  }
  
  // 替换相关代码
  if (data.relatedCodes) {
    const relatedHtml = data.relatedCodes.map(related => 
      `									<li style="margin-bottom: 10px;"><a href="${related.code.toLowerCase()}.html" style="color: #007bff;">${related.code} - ${related.desc}</a></li>`
    ).join('\n');
    
    const relatedRegex = /<li style="margin-bottom: 10px;"><a href="p0140\.html"[\s\S]*?<li style="margin-bottom: 10px;"><a href="p0145\.html"[\s\S]*?<\/li>/;
    content = content.replace(relatedRegex, relatedHtml);
  }
  
  return content;
}

// 批量创建下一代汽车技术故障码页面
const nextGenCodesToCreate = [
  // 下一代P码 - 电子节气门控制系统
  { code: 'P2100', database: nextGenPCodeDatabase },
  { code: 'P2101', database: nextGenPCodeDatabase },
  { code: 'P2102', database: nextGenPCodeDatabase },
  // 下一代P码 - NOx传感器系统
  { code: 'P2200', database: nextGenPCodeDatabase },
  // 下一代C码 - 高级制动系统
  { code: 'C0700', database: nextGenCCodeDatabase },
  { code: 'C0701', database: additionalNextGenCCodeDatabase },
  // 下一代B码 - 智能车身控制系统
  { code: 'B0400', database: nextGenBCodeDatabase }
];

let totalCreated = 0;

console.log('🚀 Creating next-generation automotive technology DTC pages...\n');

nextGenCodesToCreate.forEach(({ code, database }) => {
  if (database[code]) {
    try {
      const htmlContent = createDetailedCodePage(code, database[code]);
      fs.writeFileSync(`${code.toLowerCase()}.html`, htmlContent, 'utf8');
      console.log(`✅ Created ${code.toLowerCase()}.html - ${database[code].title}`);
      totalCreated++;
    } catch (error) {
      console.log(`❌ Failed to create ${code.toLowerCase()}.html: ${error.message}`);
    }
  }
});

console.log(`\n🎉 Successfully created ${totalCreated} next-generation automotive technology DTC pages!`);
console.log('\n📊 Next-Generation Technology Coverage:');
console.log('✅ Electronic Throttle Control Systems (P2100 series)');
console.log('✅ Advanced Braking Systems (C0700 series)');
console.log('✅ Intelligent Body Control (B0400 series)');
console.log('\nNext-generation automotive technology coverage complete! 🎯');
