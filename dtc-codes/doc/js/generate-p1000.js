const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p1000Data = require('./p1000-data');

console.log('🚀 Generating P1000 - OBD System Readiness Test Not Complete Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p1000Data);
  const outputPath = path.join(__dirname, '../../p1000.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P1000 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P1000 page:', error.message);
  process.exit(1);
}
