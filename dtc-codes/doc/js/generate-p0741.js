const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0741Data = require('./p0741-data');

console.log('🚀 Generating P0741 - Torque Converter Clutch Circuit Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0741Data);
  const outputPath = path.join(__dirname, '../../p0741.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0741 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0741 page:', error.message);
  process.exit(1);
}
