const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0108Data = require('./p0108-data');

console.log('🚀 Generating P0108 - MAP Sensor High Input Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0108Data);
  const outputPath = path.join(__dirname, '../../p0108.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0108 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0108 page:', error.message);
  process.exit(1);
}
