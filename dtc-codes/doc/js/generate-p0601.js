const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0601Data = require('./p0601-data');

console.log('🚀 Generating P0601 - Internal Control Module Memory Check Sum Error Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0601Data);
  const outputPath = path.join(__dirname, '../../p0601.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0601 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0601 page:', error.message);
  process.exit(1);
}
