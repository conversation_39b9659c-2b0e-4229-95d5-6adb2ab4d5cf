const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0340Data = require('./p0340-data');

console.log('🚀 Generating P0340 - Camshaft Position Sensor Circuit Malfunction Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0340Data);
  const outputPath = path.join(__dirname, '../../p0340.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0340 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0340 page:', error.message);
  process.exit(1);
}
