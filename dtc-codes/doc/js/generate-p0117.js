const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0117Data = require('./p0117-data');

console.log('🚀 Generating P0117 - ECT Sensor Low Input Page\n');

try {
  // 创建模板生成器实例
  const generator = new DTCTemplateGenerator();
  
  // 生成HTML内容
  console.log('📝 Generating HTML content...');
  const htmlContent = generator.generatePage(p0117Data);
  
  // 保存到文件
  const outputPath = path.join(__dirname, '../../p0117.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0117 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
  // 统计信息
  console.log('\n📊 Content Statistics:');
  console.log(`   • Total file size: ${Math.round(htmlContent.length / 1024)}KB`);
  console.log(`   • Symptoms: ${p0117Data.symptoms.length} items`);
  console.log(`   • Causes: ${p0117Data.causes.length} items`);
  console.log(`   • AI Questions: ${p0117Data.aiQuestions.length} items`);
  console.log(`   • Repair Options: ${p0117Data.costAnalysis.repairOptions.length} options`);
  console.log(`   • Diagnostic Steps: ${p0117Data.diagnosticSteps.steps.length} steps`);
  console.log(`   • Case Studies: ${p0117Data.caseStudies.length} studies`);
  console.log(`   • Related Codes: ${p0117Data.relatedCodes.length} codes`);
  
  // 验证关键组件
  console.log('\n🔍 Component Verification:');
  console.log(`   ✅ Quick Answer: ${p0117Data.quickAnswer ? 'Present' : 'Missing'}`);
  console.log(`   ✅ Cost Analysis: ${p0117Data.costAnalysis ? 'Present' : 'Missing'}`);
  console.log(`   ✅ Diagnostic Steps: ${p0117Data.diagnosticSteps ? 'Present' : 'Missing'}`);
  console.log(`   ✅ Case Studies: ${p0117Data.caseStudies.length > 0 ? 'Present' : 'Missing'}`);
  console.log(`   ✅ Sidebar: ${p0117Data.sidebarData ? 'Present' : 'Missing'}`);
  
  console.log('\n🎉 P0117 optimization completed successfully!');
  console.log('\n💡 Key Features:');
  console.log('   • Detailed ECT sensor low input diagnosis');
  console.log('   • Comprehensive resistance testing procedures');
  console.log('   • Real-world Chevrolet Silverado and Honda Accord case studies');
  console.log('   • Cost-effective repair strategies ($85-$320 range)');
  console.log('   • GeekOBD APP integration for live monitoring');
  console.log('   • Complete ECT sensor network linking');
  
  console.log('\n🔄 Next steps:');
  console.log('   1. Review generated p0117.html for accuracy');
  console.log('   2. Test page layout and functionality');
  console.log('   3. Continue with P0118 (ECT Sensor High Input)');
  console.log('   4. Build complete coolant temperature sensor diagnostic series');
  
} catch (error) {
  console.error('❌ Error generating P0117 page:', error.message);
  console.error(error.stack);
  process.exit(1);
}
