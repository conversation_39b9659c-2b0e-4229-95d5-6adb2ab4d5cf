const fs = require('fs');

// P1300 - 点火线圈主电路故障
const p1300Data = {
  title: "Ignition Coil Primary Circuit Malfunction - Cylinder 1",
  description: "The Engine Control Module has detected a malfunction in the primary circuit of the ignition coil for cylinder 1.",
  definition: "The Engine Control Module has detected a malfunction in the primary circuit of the ignition coil for cylinder 1. The primary circuit carries low voltage (12V) current to the ignition coil, which is then transformed to high voltage for spark generation. A primary circuit fault prevents proper ignition coil operation.",
  symptoms: [
    "Check engine light illuminated - Ignition coil primary circuit fault detected",
    "Engine misfiring - Cylinder 1 not firing properly",
    "Rough idle - Uneven combustion affecting idle quality",
    "Poor engine performance - Reduced power output",
    "Engine hesitation during acceleration - Ignition timing problems",
    "Increased fuel consumption - Incomplete combustion",
    "Failed emissions test - Misfires increasing emissions",
    "Engine vibration - Cylinder imbalance from ignition failure"
  ],
  causes: [
    "Faulty ignition coil - Internal primary winding failure",
    "Damaged coil primary wiring - Cut, chafed, or corroded wires",
    "Corroded coil connector - Poor electrical connection",
    "ECM ignition driver circuit fault - Module output failure",
    "Power supply issues to coil - Voltage problems",
    "Ground circuit fault - Poor electrical connection",
    "Ignition coil overheating - Thermal damage to primary windings",
    "Spark plug failure affecting coil - Excessive load on primary circuit"
  ],
  performanceImpact: "P1300 prevents proper ignition coil primary circuit operation, causing cylinder 1 misfiring, reduced performance, increased emissions, and potential catalytic converter damage.",
  caseStudies: [
    {
      title: "2019 Volkswagen Jetta - Ignition Coil Primary Failure",
      vehicle: "2019 Volkswagen Jetta, 1.4L Turbo, 65,000 miles",
      symptoms: "Engine misfiring, rough idle, P1300 code stored",
      diagnosis: "GeekOBD diagnostic scan revealed P1300 with ignition coil primary circuit fault. Primary winding resistance testing showed open circuit preventing coil operation.",
      solution: "Replaced ignition coil with OEM Volkswagen part, replaced spark plug, performed ignition system adaptation. Cleared codes with GeekOBD APP and road tested - smooth engine operation restored",
      parts: "Ignition coil ($165), spark plug ($18), dielectric grease ($8)",
      labor: "1.5 hours ($150)",
      total: "$341"
    },
    {
      title: "2018 Subaru Outback - Coil Connector Corrosion",
      vehicle: "2018 Subaru Outback, 2.5L 4-cylinder, 85,000 miles",
      symptoms: "Intermittent misfiring, P1300 appearing occasionally",
      diagnosis: "GeekOBD diagnostic scan showed intermittent P1300 with coil primary circuit issues. Found severe corrosion at ignition coil connector from moisture intrusion, causing intermittent high resistance.",
      solution: "Cleaned corroded ignition coil connector, replaced damaged terminals, applied marine-grade dielectric grease. Cleared codes with GeekOBD APP and verified stable coil operation",
      parts: "Ignition coil connector repair kit ($35), marine dielectric grease ($12), terminal cleaner ($8)",
      labor: "1.0 hour ($100)",
      total: "$155"
    }
  ],
  relatedCodes: [
    { code: "P1301", desc: "Ignition Coil Primary Circuit Malfunction - Cylinder 2" },
    { code: "P1302", desc: "Ignition Coil Primary Circuit Malfunction - Cylinder 3" },
    { code: "P1303", desc: "Ignition Coil Primary Circuit Malfunction - Cylinder 4" },
    { code: "P0351", desc: "Ignition Coil A Primary Circuit Malfunction" },
    { code: "P0301", desc: "Cylinder 1 Misfire Detected" }
  ]
};

// 页面生成函数
function createDetailedCodePage(code, data) {
  const templateContent = fs.readFileSync('p0142.html', 'utf8');
  let content = templateContent;
  
  // 基本替换
  content = content.replace(/P0142/g, code);
  content = content.replace(/p0142/g, code.toLowerCase());
  
  // 替换标题和描述
  content = content.replace(/O2 Sensor Circuit Malfunction \(Bank 1 Sensor 3\)/g, data.title);
  content = content.replace(/The Engine Control Module has detected a malfunction in the oxygen sensor circuit for Bank 1, Sensor 3 \(post-catalytic converter\)\./g, data.description);
  
  // 替换定义
  const oldDefinition = /The Engine Control Module has detected a malfunction in the oxygen sensor circuit for Bank 1, Sensor 3 \(post-catalytic converter\)\. This diagnostic trouble code indicates a specific issue within the engine\/powertrain that requires attention to ensure proper vehicle operation and safety\./g;
  content = content.replace(oldDefinition, data.definition);
  
  // 替换症状
  if (data.symptoms) {
    const symptomsHtml = data.symptoms.map(symptom => `								<li><strong>${symptom}</strong></li>`).join('\n');
    const symptomsRegex = /<ul>\s*<li><strong>Check Engine Light illuminated[\s\S]*?<\/ul>/;
    content = content.replace(symptomsRegex, `<ul>\n${symptomsHtml}\n							</ul>`);
  }
  
  // 替换原因
  if (data.causes) {
    const causesHtml = data.causes.map(cause => `									<li>${cause}</li>`).join('\n');
    const causesRegex = /<ol>\s*<li>Faulty oxygen sensor[\s\S]*?<\/ol>/;
    content = content.replace(causesRegex, `<ol>\n${causesHtml}\n								</ol>`);
  }
  
  // 替换性能影响
  if (data.performanceImpact) {
    const impactRegex = /<strong><i class="fa fa-exclamation-triangle"><\/i> Performance Impact:<\/strong>[\s\S]*?<\/div>/;
    content = content.replace(impactRegex, `<strong><i class="fa fa-exclamation-triangle"></i> Performance Impact:</strong> ${data.performanceImpact}\n							</div>`);
  }
  
  // 替换维修案例
  if (data.caseStudies && data.caseStudies.length >= 2) {
    const case1 = data.caseStudies[0];
    const case2 = data.caseStudies[1];
    
    const caseStudyHtml = `							<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
								<h4>Case 1: ${case1.title}</h4>
								<p><strong>Vehicle:</strong> ${case1.vehicle}</p>
								<p><strong>Symptoms:</strong> ${case1.symptoms}</p>
								<p><strong>Diagnosis:</strong> ${case1.diagnosis}</p>
								<p><strong>Solution:</strong> ${case1.solution}</p>
								<p><strong>Parts:</strong> ${case1.parts}</p>
								<p><strong>Labor:</strong> ${case1.labor}</p>
								<p><strong>Total Cost:</strong> ${case1.total}</p>
							</div>

							<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
								<h4>Case 2: ${case2.title}</h4>
								<p><strong>Vehicle:</strong> ${case2.vehicle}</p>
								<p><strong>Symptoms:</strong> ${case2.symptoms}</p>
								<p><strong>Diagnosis:</strong> ${case2.diagnosis}</p>
								<p><strong>Solution:</strong> ${case2.solution}</p>
								<p><strong>Parts:</strong> ${case2.parts}</p>
								<p><strong>Labor:</strong> ${case2.labor}</p>
								<p><strong>Total Cost:</strong> ${case2.total}</p>
							</div>`;
    
    const caseStudyRegex = /<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">[\s\S]*?<\/div>\s*<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">[\s\S]*?<\/div>/;
    content = content.replace(caseStudyRegex, caseStudyHtml);
  }
  
  // 替换相关代码
  if (data.relatedCodes) {
    const relatedHtml = data.relatedCodes.map(related => 
      `									<li style="margin-bottom: 10px;"><a href="${related.code.toLowerCase()}.html" style="color: #007bff;">${related.code} - ${related.desc}</a></li>`
    ).join('\n');
    
    const relatedRegex = /<li style="margin-bottom: 10px;"><a href="p0140\.html"[\s\S]*?<li style="margin-bottom: 10px;"><a href="p0145\.html"[\s\S]*?<\/li>/;
    content = content.replace(relatedRegex, relatedHtml);
  }
  
  return content;
}

try {
  const htmlContent = createDetailedCodePage('P1300', p1300Data);
  fs.writeFileSync('p1300.html', htmlContent, 'utf8');
  console.log('✅ Created p1300.html - Ignition Coil Primary Circuit Malfunction - Cylinder 1');
} catch (error) {
  console.log(`❌ Failed to create p1300.html: ${error.message}`);
}
