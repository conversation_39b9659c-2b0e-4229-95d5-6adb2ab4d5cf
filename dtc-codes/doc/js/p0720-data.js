const { DTCData } = require('./dtc-template-generator');

// P0720 Output Speed Sensor Circuit 的完整数据结构
const p0720Data = new DTCData({
  code: 'P0720',
  title: 'Output Speed Sensor Circuit',
  description: 'The Engine Control Module has detected a malfunction in the output speed sensor circuit.',
  definition: 'The Engine Control Module has detected a malfunction in the output speed sensor circuit. The output speed sensor monitors the rotational speed of the transmission output shaft, which corresponds to vehicle speed. This information is used by the TCM for shift control, gear ratio calculations, speedometer operation, and various other systems. When there are electrical problems with the output speed sensor circuit, P0720 is triggered.',
  
  symptoms: [
    'Check engine light illuminated - ECM detected output speed sensor circuit fault',
    'Speedometer not working - No speed signal to instrument cluster',
    'Transmission shifting problems - Poor shift timing and quality',
    'ABS warning light - Anti-lock brake system affected by speed sensor',
    'Cruise control not working - System requires accurate speed signal',
    'Transmission slipping - TCM cannot properly control gear engagement',
    'Harsh or erratic shifting - Incorrect speed data affecting shift control',
    'Limp mode operation - Transmission operating in safe mode',
    'Odometer not working - No distance calculation without speed signal'
  ],
  
  causes: [
    'Faulty output speed sensor - Internal sensor failure preventing signal generation',
    'Damaged sensor wiring - Broken or corroded wires affecting signal transmission',
    'Corroded sensor connector - Poor electrical contact affecting signal quality',
    'Contamination on sensor - Metal particles or debris affecting magnetic pickup',
    'Damaged reluctor ring - Missing or damaged teeth on sensor target wheel',
    'TCM internal fault - Control module unable to process sensor signals',
    'Power supply problems - Inadequate voltage to sensor circuit',
    'Ground circuit problems - Poor ground connection affecting sensor operation'
  ],
  
  performanceImpact: 'P0720 affects speedometer operation, transmission shift control, ABS function, cruise control, and other systems that rely on vehicle speed information, potentially causing poor performance and safety system failures.',
  
  quickAnswer: {
    icon: 'dashboard',
    meaning: 'Output speed sensor not providing proper vehicle speed signal - usually sensor or wiring failure.',
    fix: 'Test sensor wiring, check sensor signal, replace output speed sensor if needed',
    cost: '$150-$450',
    time: '60-150 minutes',
    drivingSafety: 'Safe to drive but speedometer won\'t work and transmission may shift poorly. Repair promptly for proper vehicle operation.'
  },
  
  aiQuestions: [
    {
      question: 'What\'s the difference between input and output speed sensors?',
      answer: 'The input speed sensor monitors transmission input shaft speed (engine/torque converter speed), while the output speed sensor monitors transmission output shaft speed (vehicle speed). Both are needed for proper transmission control and gear ratio calculations.'
    },
    {
      question: 'Can P0720 affect my ABS system?',
      answer: 'Yes, P0720 can affect ABS operation because the ABS system may use transmission output speed sensor data for vehicle speed reference. When this sensor fails, ABS may not function properly and the ABS warning light may illuminate.'
    },
    {
      question: 'Why does my speedometer not work with P0720?',
      answer: 'The speedometer gets vehicle speed information from the transmission output speed sensor. When this sensor fails or has circuit problems, the speedometer cannot display accurate speed because it\'s not receiving the necessary speed signal.'
    },
    {
      question: 'How do I test the output speed sensor?',
      answer: 'Use GeekOBD APP to monitor output speed sensor signal while driving. The signal should correspond to actual vehicle speed. You can also test sensor resistance and check for proper AC voltage signal generation with a multimeter.'
    }
  ],

  costAnalysis: {
    averageCost: '$150-$450 for most P0720 repairs',
    repairOptions: [
      {
        title: 'Output Speed Sensor Replacement',
        description: 'Replace faulty output speed sensor (75% of cases)',
        color: '#4CAF50',
        icon: 'dashboard',
        items: [
          { name: 'Output speed sensor', cost: '$60-$150' },
          { name: 'Labor (1-2 hours)', cost: '$100-$240' }
        ],
        total: '$160-$390',
        successRate: '95% success rate'
      },
      {
        title: 'Wiring Harness Repair',
        description: 'Fix damaged sensor wiring (20% of cases)',
        color: '#FF9800',
        icon: 'flash',
        items: [
          { name: 'Wiring repair materials', cost: '$25-$60' },
          { name: 'Labor (1-1.5 hours)', cost: '$100-$180' }
        ],
        total: '$125-$240',
        successRate: '90% success rate'
      },
      {
        title: 'Connector Service',
        description: 'Clean or replace corroded sensor connector (5% of cases)',
        color: '#2196F3',
        icon: 'plug',
        items: [
          { name: 'Connector cleaning/replacement', cost: '$15-$40' },
          { name: 'Labor (30-45 minutes)', cost: '$50-$90' }
        ],
        total: '$65-$130',
        successRate: '85% success rate'
      }
    ],
    savingTips: [
      'Check sensor connector for corrosion before replacing sensor',
      'Use GeekOBD APP to verify sensor signal before replacement',
      'Output speed sensors are often more accessible than input sensors',
      'Address P0720 promptly to restore speedometer and transmission operation',
      'Some vehicles have multiple speed sensors - ensure correct sensor replacement'
    ]
  },

  diagnosticSteps: {
    estimatedTime: 'PT90M',
    steps: [
      {
        title: 'Monitor Output Speed Signal',
        icon: 'search',
        description: 'Connect GeekOBD APP and monitor output speed sensor signal while driving. Signal should correspond to actual vehicle speed.',
        geekobdTip: 'GeekOBD APP can show output speed sensor readings in real-time - compare with GPS speed or known speed to verify accuracy.'
      },
      {
        title: 'Test Sensor Electrical Circuit',
        icon: 'bolt',
        description: 'Check sensor power supply, ground, and signal circuits with multimeter. Verify proper voltage and AC signal generation.',
        geekobdTip: 'Use GeekOBD APP to monitor sensor voltage while testing circuits - should show stable power supply and clean signal generation.'
      },
      {
        title: 'Visual Inspection',
        icon: 'eye',
        description: 'Inspect output speed sensor, wiring, and connector for damage, corrosion, or contamination. Check sensor mounting and air gap.',
        geekobdTip: 'Monitor sensor signal with GeekOBD APP while wiggling wires - intermittent readings indicate wiring problems.'
      },
      {
        title: 'Check Reluctor Ring',
        icon: 'cog',
        description: 'Inspect transmission output shaft reluctor ring for missing or damaged teeth that could affect sensor reading.',
        geekobdTip: 'GeekOBD APP can show sensor signal pattern - irregular or missing pulses indicate reluctor ring damage.'
      },
      {
        title: 'Component Replacement and Verification',
        icon: 'check-circle',
        description: 'Replace faulty sensor or repair wiring as diagnosed. Clear codes and verify sensor provides accurate speed readings.',
        geekobdTip: 'Use GeekOBD APP to verify output speed sensor now provides consistent, accurate readings that match actual vehicle speed.'
      }
    ],
    importantNotes: [
      'Output speed sensor provides vehicle speed information to multiple systems',
      'Check speedometer operation after sensor replacement',
      'Address P0720 promptly to restore proper transmission and safety system operation'
    ]
  },

  caseStudies: [
    {
      title: 'Honda Civic Speedometer Failure',
      vehicle: '2016 Honda Civic 1.5L CVT, 125,000 miles',
      problem: 'Customer reported non-working speedometer, harsh transmission shifts, and P0720 code. ABS warning light was also illuminated.',
      diagnosis: 'GeekOBD APP showed no output speed sensor signal. Visual inspection revealed output speed sensor connector was corroded and had poor contact.',
      solution: 'Cleaned corroded output speed sensor connector and applied dielectric grease. Connector corrosion was preventing proper signal transmission.',
      cost: 'Connector cleaning kit: $15, Dielectric grease: $8, Labor: $75, Total: $98',
      result: 'P0720 code cleared immediately. Speedometer works normally, transmission shifts smoothly, and ABS warning light turned off.'
    },
    {
      title: 'Ford Escape Sensor Failure',
      vehicle: '2017 Ford Escape 1.6L Turbo, 89,000 miles',
      problem: 'P0720 code with erratic speedometer readings and poor transmission shift quality. Cruise control also not functioning.',
      diagnosis: 'Output speed sensor wiring tested good, but GeekOBD APP showed inconsistent speed signals. Sensor was generating weak and erratic signals.',
      solution: 'Replaced output speed sensor with OEM part. Sensor had internal failure causing inconsistent signal generation.',
      cost: 'Output speed sensor: $95, Labor: $120, Total: $215',
      result: 'P0720 code cleared and speedometer now reads accurately. Transmission shifts properly and cruise control restored.'
    }
  ],

  relatedCodes: [
    { code: 'P0500', description: 'Vehicle Speed Sensor Malfunction - General speed sensor problem', color: '#e74c3c' },
    { code: 'P0700', description: 'Transmission Control System Malfunction - General transmission problem', color: '#3498db' },
    { code: 'P0715', description: 'Input Speed Sensor Circuit - Related transmission speed sensor', color: '#f39c12' },
    { code: 'P0721', description: 'Output Speed Sensor Range/Performance - Sensor signal out of range', color: '#9b59b6' },
    { code: 'P0722', description: 'Output Speed Sensor No Signal - No sensor signal detected', color: '#4a90e2' },
    { code: 'C1200', description: 'ABS Speed Sensor - Related wheel speed sensor problems', color: '#e67e22' },
    { code: 'P0741', description: 'Torque Converter Clutch Circuit - Related transmission control', color: '#27ae60' }
  ],

  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P0720',
      description: 'Use GeekOBD APP for output speed sensor testing!',
      features: [
        'Real-time speed monitoring',
        'Sensor signal verification',
        'Speedometer accuracy testing',
        'Transmission data analysis'
      ]
    },
    systemCodes: {
      title: 'Speed Sensor Codes',
      description: 'Related vehicle speed sensor codes:'
    },
    diagnosticResources: [
      {
        title: 'Output Speed Testing',
        description: 'Professional procedures for testing output speed sensors',
        icon: 'dashboard',
        url: '#diagnostic-steps'
      },
      {
        title: 'Speedometer Diagnosis',
        description: 'Diagnosing and fixing speedometer problems',
        icon: 'tachometer-alt',
        url: '../resources/speedometer-diagnosis.html'
      },
      {
        title: 'Transmission Speed Sensors',
        description: 'Understanding transmission speed sensor operation',
        icon: 'cogs',
        url: '../resources/transmission-speed-sensors.html'
      },
      {
        title: 'Vehicle Speed Systems',
        description: 'How vehicle speed affects multiple automotive systems',
        icon: 'car',
        url: '../resources/vehicle-speed-systems.html'
      }
    ],
    codeInfo: {
      system: 'Transmission Control',
      severity: 'MEDIUM',
      category: 'Speed Sensor'
    }
  }
});

module.exports = p0720Data;
