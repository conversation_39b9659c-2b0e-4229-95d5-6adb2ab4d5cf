const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0341Data = require('./p0341-data');

console.log('🚀 Generating P0341 - Camshaft Position Sensor Range/Performance Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0341Data);
  const outputPath = path.join(__dirname, '../../p0341.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0341 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0341 page:', error.message);
  process.exit(1);
}
