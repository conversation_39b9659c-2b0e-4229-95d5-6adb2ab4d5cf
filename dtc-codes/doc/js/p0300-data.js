const { DTCData } = require('./dtc-template-generator');

// P0300 Random/Multiple Cylinder Misfire 的完整数据结构
const p0300Data = new DTCData({
  code: 'P0300',
  title: 'Random Misfire Detected',
  description: 'The Engine Control Module has detected random misfires occurring across multiple cylinders or cylinders that cannot be specifically identified.',
  definition: 'The Engine Control Module has detected random misfires occurring across multiple cylinders or cylinders that cannot be specifically identified. Random misfires indicate combustion problems that affect engine performance, emissions, and can cause catalytic converter damage if left unrepaired. Unlike cylinder-specific misfire codes (P0301-P0312), P0300 indicates the ECM cannot pinpoint which cylinder is misfiring or misfires are occurring randomly across multiple cylinders.',

  symptoms: [
    'Check engine light flashing - Indicates active misfire that can damage catalytic converter',
    'Engine rough idle - Noticeable vibration and uneven engine operation at idle',
    'Loss of power during acceleration - Reduced engine performance under load',
    'Engine hesitation or stumbling - Irregular power delivery during driving',
    'Poor fuel economy - Incomplete combustion wastes fuel',
    'Engine knocking or pinging sounds - Abnormal combustion noises',
    'Strong fuel smell from exhaust - Unburned fuel passing through exhaust system',
    'Engine stalling - Severe misfires can cause engine to shut off',
    'Increased exhaust emissions - Failed emissions testing due to incomplete combustion'
  ],

  causes: [
    'Worn or fouled spark plugs - Most common cause, affecting ignition across multiple cylinders',
    'Faulty ignition coils - Multiple coil failure causing random cylinder misfires',
    'Clogged fuel injectors - Poor fuel delivery to multiple cylinders',
    'Low fuel pressure - Insufficient fuel supply affecting all cylinders',
    'Vacuum leaks - Unmetered air causing lean mixture and random misfires',
    'Carbon buildup on intake valves - Restricting airflow to multiple cylinders',
    'Faulty mass airflow sensor - Incorrect air measurement causing improper fuel mixture',
    'EGR valve problems - Excessive exhaust gas recirculation causing combustion issues'
  ],

  performanceImpact: 'P0300 causes significant engine performance degradation with rough idle, power loss, poor fuel economy, and potential catalytic converter damage from unburned fuel. Random misfires create vibration, emissions problems, and can lead to expensive engine damage if not addressed promptly.',
  
  quickAnswer: {
    icon: 'fire',
    meaning: 'Random misfires detected across multiple cylinders - usually worn spark plugs or ignition system problems.',
    fix: 'Replace spark plugs, check ignition coils, clean fuel injectors',
    cost: '$150-$800',
    time: '2-4 hours',
    drivingSafety: 'Avoid driving with flashing check engine light - can damage catalytic converter. Safe for short distances with steady light.'
  },

  aiQuestions: [
    {
      question: 'What\'s the difference between P0300 and cylinder-specific misfire codes?',
      answer: 'P0300 indicates random misfires that the ECM cannot attribute to a specific cylinder, while P0301-P0312 identify misfires in specific cylinders. P0300 often suggests system-wide problems like fuel pressure, spark plugs, or vacuum leaks affecting multiple cylinders, rather than isolated component failures.'
    },
    {
      question: 'Why is my check engine light flashing with P0300?',
      answer: 'A flashing check engine light with P0300 indicates active misfires severe enough to damage the catalytic converter. The ECM flashes the light to warn that unburned fuel is entering the exhaust system, which can overheat and destroy the expensive catalytic converter. Stop driving immediately and diagnose the problem.'
    },
    {
      question: 'Can bad gas cause P0300 random misfires?',
      answer: 'Yes, contaminated fuel with water, dirt, or wrong octane rating can cause P0300. Poor quality gasoline may not ignite properly, causing random misfires across cylinders. Try adding fuel system cleaner and filling with high-quality fuel. If P0300 persists after fuel treatment, check ignition and fuel system components.'
    },
    {
      question: 'How do I diagnose P0300 with GeekOBD APP?',
      answer: 'GeekOBD APP can monitor misfire counters for each cylinder, fuel trim values, and ignition timing. Look for patterns - if all cylinders show similar misfire counts, suspect fuel pressure or spark plugs. If misfires are truly random with no pattern, check for vacuum leaks or MAF sensor problems.'
    }
  ],
  
  costAnalysis: {
    averageCost: '[平均成本]',
    repairOptions: [
      {
        title: '[最常见修复]',
        description: '[描述]',
        color: '#4CAF50',
        icon: 'wrench',
        items: [
          { name: '[部件1]', cost: '$[范围]' },
          { name: '[人工费]', cost: '$[范围]' }
        ],
        total: '$[总计范围]',
        successRate: '[成功率]'
      }
      // 添加更多修复选项...
    ],
    savingTips: [
      '[省钱建议1]',
      '[省钱建议2]',
      // 添加更多建议...
    ]
  },
  
  diagnosticSteps: {
    estimatedTime: 'PT30M',
    steps: [
      {
        title: 'Initial Diagnosis',
        icon: 'search',
        description: '[诊断步骤描述]',
        geekobdTip: '[GeekOBD APP使用建议]'
      }
      // 添加更多步骤...
    ],
    importantNotes: [
      '[重要注意事项1]',
      '[重要注意事项2]'
    ]
  },
  
  caseStudies: [
    {
      title: '[案例标题]',
      vehicle: '[车辆信息]',
      problem: '[问题描述]',
      diagnosis: '[诊断过程]',
      solution: '[解决方案]',
      cost: '[实际成本]',
      result: '[修复结果]'
    }
    // 添加更多案例...
  ],
  
  relatedCodes: [
    // 需要添加相关代码
    { code: '[相关代码1]', description: '[描述]', color: '#4a90e2' }
    // 添加更多相关代码...
  ],
  
  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P0300',
      description: 'Use GeekOBD APP for professional P0300 diagnosis!',
      features: [
        'Real-time data monitoring',
        'Step-by-step diagnosis',
        'Repair verification',
        'Cost estimation'
      ]
    },
    systemCodes: {
      title: '[系统名称] Codes',
      description: 'Related diagnostic trouble codes:'
    },
    diagnosticResources: [
      {
        title: 'P0300 Testing Guide',
        description: 'Professional diagnostic procedures',
        icon: 'book',
        url: '#diagnostic-steps'
      },
      {
        title: 'Wiring Diagrams',
        description: 'Circuit diagrams and specifications',
        icon: 'sitemap',
        url: '../resources/wiring-diagrams.html'
      },
      {
        title: 'Repair Procedures',
        description: 'Step-by-step repair instructions',
        icon: 'wrench',
        url: '../resources/repair-procedures.html'
      }
    ],
    codeInfo: {
      system: '[系统名称]',
      severity: 'MEDIUM', // HIGH, MEDIUM, LOW
      category: '[类别]'
    }
  }
});

module.exports = p0300Data;