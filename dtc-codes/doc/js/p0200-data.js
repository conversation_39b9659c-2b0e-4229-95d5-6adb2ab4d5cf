const { DTCData } = require('./dtc-template-generator');

// P0200 Injector Circuit Malfunction 的完整数据结构
const p0200Data = new DTCData({
  code: 'P0200',
  title: 'Injector Circuit Malfunction',
  description: 'The Engine Control Module has detected an electrical malfunction in the fuel injector circuit.',
  definition: 'The Engine Control Module has detected an electrical malfunction in the fuel injector circuit. This is a general code that indicates a problem with the fuel injection system\'s electrical components, which may affect one or multiple injectors. The injector circuit includes the injectors, wiring harness, connectors, and ECM connections. P0200 is triggered when there are voltage irregularities, open circuits, short circuits, or other electrical faults that prevent proper fuel injector operation.',
  
  symptoms: [
    'Check engine light illuminated - ECM detected injector circuit electrical fault',
    'Engine running rough or misfiring - Poor fuel delivery from faulty injectors',
    'Hard starting or no start - Insufficient fuel delivery during startup',
    'Poor engine performance - Reduced power and acceleration',
    'Engine stalling - Inconsistent fuel delivery causing engine shutdown',
    'Poor fuel economy - Incorrect fuel delivery amounts',
    'Black or white smoke from exhaust - Rich or lean mixture from injector problems',
    'Engine knocking or pinging - Incorrect fuel delivery affecting combustion',
    'Strong fuel smell - Leaking injectors or rich mixture conditions'
  ],
  
  causes: [
    'Faulty fuel injector - Internal injector failure preventing proper operation',
    'Open circuit in injector wiring - Broken wire preventing injector activation',
    'Short circuit in injector harness - Wire touching ground or power',
    'Corroded injector connector - Poor electrical contact affecting injector operation',
    'ECM internal fault - Control module unable to control injector circuits',
    'Damaged wiring harness - Physical damage from heat, vibration, or wear',
    'Faulty injector driver circuit - ECM driver circuit failure',
    'Power supply problems - Inadequate voltage to injector circuits'
  ],
  
  performanceImpact: 'P0200 causes poor engine performance, rough running, hard starting, and potential engine damage from incorrect fuel delivery. The engine may run in limp mode with reduced power and poor fuel economy.',
  
  quickAnswer: {
    icon: 'tint',
    meaning: 'Electrical problem in fuel injector circuit - wiring, connector, or injector electrical failure.',
    fix: 'Check wiring, test injector resistance, replace faulty injector',
    cost: '$180-$650',
    time: '90-180 minutes',
    drivingSafety: 'May be unsafe to drive if engine stalls or runs very rough. Repair promptly to prevent engine damage.'
  },
  
  aiQuestions: [
    {
      question: 'What\'s the difference between P0200 and P0201-P0208 injector codes?',
      answer: 'P0200 is a general injector circuit malfunction affecting the overall system, while P0201-P0208 indicate specific cylinder injector problems. P0200 may indicate multiple injector issues or problems with the injector power supply circuit.'
    },
    {
      question: 'Can low fuel pressure cause P0200?',
      answer: 'No, P0200 specifically indicates electrical circuit problems, not fuel pressure issues. Low fuel pressure would cause performance problems but not trigger P0200. However, both electrical and fuel pressure problems can occur simultaneously.'
    },
    {
      question: 'How do I test fuel injector circuits for P0200?',
      answer: 'Use a multimeter to test injector resistance (usually 12-16 ohms) and check for power and ground at injector connectors. GeekOBD APP can show injector pulse width and help identify which injectors are not functioning properly.'
    },
    {
      question: 'Can P0200 cause engine damage?',
      answer: 'Yes, P0200 can cause engine damage if injectors are stuck open (causing flooding and hydrolock) or if lean conditions from non-functioning injectors cause overheating and detonation. Address P0200 promptly to prevent expensive engine damage.'
    }
  ],

  costAnalysis: {
    averageCost: '$180-$650 for most P0200 repairs',
    repairOptions: [
      {
        title: 'Single Injector Replacement',
        description: 'Replace one faulty injector (40% of cases)',
        color: '#4CAF50',
        icon: 'tint',
        items: [
          { name: 'Fuel injector', cost: '$80-$200' },
          { name: 'Labor (1-2 hours)', cost: '$100-$240' }
        ],
        total: '$180-$440',
        successRate: '90% success rate'
      },
      {
        title: 'Injector Set Replacement',
        description: 'Replace all injectors if multiple failures (35% of cases)',
        color: '#2196F3',
        icon: 'refresh',
        items: [
          { name: 'Injector set (4-8 injectors)', cost: '$300-$800' },
          { name: 'Labor (2-4 hours)', cost: '$200-$480' }
        ],
        total: '$500-$1280',
        successRate: '95% success rate'
      },
      {
        title: 'Wiring Harness Repair',
        description: 'Fix damaged injector wiring (25% of cases)',
        color: '#FF9800',
        icon: 'flash',
        items: [
          { name: 'Wiring repair materials', cost: '$30-$80' },
          { name: 'Diagnostic time', cost: '$120-$200' },
          { name: 'Labor (1.5-3 hours)', cost: '$150-$360' }
        ],
        total: '$300-$640',
        successRate: '85% success rate'
      }
    ],
    savingTips: [
      'Test individual injector resistance before replacing entire set',
      'Check wiring and connectors first - may save expensive injector replacement',
      'Consider injector cleaning service before replacement if contamination suspected',
      'Replace injectors in sets for best performance balance',
      'Use OEM or high-quality injectors to prevent premature failure'
    ]
  },

  diagnosticSteps: {
    estimatedTime: 'PT120M',
    steps: [
      {
        title: 'Check for Specific Injector Codes',
        icon: 'search',
        description: 'Scan for additional codes P0201-P0208 that identify specific cylinder injector problems. P0200 may be accompanied by individual injector codes.',
        geekobdTip: 'GeekOBD APP can show all injector-related codes and help identify which specific injectors are affected by the circuit malfunction.'
      },
      {
        title: 'Test Injector Resistance',
        icon: 'bolt',
        description: 'Disconnect injector connectors and test resistance across injector terminals. Normal resistance is typically 12-16 ohms. Infinite resistance indicates open injector.',
        geekobdTip: 'Use GeekOBD APP to monitor injector pulse width while testing - no pulse width indicates electrical circuit problems.'
      },
      {
        title: 'Check Injector Power and Ground',
        icon: 'flash',
        description: 'Test for 12V power and good ground at injector connectors with key on. Check injector pulse signal with test light or oscilloscope.',
        geekobdTip: 'GeekOBD APP can show injector duty cycle and pulse width - compare readings between cylinders to identify faulty circuits.'
      },
      {
        title: 'Visual Inspection',
        icon: 'eye',
        description: 'Inspect injector wiring harness, connectors, and fuel rail for damage, corrosion, or signs of fuel leaks that could affect electrical connections.',
        geekobdTip: 'Monitor injector operation with GeekOBD APP while wiggling wires - intermittent readings indicate wiring problems.'
      },
      {
        title: 'Component Replacement and Testing',
        icon: 'check-circle',
        description: 'Replace faulty injectors or repair wiring as diagnosed. Clear codes and test engine operation under various load conditions.',
        geekobdTip: 'Use GeekOBD APP to verify all injectors now show proper pulse width and duty cycle, confirming successful repair.'
      }
    ],
    importantNotes: [
      'P0200 indicates electrical problems, not fuel pressure issues',
      'Test individual injector circuits to identify specific problems',
      'Address P0200 promptly to prevent engine damage from poor fuel delivery'
    ]
  },

  caseStudies: [
    {
      title: 'Honda Accord Multiple Injector Failure',
      vehicle: '2015 Honda Accord 2.4L 4-cylinder, 135,000 miles',
      problem: 'Customer reported rough running, poor fuel economy, and P0200 code along with P0201 and P0204 codes. Engine would misfire under load.',
      diagnosis: 'GeekOBD APP showed no pulse width for cylinders 1 and 4 injectors. Resistance testing revealed both injectors had infinite resistance, indicating internal coil failure.',
      solution: 'Replaced all four fuel injectors as a set since two had already failed and others showed signs of wear. Also cleaned fuel rail and replaced fuel filter.',
      cost: 'Injector set: $420, Fuel filter: $35, Labor: $280, Total: $735',
      result: 'P0200 and individual injector codes cleared. Engine now runs smoothly with restored power and fuel economy improved by 4 MPG.'
    },
    {
      title: 'Ford F-150 Wiring Harness Damage',
      vehicle: '2017 Ford F-150 3.5L V6, 78,000 miles',
      problem: 'Intermittent P0200 code with occasional rough idle and engine hesitation. Problem seemed worse in hot weather.',
      diagnosis: 'Found injector wiring harness had been damaged by heat from nearby exhaust component. Several wires had damaged insulation causing intermittent shorts to ground.',
      solution: 'Repaired damaged section of injector wiring harness and rerouted wiring away from heat source. Added heat shielding to prevent recurrence.',
      cost: 'Wiring repair kit: $65, Heat shielding: $35, Labor: $240, Total: $340',
      result: 'P0200 code has not returned after 10 months. All injectors now operate properly and engine performance is consistent in all weather conditions.'
    }
  ],

  relatedCodes: [
    { code: 'P0201', description: 'Injector Circuit Malfunction Cylinder 1 - Specific cylinder 1 injector', color: '#e74c3c' },
    { code: 'P0202', description: 'Injector Circuit Malfunction Cylinder 2 - Specific cylinder 2 injector', color: '#3498db' },
    { code: 'P0203', description: 'Injector Circuit Malfunction Cylinder 3 - Specific cylinder 3 injector', color: '#f39c12' },
    { code: 'P0204', description: 'Injector Circuit Malfunction Cylinder 4 - Specific cylinder 4 injector', color: '#9b59b6' },
    { code: 'P0171', description: 'System Too Lean Bank 1 - Can be caused by non-functioning injectors', color: '#4a90e2' },
    { code: 'P0172', description: 'System Too Rich Bank 1 - Can be caused by stuck-open injectors', color: '#27ae60' },
    { code: 'P0300', description: 'Random Misfire - Often caused by injector circuit problems', color: '#e67e22' }
  ],

  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P0200',
      description: 'Use GeekOBD APP for comprehensive injector circuit testing!',
      features: [
        'Injector pulse width monitoring',
        'Individual cylinder analysis',
        'Circuit fault identification',
        'Performance verification'
      ]
    },
    systemCodes: {
      title: 'Fuel Injector Codes',
      description: 'Related fuel injection system codes:'
    },
    diagnosticResources: [
      {
        title: 'Injector Circuit Testing',
        description: 'Professional procedures for testing fuel injector circuits',
        icon: 'tint',
        url: '#diagnostic-steps'
      },
      {
        title: 'Fuel Injection Systems',
        description: 'Understanding modern fuel injection operation',
        icon: 'cogs',
        url: '../resources/fuel-injection-systems.html'
      },
      {
        title: 'Injector Replacement',
        description: 'Step-by-step injector replacement procedures',
        icon: 'wrench',
        url: '../resources/injector-replacement.html'
      },
      {
        title: 'Electrical Diagnostics',
        description: 'Advanced electrical testing for fuel systems',
        icon: 'flash',
        url: '../resources/electrical-diagnostics.html'
      }
    ],
    codeInfo: {
      system: 'Fuel Injection',
      severity: 'HIGH',
      category: 'Electrical Circuit'
    }
  }
});

module.exports = p0200Data;
