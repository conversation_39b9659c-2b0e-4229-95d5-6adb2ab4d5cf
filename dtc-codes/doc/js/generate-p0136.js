const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0136Data = require('./p0136-data');

console.log('🚀 Generating P0136 - O2 Sensor Low Voltage Bank 1 Sensor 2 Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0136Data);
  const outputPath = path.join(__dirname, '../../p0136.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0136 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0136 page:', error.message);
  process.exit(1);
}
