const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0705Data = require('./p0705-data');

console.log('🚀 Generating P0705 - Transmission Range Sensor Circuit Malfunction Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0705Data);
  const outputPath = path.join(__dirname, '../../p0705.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0705 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0705 page:', error.message);
  process.exit(1);
}
