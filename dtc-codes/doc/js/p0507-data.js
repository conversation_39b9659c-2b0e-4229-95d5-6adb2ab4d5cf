const { DTCData } = require('./dtc-template-generator');

// P0507 Idle Air Control System RPM Higher Than Expected 的完整数据结构
const p0507Data = new DTCData({
  code: 'P0507',
  title: 'Idle Air Control System RPM Higher Than Expected',
  description: 'The Engine Control Module has detected that the idle RPM is higher than the expected target range.',
  definition: 'The Engine Control Module has detected that the idle RPM is higher than the expected target range. The idle air control (IAC) system regulates engine idle speed by controlling the amount of air bypassing the throttle plate when the throttle is closed. When the ECM detects that idle RPM is consistently higher than the programmed target despite attempts to reduce it through the IAC system, P0507 is triggered.',
  
  symptoms: [
    'Check engine light illuminated - ECM detected high idle RPM condition',
    'High idle RPM - Engine idles faster than normal (typically above 1000 RPM)',
    'Engine racing at idle - RPM significantly higher than expected',
    'Difficulty controlling idle speed - RPM fluctuates or stays high',
    'Poor fuel economy - Higher RPM at idle increases fuel consumption',
    'Engine overheating at idle - Higher RPM generates more heat',
    'Transmission engagement problems - High idle affects shift quality',
    'Engine noise at idle - Louder operation due to higher RPM',
    'Emissions test failure - High idle affects emission levels'
  ],
  
  causes: [
    'Vacuum leak - Unmetered air entering intake causing high idle',
    'Faulty IAC valve - Valve stuck open allowing too much air bypass',
    'Dirty throttle body - Carbon buildup preventing proper throttle closure',
    'Faulty throttle position sensor - Incorrect throttle position feedback',
    'Damaged intake manifold gasket - Allowing unmetered air into engine',
    'Faulty PCV system - Excessive crankcase ventilation affecting idle',
    'Stuck open EGR valve - Allowing exhaust gases to affect idle quality',
    'ECM calibration issues - Software problems with idle control strategy'
  ],
  
  performanceImpact: 'P0507 causes high idle RPM, poor fuel economy, potential overheating at idle, and may affect transmission operation and emissions compliance.',
  
  quickAnswer: {
    icon: 'arrow-up',
    meaning: 'Engine idling too fast - usually vacuum leak, stuck IAC valve, or dirty throttle body.',
    fix: 'Check for vacuum leaks, clean throttle body, test IAC valve operation',
    cost: '$120-$480',
    time: '60-180 minutes',
    drivingSafety: 'Safe to drive but engine will idle fast and use more fuel. May affect transmission engagement and cause overheating at idle.'
  },
  
  aiQuestions: [
    {
      question: 'What causes high idle RPM?',
      answer: 'High idle RPM is most commonly caused by vacuum leaks allowing unmetered air into the engine, a stuck-open IAC valve, or a dirty throttle body preventing proper closure. The ECM tries to compensate but cannot reduce RPM to target levels.'
    },
    {
      question: 'Can a vacuum leak cause P0507?',
      answer: 'Yes, vacuum leaks are one of the most common causes of P0507. When unmetered air enters the intake system, the engine RPM increases and the ECM cannot reduce it to normal idle speed through the IAC system.'
    },
    {
      question: 'How do I find a vacuum leak?',
      answer: 'Use carburetor cleaner or propane around vacuum lines and intake components while engine is running. RPM changes indicate leak locations. GeekOBD APP can show changes in fuel trims that help identify vacuum leak severity.'
    },
    {
      question: 'Can P0507 cause transmission problems?',
      answer: 'Yes, high idle RPM can affect automatic transmission engagement, causing harsh engagement into drive or reverse. The transmission expects normal idle RPM for proper operation, and high RPM can cause engagement issues.'
    }
  ],

  costAnalysis: {
    averageCost: '$120-$480 for most P0507 repairs',
    repairOptions: [
      {
        title: 'Vacuum Leak Repair',
        description: 'Fix vacuum leaks causing high idle (50% of cases)',
        color: '#4CAF50',
        icon: 'compress',
        items: [
          { name: 'Vacuum hoses/gaskets', cost: '$20-$80' },
          { name: 'Labor (1-2 hours)', cost: '$100-$240' }
        ],
        total: '$120-$320',
        successRate: '90% success rate'
      },
      {
        title: 'IAC Valve Replacement',
        description: 'Replace stuck or faulty IAC valve (30% of cases)',
        color: '#2196F3',
        icon: 'arrow-up',
        items: [
          { name: 'IAC valve', cost: '$80-$200' },
          { name: 'Labor (1-1.5 hours)', cost: '$100-$180' }
        ],
        total: '$180-$380',
        successRate: '85% success rate'
      },
      {
        title: 'Throttle Body Cleaning',
        description: 'Clean dirty throttle body (20% of cases)',
        color: '#FF9800',
        icon: 'refresh',
        items: [
          { name: 'Throttle body cleaner', cost: '$15-$30' },
          { name: 'Labor (1-1.5 hours)', cost: '$100-$180' }
        ],
        total: '$115-$210',
        successRate: '70% success rate'
      }
    ],
    savingTips: [
      'Check for obvious vacuum leaks first - may be simple hose replacement',
      'Try throttle body cleaning before replacing expensive IAC valve',
      'Use GeekOBD APP to monitor idle RPM and fuel trims during diagnosis',
      'Some IAC valves can be cleaned rather than replaced',
      'Address P0507 promptly to prevent transmission and overheating problems'
    ]
  },

  diagnosticSteps: {
    estimatedTime: 'PT120M',
    steps: [
      {
        title: 'Monitor Idle RPM and Control',
        icon: 'tachometer',
        description: 'Connect GeekOBD APP and monitor actual idle RPM vs target RPM. Check IAC valve position and duty cycle during idle control attempts.',
        geekobdTip: 'GeekOBD APP can show target vs actual RPM and IAC valve activity - large differences indicate system problems preventing proper idle control.'
      },
      {
        title: 'Check for Vacuum Leaks',
        icon: 'search',
        description: 'Inspect vacuum lines, intake manifold, and throttle body for leaks. Use carburetor cleaner or propane to identify leak locations.',
        geekobdTip: 'Monitor fuel trims with GeekOBD APP during leak testing - positive fuel trims indicate vacuum leaks causing lean conditions.'
      },
      {
        title: 'Test IAC Valve Operation',
        icon: 'play',
        description: 'Command IAC valve operation with scan tool and verify valve responds properly. Check valve for carbon buildup or mechanical problems.',
        geekobdTip: 'Use GeekOBD APP to command IAC valve positions - RPM should change when valve position changes, indicating proper valve operation.'
      },
      {
        title: 'Inspect Throttle Body',
        icon: 'eye',
        description: 'Remove air intake and inspect throttle body for carbon buildup, proper throttle plate closure, and TPS operation.',
        geekobdTip: 'Monitor TPS readings with GeekOBD APP while checking throttle closure - should read minimum voltage when throttle is fully closed.'
      },
      {
        title: 'Repair and Verify',
        icon: 'check-circle',
        description: 'Repair vacuum leaks, clean throttle body, or replace IAC valve as diagnosed. Clear codes and verify idle RPM returns to normal.',
        geekobdTip: 'Use GeekOBD APP to verify idle RPM now matches target RPM and IAC valve can properly control idle speed.'
      }
    ],
    importantNotes: [
      'Vacuum leaks are the most common cause of P0507',
      'Check fuel trims to help identify vacuum leak severity',
      'IAC valve cleaning may resolve issues without replacement'
    ]
  },

  caseStudies: [
    {
      title: 'Toyota Corolla Vacuum Leak',
      vehicle: '2016 Toyota Corolla 1.8L 4-cylinder, 125,000 miles',
      problem: 'Customer reported high idle RPM (1200 RPM) and P0507 code. Engine would race at idle and transmission engaged harshly.',
      diagnosis: 'GeekOBD APP showed target idle of 750 RPM but actual idle of 1200 RPM. Found cracked vacuum line to brake booster causing large vacuum leak.',
      solution: 'Replaced cracked brake booster vacuum line. Also inspected other vacuum lines and found two additional small leaks that were repaired.',
      cost: 'Vacuum hoses: $35, Labor: $95, Total: $130',
      result: 'P0507 code cleared and idle RPM returned to normal 750 RPM. Transmission engagement smooth and fuel economy improved.'
    },
    {
      title: 'Honda Accord Stuck IAC Valve',
      vehicle: '2015 Honda Accord 2.4L 4-cylinder, 145,000 miles',
      problem: 'P0507 code with idle RPM fluctuating between 900-1300 RPM. No obvious vacuum leaks found during initial inspection.',
      diagnosis: 'GeekOBD APP showed IAC valve was not responding properly to commands. Valve was stuck partially open due to carbon buildup.',
      solution: 'Removed and cleaned IAC valve thoroughly with carburetor cleaner. Valve was heavily carboned but cleaning restored proper operation.',
      cost: 'IAC valve cleaning: $0, Labor: $120, Total: $120',
      result: 'P0507 code cleared and idle RPM stabilized at target 750 RPM. IAC valve now responds properly to ECM commands.'
    }
  ],

  relatedCodes: [
    { code: 'P0505', description: 'Idle Air Control System - General IAC system malfunction', color: '#e74c3c' },
    { code: 'P0506', description: 'Idle Air Control System RPM Lower Than Expected - Opposite condition', color: '#3498db' },
    { code: 'P0508', description: 'Cold Air Intake System Low - Related intake air problems', color: '#f39c12' },
    { code: 'P0171', description: 'System Too Lean Bank 1 - Often caused by vacuum leaks', color: '#9b59b6' },
    { code: 'P0174', description: 'System Too Lean Bank 2 - Often caused by vacuum leaks', color: '#4a90e2' },
    { code: 'P0121', description: 'TPS Range/Performance - Related throttle position problems', color: '#e67e22' },
    { code: 'P0401', description: 'EGR Flow Insufficient - Can affect idle quality', color: '#27ae60' }
  ],

  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P0507',
      description: 'Use GeekOBD APP for idle control system testing!',
      features: [
        'Real-time RPM monitoring',
        'IAC valve testing',
        'Fuel trim analysis',
        'Vacuum leak detection'
      ]
    },
    systemCodes: {
      title: 'Idle Control Codes',
      description: 'Related idle air control system codes:'
    },
    diagnosticResources: [
      {
        title: 'Idle Control Testing',
        description: 'Professional procedures for testing idle air control systems',
        icon: 'arrow-up',
        url: '#diagnostic-steps'
      },
      {
        title: 'Vacuum Leak Detection',
        description: 'Finding and repairing vacuum leaks in intake systems',
        icon: 'search',
        url: '../resources/vacuum-leak-detection.html'
      },
      {
        title: 'Throttle Body Service',
        description: 'Cleaning and maintaining throttle body systems',
        icon: 'refresh',
        url: '../resources/throttle-body-service.html'
      },
      {
        title: 'Idle Quality Problems',
        description: 'Diagnosing and fixing idle quality issues',
        icon: 'cog',
        url: '../resources/idle-quality-problems.html'
      }
    ],
    codeInfo: {
      system: 'Engine Management',
      severity: 'MEDIUM',
      category: 'Idle Control'
    }
  }
});

module.exports = p0507Data;
