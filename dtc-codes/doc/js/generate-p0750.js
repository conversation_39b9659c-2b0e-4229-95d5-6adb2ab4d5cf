const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0750Data = require('./p0750-data');

console.log('🚀 Generating P0750 - Shift Solenoid A Malfunction Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0750Data);
  const outputPath = path.join(__dirname, '../../p0750.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0750 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0750 page:', error.message);
  process.exit(1);
}
