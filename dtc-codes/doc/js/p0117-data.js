const { DTCData } = require('./dtc-template-generator');

// P0117 ECT Sensor Low Input 的完整数据结构
const p0117Data = new DTCData({
  code: 'P0117',
  title: 'ECT Sensor Low Input',
  description: 'The Engine Control Module has detected that the Engine Coolant Temperature sensor is reading extremely hot temperatures.',
  definition: 'The Engine Control Module has detected that the Engine Coolant Temperature (ECT) sensor is producing readings that indicate extremely hot coolant temperatures (typically 300°F or higher) when the actual coolant temperature should be much lower. This sensor monitors coolant temperature to help the ECM control fuel injection, ignition timing, cooling fan operation, and transmission shift points. When the sensor reads too hot, it can cause rich fuel mixture, overheating protection mode, and poor engine performance.',

  symptoms: [
    'Check engine light illuminated - ECT sensor fault detected',
    'Engine running rich - ECM thinks coolant is overheating',
    'Cooling fans running constantly - ECM trying to cool "hot" engine',
    'Poor fuel economy - Rich fuel mixture from hot temperature reading',
    'Engine hesitation or rough idle - Incorrect fuel/timing calculations',
    'Hard starting when engine is cold - ECM thinks engine is already hot',
    'Transmission shifting problems - Incorrect temperature data affects shift points',
    'Engine may enter "limp mode" - Protection against perceived overheating',
    'Temperature gauge reading incorrectly high - If connected to same sensor'
  ],

  causes: [
    'Faulty ECT sensor - Internal component failure causing low resistance',
    'Short circuit in ECT sensor wiring - Wire touching ground causing low resistance',
    'Corroded ECT sensor connector - Poor electrical contact causing resistance drop',
    'ECT sensor contamination - Conductive debris causing short circuit',
    'Damaged ECT sensor threads - Physical damage affecting sensor operation',
    'ECM internal fault - Module misreading sensor signal',
    'Wiring harness damage - Short to ground in sensor circuit',
    'Incorrect ECT sensor installation - Wrong sensor type or poor thermal contact'
  ],
  
  performanceImpact: 'P0117 causes the ECM to receive incorrect coolant temperature data, leading to overly rich fuel mixture, constantly running cooling fans, poor fuel economy, potential engine flooding, and incorrect transmission shift points.',

  quickAnswer: {
    icon: 'thermometer-full',
    meaning: 'ECT sensor reading extremely hot temperatures (300°F+) when coolant is actually cooler - usually short circuit or sensor failure.',
    fix: 'Replace ECT sensor or repair short circuit',
    cost: '$85-$320',
    time: '30-60 minutes',
    drivingSafety: 'Safe to drive short distances, but expect poor fuel economy and constantly running fans. Repair soon to prevent engine flooding.'
  },

  aiQuestions: [
    {
      question: 'What\'s the difference between P0117 and P0118 ECT codes?',
      answer: 'P0117 indicates the ECT sensor is reading too hot (low input voltage), while P0118 indicates too cold readings (high input voltage). P0117 typically means a short circuit or sensor reading actual high temperatures, while P0118 usually indicates an open circuit or failed sensor defaulting to -40°F.'
    },
    {
      question: 'Why does P0117 cause rich fuel mixture?',
      answer: 'When the ECM thinks the coolant is extremely hot, it assumes the engine is overheating and enriches the fuel mixture to help cool combustion temperatures. This protective strategy results in poor fuel economy and potential engine flooding, especially during cold starts.'
    },
    {
      question: 'Can actual engine overheating cause P0117?',
      answer: 'While severe overheating can affect ECT readings, P0117 typically indicates sensor readings far beyond normal operating temperatures (300°F+). True P0117 is usually caused by electrical faults rather than actual overheating, unless there\'s a catastrophic cooling system failure.'
    },
    {
      question: 'How do I test an ECT sensor for P0117?',
      answer: 'Measure resistance across sensor terminals - it should decrease as temperature increases. At room temperature (68°F), expect around 2,500 ohms. If you get very low resistance (under 100 ohms) regardless of temperature, the sensor has failed. GeekOBD APP can monitor live ECT readings to confirm the fault.'
    }
  ],
  
  costAnalysis: {
    averageCost: '$85-$320 for most P0117 repairs',
    repairOptions: [
      {
        title: 'ECT Sensor Replacement',
        description: 'Most common fix - Replace faulty sensor (80% of cases)',
        color: '#4CAF50',
        icon: 'thermometer-full',
        items: [
          { name: 'ECT Sensor', cost: '$35-$95' },
          { name: 'Coolant (if drained)', cost: '$15-$35' },
          { name: 'Labor (0.5-1 hour)', cost: '$50-$120' }
        ],
        total: '$100-$250',
        successRate: '95% success rate'
      },
      {
        title: 'Wiring Repair',
        description: 'Fix short circuit in sensor wiring (15% of cases)',
        color: '#FF9800',
        icon: 'plug',
        items: [
          { name: 'Wire repair/splice', cost: '$20-$40' },
          { name: 'Diagnostic time', cost: '$80-$150' },
          { name: 'Labor (1-2 hours)', cost: '$100-$240' }
        ],
        total: '$200-$430',
        successRate: '90% success rate'
      },
      {
        title: 'Connector Cleaning/Replacement',
        description: 'Clean corroded connector or replace if damaged (5% of cases)',
        color: '#2196F3',
        icon: 'wrench',
        items: [
          { name: 'Connector cleaning', cost: '$25-$45' },
          { name: 'New connector (if needed)', cost: '$30-$70' },
          { name: 'Labor (0.5-1 hour)', cost: '$50-$120' }
        ],
        total: '$105-$235',
        successRate: '85% success rate'
      }
    ],
    savingTips: [
      'Check connector first - 10% of P0117 cases are just corroded connections',
      'ECT sensors are usually accessible - consider DIY replacement to save $50-120 in labor',
      'Test sensor resistance before buying parts - confirm failure first',
      'Some aftermarket sensors cost 40% less than OEM with same reliability',
      'If coolant needs draining, combine with scheduled coolant service to save money'
    ]
  },
  
  diagnosticSteps: {
    estimatedTime: 'PT45M',
    steps: [
      {
        title: 'Verify P0117 Code and Symptoms',
        icon: 'search',
        description: 'Connect scan tool and confirm P0117 is present. Check current ECT reading - should show extremely high temperature (300°F+) even when engine is cold. Note if cooling fans are running constantly.',
        geekobdTip: 'Use GeekOBD APP to monitor live ECT sensor data. Look for readings above 300°F when engine is cold - this confirms P0117 fault.'
      },
      {
        title: 'Visual Inspection of ECT Sensor',
        icon: 'eye',
        description: 'Locate ECT sensor (usually in cylinder head, intake manifold, or radiator). Check for physical damage, coolant leaks around sensor, or signs of overheating. Inspect connector for corrosion.',
        geekobdTip: 'GeekOBD APP can help identify which ECT sensor is faulty if vehicle has multiple temperature sensors.'
      },
      {
        title: 'Test ECT Sensor Resistance',
        icon: 'thermometer-full',
        description: 'Disconnect sensor and measure resistance across terminals with multimeter. At 68°F, expect ~2,500 ohms. If resistance is very low (under 100 ohms), sensor has internal short circuit.',
        geekobdTip: 'Compare resistance readings with GeekOBD APP temperature charts to verify sensor is within specification for current coolant temperature.'
      },
      {
        title: 'Check Wiring and Connector',
        icon: 'plug',
        description: 'Inspect wiring harness for damage, shorts to ground, or pinched wires. Clean connector terminals and check for proper connection. Test continuity from sensor to ECM.',
        geekobdTip: 'Use GeekOBD APP to monitor sensor voltage while wiggling wires - voltage should remain stable if wiring is good.'
      },
      {
        title: 'Verify Repair and Test Drive',
        icon: 'check-circle',
        description: 'After replacing sensor or repairing wiring, clear codes and test drive. Monitor ECT readings to ensure they respond normally to engine temperature changes.',
        geekobdTip: 'GeekOBD APP provides real-time verification - ECT should read close to ambient temperature when cold, gradually increase to 180-220°F during normal operation.'
      }
    ],
    importantNotes: [
      'P0117 indicates sensor reading too hot - do not confuse with P0118 (too cold)',
      'Always test sensor resistance before replacement - connector issues can mimic sensor failure',
      'ECT sensor affects fuel mixture and cooling fans - driving with P0117 causes poor fuel economy'
    ]
  },
  
  caseStudies: [
    {
      title: 'Chevrolet Silverado ECT Sensor Short Circuit',
      vehicle: '2019 Chevrolet Silverado 5.3L V8, 95,000 miles',
      problem: 'Customer complained of poor fuel economy (dropped from 18 to 12 MPG), constantly running cooling fans, and check engine light. Truck was running rough and seemed to be "flooding" during cold starts.',
      diagnosis: 'GeekOBD APP showed P0117 code with ECT reading constant 325°F even when engine was stone cold. Resistance test revealed ECT sensor had only 35 ohms resistance (should be ~2,500 ohms at room temperature), indicating internal short circuit.',
      solution: 'Replaced ECT sensor located in cylinder head near thermostat housing. Sensor required partial coolant drain and took 45 minutes to replace due to tight access.',
      cost: 'ECT sensor: $68, Coolant: $25, Labor: $90, Total: $183',
      result: 'P0117 code cleared immediately. ECT now reads correctly (75°F cold, 195°F operating). Fuel economy returned to normal 18 MPG, cooling fans operate normally, smooth cold starts restored.'
    },
    {
      title: 'Honda Accord Wiring Harness Damage',
      vehicle: '2016 Honda Accord 2.4L 4-cylinder, 88,000 miles',
      problem: 'Intermittent P0117 code with occasional poor idle and rich exhaust smell. Problem seemed to occur more often during hot weather or after driving over rough roads.',
      diagnosis: 'Initial ECT sensor test showed normal resistance, but GeekOBD APP revealed intermittent spikes to 350°F+ during driving. Wire wiggle test found damaged section of harness near exhaust manifold where wires had been damaged by heat.',
      solution: 'Repaired damaged section of ECT sensor wiring harness. Cut out heat-damaged portion and spliced in new high-temperature wire with proper heat shielding.',
      cost: 'High-temp wire kit: $35, Heat shielding: $20, Diagnostic time: $120, Labor: $160, Total: $335',
      result: 'P0117 code has not returned after 4 months. ECT readings remain stable during all driving conditions. Customer reports normal fuel economy and no more rich exhaust smell.'
    }
  ],

  relatedCodes: [
    { code: 'P0118', description: 'ECT Sensor High Input - Opposite condition (too cold readings)', color: '#3498db' },
    { code: 'P0125', description: 'Insufficient Coolant Temperature - Related coolant temperature issue', color: '#e74c3c' },
    { code: 'P0128', description: 'Coolant Thermostat Rationality - Related cooling system problem', color: '#f39c12' },
    { code: 'P0115', description: 'ECT Sensor Circuit Malfunction - General ECT circuit problem', color: '#4a90e2' },
    { code: 'P0172', description: 'System Too Rich Bank 1 - Can be caused by incorrect ECT readings', color: '#9b59b6' },
    { code: 'P0175', description: 'System Too Rich Bank 2 - Can be caused by incorrect ECT readings', color: '#9b59b6' },
    { code: 'P0300', description: 'Random Misfire - Can be caused by rich mixture from P0117', color: '#e67e22' }
  ],
  
  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P0117',
      description: 'Use GeekOBD APP for professional ECT sensor diagnosis!',
      features: [
        'Live coolant temperature monitoring',
        'Resistance testing guidance',
        'Cooling system analysis',
        'Repair verification tools'
      ]
    },
    systemCodes: {
      title: 'ECT Sensor Codes',
      description: 'Related Engine Coolant Temperature sensor codes:'
    },
    diagnosticResources: [
      {
        title: 'ECT Sensor Testing Guide',
        description: 'Professional resistance and voltage testing procedures',
        icon: 'thermometer-full',
        url: '#diagnostic-steps'
      },
      {
        title: 'ECT Wiring Diagrams',
        description: 'Sensor circuit diagrams and pin configurations',
        icon: 'sitemap',
        url: '../resources/ect-wiring-diagrams.html'
      },
      {
        title: 'Temperature Charts',
        description: 'ECT sensor resistance vs temperature specifications',
        icon: 'line-chart',
        url: '../resources/ect-temperature-charts.html'
      },
      {
        title: 'Cooling System Guide',
        description: 'Complete cooling system diagnostic procedures',
        icon: 'tint',
        url: '../resources/cooling-system-guide.html'
      }
    ],
    codeInfo: {
      system: 'Engine Management',
      severity: 'MEDIUM',
      category: 'Engine Coolant Temperature'
    }
  }
});

module.exports = p0117Data;