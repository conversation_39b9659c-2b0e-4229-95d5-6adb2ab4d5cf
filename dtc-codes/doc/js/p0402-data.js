const { DTCData } = require('./dtc-template-generator');

// P0402 EGR Flow Excessive 的完整数据结构
const p0402Data = new DTCData({
  code: 'P0402',
  title: 'EGR Flow Excessive',
  description: 'The Engine Control Module has detected excessive exhaust gas recirculation flow.',
  definition: 'The Engine Control Module has detected excessive exhaust gas recirculation (EGR) flow. The EGR system is designed to recirculate a controlled amount of exhaust gases back into the intake manifold to reduce combustion temperatures and NOx emissions. When the ECM detects more EGR flow than commanded or expected, P0402 is triggered. This typically indicates a stuck-open EGR valve, vacuum system problems, or control system malfunctions.',
  
  symptoms: [
    'Check engine light illuminated - ECM detected excessive EGR flow',
    'Rough idle - Too much EGR flow diluting air/fuel mixture at idle',
    'Engine stalling - Excessive exhaust gas reducing combustion efficiency',
    'Poor acceleration - EGR flow reducing available oxygen for combustion',
    'Engine hesitation - Inconsistent power delivery due to excessive EGR',
    'Black smoke from exhaust - Rich mixture from excessive EGR dilution',
    'Poor fuel economy - Engine compensating for excessive EGR with more fuel',
    'Engine surging - Unstable combustion from too much exhaust gas recirculation',
    'Hard starting - Excessive EGR affecting combustion during startup'
  ],
  
  causes: [
    'Stuck open EGR valve - Valve unable to close properly allowing continuous flow',
    'Faulty EGR vacuum solenoid - Solenoid stuck open providing constant vacuum',
    'Vacuum leak after EGR solenoid - Causing EGR valve to stay open',
    'Damaged EGR valve diaphragm - Preventing proper valve closure',
    'Faulty EGR position sensor - Providing incorrect feedback to ECM',
    'ECM software issues - Control module commanding excessive EGR flow',
    'Carbon buildup preventing valve closure - Deposits keeping valve partially open',
    'Faulty EGR control module - Electronic control system malfunction'
  ],
  
  performanceImpact: 'P0402 causes poor engine performance, rough idle, stalling, and poor fuel economy due to excessive exhaust gas diluting the air/fuel mixture and reducing combustion efficiency.',
  
  quickAnswer: {
    icon: 'arrow-up',
    meaning: 'Too much exhaust gas flowing through EGR system - usually stuck-open valve or vacuum system problem.',
    fix: 'Check EGR valve operation, test vacuum system, replace stuck EGR valve',
    cost: '$180-$650',
    time: '90-180 minutes',
    drivingSafety: 'Safe to drive but expect poor performance, rough idle, and possible stalling. Repair promptly to restore normal operation.'
  },
  
  aiQuestions: [
    {
      question: 'What\'s the difference between P0401 and P0402?',
      answer: 'P0401 indicates insufficient EGR flow (too little), while P0402 indicates excessive EGR flow (too much). P0402 typically causes more noticeable drivability problems like rough idle and stalling because too much EGR severely affects combustion.'
    },
    {
      question: 'Can excessive EGR flow damage my engine?',
      answer: 'Excessive EGR flow typically won\'t cause immediate engine damage, but it can cause poor combustion, carbon buildup, and increased wear over time. The main concerns are poor performance and potential stalling while driving.'
    },
    {
      question: 'Why does my engine stall with P0402?',
      answer: 'Excessive EGR flow dilutes the air/fuel mixture with inert exhaust gases, reducing the oxygen available for combustion. This can make the mixture too lean to sustain combustion, especially at idle, causing stalling.'
    },
    {
      question: 'How do I test if my EGR valve is stuck open?',
      answer: 'Use GeekOBD APP to command EGR valve closed while monitoring engine RPM at idle. If RPM doesn\'t increase when valve is commanded closed, the valve is likely stuck open. You can also manually check if the valve closes properly.'
    }
  ],

  costAnalysis: {
    averageCost: '$180-$650 for most P0402 repairs',
    repairOptions: [
      {
        title: 'EGR Valve Replacement',
        description: 'Replace stuck-open EGR valve (60% of cases)',
        color: '#4CAF50',
        icon: 'arrow-up',
        items: [
          { name: 'EGR valve', cost: '$120-$400' },
          { name: 'Labor (1-2 hours)', cost: '$100-$240' }
        ],
        total: '$220-$640',
        successRate: '90% success rate'
      },
      {
        title: 'Vacuum System Repair',
        description: 'Fix faulty vacuum solenoid or leaks (30% of cases)',
        color: '#2196F3',
        icon: 'compress',
        items: [
          { name: 'Vacuum solenoid/lines', cost: '$40-$150' },
          { name: 'Labor (1-1.5 hours)', cost: '$100-$180' }
        ],
        total: '$140-$330',
        successRate: '85% success rate'
      },
      {
        title: 'EGR System Cleaning',
        description: 'Clean carbon buildup preventing valve closure (10% of cases)',
        color: '#FF9800',
        icon: 'refresh',
        items: [
          { name: 'EGR cleaner and gaskets', cost: '$25-$50' },
          { name: 'Labor (1.5-2.5 hours)', cost: '$150-$300' }
        ],
        total: '$175-$350',
        successRate: '60% success rate'
      }
    ],
    savingTips: [
      'Test EGR valve closure with GeekOBD APP before replacement',
      'Check vacuum system first - may be simple solenoid or line repair',
      'Stuck-open valves usually require replacement rather than cleaning',
      'Address P0402 promptly to prevent poor performance and stalling',
      'Consider EGR valve cleaning if carbon buildup is preventing closure'
    ]
  },

  diagnosticSteps: {
    estimatedTime: 'PT90M',
    steps: [
      {
        title: 'Test EGR Valve Closure',
        icon: 'stop',
        description: 'Connect GeekOBD APP and command EGR valve closed while monitoring engine RPM at idle. RPM should increase if valve closes properly.',
        geekobdTip: 'GeekOBD APP can command EGR valve closed - significant RPM increase indicates valve is closing properly, no change suggests stuck-open valve.'
      },
      {
        title: 'Check EGR Position Sensor',
        icon: 'tachometer',
        description: 'Monitor EGR valve position sensor readings while commanding valve operation. Position should change when valve is commanded open/closed.',
        geekobdTip: 'Use GeekOBD APP to monitor EGR position - readings should match commanded position, discrepancies indicate sensor or valve problems.'
      },
      {
        title: 'Test Vacuum System Operation',
        icon: 'compress',
        description: 'Check EGR vacuum solenoid operation and vacuum lines for proper function. Verify solenoid can control vacuum to EGR valve.',
        geekobdTip: 'GeekOBD APP can command vacuum solenoid operation - monitor vacuum at EGR valve to verify proper solenoid control.'
      },
      {
        title: 'Visual Inspection',
        icon: 'eye',
        description: 'Remove EGR valve and inspect for carbon buildup preventing closure, damaged diaphragm, or mechanical problems with valve operation.',
        geekobdTip: 'Monitor EGR position with GeekOBD APP while manually operating valve - should show position changes if sensor and valve mechanism work properly.'
      },
      {
        title: 'Component Replacement and Verification',
        icon: 'check-circle',
        description: 'Replace faulty EGR valve or repair vacuum system as diagnosed. Clear codes and verify EGR system operates within normal parameters.',
        geekobdTip: 'Use GeekOBD APP to verify EGR valve now closes properly and engine RPM responds appropriately to EGR commands.'
      }
    ],
    importantNotes: [
      'P0402 usually indicates stuck-open EGR valve requiring replacement',
      'Test valve closure response before assuming valve failure',
      'Check vacuum system operation - may be control problem rather than valve'
    ]
  },

  caseStudies: [
    {
      title: 'Ford Focus Stuck EGR Valve',
      vehicle: '2016 Ford Focus 2.0L 4-cylinder, 118,000 miles',
      problem: 'Customer reported rough idle, engine stalling at traffic lights, and P0402 code. Engine would occasionally stall when coming to a stop.',
      diagnosis: 'GeekOBD APP showed EGR valve position sensor indicated valve was partially open even when commanded closed. EGR valve was mechanically stuck open due to carbon buildup.',
      solution: 'Replaced EGR valve assembly. Attempted cleaning was unsuccessful as carbon deposits had warped the valve seat preventing proper closure.',
      cost: 'EGR valve: $165, Labor: $135, Total: $300',
      result: 'P0402 code cleared immediately. Engine now idles smoothly and no more stalling problems. EGR valve closes properly when commanded.'
    },
    {
      title: 'Honda Civic Vacuum Solenoid Failure',
      vehicle: '2017 Honda Civic 1.5L Turbo, 85,000 miles',
      problem: 'Intermittent P0402 code with occasional rough idle. Problem seemed worse during city driving with frequent stops.',
      diagnosis: 'EGR valve tested mechanically good, but GeekOBD APP showed vacuum solenoid was not responding to close commands. Solenoid was stuck in open position.',
      solution: 'Replaced faulty EGR vacuum solenoid. Solenoid was providing constant vacuum to EGR valve, keeping it partially open.',
      cost: 'EGR vacuum solenoid: $75, Labor: $95, Total: $170',
      result: 'P0402 code has not returned after 6 months. EGR valve now operates properly and rough idle eliminated during city driving.'
    }
  ],

  relatedCodes: [
    { code: 'P0400', description: 'EGR Flow Malfunction - General EGR flow problem', color: '#e74c3c' },
    { code: 'P0401', description: 'EGR Flow Insufficient - Not enough EGR flow detected', color: '#3498db' },
    { code: 'P0403', description: 'EGR Circuit Malfunction - Electrical control problems', color: '#f39c12' },
    { code: 'P0404', description: 'EGR Position Sensor Range/Performance - Position sensor issues', color: '#9b59b6' },
    { code: 'P0405', description: 'EGR Position Sensor Low - Position sensor voltage too low', color: '#4a90e2' },
    { code: 'P0406', description: 'EGR Position Sensor High - Position sensor voltage too high', color: '#e67e22' },
    { code: 'P0172', description: 'System Too Rich - Can be caused by excessive EGR flow', color: '#27ae60' }
  ],

  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P0402',
      description: 'Use GeekOBD APP for EGR valve closure testing!',
      features: [
        'EGR valve command testing',
        'Position sensor monitoring',
        'Vacuum system verification',
        'RPM response analysis'
      ]
    },
    systemCodes: {
      title: 'EGR System Codes',
      description: 'Related exhaust gas recirculation codes:'
    },
    diagnosticResources: [
      {
        title: 'EGR Valve Testing',
        description: 'Professional procedures for testing EGR valve operation',
        icon: 'arrow-up',
        url: '#diagnostic-steps'
      },
      {
        title: 'Vacuum System Diagnosis',
        description: 'Testing and repairing automotive vacuum systems',
        icon: 'compress',
        url: '../resources/vacuum-system-diagnosis.html'
      },
      {
        title: 'Idle Quality Issues',
        description: 'Diagnosing and fixing rough idle problems',
        icon: 'cog',
        url: '../resources/idle-quality-issues.html'
      },
      {
        title: 'Stalling Diagnosis',
        description: 'Understanding and fixing engine stalling problems',
        icon: 'stop',
        url: '../resources/stalling-diagnosis.html'
      }
    ],
    codeInfo: {
      system: 'Emissions Control',
      severity: 'MEDIUM',
      category: 'EGR System'
    }
  }
});

module.exports = p0402Data;
