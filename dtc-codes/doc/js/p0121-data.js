const { DTCData } = require('./dtc-template-generator');

// P0121 TPS Range/Performance 的完整数据结构
const p0121Data = new DTCData({
  code: 'P0121',
  title: 'TPS Range/Performance',
  description: 'The Engine Control Module has detected that the Throttle Position Sensor signal is outside the expected range or not performing within specifications.',
  definition: 'The Engine Control Module has detected that the Throttle Position Sensor (TPS) signal is outside the expected range or not performing within specifications. The TPS measures throttle blade position to help the ECM determine driver intent and calculate proper fuel injection and ignition timing. When the TPS reading doesn\'t correlate with other engine parameters like MAP sensor, MAF sensor, or expected throttle response, P0121 is triggered.',
  
  symptoms: [
    'Check engine light illuminated - ECM detected TPS performance issue',
    'Poor throttle response - Delayed or inconsistent acceleration',
    'Engine hesitation during acceleration - TPS not accurately reporting throttle position',
    'Rough idle or stalling - Incorrect idle throttle position readings',
    'Engine surging at cruise speeds - Inconsistent TPS readings causing fuel fluctuations',
    'Transmission shifting problems - TPS data affects automatic transmission operation',
    'Reduced power output - ECM limiting performance due to TPS uncertainty',
    'Poor fuel economy - Non-optimized fuel delivery from inaccurate TPS data',
    'Cruise control malfunction - System requires accurate throttle position feedback'
  ],
  
  causes: [
    'Dirty or contaminated TPS sensor - Carbon buildup affecting sensor operation',
    'Worn TPS sensor - Internal components degraded from age and use',
    'Throttle body carbon buildup - Restricting throttle blade movement',
    'Damaged throttle shaft or bushings - Causing erratic throttle blade position',
    'Loose TPS mounting - Sensor not properly aligned with throttle shaft',
    'Vacuum leaks affecting throttle operation - Unmetered air changing expected readings',
    'Faulty throttle cable or linkage - Mechanical problems affecting throttle response',
    'ECM calibration issues - Software not properly interpreting TPS signals'
  ],
  
  performanceImpact: 'P0121 causes poor throttle response, inconsistent engine performance, transmission shifting problems, and reduced fuel economy due to the ECM\'s inability to accurately determine driver intent and optimize engine operation.',
  
  quickAnswer: {
    icon: 'tachometer',
    meaning: 'TPS sensor reading outside expected range - usually dirty sensor or throttle body carbon buildup.',
    fix: 'Clean throttle body, replace TPS sensor, check throttle linkage',
    cost: '$120-$450',
    time: '60-150 minutes',
    drivingSafety: 'Safe to drive but expect poor throttle response and performance. Clean throttle body first as simple fix.'
  },
  
  aiQuestions: [
    {
      question: 'What\'s the difference between P0121 and P0120 TPS codes?',
      answer: 'P0121 indicates the TPS is working electrically but providing readings outside expected range, while P0120 indicates an electrical circuit problem. P0121 is often caused by dirty throttle body or worn sensor, while P0120 is typically wiring or connector issues.'
    },
    {
      question: 'Can a dirty throttle body cause P0121?',
      answer: 'Yes, carbon buildup on the throttle body is one of the most common causes of P0121. Carbon deposits can prevent the throttle blade from closing completely or moving smoothly, causing TPS readings that don\'t match expected values for the throttle position.'
    },
    {
      question: 'How do I test TPS sensor for P0121?',
      answer: 'Use GeekOBD APP to monitor TPS voltage while slowly moving throttle from closed to wide open. Voltage should increase smoothly from about 0.5V to 4.5V without jumps or dead spots. Erratic readings or values outside this range indicate TPS problems.'
    },
    {
      question: 'Why does P0121 affect transmission shifting?',
      answer: 'The transmission uses TPS data to determine shift points and shift firmness based on driver demand. When TPS readings are inaccurate, the transmission may shift too early, too late, or with incorrect firmness, leading to poor performance and potential transmission damage.'
    }
  ],

  costAnalysis: {
    averageCost: '$120-$450 for most P0121 repairs',
    repairOptions: [
      {
        title: 'Throttle Body Cleaning',
        description: 'Most common first step - Clean carbon buildup (40% success rate)',
        color: '#4CAF50',
        icon: 'refresh',
        items: [
          { name: 'Throttle body cleaner', cost: '$12-$25' },
          { name: 'Labor (45-75 minutes)', cost: '$60-$150' }
        ],
        total: '$72-$175',
        successRate: '40% success rate'
      },
      {
        title: 'TPS Sensor Replacement',
        description: 'Replace worn or damaged sensor (50% of cases)',
        color: '#2196F3',
        icon: 'tachometer',
        items: [
          { name: 'TPS sensor', cost: '$45-$120' },
          { name: 'Labor (60-90 minutes)', cost: '$80-$180' }
        ],
        total: '$125-$300',
        successRate: '95% success rate'
      },
      {
        title: 'Throttle Body Replacement',
        description: 'Replace entire throttle body if shaft/bushings worn (10% of cases)',
        color: '#FF9800',
        icon: 'cog',
        items: [
          { name: 'Throttle body assembly', cost: '$200-$450' },
          { name: 'Labor (1.5-2.5 hours)', cost: '$150-$300' }
        ],
        total: '$350-$750',
        successRate: '98% success rate'
      }
    ],
    savingTips: [
      'Always try throttle body cleaning first - fixes 40% of P0121 cases for under $100',
      'Use GeekOBD APP to test TPS response before and after cleaning',
      'Check for vacuum leaks before replacing expensive components',
      'TPS replacement is often DIY-friendly, saving $80-180 in labor',
      'Consider throttle body service as preventive maintenance every 60k miles'
    ]
  },

  diagnosticSteps: {
    estimatedTime: 'PT90M',
    steps: [
      {
        title: 'Monitor TPS Performance',
        icon: 'line-chart',
        description: 'Connect GeekOBD APP and monitor TPS voltage while slowly moving throttle from closed to wide open. Look for smooth voltage increase from 0.5V to 4.5V without jumps or dead spots.',
        geekobdTip: 'GeekOBD APP can graph TPS voltage over time - look for erratic readings, stuck values, or voltage outside normal 0.5-4.5V range.'
      },
      {
        title: 'Visual Inspection',
        icon: 'eye',
        description: 'Remove air intake and inspect throttle body for carbon buildup, damaged throttle blade, or worn throttle shaft. Check TPS mounting and alignment.',
        geekobdTip: 'Use GeekOBD APP to monitor TPS readings while manually moving throttle blade - readings should change smoothly with blade movement.'
      },
      {
        title: 'Throttle Body Cleaning',
        icon: 'refresh',
        description: 'Clean throttle body thoroughly with appropriate cleaner, removing all carbon deposits from blade, bore, and TPS area. Allow to dry completely before reassembly.',
        geekobdTip: 'Monitor TPS readings with GeekOBD APP after cleaning - improved linearity and proper voltage range indicate successful cleaning.'
      },
      {
        title: 'TPS Calibration Test',
        icon: 'cogs',
        description: 'Test TPS calibration by checking voltage at idle (should be 0.5-0.9V) and wide open throttle (should be 4.0-4.8V). Verify smooth transition between positions.',
        geekobdTip: 'GeekOBD APP can perform TPS sweep test - voltage should increase linearly without jumps or dead spots throughout throttle range.'
      },
      {
        title: 'Component Replacement and Relearn',
        icon: 'check-circle',
        description: 'Replace TPS sensor or throttle body as needed. Clear codes and perform throttle relearn procedure if required by vehicle manufacturer.',
        geekobdTip: 'Use GeekOBD APP to verify TPS readings are now within specification and respond properly to throttle input after replacement.'
      }
    ],
    importantNotes: [
      'Clean throttle body first - resolves 40% of P0121 cases',
      'TPS voltage should increase smoothly from 0.5V to 4.5V',
      'Some vehicles require throttle relearn procedure after service'
    ]
  },

  caseStudies: [
    {
      title: 'Honda Accord Carbon Buildup',
      vehicle: '2016 Honda Accord 2.4L 4-cylinder, 95,000 miles',
      problem: 'Customer reported poor acceleration, engine hesitation, and occasional stalling at idle. P0121 code was present with rough idle symptoms.',
      diagnosis: 'GeekOBD APP showed TPS voltage was erratic and wouldn\'t drop below 1.2V at idle (should be 0.5-0.9V). Visual inspection revealed heavy carbon buildup preventing throttle blade from closing completely.',
      solution: 'Thoroughly cleaned throttle body with throttle body cleaner, removing all carbon deposits. Performed throttle relearn procedure as specified by Honda.',
      cost: 'Throttle body cleaner: $18, Labor: $95, Total: $113',
      result: 'P0121 code cleared immediately. TPS now reads 0.7V at idle and 4.4V at WOT. Engine idles smoothly and acceleration response fully restored.'
    },
    {
      title: 'Ford Escape Worn TPS Sensor',
      vehicle: '2015 Ford Escape 1.6L Turbo, 118,000 miles',
      problem: 'Intermittent P0121 code with poor throttle response and transmission shifting harshly. Problem seemed to worsen with engine temperature.',
      diagnosis: 'Throttle body was clean, but GeekOBD APP showed TPS voltage had dead spots between 2.0-2.5V where voltage wouldn\'t change despite throttle movement. Internal TPS wear was causing signal dropout.',
      solution: 'Replaced TPS sensor with OEM part. Sensor was integrated into throttle body, requiring complete throttle body replacement on this model.',
      cost: 'Throttle body assembly: $285, Labor: $150, Total: $435',
      result: 'P0121 code cleared and has not returned. TPS voltage now increases smoothly throughout range and transmission shifting returned to normal.'
    }
  ],

  relatedCodes: [
    { code: 'P0120', description: 'TPS Circuit Malfunction - Electrical circuit problems', color: '#e74c3c' },
    { code: 'P0122', description: 'TPS Low Input - Sensor reading too low voltage', color: '#3498db' },
    { code: 'P0123', description: 'TPS High Input - Sensor reading too high voltage', color: '#f39c12' },
    { code: 'P0124', description: 'TPS Intermittent - Intermittent sensor readings', color: '#9b59b6' },
    { code: 'P0171', description: 'System Too Lean Bank 1 - Can be caused by TPS problems', color: '#9b59b6' },
    { code: 'P0172', description: 'System Too Rich Bank 1 - Can be caused by TPS problems', color: '#9b59b6' },
    { code: 'P0300', description: 'Random Misfire - Poor performance from incorrect throttle data', color: '#e67e22' }
  ],

  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P0121',
      description: 'Use GeekOBD APP for comprehensive TPS performance testing!',
      features: [
        'Real-time TPS voltage monitoring',
        'Throttle sweep testing',
        'Performance verification',
        'Calibration assistance'
      ]
    },
    systemCodes: {
      title: 'TPS Sensor Codes',
      description: 'Related throttle position sensor codes:'
    },
    diagnosticResources: [
      {
        title: 'TPS Performance Testing',
        description: 'Professional procedures for testing TPS sensor operation',
        icon: 'tachometer',
        url: '#diagnostic-steps'
      },
      {
        title: 'Throttle Body Service',
        description: 'Complete throttle body cleaning and maintenance guide',
        icon: 'refresh',
        url: '../resources/throttle-body-service.html'
      },
      {
        title: 'TPS Calibration',
        description: 'Throttle position sensor calibration and relearn procedures',
        icon: 'cogs',
        url: '../resources/tps-calibration.html'
      },
      {
        title: 'Throttle Response Issues',
        description: 'Diagnosing and fixing poor throttle response problems',
        icon: 'dashboard',
        url: '../resources/throttle-response-issues.html'
      }
    ],
    codeInfo: {
      system: 'Engine Management',
      severity: 'MEDIUM',
      category: 'Sensor Performance'
    }
  }
});

module.exports = p0121Data;
