const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0508Data = require('./p0508-data');

console.log('🚀 Generating P0508 - Cold Air Intake System Low Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0508Data);
  const outputPath = path.join(__dirname, '../../p0508.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0508 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0508 page:', error.message);
  process.exit(1);
}
