const { DTCData } = require('./dtc-template-generator');

// P0715 Input/Turbine Speed Sensor Circuit 的完整数据结构
const p0715Data = new DTCData({
  code: 'P0715',
  title: 'Input/Turbine Speed Sensor Circuit',
  description: 'The Engine Control Module has detected a malfunction in the input/turbine speed sensor circuit.',
  definition: 'The Engine Control Module has detected a malfunction in the input/turbine speed sensor circuit. The input speed sensor (also called turbine speed sensor) monitors the rotational speed of the transmission input shaft or torque converter turbine. This information is used by the TCM to determine proper shift points, calculate gear ratios, and detect transmission slippage. When there are electrical problems with the sensor circuit, P0715 is triggered.',
  
  symptoms: [
    'Check engine light illuminated - ECM detected input speed sensor circuit fault',
    'Transmission shifting problems - Harsh, delayed, or erratic shifts',
    'Transmission slipping - Loss of power transfer during acceleration',
    'No upshift or downshift - Transmission stuck in one gear',
    'Speedometer erratic or not working - Speed calculation affected',
    'Transmission overheating - Poor shift control causing excessive heat',
    'Limp mode operation - Transmission operating in safe mode',
    'Poor fuel economy - Transmission not shifting optimally',
    'Engine stalling - Torque converter lockup problems'
  ],
  
  causes: [
    'Faulty input speed sensor - Internal sensor failure preventing signal generation',
    'Damaged sensor wiring - Broken or corroded wires affecting signal transmission',
    'Corroded sensor connector - Poor electrical contact affecting signal quality',
    'Contamination on sensor - Metal particles or debris affecting magnetic pickup',
    'Damaged reluctor ring - Missing or damaged teeth on sensor target wheel',
    'TCM internal fault - Control module unable to process sensor signals',
    'Power supply problems - Inadequate voltage to sensor circuit',
    'Ground circuit problems - Poor ground connection affecting sensor operation'
  ],
  
  performanceImpact: 'P0715 causes poor transmission operation, harsh shifting, potential transmission damage from improper shift control, and may affect speedometer and other systems that rely on transmission speed data.',
  
  quickAnswer: {
    icon: 'tachometer-alt',
    meaning: 'Input speed sensor not providing proper signal to transmission control - usually sensor or wiring failure.',
    fix: 'Test sensor wiring, check sensor signal, replace input speed sensor if needed',
    cost: '$180-$480',
    time: '90-180 minutes',
    drivingSafety: 'May be unsafe if transmission is slipping or not shifting properly. Have diagnosed promptly to prevent transmission damage.'
  },
  
  aiQuestions: [
    {
      question: 'What does the input speed sensor do?',
      answer: 'The input speed sensor monitors the rotational speed of the transmission input shaft or torque converter turbine. The TCM uses this information to calculate gear ratios, detect slippage, determine shift points, and control torque converter lockup.'
    },
    {
      question: 'Can P0715 cause transmission damage?',
      answer: 'Yes, P0715 can cause transmission damage because the TCM cannot properly control shifting without accurate input speed data. This can lead to harsh shifts, slippage, overheating, and internal transmission component damage.'
    },
    {
      question: 'How do I test the input speed sensor?',
      answer: 'Use GeekOBD APP to monitor input speed sensor signal while driving. The signal should increase with engine RPM and show consistent readings. You can also test sensor resistance and check for proper AC voltage signal generation.'
    },
    {
      question: 'Why does my speedometer not work with P0715?',
      answer: 'Some vehicles calculate vehicle speed using transmission input and output speed sensors. When the input speed sensor fails, the speedometer may not work properly because the system cannot accurately calculate vehicle speed.'
    }
  ],

  costAnalysis: {
    averageCost: '$180-$480 for most P0715 repairs',
    repairOptions: [
      {
        title: 'Input Speed Sensor Replacement',
        description: 'Replace faulty input speed sensor (70% of cases)',
        color: '#4CAF50',
        icon: 'tachometer-alt',
        items: [
          { name: 'Input speed sensor', cost: '$80-$180' },
          { name: 'Labor (1.5-2.5 hours)', cost: '$150-$300' }
        ],
        total: '$230-$480',
        successRate: '90% success rate'
      },
      {
        title: 'Wiring Harness Repair',
        description: 'Fix damaged sensor wiring (20% of cases)',
        color: '#FF9800',
        icon: 'flash',
        items: [
          { name: 'Wiring repair materials', cost: '$30-$80' },
          { name: 'Labor (1-2 hours)', cost: '$100-$240' }
        ],
        total: '$130-$320',
        successRate: '85% success rate'
      },
      {
        title: 'Connector Service',
        description: 'Clean or replace corroded sensor connector (10% of cases)',
        color: '#2196F3',
        icon: 'plug',
        items: [
          { name: 'Connector cleaning/replacement', cost: '$20-$50' },
          { name: 'Labor (30-60 minutes)', cost: '$50-$120' }
        ],
        total: '$70-$170',
        successRate: '80% success rate'
      }
    ],
    savingTips: [
      'Check sensor connector for corrosion before replacing sensor',
      'Use GeekOBD APP to verify sensor signal before replacement',
      'Some input speed sensors are accessible for DIY replacement',
      'Address P0715 promptly to prevent transmission damage',
      'Check transmission fluid level - low fluid can affect sensor operation'
    ]
  },

  diagnosticSteps: {
    estimatedTime: 'PT120M',
    steps: [
      {
        title: 'Monitor Input Speed Signal',
        icon: 'search',
        description: 'Connect GeekOBD APP and monitor input speed sensor signal while driving. Signal should increase with engine RPM and show consistent readings.',
        geekobdTip: 'GeekOBD APP can show input speed sensor RPM in real-time - compare with engine RPM to verify sensor accuracy and operation.'
      },
      {
        title: 'Test Sensor Electrical Circuit',
        icon: 'bolt',
        description: 'Check sensor power supply, ground, and signal circuits with multimeter. Verify proper voltage and AC signal generation.',
        geekobdTip: 'Use GeekOBD APP to monitor sensor voltage while testing circuits - should show stable power supply and clean signal generation.'
      },
      {
        title: 'Visual Inspection',
        icon: 'eye',
        description: 'Inspect input speed sensor, wiring, and connector for damage, corrosion, or contamination. Check sensor mounting and air gap.',
        geekobdTip: 'Monitor sensor signal with GeekOBD APP while wiggling wires - intermittent readings indicate wiring problems.'
      },
      {
        title: 'Check Reluctor Ring',
        icon: 'cog',
        description: 'Inspect transmission input shaft reluctor ring for missing or damaged teeth that could affect sensor reading.',
        geekobdTip: 'GeekOBD APP can show sensor signal pattern - irregular or missing pulses indicate reluctor ring damage.'
      },
      {
        title: 'Component Replacement and Verification',
        icon: 'check-circle',
        description: 'Replace faulty sensor or repair wiring as diagnosed. Clear codes and verify sensor provides accurate speed readings.',
        geekobdTip: 'Use GeekOBD APP to verify input speed sensor now provides consistent, accurate readings that match transmission operation.'
      }
    ],
    importantNotes: [
      'Input speed sensor is critical for proper transmission operation',
      'Check transmission fluid level - low fluid can affect sensor operation',
      'Address P0715 promptly to prevent transmission damage from poor shift control'
    ]
  },

  caseStudies: [
    {
      title: 'Chevrolet Malibu Sensor Contamination',
      vehicle: '2016 Chevrolet Malibu 1.5L Turbo, 118,000 miles',
      problem: 'Customer reported harsh shifting, transmission slipping, and P0715 code. Speedometer was also reading erratically.',
      diagnosis: 'GeekOBD APP showed intermittent input speed sensor signal. Found sensor was contaminated with metal particles from transmission wear.',
      solution: 'Replaced input speed sensor and performed transmission fluid change. Metal contamination indicated internal transmission wear.',
      cost: 'Input speed sensor: $125, Transmission service: $180, Labor: $160, Total: $465',
      result: 'P0715 code cleared and transmission shifts smoothly. Speedometer now reads accurately and no more slipping.'
    },
    {
      title: 'Ford Fusion Wiring Damage',
      vehicle: '2017 Ford Fusion 2.5L 4-cylinder, 89,000 miles',
      problem: 'Intermittent P0715 code with occasional harsh shifts. Problem seemed worse in wet weather conditions.',
      diagnosis: 'Input speed sensor tested good, but GeekOBD APP showed intermittent signal loss. Found sensor wiring damaged by road debris.',
      solution: 'Repaired damaged section of input speed sensor wiring and added protective covering to prevent future damage.',
      cost: 'Wiring repair kit: $45, Protective covering: $25, Labor: $140, Total: $210',
      result: 'P0715 code has not returned after 6 months. Transmission operates properly in all weather conditions.'
    }
  ],

  relatedCodes: [
    { code: 'P0700', description: 'Transmission Control System Malfunction - General transmission problem', color: '#e74c3c' },
    { code: 'P0716', description: 'Input Speed Sensor Range/Performance - Sensor signal out of range', color: '#3498db' },
    { code: 'P0717', description: 'Input Speed Sensor No Signal - No sensor signal detected', color: '#f39c12' },
    { code: 'P0720', description: 'Output Speed Sensor Circuit - Related transmission speed sensor', color: '#9b59b6' },
    { code: 'P0500', description: 'Vehicle Speed Sensor Malfunction - Related speed sensor problems', color: '#4a90e2' },
    { code: 'P0741', description: 'Torque Converter Clutch Circuit - Related transmission control', color: '#e67e22' },
    { code: 'P0750', description: 'Shift Solenoid A Malfunction - Related transmission control', color: '#27ae60' }
  ],

  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P0715',
      description: 'Use GeekOBD APP for input speed sensor testing!',
      features: [
        'Real-time speed monitoring',
        'Sensor signal verification',
        'Transmission data analysis',
        'Shift pattern monitoring'
      ]
    },
    systemCodes: {
      title: 'Transmission Speed Codes',
      description: 'Related transmission speed sensor codes:'
    },
    diagnosticResources: [
      {
        title: 'Speed Sensor Testing',
        description: 'Professional procedures for testing transmission speed sensors',
        icon: 'tachometer-alt',
        url: '#diagnostic-steps'
      },
      {
        title: 'Transmission Diagnostics',
        description: 'Advanced transmission diagnostic techniques',
        icon: 'cogs',
        url: '../resources/transmission-diagnostics.html'
      },
      {
        title: 'Sensor Replacement',
        description: 'Step-by-step speed sensor replacement procedures',
        icon: 'wrench',
        url: '../resources/sensor-replacement.html'
      },
      {
        title: 'Shift Control Systems',
        description: 'Understanding transmission shift control operation',
        icon: 'exchange-alt',
        url: '../resources/shift-control-systems.html'
      }
    ],
    codeInfo: {
      system: 'Transmission Control',
      severity: 'HIGH',
      category: 'Speed Sensor'
    }
  }
});

module.exports = p0715Data;
