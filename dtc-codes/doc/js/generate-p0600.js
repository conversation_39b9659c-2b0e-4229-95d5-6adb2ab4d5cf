const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0600Data = require('./p0600-data');

console.log('🚀 Generating P0600 - Serial Communication Link Malfunction Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0600Data);
  const outputPath = path.join(__dirname, '../../p0600.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0600 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0600 page:', error.message);
  process.exit(1);
}
