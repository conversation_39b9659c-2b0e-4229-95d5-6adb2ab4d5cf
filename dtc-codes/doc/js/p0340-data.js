const { DTCData } = require('./dtc-template-generator');

// P0340 Camshaft Position Sensor Circuit Malfunction 的完整数据结构
const p0340Data = new DTCData({
  code: 'P0340',
  title: 'Camshaft Position Sensor Circuit Malfunction',
  description: 'The Engine Control Module has detected an electrical malfunction in the camshaft position sensor circuit.',
  definition: 'The Engine Control Module has detected an electrical malfunction in the camshaft position sensor circuit. The camshaft position sensor monitors the position and speed of the camshaft to help the ECM determine proper ignition timing and fuel injection timing. When there are electrical problems with the sensor circuit, including open circuits, short circuits, or signal irregularities, P0340 is triggered.',
  
  symptoms: [
    'Check engine light illuminated - ECM detected camshaft position sensor circuit fault',
    'Engine cranks but won\'t start - ECM cannot determine camshaft position for ignition timing',
    'Engine stalling - Loss of camshaft position signal during operation',
    'Rough idle - Incorrect ignition timing from poor sensor signal',
    'Poor engine performance - Reduced power and acceleration',
    'Engine misfiring - Incorrect ignition timing causing combustion problems',
    'Hard starting - ECM using backup timing strategies',
    'Engine running in limp mode - ECM limiting performance due to sensor uncertainty',
    'Poor fuel economy - Non-optimized ignition and fuel timing'
  ],
  
  causes: [
    'Faulty camshaft position sensor - Internal sensor failure preventing signal generation',
    'Open circuit in sensor wiring - Broken wire preventing signal transmission',
    'Short circuit in sensor harness - Wire touching ground or power',
    'Corroded sensor connector - Poor electrical contact affecting signal quality',
    'ECM internal fault - Control module unable to process sensor signals',
    'Damaged wiring harness - Physical damage from heat, vibration, or wear',
    'Faulty sensor power supply - Inadequate voltage to sensor circuit',
    'Timing chain/belt problems - Mechanical issues affecting sensor operation'
  ],
  
  performanceImpact: 'P0340 can prevent engine starting or cause severe performance problems including stalling, misfiring, and poor fuel economy due to incorrect ignition and fuel injection timing.',
  
  quickAnswer: {
    icon: 'cog',
    meaning: 'Electrical problem in camshaft position sensor circuit - sensor, wiring, or connector failure.',
    fix: 'Check sensor wiring, test sensor signal, replace camshaft position sensor',
    cost: '$120-$450',
    time: '60-150 minutes',
    drivingSafety: 'May not start or stall while driving. If running, drive carefully to repair shop. Can cause sudden engine shutdown.'
  },
  
  aiQuestions: [
    {
      question: 'Can P0340 prevent my engine from starting?',
      answer: 'Yes, P0340 can prevent engine starting because the ECM needs camshaft position information to determine proper ignition timing. Without this signal, the ECM may not fire the spark plugs at the correct time, preventing combustion.'
    },
    {
      question: 'What\'s the difference between camshaft and crankshaft position sensors?',
      answer: 'The crankshaft position sensor monitors engine RPM and piston position, while the camshaft position sensor monitors valve timing. Both are needed for proper ignition timing, but camshaft position is specifically needed for sequential fuel injection and variable valve timing systems.'
    },
    {
      question: 'Can a bad timing chain cause P0340?',
      answer: 'Yes, a stretched timing chain can cause P0340 by changing the relationship between the crankshaft and camshaft. This can make the camshaft position sensor signal appear erratic or out of sync, triggering the code even if the sensor itself is good.'
    },
    {
      question: 'How do I test a camshaft position sensor?',
      answer: 'Use GeekOBD APP to monitor camshaft position sensor signal while cranking the engine. You should see a consistent square wave pattern. Use a multimeter to check sensor power supply (usually 5V or 12V) and ground circuits.'
    }
  ],

  costAnalysis: {
    averageCost: '$120-$450 for most P0340 repairs',
    repairOptions: [
      {
        title: 'Camshaft Position Sensor Replacement',
        description: 'Most common fix - Replace faulty sensor (65% of cases)',
        color: '#4CAF50',
        icon: 'cog',
        items: [
          { name: 'Camshaft position sensor', cost: '$50-$150' },
          { name: 'Labor (1-2 hours)', cost: '$100-$240' }
        ],
        total: '$150-$390',
        successRate: '90% success rate'
      },
      {
        title: 'Wiring Harness Repair',
        description: 'Fix damaged sensor wiring (25% of cases)',
        color: '#FF9800',
        icon: 'flash',
        items: [
          { name: 'Wiring repair materials', cost: '$25-$60' },
          { name: 'Diagnostic time', cost: '$100-$150' },
          { name: 'Labor (1-2 hours)', cost: '$100-$240' }
        ],
        total: '$225-$450',
        successRate: '85% success rate'
      },
      {
        title: 'Connector Service',
        description: 'Clean or replace corroded sensor connector (10% of cases)',
        color: '#2196F3',
        icon: 'plug',
        items: [
          { name: 'Connector cleaning/replacement', cost: '$20-$50' },
          { name: 'Dielectric grease', cost: '$8-$15' },
          { name: 'Labor (30-60 minutes)', cost: '$50-$120' }
        ],
        total: '$78-$185',
        successRate: '80% success rate'
      }
    ],
    savingTips: [
      'Check connector and wiring first - may save expensive sensor replacement',
      'Use GeekOBD APP to verify sensor signal before replacement',
      'Some sensors are accessible for DIY replacement, saving $100-240 in labor',
      'Check timing chain condition if sensor replacement doesn\'t fix code',
      'Address P0340 promptly to prevent no-start conditions'
    ]
  },

  diagnosticSteps: {
    estimatedTime: 'PT90M',
    steps: [
      {
        title: 'Check for Sensor Signal',
        icon: 'search',
        description: 'Connect GeekOBD APP and monitor camshaft position sensor signal while cranking engine. Should see consistent square wave pattern indicating sensor operation.',
        geekobdTip: 'GeekOBD APP can show camshaft position sensor RPM and signal quality - no signal or erratic readings indicate sensor or circuit problems.'
      },
      {
        title: 'Test Sensor Power Supply',
        icon: 'bolt',
        description: 'Check sensor power supply voltage (usually 5V or 12V) and ground circuit with multimeter. Verify proper voltage at sensor connector with key on.',
        geekobdTip: 'Use GeekOBD APP to monitor sensor voltage while testing - stable readings indicate good power supply circuits.'
      },
      {
        title: 'Visual Inspection',
        icon: 'eye',
        description: 'Inspect camshaft position sensor, wiring harness, and connector for damage, corrosion, or signs of oil contamination affecting sensor operation.',
        geekobdTip: 'Monitor sensor signal with GeekOBD APP while wiggling wires - intermittent readings indicate wiring problems.'
      },
      {
        title: 'Check Timing Chain/Belt Condition',
        icon: 'link',
        description: 'Verify timing chain or belt condition if sensor tests good electrically. Stretched timing components can cause sensor correlation problems.',
        geekobdTip: 'GeekOBD APP can show camshaft and crankshaft position correlation - significant deviation indicates timing chain/belt problems.'
      },
      {
        title: 'Component Replacement and Verification',
        icon: 'check-circle',
        description: 'Replace faulty sensor or repair wiring as diagnosed. Clear codes and verify sensor signal is now present and stable during engine operation.',
        geekobdTip: 'Use GeekOBD APP to verify camshaft position sensor now provides consistent signal and engine timing is correct.'
      }
    ],
    importantNotes: [
      'P0340 can prevent engine starting - diagnose promptly',
      'Check timing chain/belt condition if sensor replacement doesn\'t fix code',
      'Oil contamination can damage camshaft position sensors'
    ]
  },

  caseStudies: [
    {
      title: 'Subaru Outback No-Start Condition',
      vehicle: '2016 Subaru Outback 2.5L 4-cylinder, 95,000 miles',
      problem: 'Customer reported engine cranks but won\'t start. P0340 code was present and engine would occasionally start after multiple attempts.',
      diagnosis: 'GeekOBD APP showed no camshaft position sensor signal during cranking. Visual inspection revealed camshaft position sensor connector had oil contamination causing poor electrical contact.',
      solution: 'Cleaned oil-contaminated connector thoroughly and replaced camshaft position sensor that had been damaged by oil exposure. Also fixed valve cover gasket leak causing oil contamination.',
      cost: 'Camshaft position sensor: $85, Valve cover gasket: $45, Labor: $180, Total: $310',
      result: 'P0340 code cleared and engine now starts immediately every time. No more intermittent starting problems after 6 months.'
    },
    {
      title: 'Honda Civic Timing Chain Issue',
      vehicle: '2015 Honda Civic 1.8L 4-cylinder, 145,000 miles',
      problem: 'P0340 code with engine running rough and occasional stalling. Camshaft position sensor had been replaced twice but code kept returning.',
      diagnosis: 'GeekOBD APP showed camshaft position sensor signal was present but timing correlation with crankshaft was incorrect. Found timing chain had stretched significantly.',
      solution: 'Replaced stretched timing chain, tensioner, and guides. Previous sensor replacements were unnecessary - timing chain stretch was causing correlation problems.',
      cost: 'Timing chain kit: $280, Labor: $450, Total: $730',
      result: 'P0340 code cleared permanently. Engine runs smoothly and timing correlation is now correct. No more sensor-related codes.'
    }
  ],

  relatedCodes: [
    { code: 'P0341', description: 'Camshaft Position Sensor Range/Performance - Sensor signal out of range', color: '#4a90e2' },
    { code: 'P0342', description: 'Camshaft Position Sensor Low Input - Sensor signal too low', color: '#3498db' },
    { code: 'P0343', description: 'Camshaft Position Sensor High Input - Sensor signal too high', color: '#e74c3c' },
    { code: 'P0335', description: 'Crankshaft Position Sensor Circuit - Related position sensor', color: '#f39c12' },
    { code: 'P0016', description: 'Crankshaft/Camshaft Correlation - Timing chain/belt problems', color: '#9b59b6' },
    { code: 'P0300', description: 'Random Misfire - Can be caused by timing problems', color: '#e67e22' },
    { code: 'P0201', description: 'Injector Circuit - Sequential injection requires cam position', color: '#27ae60' }
  ],

  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P0340',
      description: 'Use GeekOBD APP for camshaft position sensor testing!',
      features: [
        'Sensor signal monitoring',
        'Timing correlation analysis',
        'Circuit voltage testing',
        'No-start diagnosis'
      ]
    },
    systemCodes: {
      title: 'Position Sensor Codes',
      description: 'Related camshaft and crankshaft position codes:'
    },
    diagnosticResources: [
      {
        title: 'Camshaft Position Testing',
        description: 'Professional procedures for testing camshaft position sensors',
        icon: 'cog',
        url: '#diagnostic-steps'
      },
      {
        title: 'No-Start Diagnosis',
        description: 'Systematic approach to diagnosing no-start conditions',
        icon: 'power-off',
        url: '../resources/no-start-diagnosis.html'
      },
      {
        title: 'Timing Chain Diagnosis',
        description: 'Identifying and diagnosing timing chain problems',
        icon: 'link',
        url: '../resources/timing-chain-diagnosis.html'
      },
      {
        title: 'Engine Timing Systems',
        description: 'Understanding camshaft and crankshaft timing relationships',
        icon: 'clock-o',
        url: '../resources/engine-timing-systems.html'
      }
    ],
    codeInfo: {
      system: 'Engine Management',
      severity: 'HIGH',
      category: 'Position Sensor'
    }
  }
});

module.exports = p0340Data;
