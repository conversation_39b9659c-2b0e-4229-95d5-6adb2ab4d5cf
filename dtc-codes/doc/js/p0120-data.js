const { DTCData } = require('./dtc-template-generator');

// P0120 TPS Circuit Malfunction 的完整数据结构
const p0120Data = new DTCData({
  code: 'P0120',
  title: 'TPS Circuit Malfunction',
  description: 'The Engine Control Module has detected an electrical malfunction in the Throttle Position Sensor circuit.',
  definition: 'The Engine Control Module has detected an electrical malfunction in the Throttle Position Sensor (TPS) circuit. This indicates a problem with the electrical components of the TPS system rather than the sensor readings themselves. The TPS circuit includes the sensor, wiring harness, connector, and ECM connections. P0120 is triggered when there are voltage irregularities, open circuits, short circuits, or other electrical faults that prevent proper communication between the TPS and ECM.',
  
  symptoms: [
    'Check engine light illuminated - ECM detected TPS circuit electrical fault',
    'Engine running in limp mode - ECM using backup throttle position calculations',
    'Poor acceleration response - Incorrect throttle position data affects fuel delivery',
    'Engine stalling at idle - ECM cannot determine proper idle throttle position',
    'Rough idle or surging - Inconsistent fuel mixture from missing TPS data',
    'Hard starting - ECM unable to calculate proper fuel delivery for throttle position',
    'Transmission shifting problems - TPS data affects automatic transmission shift points',
    'Cruise control not working - System requires accurate throttle position data',
    'Poor fuel economy - Non-optimized fuel delivery without TPS feedback'
  ],
  
  causes: [
    'Open circuit in TPS wiring - Broken wire preventing signal transmission',
    'Short circuit in TPS harness - Wire touching ground or power',
    'Faulty TPS connector - Corroded, damaged, or loose connection',
    'Failed TPS sensor - Internal electrical failure in sensor components',
    'ECM internal fault - Control module unable to process TPS signals',
    'Damaged wiring harness - Physical damage from heat, vibration, or wear',
    'Poor ground connection - Inadequate ground circuit for TPS sensor',
    'Throttle body problems - Carbon buildup affecting TPS operation'
  ],
  
  performanceImpact: 'P0120 forces the ECM to operate without accurate throttle position data, using default throttle assumptions that result in poor acceleration, rough idle, transmission shifting problems, and significantly reduced engine performance.',
  
  quickAnswer: {
    icon: 'flash',
    meaning: 'Electrical problem in TPS circuit - wiring, connector, or sensor electrical failure.',
    fix: 'Check wiring, test connections, replace TPS if needed',
    cost: '$95-$380',
    time: '60-120 minutes',
    drivingSafety: 'Safe to drive but expect poor performance and possible stalling. Repair soon for normal operation.'
  },
  
  aiQuestions: [
    {
      question: 'What\'s the difference between P0120 and P0121/P0122 TPS codes?',
      answer: 'P0120 indicates an electrical circuit problem (wiring, connector, or sensor electrical failure), while P0121/P0122 indicate the sensor is working electrically but providing readings outside expected range. P0120 is typically easier to diagnose with electrical testing.'
    },
    {
      question: 'Can a dirty throttle body cause P0120?',
      answer: 'A dirty throttle body typically causes P0121 (range/performance) rather than P0120. However, severe carbon buildup can sometimes interfere with TPS operation or cause connector problems, potentially leading to P0120. Clean throttle body first, then test TPS circuit.'
    },
    {
      question: 'How do I test TPS circuit for P0120?',
      answer: 'Use a multimeter to check for 5V reference voltage, good ground, and signal wire continuity. GeekOBD APP can show if the ECM is receiving any signal from the TPS - no data or fixed values indicate circuit problems rather than sensor range issues.'
    },
    {
      question: 'Why does P0120 affect transmission shifting?',
      answer: 'Automatic transmissions use TPS data to determine when to shift gears and how firm the shifts should be. Without accurate throttle position information, the transmission cannot properly interpret driver intent, leading to harsh shifts, delayed shifts, or staying in lower gears.'
    }
  ],

  costAnalysis: {
    averageCost: '$95-$380 for most P0120 repairs',
    repairOptions: [
      {
        title: 'TPS Sensor Replacement',
        description: 'Most common fix - Replace sensor with internal electrical failure (60% of cases)',
        color: '#4CAF50',
        icon: 'tachometer',
        items: [
          { name: 'TPS sensor', cost: '$45-$120' },
          { name: 'Labor (45-90 minutes)', cost: '$60-$180' }
        ],
        total: '$105-$300',
        successRate: '95% success rate'
      },
      {
        title: 'Wiring Harness Repair',
        description: 'Fix damaged TPS sensor wiring (30% of cases)',
        color: '#FF9800',
        icon: 'flash',
        items: [
          { name: 'Wiring repair materials', cost: '$25-$60' },
          { name: 'Diagnostic time', cost: '$100-$160' },
          { name: 'Labor (1-2 hours)', cost: '$100-$240' }
        ],
        total: '$225-$460',
        successRate: '90% success rate'
      },
      {
        title: 'Connector Service',
        description: 'Clean or replace corroded TPS connector (10% of cases)',
        color: '#2196F3',
        icon: 'plug',
        items: [
          { name: 'Connector cleaning/replacement', cost: '$20-$55' },
          { name: 'Dielectric grease', cost: '$8-$15' },
          { name: 'Labor (30-60 minutes)', cost: '$50-$120' }
        ],
        total: '$78-$190',
        successRate: '85% success rate'
      }
    ],
    savingTips: [
      'Check connector first - 15% of P0120 cases are just corroded connections',
      'Clean throttle body before TPS replacement - may resolve connection issues',
      'Use multimeter to test circuits before replacing expensive components',
      'GeekOBD APP can help identify if problem is sensor or wiring related',
      'TPS replacement often requires throttle body removal - factor in labor time'
    ]
  },

  diagnosticSteps: {
    estimatedTime: 'PT90M',
    steps: [
      {
        title: 'Check for TPS Data',
        icon: 'search',
        description: 'Connect GeekOBD APP and check if TPS data is available. With P0120, you may see no data, fixed values, or complete absence of throttle position readings.',
        geekobdTip: 'GeekOBD APP will show if ECM is receiving TPS signals - complete absence of data indicates circuit failure rather than sensor range issues.'
      },
      {
        title: 'Visual Inspection',
        icon: 'eye',
        description: 'Inspect TPS sensor, throttle body, and wiring for damage. Look for corroded pins, damaged wires, carbon buildup, or signs of wear on throttle shaft.',
        geekobdTip: 'Use GeekOBD APP to monitor for any signal while moving throttle - intermittent data indicates connection problems.'
      },
      {
        title: 'Electrical Circuit Testing',
        icon: 'bolt',
        description: 'Test TPS power supply (5V reference), ground circuit, and signal wire continuity with multimeter. Check for proper voltage at sensor connector.',
        geekobdTip: 'GeekOBD APP should show stable readings when circuits are good - use multimeter to verify actual voltages match expected values.'
      },
      {
        title: 'Throttle Body Cleaning',
        icon: 'refresh',
        description: 'Clean throttle body and TPS area with appropriate cleaner. Carbon buildup can interfere with TPS operation and electrical connections.',
        geekobdTip: 'Monitor TPS readings with GeekOBD APP during cleaning - improved signal stability indicates cleaning helped connection issues.'
      },
      {
        title: 'Component Replacement and Calibration',
        icon: 'check-circle',
        description: 'Replace faulty wiring, connector, or TPS as diagnosed. Clear codes and perform TPS relearn procedure if required by vehicle.',
        geekobdTip: 'GeekOBD APP should now show stable TPS readings that respond smoothly to throttle movement, confirming successful circuit repair.'
      }
    ],
    importantNotes: [
      'P0120 is electrical circuit problem, not sensor range issue',
      'Clean throttle body before replacing TPS - may resolve connection problems',
      'Some vehicles require TPS relearn procedure after replacement'
    ]
  },

  caseStudies: [
    {
      title: 'Toyota Camry Carbon Buildup Issue',
      vehicle: '2015 Toyota Camry 2.5L 4-cylinder, 125,000 miles',
      problem: 'Customer reported poor acceleration, engine stalling at idle, and P0120 code. Throttle response was very poor and engine would surge.',
      diagnosis: 'GeekOBD APP showed no TPS data available. Visual inspection revealed severe carbon buildup around TPS sensor area, causing connector corrosion and poor electrical contact.',
      solution: 'Thoroughly cleaned throttle body and TPS area, cleaned corroded connector pins, and applied dielectric grease. Carbon buildup had created electrical path to ground.',
      cost: 'Throttle body cleaner: $15, Connector cleaning kit: $18, Labor: $120, Total: $153',
      result: 'P0120 code cleared immediately. TPS data now available and throttle response fully restored. Engine idles smoothly and accelerates normally.'
    },
    {
      title: 'Ford F-150 Wiring Harness Damage',
      vehicle: '2017 Ford F-150 3.5L V6, 89,000 miles',
      problem: 'Intermittent P0120 code with occasional limp mode activation. Problem seemed worse when engine bay was hot or during rough driving conditions.',
      diagnosis: 'Found TPS wiring harness had been damaged by heat from nearby exhaust component, creating intermittent open circuit. GeekOBD APP showed TPS data would disappear randomly.',
      solution: 'Repaired heat-damaged section of TPS wiring harness and rerouted wiring away from heat source. Added heat shielding to prevent recurrence.',
      cost: 'Wiring repair kit: $35, Heat shielding: $25, Labor: $180, Total: $240',
      result: 'P0120 code has not returned after 8 months. TPS data remains stable even under high-temperature conditions and no more limp mode activation.'
    }
  ],

  relatedCodes: [
    { code: 'P0121', description: 'TPS Range/Performance - Sensor working but readings out of range', color: '#4a90e2' },
    { code: 'P0122', description: 'TPS Low Input - Sensor reading too low voltage', color: '#3498db' },
    { code: 'P0123', description: 'TPS High Input - Sensor reading too high voltage', color: '#e74c3c' },
    { code: 'P0124', description: 'TPS Intermittent - Intermittent sensor readings', color: '#f39c12' },
    { code: 'P0171', description: 'System Too Lean Bank 1 - Can result from missing TPS data', color: '#9b59b6' },
    { code: 'P0172', description: 'System Too Rich Bank 1 - Can result from default fuel maps', color: '#9b59b6' },
    { code: 'P0300', description: 'Random Misfire - Poor performance from non-optimal fuel delivery', color: '#e67e22' }
  ],

  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P0120',
      description: 'Use GeekOBD APP for TPS circuit diagnosis!',
      features: [
        'Circuit connectivity testing',
        'Real-time throttle monitoring',
        'Wiring problem identification',
        'Performance verification'
      ]
    },
    systemCodes: {
      title: 'TPS Sensor Codes',
      description: 'Related throttle position sensor codes:'
    },
    diagnosticResources: [
      {
        title: 'Electrical Circuit Testing',
        description: 'Professional procedures for testing TPS circuits',
        icon: 'flash',
        url: '#diagnostic-steps'
      },
      {
        title: 'Throttle Body Service',
        description: 'Proper throttle body cleaning and maintenance',
        icon: 'refresh',
        url: '../resources/throttle-body-service.html'
      },
      {
        title: 'TPS Calibration',
        description: 'TPS relearn and calibration procedures',
        icon: 'cogs',
        url: '../resources/tps-calibration.html'
      },
      {
        title: 'Limp Mode Recovery',
        description: 'Understanding and clearing limp mode conditions',
        icon: 'exclamation-triangle',
        url: '../resources/limp-mode-recovery.html'
      }
    ],
    codeInfo: {
      system: 'Engine Management',
      severity: 'MEDIUM',
      category: 'Electrical Circuit'
    }
  }
});

module.exports = p0120Data;
