const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0113Data = require('./p0113-data');

/**
 * 生成P0113页面示例
 * 演示如何使用模板系统生成完全优化的DTC页面
 */

console.log('🚀 Generating P0113 page using template system...\n');

try {
  // 创建模板生成器实例
  const generator = new DTCTemplateGenerator();
  
  // 生成HTML内容
  console.log('📝 Generating HTML content...');
  const htmlContent = generator.generatePage(p0113Data);
  
  // 写入文件
  const outputPath = path.join(__dirname, '../../p0113-template.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ Successfully generated p0113-template.html');
  console.log(`📁 File saved to: ${outputPath}`);
  
  // 显示生成的内容统计
  const stats = {
    totalLines: htmlContent.split('\n').length,
    fileSize: Math.round(htmlContent.length / 1024),
    hasStructuredData: htmlContent.includes('application/ld+json'),
    hasQuickAnswer: htmlContent.includes('quick-answer'),
    hasAIQuestions: htmlContent.includes('ai-qa'),
    hasCostAnalysis: htmlContent.includes('cost-info'),
    hasDiagnosticSteps: htmlContent.includes('diagnostic-steps'),
    hasCaseStudies: htmlContent.includes('Real Repair Case Studies'),
    hasSidebar: htmlContent.includes('GeekOBD APP')
  };
  
  console.log('\n📊 Generated Content Statistics:');
  console.log(`   Total lines: ${stats.totalLines}`);
  console.log(`   File size: ${stats.fileSize}KB`);
  console.log(`   ✅ Structured Data: ${stats.hasStructuredData ? 'Yes' : 'No'}`);
  console.log(`   ✅ Quick Answer: ${stats.hasQuickAnswer ? 'Yes' : 'No'}`);
  console.log(`   ✅ AI Questions: ${stats.hasAIQuestions ? 'Yes' : 'No'}`);
  console.log(`   ✅ Cost Analysis: ${stats.hasCostAnalysis ? 'Yes' : 'No'}`);
  console.log(`   ✅ Diagnostic Steps: ${stats.hasDiagnosticSteps ? 'Yes' : 'No'}`);
  console.log(`   ✅ Case Studies: ${stats.hasCaseStudies ? 'Yes' : 'No'}`);
  console.log(`   ✅ Sidebar: ${stats.hasSidebar ? 'Yes' : 'No'}`);
  
  console.log('\n🎉 Template generation completed successfully!');
  console.log('\n💡 Benefits of template system:');
  console.log('   • Consistent layout and structure');
  console.log('   • No manual HTML editing errors');
  console.log('   • All SEO optimizations included automatically');
  console.log('   • Easy to maintain and update');
  console.log('   • Scalable for hundreds of DTC codes');
  
  console.log('\n🔄 Next steps:');
  console.log('   1. Review generated p0113-template.html');
  console.log('   2. Compare with manually created p0113.html');
  console.log('   3. Create data files for other DTC codes');
  console.log('   4. Batch generate all DTC pages');
  
} catch (error) {
  console.error('❌ Error generating P0113 page:', error.message);
  console.error(error.stack);
  process.exit(1);
}

console.log('\n🎯 Template system ready for production use!');
