const fs = require('fs');
const path = require('path');

// 最近生成的故障码列表
const recentDTCCodes = [
    // 最近生成的P系统故障码
    { code: 'P0508', title: 'Cold Air Intake System Low', category: 'P0500-P0599' },
    { code: 'P0600', title: 'Serial Communication Link Malfunction', category: 'P0600-P0699' },
    { code: 'P0601', title: 'Internal Control Module Memory Check Sum Error', category: 'P0600-P0699' },
    { code: 'P0741', title: 'Torque Converter Clutch Circuit', category: 'P0700-P0799' },
    { code: 'P0750', title: 'Shift Solenoid A Malfunction', category: 'P0700-P0799' },
    { code: 'P0755', title: 'Shift Solenoid B Malfunction', category: 'P0700-P0799' },
    { code: 'P0760', title: 'Shift Solenoid C Malfunction', category: 'P0700-P0799' },
    { code: 'P1100', title: 'Mass Air Flow Sensor Intermittent', category: 'P1000-P1999' },
    { code: 'P1101', title: 'Mass Air Flow Sensor Out of Self-Test Range', category: 'P1000-P1999' },
    { code: 'P1130', title: 'Lack of Upstream Heated Oxygen Sensor Switch - Sensor Indicates Lean', category: 'P1000-P1999' },
    { code: 'P1131', title: 'Lack of Upstream Heated Oxygen Sensor Switch - Sensor Indicates Rich', category: 'P1000-P1999' },
    { code: 'P1150', title: 'Lack of Downstream Heated Oxygen Sensor Switch - Sensor Indicates Lean', category: 'P1000-P1999' },
    { code: 'P1151', title: 'Lack of Downstream Heated Oxygen Sensor Switch - Sensor Indicates Rich', category: 'P1000-P1999' }
];

// 生成故障码HTML条目
function generateDTCEntry(dtc) {
    const shortTitle = dtc.title.length > 25 ? dtc.title.substring(0, 22) + '...' : dtc.title;
    return `<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../${dtc.code.toLowerCase()}.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">${dtc.code}</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">${shortTitle}</div>
  </div>`;
}

// 更新引擎分类页面
function updateEngineCategory() {
    const enginePagePath = path.join(__dirname, '../../engine/index.html');

    if (!fs.existsSync(enginePagePath)) {
        console.log('❌ Engine category page not found');
        return;
    }

    let content = fs.readFileSync(enginePagePath, 'utf8');

    // 手动添加缺失的故障码到对应位置

    // 添加P0508到P0500-P0599部分
    if (!content.includes('P0508')) {
        const p0507Position = content.indexOf('onclick="window.location.href=\'../p0507.html\'">');
        if (p0507Position !== -1) {
            const insertAfter = content.indexOf('</div>', p0507Position + 200);
            if (insertAfter !== -1) {
                const newEntry = `\n<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../p0508.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">P0508</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Cold Air Intake System L...</div>
  </div>`;
                content = content.slice(0, insertAfter + 6) + newEntry + content.slice(insertAfter + 6);
                console.log('📝 Added P0508 to P0500-P0599 section');
            }
        }
    }

    // 添加P0741到P0700-P0799部分
    if (!content.includes('P0741')) {
        const p0722Position = content.indexOf('onclick="window.location.href=\'../p0722.html\'">');
        if (p0722Position !== -1) {
            const insertAfter = content.indexOf('</div>', p0722Position + 200);
            if (insertAfter !== -1) {
                const newEntry = `\n<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../p0741.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">P0741</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Torque Converter Clutch...</div>
  </div>`;
                content = content.slice(0, insertAfter + 6) + newEntry + content.slice(insertAfter + 6);
                console.log('📝 Added P0741 to P0700-P0799 section');
            }
        }
    }

    // 添加P0750到P0700-P0799部分
    if (!content.includes('P0750')) {
        const p0741Position = content.indexOf('onclick="window.location.href=\'../p0741.html\'">');
        if (p0741Position !== -1) {
            const insertAfter = content.indexOf('</div>', p0741Position + 200);
            if (insertAfter !== -1) {
                const newEntry = `\n<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../p0750.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">P0750</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Shift Solenoid A Malfunc...</div>
  </div>`;
                content = content.slice(0, insertAfter + 6) + newEntry + content.slice(insertAfter + 6);
                console.log('📝 Added P0750 to P0700-P0799 section');
            }
        }
    }

    // 添加P0755到P0700-P0799部分
    if (!content.includes('P0755')) {
        const p0750Position = content.indexOf('onclick="window.location.href=\'../p0750.html\'">');
        if (p0750Position !== -1) {
            const insertAfter = content.indexOf('</div>', p0750Position + 200);
            if (insertAfter !== -1) {
                const newEntry = `\n<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../p0755.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">P0755</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Shift Solenoid B Malfunc...</div>
  </div>`;
                content = content.slice(0, insertAfter + 6) + newEntry + content.slice(insertAfter + 6);
                console.log('📝 Added P0755 to P0700-P0799 section');
            }
        }
    }

    // 添加P0760到P0700-P0799部分
    if (!content.includes('P0760')) {
        const p0755Position = content.indexOf('onclick="window.location.href=\'../p0755.html\'">');
        if (p0755Position !== -1) {
            const insertAfter = content.indexOf('</div>', p0755Position + 200);
            if (insertAfter !== -1) {
                const newEntry = `\n<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../p0760.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">P0760</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Shift Solenoid C Malfunc...</div>
  </div>`;
                content = content.slice(0, insertAfter + 6) + newEntry + content.slice(insertAfter + 6);
                console.log('📝 Added P0760 to P0700-P0799 section');
            }
        }
    }

    // 添加P1100到P1000-P1999部分
    if (!content.includes('P1100')) {
        const p1001Position = content.indexOf('onclick="window.location.href=\'../p1001.html\'">');
        if (p1001Position !== -1) {
            const insertAfter = content.indexOf('</div>', p1001Position + 200);
            if (insertAfter !== -1) {
                const newEntry = `\n<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../p1100.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">P1100</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Mass Air Flow Sensor Int...</div>
  </div>`;
                content = content.slice(0, insertAfter + 6) + newEntry + content.slice(insertAfter + 6);
                console.log('📝 Added P1100 to P1000-P1999 section');
            }
        }
    }

    // 添加P1101到P1000-P1999部分
    if (!content.includes('P1101')) {
        const p1100Position = content.indexOf('onclick="window.location.href=\'../p1100.html\'">');
        if (p1100Position !== -1) {
            const insertAfter = content.indexOf('</div>', p1100Position + 200);
            if (insertAfter !== -1) {
                const newEntry = `\n<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../p1101.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">P1101</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Mass Air Flow Sensor Out...</div>
  </div>`;
                content = content.slice(0, insertAfter + 6) + newEntry + content.slice(insertAfter + 6);
                console.log('📝 Added P1101 to P1000-P1999 section');
            }
        }
    }

    // 添加P1130到P1000-P1999部分
    if (!content.includes('P1130')) {
        const p1101Position = content.indexOf('onclick="window.location.href=\'../p1101.html\'">');
        if (p1101Position !== -1) {
            const insertAfter = content.indexOf('</div>', p1101Position + 200);
            if (insertAfter !== -1) {
                const newEntry = `\n<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../p1130.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">P1130</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Upstream HO2S Switch - L...</div>
  </div>`;
                content = content.slice(0, insertAfter + 6) + newEntry + content.slice(insertAfter + 6);
                console.log('📝 Added P1130 to P1000-P1999 section');
            }
        }
    }

    // 添加P1131到P1000-P1999部分
    if (!content.includes('P1131')) {
        const p1130Position = content.indexOf('onclick="window.location.href=\'../p1130.html\'">');
        if (p1130Position !== -1) {
            const insertAfter = content.indexOf('</div>', p1130Position + 200);
            if (insertAfter !== -1) {
                const newEntry = `\n<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../p1131.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">P1131</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Upstream HO2S Switch - R...</div>
  </div>`;
                content = content.slice(0, insertAfter + 6) + newEntry + content.slice(insertAfter + 6);
                console.log('📝 Added P1131 to P1000-P1999 section');
            }
        }
    }

    // 添加P1150到P1000-P1999部分
    if (!content.includes('P1150')) {
        const p1131Position = content.indexOf('onclick="window.location.href=\'../p1131.html\'">');
        if (p1131Position !== -1) {
            const insertAfter = content.indexOf('</div>', p1131Position + 200);
            if (insertAfter !== -1) {
                const newEntry = `\n<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../p1150.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">P1150</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Downstream HO2S Switch -...</div>
  </div>`;
                content = content.slice(0, insertAfter + 6) + newEntry + content.slice(insertAfter + 6);
                console.log('📝 Added P1150 to P1000-P1999 section');
            }
        }
    }

    // 添加P1151到P1000-P1999部分
    if (!content.includes('P1151')) {
        const p1150Position = content.indexOf('onclick="window.location.href=\'../p1150.html\'">');
        if (p1150Position !== -1) {
            const insertAfter = content.indexOf('</div>', p1150Position + 200);
            if (insertAfter !== -1) {
                const newEntry = `\n<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../p1151.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">P1151</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Downstream HO2S Switch -...</div>
  </div>`;
                content = content.slice(0, insertAfter + 6) + newEntry + content.slice(insertAfter + 6);
                console.log('📝 Added P1151 to P1000-P1999 section');
            }
        }
    }

    // 写入更新后的内容
    fs.writeFileSync(enginePagePath, content, 'utf8');
    console.log('✅ Engine category page updated');
}

// 更新网络分类页面
function updateNetworkCategory() {
    const networkPagePath = path.join(__dirname, '../../network/index.html');

    if (!fs.existsSync(networkPagePath)) {
        console.log('❌ Network category page not found');
        return;
    }

    let content = fs.readFileSync(networkPagePath, 'utf8');

    // 添加P0600到P0600-P0699部分
    if (!content.includes('P0600')) {
        // 查找U0100的位置，在它之前插入P0600
        const u0100Position = content.indexOf('onclick="window.location.href=\'../u0100.html\'">');
        if (u0100Position !== -1) {
            const insertBefore = content.lastIndexOf('<div style="background: white;', u0100Position);
            if (insertBefore !== -1) {
                const newEntry = `<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../p0600.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">P0600</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Serial Communication Li...</div>
  </div>\n`;
                content = content.slice(0, insertBefore) + newEntry + content.slice(insertBefore);
                console.log('📝 Added P0600 to Network category');
            }
        }
    }

    // 添加P0601到P0600-P0699部分
    if (!content.includes('P0601')) {
        const p0600Position = content.indexOf('onclick="window.location.href=\'../p0600.html\'">');
        if (p0600Position !== -1) {
            const insertAfter = content.indexOf('</div>', p0600Position + 200);
            if (insertAfter !== -1) {
                const newEntry = `\n<div style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 12px; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'" onclick="window.location.href='../p0601.html'">
    <div style="color: #007bff; font-weight: bold; font-size: 14px; margin-bottom: 4px;">P0601</div>
    <div style="color: #666; font-size: 12px; line-height: 1.3;">Internal Control Module...</div>
  </div>`;
                content = content.slice(0, insertAfter + 6) + newEntry + content.slice(insertAfter + 6);
                console.log('📝 Added P0601 to Network category');
            }
        }
    }

    // 写入更新后的内容
    fs.writeFileSync(networkPagePath, content, 'utf8');
    console.log('✅ Network category page updated');
}

// 主函数
function main() {
    console.log('🚀 Updating category pages with missing DTC codes\n');

    try {
        updateEngineCategory();
        updateNetworkCategory();
        console.log('\n✅ Category pages update completed!');
    } catch (error) {
        console.error('❌ Error updating category pages:', error.message);
        process.exit(1);
    }
}

main();
