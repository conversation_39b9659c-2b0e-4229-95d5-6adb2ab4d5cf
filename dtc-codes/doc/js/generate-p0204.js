const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0204Data = require('./p0204-data');

console.log('🚀 Generating P0204 - Injector Circuit Malfunction Cylinder 4 Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0204Data);
  const outputPath = path.join(__dirname, '../../p0204.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0204 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0204 page:', error.message);
  process.exit(1);
}
