const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');

console.log('🔄 Regenerating All DTC Pages with Breadcrumb Navigation\n');

// 需要重新生成的页面列表
const dtcCodes = [
  'p0112', 'p0113', 'p0114', 'p0117', 'p0118', 'p0125'
];

const generator = new DTCTemplateGenerator();
let successCount = 0;
let errorCount = 0;

console.log(`📋 Found ${dtcCodes.length} DTC pages to regenerate\n`);

for (const code of dtcCodes) {
  try {
    console.log(`🔄 Regenerating ${code.toUpperCase()}...`);
    
    // 加载数据文件
    const dataPath = `./${code}-data.js`;
    if (!fs.existsSync(dataPath)) {
      console.log(`   ❌ Data file not found: ${dataPath}`);
      errorCount++;
      continue;
    }
    
    // 动态导入数据
    delete require.cache[require.resolve(dataPath)];
    const dtcData = require(dataPath);
    
    // 生成HTML内容
    const htmlContent = generator.generatePage(dtcData);
    
    // 保存到文件
    const outputPath = path.join(__dirname, `../../${code}.html`);
    fs.writeFileSync(outputPath, htmlContent, 'utf8');
    
    console.log(`   ✅ ${code.toUpperCase()} regenerated successfully`);
    successCount++;
    
  } catch (error) {
    console.log(`   ❌ Error regenerating ${code.toUpperCase()}: ${error.message}`);
    errorCount++;
  }
}

console.log('\n📊 Regeneration Summary:');
console.log(`   ✅ Successfully regenerated: ${successCount} pages`);
console.log(`   ❌ Failed to regenerate: ${errorCount} pages`);
console.log(`   📄 Total processed: ${dtcCodes.length} pages`);

if (successCount > 0) {
  console.log('\n🎉 Breadcrumb Navigation Added to All Pages!');
  console.log('\n🧭 Navigation Structure:');
  console.log('   Home » DTC Codes » Engine Codes » [CODE]');
  
  console.log('\n✨ Updated Features:');
  console.log('   • Breadcrumb navigation for better user experience');
  console.log('   • Consistent navigation structure across all pages');
  console.log('   • Improved SEO with structured navigation');
  console.log('   • Better accessibility with proper nav elements');
  
  console.log('\n📈 Completed Series:');
  console.log('   🌡️  IAT Sensor Series: P0112, P0113, P0114');
  console.log('   🌡️  ECT Sensor Series: P0117, P0118, P0125');
  
  console.log('\n🔄 Ready for Next Series:');
  console.log('   🔥 Misfire Series: P0300, P0301, P0302');
  console.log('   🏭 Catalyst Series: P0420, P0430');
}

if (errorCount > 0) {
  console.log('\n⚠️  Please check the failed pages and regenerate manually if needed.');
}

console.log('\n🚀 All regeneration tasks completed!');
