const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0441Data = require('./p0441-data');

console.log('🚀 Generating P0441 - EVAP Purge Flow Incorrect Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0441Data);
  const outputPath = path.join(__dirname, '../../p0441.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0441 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0441 page:', error.message);
  process.exit(1);
}
