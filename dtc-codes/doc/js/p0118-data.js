const { DTCData } = require('./dtc-template-generator');

// P0118 ECT Sensor High Input 的完整数据结构
const p0118Data = new DTCData({
  code: 'P0118',
  title: 'ECT Sensor High Input',
  description: 'The Engine Control Module has detected that the Engine Coolant Temperature sensor is reading extremely cold temperatures.',
  definition: 'The Engine Control Module has detected that the Engine Coolant Temperature (ECT) sensor is producing readings that indicate extremely cold coolant temperatures (typically -40°F or lower) when the actual coolant temperature should be much higher. This occurs when the sensor circuit has high resistance or is open, causing the ECM to default to minimum temperature readings. The ECT sensor is critical for fuel injection timing, ignition advance, cooling fan control, and transmission operation.',

  symptoms: [
    'Check engine light illuminated - ECT sensor circuit fault detected',
    'Engine running lean - ECM thinks coolant is extremely cold',
    'Cooling fans never turning on - ECM believes engine is cold',
    'Poor fuel economy - Lean fuel mixture from cold temperature reading',
    'Engine knocking or pinging - Advanced timing due to "cold" readings',
    'Hard starting when engine is warm - ECM provides cold start enrichment',
    'Engine overheating - Cooling fans not activated due to false cold readings',
    'Transmission not shifting properly - Incorrect temperature data affects shift points',
    'Temperature gauge reading incorrectly low - If connected to same sensor'
  ],

  causes: [
    'Open circuit in ECT sensor wiring - Broken wire causing high resistance',
    'Faulty ECT sensor - Internal component failure causing open circuit',
    'Corroded or loose ECT sensor connector - Poor electrical contact',
    'ECT sensor contamination - Non-conductive debris blocking electrical contact',
    'Damaged ECT sensor threads - Poor thermal or electrical contact',
    'ECM internal fault - Module not receiving sensor signal properly',
    'Wiring harness damage - Cut or damaged wires in sensor circuit',
    'Incorrect ECT sensor - Wrong resistance range for vehicle application'
  ],
  
  performanceImpact: 'P0118 causes the ECM to receive incorrect coolant temperature data showing extremely cold readings, leading to lean fuel mixture, advanced ignition timing, engine knock, potential overheating due to non-functioning cooling fans, and poor transmission shift quality.',

  quickAnswer: {
    icon: 'snowflake-o',
    meaning: 'ECT sensor reading extremely cold temperatures (-40°F) when coolant is actually warm - usually open circuit or sensor failure.',
    fix: 'Replace ECT sensor or repair open circuit',
    cost: '$90-$380',
    time: '45-75 minutes',
    drivingSafety: 'Risky to drive - engine may overheat due to non-functioning cooling fans. Repair immediately to prevent engine damage.'
  },

  aiQuestions: [
    {
      question: 'What\'s the difference between P0118 and P0117 ECT codes?',
      answer: 'P0118 indicates the ECT sensor is reading too cold (high input voltage/open circuit), while P0117 indicates too hot readings (low input voltage/short circuit). P0118 is more dangerous because cooling fans won\'t turn on, potentially causing overheating.'
    },
    {
      question: 'Why is P0118 more serious than other ECT codes?',
      answer: 'P0118 prevents cooling fans from operating because the ECM thinks the engine is cold. This can lead to actual engine overheating, especially in stop-and-go traffic or hot weather. The lean fuel mixture and advanced timing can also cause engine knock and damage.'
    },
    {
      question: 'Can P0118 cause engine overheating?',
      answer: 'Yes, P0118 is one of the few diagnostic codes that can directly cause engine overheating. When the ECM reads -40°F from the ECT sensor, it never turns on the cooling fans, even when the engine reaches dangerous temperatures. This makes P0118 a high-priority repair.'
    },
    {
      question: 'How do I test for P0118 open circuit?',
      answer: 'Disconnect the ECT sensor and measure resistance - if it reads infinite ohms (OL on multimeter), the sensor has failed open. Check wiring continuity from sensor to ECM. GeekOBD APP will show -40°F reading when circuit is open, confirming the diagnosis.'
    }
  ],
  
  costAnalysis: {
    averageCost: '$90-$380 for most P0118 repairs',
    repairOptions: [
      {
        title: 'ECT Sensor Replacement',
        description: 'Most common fix - Replace failed open sensor (75% of cases)',
        color: '#4CAF50',
        icon: 'thermometer-empty',
        items: [
          { name: 'ECT Sensor', cost: '$40-$110' },
          { name: 'Coolant (if drained)', cost: '$15-$35' },
          { name: 'Labor (0.5-1.5 hours)', cost: '$60-$180' }
        ],
        total: '$115-$325',
        successRate: '95% success rate'
      },
      {
        title: 'Wiring Harness Repair',
        description: 'Fix open circuit in sensor wiring (20% of cases)',
        color: '#FF9800',
        icon: 'plug',
        items: [
          { name: 'Wire repair/replacement', cost: '$25-$60' },
          { name: 'Diagnostic time', cost: '$100-$180' },
          { name: 'Labor (1.5-3 hours)', cost: '$150-$360' }
        ],
        total: '$275-$600',
        successRate: '90% success rate'
      },
      {
        title: 'Connector Replacement',
        description: 'Replace corroded or damaged connector (5% of cases)',
        color: '#2196F3',
        icon: 'wrench',
        items: [
          { name: 'ECT connector kit', cost: '$35-$85' },
          { name: 'Splice materials', cost: '$15-$25' },
          { name: 'Labor (1-2 hours)', cost: '$100-$240' }
        ],
        total: '$150-$350',
        successRate: '85% success rate'
      }
    ],
    savingTips: [
      'P0118 is urgent - don\'t delay repair as overheating can cause expensive engine damage',
      'Test sensor resistance first - infinite resistance confirms open circuit failure',
      'Check connector before replacing sensor - 15% of cases are connector issues',
      'ECT sensors are usually accessible - DIY replacement can save $60-180 in labor',
      'Monitor coolant temperature closely until repair - watch for overheating signs'
    ]
  },
  
  diagnosticSteps: {
    estimatedTime: 'PT60M',
    steps: [
      {
        title: 'Verify P0118 Code and Check Cooling System',
        icon: 'exclamation-triangle',
        description: 'Connect scan tool and confirm P0118 is present. Check current ECT reading - should show -40°F even when engine is warm. IMMEDIATELY check if cooling fans are working and monitor engine temperature to prevent overheating.',
        geekobdTip: 'Use GeekOBD APP to monitor live ECT sensor data. Reading of -40°F when engine is warm confirms P0118 fault. Watch coolant temperature closely!'
      },
      {
        title: 'Visual Inspection and Safety Check',
        icon: 'eye',
        description: 'Locate ECT sensor and inspect for physical damage, loose connections, or coolant leaks. Check that cooling fans can be manually activated. Ensure engine is not overheating before proceeding.',
        geekobdTip: 'GeekOBD APP can command cooling fan operation on many vehicles - use this to verify fans work while diagnosing ECT sensor.'
      },
      {
        title: 'Test ECT Sensor Resistance',
        icon: 'thermometer-empty',
        description: 'Disconnect sensor and measure resistance across terminals. If multimeter reads "OL" (infinite resistance), sensor has failed open. Normal resistance at operating temperature should be 200-1000 ohms.',
        geekobdTip: 'Compare resistance readings with GeekOBD APP temperature specifications. Open circuit (infinite resistance) confirms P0118 diagnosis.'
      },
      {
        title: 'Check Wiring Continuity',
        icon: 'plug',
        description: 'Test continuity from ECT sensor connector to ECM. Check for broken wires, corroded connections, or damaged harness. Pay special attention to areas near heat sources.',
        geekobdTip: 'Use GeekOBD APP to monitor sensor voltage while checking connections - should see voltage change when circuit is completed.'
      },
      {
        title: 'Verify Repair and Monitor Temperature',
        icon: 'check-circle',
        description: 'After replacing sensor or repairing wiring, clear codes and monitor ECT readings. Verify cooling fans activate when temperature reaches normal operating range (195-220°F).',
        geekobdTip: 'GeekOBD APP provides continuous monitoring - ECT should read actual coolant temperature and cooling fans should activate automatically.'
      }
    ],
    importantNotes: [
      'P0118 can cause engine overheating - monitor temperature closely during diagnosis',
      'Cooling fans may not work with P0118 - be prepared to shut off engine if overheating occurs',
      'Open circuit diagnosis is usually straightforward - infinite resistance confirms sensor failure'
    ]
  },
  
  caseStudies: [
    {
      title: 'Ford Explorer Overheating Emergency',
      vehicle: '2018 Ford Explorer 3.5L V6, 72,000 miles',
      problem: 'Customer called roadside assistance for overheating. Engine temperature gauge was pegged at maximum, but cooling fans never turned on. Check engine light was on with P0118 code stored.',
      diagnosis: 'GeekOBD APP showed ECT reading constant -40°F even with engine at dangerous temperatures. Resistance test of ECT sensor showed infinite resistance (open circuit). Sensor had failed internally, preventing cooling fan activation.',
      solution: 'Immediately shut off engine to prevent damage. Replaced ECT sensor located in cylinder head. Sensor required partial coolant drain and careful installation to avoid cross-threading.',
      cost: 'ECT sensor: $78, Coolant: $28, Emergency service: $150, Labor: $120, Total: $376',
      result: 'P0118 code cleared immediately. ECT now reads correctly and cooling fans activate at 210°F as designed. No engine damage occurred due to quick diagnosis and repair.'
    },
    {
      title: 'Nissan Altima Wiring Harness Failure',
      vehicle: '2017 Nissan Altima 2.5L 4-cylinder, 95,000 miles',
      problem: 'Intermittent P0118 code with occasional overheating in traffic. Customer noticed cooling fans would sometimes not turn on, and engine would knock during acceleration.',
      diagnosis: 'ECT sensor tested normal resistance, but GeekOBD APP showed intermittent -40°F readings. Found broken wire in ECT harness near alternator where vibration had caused wire fatigue and eventual open circuit.',
      solution: 'Repaired broken wire in ECT sensor harness. Cut out damaged section and spliced in new automotive-grade wire with proper insulation and strain relief.',
      cost: 'Wire repair kit: $45, Diagnostic time: $160, Labor: $180, Total: $385',
      result: 'P0118 code has not returned after 8 months. ECT readings remain stable and cooling fans operate normally. Customer reports no more overheating or engine knock.'
    }
  ],

  relatedCodes: [
    { code: 'P0117', description: 'ECT Sensor Low Input - Opposite condition (too hot readings)', color: '#e74c3c' },
    { code: 'P0125', description: 'Insufficient Coolant Temperature - Related coolant temperature issue', color: '#f39c12' },
    { code: 'P0128', description: 'Coolant Thermostat Rationality - Related cooling system problem', color: '#3498db' },
    { code: 'P0115', description: 'ECT Sensor Circuit Malfunction - General ECT circuit problem', color: '#4a90e2' },
    { code: 'P0171', description: 'System Too Lean Bank 1 - Can be caused by incorrect ECT readings', color: '#9b59b6' },
    { code: 'P0174', description: 'System Too Lean Bank 2 - Can be caused by incorrect ECT readings', color: '#9b59b6' },
    { code: 'P0325', description: 'Knock Sensor Circuit - Engine knock from advanced timing due to P0118', color: '#e67e22' }
  ],
  
  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P0118',
      description: 'Use GeekOBD APP for critical ECT sensor diagnosis!',
      features: [
        'Emergency temperature monitoring',
        'Cooling fan control testing',
        'Open circuit detection',
        'Overheating prevention alerts'
      ]
    },
    systemCodes: {
      title: 'ECT Sensor Codes',
      description: 'Related Engine Coolant Temperature sensor codes:'
    },
    diagnosticResources: [
      {
        title: 'Emergency P0118 Guide',
        description: 'Critical procedures to prevent engine overheating',
        icon: 'exclamation-triangle',
        url: '#diagnostic-steps'
      },
      {
        title: 'Open Circuit Testing',
        description: 'Professional procedures for testing open circuits',
        icon: 'plug',
        url: '../resources/open-circuit-testing.html'
      },
      {
        title: 'Cooling System Safety',
        description: 'Overheating prevention and emergency procedures',
        icon: 'fire-extinguisher',
        url: '../resources/cooling-system-safety.html'
      },
      {
        title: 'ECT Specifications',
        description: 'Resistance values and temperature correlations',
        icon: 'line-chart',
        url: '../resources/ect-specifications.html'
      }
    ],
    codeInfo: {
      system: 'Engine Management',
      severity: 'HIGH',
      category: 'Engine Coolant Temperature'
    }
  }
});

module.exports = p0118Data;