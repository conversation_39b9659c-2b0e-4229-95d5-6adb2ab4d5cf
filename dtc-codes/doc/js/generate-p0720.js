const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0720Data = require('./p0720-data');

console.log('🚀 Generating P0720 - Output Speed Sensor Circuit Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0720Data);
  const outputPath = path.join(__dirname, '../../p0720.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0720 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0720 page:', error.message);
  process.exit(1);
}
