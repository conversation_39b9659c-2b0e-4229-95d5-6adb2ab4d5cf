const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0500Data = require('./p0500-data');

console.log('🚀 Generating P0500 - Vehicle Speed Sensor Malfunction Page\n');

try {
  const generator = new DTCTemplateGenerator();
  const htmlContent = generator.generatePage(p0500Data);
  const outputPath = path.join(__dirname, '../../p0500.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0500 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
} catch (error) {
  console.error('❌ Error generating P0500 page:', error.message);
  process.exit(1);
}
