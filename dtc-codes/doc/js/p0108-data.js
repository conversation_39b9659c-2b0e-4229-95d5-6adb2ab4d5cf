const { DTCData } = require('./dtc-template-generator');

// P0108 MAP Sensor High Input 的完整数据结构
const p0108Data = new DTCData({
  code: 'P0108',
  title: 'MAP Sensor High Input',
  description: 'The Engine Control Module has detected that the Manifold Absolute Pressure sensor is reading abnormally high pressure values.',
  definition: 'The Engine Control Module has detected that the Manifold Absolute Pressure (MAP) sensor is reading abnormally high pressure values, typically above 95 kPa (28 inHg) when lower readings are expected. This indicates the sensor is either reading low vacuum conditions or has an electrical fault causing high voltage output. The MAP sensor measures intake manifold pressure to calculate engine load for proper fuel injection timing and duration.',
  
  symptoms: [
    'Check engine light illuminated - ECM detected MAP sensor high input fault',
    'Engine running lean - ECM thinks engine is at idle/low load due to high pressure reading',
    'Poor acceleration - Insufficient fuel delivery based on incorrect load calculation',
    'Engine hesitation or stumbling - Lean mixture causes poor combustion',
    'Engine knocking or pinging - Lean mixture burns too quickly causing detonation',
    'Hard starting - Insufficient fuel for startup based on false readings',
    'Rough idle or stalling - Lean mixture causes unstable combustion',
    'Poor fuel economy - Engine struggling with insufficient fuel',
    'Engine backfiring - Lean mixture igniting in intake manifold'
  ],
  
  causes: [
    'Faulty MAP sensor - Internal sensor failure reading constant high pressure',
    'Open circuit in MAP sensor wiring - Broken wire causing high voltage reading',
    'Damaged MAP sensor connector - Poor connection causing intermittent high readings',
    'MAP sensor vacuum line disconnected - Sensor reading atmospheric pressure',
    'Blocked intake manifold - Restriction causing actual high pressure readings',
    'ECM internal fault - Control module misreading MAP sensor signals',
    'Incorrect MAP sensor - Wrong sensor type reading different pressure range',
    'Turbocharger problems - Boost pressure affecting MAP sensor readings'
  ],
  
  performanceImpact: 'P0108 causes the ECM to deliver insufficient fuel based on false low-load readings, resulting in lean combustion, poor performance, potential engine knock, and possible engine damage from detonation.',
  
  quickAnswer: {
    icon: 'arrow-up',
    meaning: 'MAP sensor reading abnormally high pressure - causes lean fuel mixture and poor performance.',
    fix: 'Replace MAP sensor, check vacuum line connection, inspect wiring',
    cost: '$85-$320',
    time: '45-90 minutes',
    drivingSafety: 'Safe to drive short distances but avoid heavy acceleration. Repair soon to prevent engine damage from lean combustion.'
  },
  
  aiQuestions: [
    {
      question: 'What\'s the difference between P0108 and P0107 MAP codes?',
      answer: 'P0108 indicates the MAP sensor is reading too high pressure (low vacuum), while P0107 indicates too low pressure (high vacuum). P0108 typically causes lean fuel mixture because the ECM thinks the engine is at idle, while P0107 causes rich mixture.'
    },
    {
      question: 'Can a disconnected vacuum line cause P0108?',
      answer: 'Yes, if the MAP sensor vacuum line is disconnected, the sensor will read atmospheric pressure (around 100 kPa) instead of manifold vacuum (20-30 kPa at idle). This high reading triggers P0108 and causes the ECM to reduce fuel delivery, creating a lean condition.'
    },
    {
      question: 'Why does P0108 cause engine knock?',
      answer: 'When the MAP sensor reads high pressure, the ECM reduces fuel delivery thinking the engine is at low load. This creates a lean air/fuel mixture that burns faster and hotter, causing combustion pressure to peak too early and create the knocking sound that can damage the engine.'
    },
    {
      question: 'How do I test MAP sensor voltage for P0108?',
      answer: 'Use GeekOBD APP to monitor MAP sensor voltage - should vary from 1-4.5V based on pressure. With P0108, you\'ll typically see voltage stuck near 4.5V or higher. At idle, expect 1-2V; at wide open throttle, expect 4-4.5V. Constant high voltage confirms P0108 diagnosis.'
    }
  ],

  costAnalysis: {
    averageCost: '$85-$320 for most P0108 repairs',
    repairOptions: [
      {
        title: 'MAP Sensor Replacement',
        description: 'Most common fix - Replace failed MAP sensor (75% of cases)',
        color: '#4CAF50',
        icon: 'arrow-up',
        items: [
          { name: 'MAP sensor', cost: '$35-$85' },
          { name: 'Labor (45-75 minutes)', cost: '$60-$150' }
        ],
        total: '$95-$235',
        successRate: '95% success rate'
      },
      {
        title: 'Vacuum Line Repair',
        description: 'Reconnect or replace MAP sensor vacuum line (15% of cases)',
        color: '#2196F3',
        icon: 'road',
        items: [
          { name: 'Vacuum hose', cost: '$8-$20' },
          { name: 'Fittings', cost: '$5-$15' },
          { name: 'Labor (30-45 minutes)', cost: '$50-$90' }
        ],
        total: '$63-$125',
        successRate: '98% success rate'
      },
      {
        title: 'Wiring Repair',
        description: 'Fix open circuit in MAP sensor wiring (10% of cases)',
        color: '#FF9800',
        icon: 'plug',
        items: [
          { name: 'Wiring repair materials', cost: '$15-$40' },
          { name: 'Diagnostic time', cost: '$80-$120' },
          { name: 'Labor (1-2 hours)', cost: '$100-$240' }
        ],
        total: '$195-$400',
        successRate: '90% success rate'
      }
    ],
    savingTips: [
      'Check vacuum line connection first - 15% of P0108 cases are just loose hoses',
      'Verify MAP sensor voltage before replacement - constant high voltage confirms failure',
      'MAP sensor replacement is DIY-friendly, saving $60-150 in labor costs',
      'Use GeekOBD APP to verify repair - readings should respond to throttle changes',
      'Address P0108 quickly to prevent engine knock damage from lean mixture'
    ]
  },

  diagnosticSteps: {
    estimatedTime: 'PT45M',
    steps: [
      {
        title: 'Monitor MAP Sensor Voltage',
        icon: 'bolt',
        description: 'Connect GeekOBD APP and monitor MAP sensor voltage. With P0108, expect constant high voltage (near 4.5V) regardless of throttle position. Normal operation shows 1-2V at idle, 4-4.5V at wide open throttle.',
        geekobdTip: 'GeekOBD APP can graph MAP voltage over time - P0108 typically shows flat line at high voltage instead of normal pressure variations.'
      },
      {
        title: 'Check Vacuum Line Connection',
        icon: 'road',
        description: 'Inspect MAP sensor vacuum line connection to intake manifold. A disconnected or cracked line will cause sensor to read atmospheric pressure, triggering P0108.',
        geekobdTip: 'With GeekOBD APP monitoring, reconnecting vacuum line should immediately drop MAP voltage from 4.5V to 1-2V at idle.'
      },
      {
        title: 'Visual Inspection',
        icon: 'eye',
        description: 'Inspect MAP sensor, wiring harness, and connector for damage. Look for corroded pins, damaged wires, or signs of contamination on the sensor.',
        geekobdTip: 'Use GeekOBD APP to monitor voltage while wiggling wires - if readings change, you\'ve found intermittent wiring problems.'
      },
      {
        title: 'Electrical Testing',
        icon: 'plug',
        description: 'Test MAP sensor power supply (5V reference), ground circuit, and signal wire continuity. Check for open circuits that could cause high voltage readings.',
        geekobdTip: 'GeekOBD APP should show stable 5V reference voltage - if missing or fluctuating, check ECM power supply or wiring connections.'
      },
      {
        title: 'Component Replacement and Verification',
        icon: 'check-circle',
        description: 'Replace faulty MAP sensor, repair vacuum line, or fix wiring as diagnosed. Clear codes and road test while monitoring MAP sensor voltage for proper operation.',
        geekobdTip: 'GeekOBD APP should now show MAP voltage varying smoothly from 1-2V at idle to 4-4.5V at full throttle, confirming successful repair.'
      }
    ],
    importantNotes: [
      'Check vacuum line connection before replacing sensor - simple and common fix',
      'Constant high voltage reading is key diagnostic indicator for P0108',
      'Lean fuel mixture from P0108 can cause engine knock and damage'
    ]
  },

  caseStudies: [
    {
      title: 'Toyota Corolla Disconnected Vacuum Line',
      vehicle: '2017 Toyota Corolla 1.8L 4-cylinder, 68,000 miles',
      problem: 'Customer reported poor acceleration, engine hesitation, and occasional knocking sounds. P0108 code was present with lean fuel trim readings.',
      diagnosis: 'GeekOBD APP showed MAP sensor voltage constant at 4.6V regardless of throttle position. Visual inspection revealed MAP sensor vacuum line had disconnected from intake manifold during recent air filter service.',
      solution: 'Reconnected MAP sensor vacuum line to intake manifold and secured with proper clamp. No parts needed, just proper connection.',
      cost: 'Vacuum line clamp: $2, Labor: $30, Total: $32',
      result: 'P0108 code cleared immediately. MAP voltage now reads 1.8V at idle and varies properly with throttle. Engine performance and acceleration fully restored.'
    },
    {
      title: 'Honda Civic Failed MAP Sensor',
      vehicle: '2016 Honda Civic 1.5L Turbo, 89,000 miles',
      problem: 'Engine running lean with poor fuel economy, hesitation during acceleration, and P0108 code. Vacuum line was properly connected.',
      diagnosis: 'GeekOBD APP monitoring showed MAP voltage stuck at 4.8V even with vacuum applied directly to sensor. Electrical testing confirmed proper power and ground, indicating internal sensor failure.',
      solution: 'Replaced MAP sensor with OEM part. Sensor had failed internally, unable to respond to pressure changes despite proper electrical connections.',
      cost: 'MAP sensor: $72, Labor: $85, Total: $157',
      result: 'P0108 code cleared and has not returned. MAP sensor now responds correctly to pressure changes, fuel economy improved by 4 MPG.'
    }
  ],

  relatedCodes: [
    { code: 'P0106', description: 'MAP Sensor Range/Performance - General MAP sensor performance issues', color: '#4a90e2' },
    { code: 'P0107', description: 'MAP Sensor Low Input - Opposite condition (too low pressure)', color: '#3498db' },
    { code: 'P0105', description: 'MAP Sensor Circuit Malfunction - Electrical circuit problems', color: '#f39c12' },
    { code: 'P0171', description: 'System Too Lean Bank 1 - Often caused by P0108 MAP sensor issues', color: '#9b59b6' },
    { code: 'P0174', description: 'System Too Lean Bank 2 - Often caused by P0108 MAP sensor issues', color: '#9b59b6' },
    { code: 'P0325', description: 'Knock Sensor Circuit - Engine knock from lean mixture due to P0108', color: '#e67e22' },
    { code: 'P0300', description: 'Random Misfire - Lean mixture from P0108 can cause misfires', color: '#27ae60' }
  ],

  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P0108',
      description: 'Use GeekOBD APP for accurate MAP sensor voltage monitoring!',
      features: [
        'Real-time MAP voltage tracking',
        'Vacuum line connection testing',
        'Lean mixture detection',
        'Engine knock prevention alerts'
      ]
    },
    systemCodes: {
      title: 'MAP Sensor Codes',
      description: 'Related manifold pressure sensor codes:'
    },
    diagnosticResources: [
      {
        title: 'MAP High Voltage Testing',
        description: 'Professional procedures for diagnosing high MAP readings',
        icon: 'arrow-up',
        url: '#diagnostic-steps'
      },
      {
        title: 'Vacuum Line Inspection',
        description: 'Complete guide to checking MAP sensor vacuum connections',
        icon: 'road',
        url: '../resources/vacuum-line-inspection.html'
      },
      {
        title: 'Lean Mixture Diagnosis',
        description: 'Understanding and preventing lean fuel conditions',
        icon: 'tachometer',
        url: '../resources/lean-mixture-diagnosis.html'
      },
      {
        title: 'Engine Knock Prevention',
        description: 'Protecting your engine from detonation damage',
        icon: 'shield',
        url: '../resources/engine-knock-prevention.html'
      }
    ],
    codeInfo: {
      system: 'Engine Management',
      severity: 'MEDIUM',
      category: 'Sensor Input'
    }
  }
});

module.exports = p0108Data;
