const fs = require('fs');
const path = require('path');
const { DTCTemplateGenerator } = require('./dtc-template-generator');
const p0112Data = require('./p0112-data');

console.log('🚀 Generating P0112 - IAT Sensor Low Input Page\n');

try {
  // 创建模板生成器实例
  const generator = new DTCTemplateGenerator();
  
  // 生成HTML内容
  console.log('📝 Generating HTML content...');
  const htmlContent = generator.generatePage(p0112Data);
  
  // 保存到文件
  const outputPath = path.join(__dirname, '../../p0112.html');
  fs.writeFileSync(outputPath, htmlContent, 'utf8');
  
  console.log('✅ P0112 page generated successfully!');
  console.log(`📄 File saved: ${outputPath}`);
  
  // 统计信息
  console.log('\n📊 Content Statistics:');
  console.log(`   • Total file size: ${Math.round(htmlContent.length / 1024)}KB`);
  console.log(`   • Symptoms: ${p0112Data.symptoms.length} items`);
  console.log(`   • Causes: ${p0112Data.causes.length} items`);
  console.log(`   • AI Questions: ${p0112Data.aiQuestions.length} items`);
  console.log(`   • Repair Options: ${p0112Data.costAnalysis.repairOptions.length} options`);
  console.log(`   • Diagnostic Steps: ${p0112Data.diagnosticSteps.steps.length} steps`);
  console.log(`   • Case Studies: ${p0112Data.caseStudies.length} studies`);
  console.log(`   • Related Codes: ${p0112Data.relatedCodes.length} codes`);
  
  // 验证关键组件
  console.log('\n🔍 Component Verification:');
  console.log(`   ✅ Quick Answer: ${p0112Data.quickAnswer ? 'Present' : 'Missing'}`);
  console.log(`   ✅ Cost Analysis: ${p0112Data.costAnalysis ? 'Present' : 'Missing'}`);
  console.log(`   ✅ Diagnostic Steps: ${p0112Data.diagnosticSteps ? 'Present' : 'Missing'}`);
  console.log(`   ✅ Case Studies: ${p0112Data.caseStudies.length > 0 ? 'Present' : 'Missing'}`);
  console.log(`   ✅ Sidebar: ${p0112Data.sidebarData ? 'Present' : 'Missing'}`);
  
  console.log('\n🎉 P0112 optimization completed successfully!');
  console.log('\n💡 Key Features:');
  console.log('   • Detailed IAT sensor low input diagnosis');
  console.log('   • Comprehensive resistance testing procedures');
  console.log('   • Real-world Ford F-150 and Honda Civic case studies');
  console.log('   • Cost-effective repair strategies ($75-$280 range)');
  console.log('   • GeekOBD APP integration for live monitoring');
  console.log('   • Complete IAT sensor network linking');
  
  console.log('\n🔄 Next steps:');
  console.log('   1. Review generated p0112.html for accuracy');
  console.log('   2. Test page layout and functionality');
  console.log('   3. Continue with P0114 (IAT Sensor Intermittent)');
  console.log('   4. Build complete IAT sensor diagnostic series');
  
} catch (error) {
  console.error('❌ Error generating P0112 page:', error.message);
  console.error(error.stack);
  process.exit(1);
}
