const { DTCData } = require('./dtc-template-generator');

// P1151 Lack of Downstream Heated Oxygen Sensor Switch - Sensor Indicates Rich 的完整数据结构
const p1151Data = new DTCData({
  code: 'P1151',
  title: 'Lack of Downstream Heated Oxygen Sensor Switch - Sensor Indicates Rich',
  description: 'The downstream heated oxygen sensor is not switching properly and indicates a rich condition.',
  definition: 'P1151 indicates that the downstream heated oxygen sensor (HO2S) is not switching properly and is stuck indicating a rich condition. This is primarily a Ford-specific code. The downstream oxygen sensor, located after the catalytic converter, should show relatively stable readings with occasional switching. When the sensor remains at high voltage (rich indication) and doesn\'t respond properly to catalytic converter operation, P1151 is triggered.',
  
  symptoms: [
    'Check engine light illuminated - ECM detected downstream oxygen sensor problem',
    'Failed emissions test - Catalytic converter monitoring affected',
    'Poor fuel economy - ECM may compensate for perceived catalyst problems',
    'Engine hesitation - Incorrect feedback affecting fuel control',
    'Rough idle - Unstable fuel mixture from faulty sensor feedback',
    'Engine performance issues - Incorrect catalyst efficiency monitoring',
    'Catalytic converter damage - Poor monitoring leading to overheating',
    'White smoke from exhaust - Lean mixture from ECM overcompensation',
    'Rotten egg smell from exhaust - Catalytic converter problems'
  ],
  
  causes: [
    'Faulty downstream oxygen sensor - Sensor not responding properly to exhaust conditions',
    'Contaminated oxygen sensor - Fuel additives or oil affecting sensor readings',
    'Catalytic converter failure - Poor catalyst efficiency affecting sensor readings',
    'Fuel system problems - Rich mixture affecting catalyst and sensor operation',
    'Damaged oxygen sensor wiring - Electrical problems affecting sensor signal',
    'ECM internal fault - Control module not properly interpreting sensor signals',
    'Engine mechanical problems - Poor combustion affecting exhaust composition',
    'Exhaust system restrictions - Backpressure affecting sensor operation'
  ],
  
  performanceImpact: 'P1151 affects catalytic converter monitoring, emissions compliance, and may indicate catalyst or fuel system problems that can lead to expensive repairs if not addressed promptly.',
  
  quickAnswer: {
    icon: 'industry',
    meaning: 'Downstream oxygen sensor stuck reading rich - usually faulty sensor or catalyst problems.',
    fix: 'Test downstream O2 sensor, check catalytic converter efficiency, replace sensor if needed',
    cost: '$200-$1200',
    time: '90-240 minutes',
    drivingSafety: 'Safe to drive but may indicate catalyst problems. Diagnose promptly to prevent expensive catalyst damage.'
  },
  
  aiQuestions: [
    {
      question: 'What\'s the difference between P1150 and P1151?',
      answer: 'P1150 indicates the downstream oxygen sensor is stuck reading lean (low voltage), while P1151 indicates it\'s stuck reading rich (high voltage). Both indicate the sensor is not switching properly in response to catalytic converter operation.'
    },
    {
      question: 'Can fuel system problems cause P1151?',
      answer: 'Yes, fuel system problems that create rich conditions can cause P1151. If the engine runs rich, the catalytic converter may not process exhaust properly, causing the downstream sensor to read rich consistently.'
    },
    {
      question: 'Is the downstream sensor as important as the upstream sensor?',
      answer: 'Downstream sensors are primarily for emissions monitoring rather than fuel control. However, they\'re important for detecting catalytic converter problems and ensuring emissions compliance.'
    },
    {
      question: 'How can I tell if it\'s the sensor or catalytic converter?',
      answer: 'Use GeekOBD APP to monitor both sensors. If the upstream sensor switches normally but the downstream sensor doesn\'t respond appropriately to mixture changes, it may indicate catalyst problems affecting downstream sensor readings.'
    }
  ],

  costAnalysis: {
    averageCost: '$200-$1200 for most P1151 repairs',
    repairOptions: [
      {
        title: 'Downstream O2 Sensor Replacement',
        description: 'Replace faulty downstream oxygen sensor (60% of cases)',
        color: '#4CAF50',
        icon: 'industry',
        items: [
          { name: 'Downstream O2 sensor', cost: '$80-$180' },
          { name: 'Labor (1-2 hours)', cost: '$100-$240' }
        ],
        total: '$180-$420',
        successRate: '75% success rate'
      },
      {
        title: 'Fuel System Repair',
        description: 'Fix fuel system problems causing rich conditions (20% of cases)',
        color: '#FF9800',
        icon: 'gas-pump',
        items: [
          { name: 'Fuel system components', cost: '$100-$400' },
          { name: 'Labor (2-3 hours)', cost: '$200-$360' }
        ],
        total: '$300-$760',
        successRate: '90% success rate'
      },
      {
        title: 'Catalytic Converter Replacement',
        description: 'Replace failed catalytic converter (20% of cases)',
        color: '#9C27B0',
        icon: 'leaf',
        items: [
          { name: 'Catalytic converter', cost: '$400-$1200' },
          { name: 'Labor (2-4 hours)', cost: '$200-$480' }
        ],
        total: '$600-$1680',
        successRate: '98% success rate'
      }
    ],
    savingTips: [
      'Test downstream O2 sensor operation before replacing',
      'Use GeekOBD APP to monitor catalyst efficiency and fuel trims',
      'Check fuel system for rich conditions before expensive catalyst replacement',
      'Address P1151 promptly to prevent catalyst damage',
      'Monitor both upstream and downstream sensors for comparison'
    ]
  },

  diagnosticSteps: {
    estimatedTime: 'PT150M',
    steps: [
      {
        title: 'Monitor Downstream O2 Sensor',
        icon: 'search',
        description: 'Connect GeekOBD APP and monitor downstream oxygen sensor readings. Sensor should show more stable readings than upstream sensor.',
        geekobdTip: 'GeekOBD APP can show downstream O2 sensor voltage - stuck rich sensor will show constant high voltage around 0.8-0.9V.'
      },
      {
        title: 'Compare with Upstream Sensor',
        icon: 'balance-scale',
        description: 'Monitor both upstream and downstream sensors to evaluate catalytic converter efficiency and sensor operation.',
        geekobdTip: 'Use GeekOBD APP to compare upstream switching with downstream response - healthy catalyst shows different patterns between sensors.'
      },
      {
        title: 'Check Fuel System Operation',
        icon: 'gas-pump',
        description: 'Monitor fuel trims and test fuel system for problems that could cause rich conditions affecting catalyst and sensor.',
        geekobdTip: 'Monitor fuel trims with GeekOBD APP - negative trims may indicate rich conditions causing downstream sensor problems.'
      },
      {
        title: 'Test Catalytic Converter Efficiency',
        icon: 'industry',
        description: 'Evaluate catalytic converter operation by monitoring temperature and efficiency through sensor comparison.',
        geekobdTip: 'Use GeekOBD APP catalyst monitor if available - compare upstream and downstream sensor patterns to evaluate catalyst efficiency.'
      },
      {
        title: 'Component Replacement and Verification',
        icon: 'check-circle',
        description: 'Replace downstream oxygen sensor or repair identified problems. Clear codes and verify proper sensor operation.',
        geekobdTip: 'Use GeekOBD APP to verify new downstream O2 sensor responds appropriately to catalytic converter and fuel system operation.'
      }
    ],
    importantNotes: [
      'P1151 may indicate catalytic converter or fuel system problems',
      'Compare upstream and downstream sensor patterns for diagnosis',
      'Address rich fuel conditions that can damage catalytic converter'
    ]
  },

  caseStudies: [
    {
      title: 'Ford Mustang Rich Fuel Condition',
      vehicle: '2016 Ford Mustang 2.3L Turbo, 105,000 miles',
      problem: 'Customer reported P1151 code and poor fuel economy. Downstream oxygen sensor was reading consistently rich.',
      diagnosis: 'GeekOBD APP showed negative fuel trims indicating rich condition. Found faulty fuel pressure regulator causing high fuel pressure.',
      solution: 'Replaced fuel pressure regulator and cleaned fuel injectors. High fuel pressure was causing rich mixture affecting downstream sensor.',
      cost: 'Fuel pressure regulator: $95, Injector cleaning: $120, Labor: $160, Total: $375',
      result: 'P1151 code cleared after fuel system repair. Downstream O2 sensor now responds properly and fuel economy improved.'
    },
    {
      title: 'Ford Explorer Failed Catalytic Converter',
      vehicle: '2017 Ford Explorer 3.5L V6, 155,000 miles',
      problem: 'P1151 code with rotten egg smell from exhaust and poor performance. Downstream O2 sensor replacement did not fix the issue.',
      diagnosis: 'New downstream O2 sensor still showed rich readings. GeekOBD APP showed poor catalyst efficiency with minimal difference between upstream and downstream sensors.',
      solution: 'Replaced catalytic converter with OEM part. Original converter had failed and was not processing exhaust properly.',
      cost: 'Catalytic converter: $750, Labor: $280, Total: $1030',
      result: 'P1151 code cleared after catalyst replacement. Downstream O2 sensor now shows proper response and rotten egg smell eliminated.'
    }
  ],

  relatedCodes: [
    { code: 'P1150', description: 'Lack of Downstream HO2S Switch - Sensor Indicates Lean - Opposite condition', color: '#e74c3c' },
    { code: 'P0137', description: 'O2 Sensor Circuit Low Voltage Bank 1 Sensor 2 - Related downstream O2 sensor', color: '#3498db' },
    { code: 'P0138', description: 'O2 Sensor Circuit High Voltage Bank 1 Sensor 2 - Related downstream O2 sensor', color: '#f39c12' },
    { code: 'P0139', description: 'O2 Sensor Circuit Slow Response Bank 1 Sensor 2 - Related downstream O2 sensor', color: '#9b59b6' },
    { code: 'P0420', description: 'Catalyst System Efficiency Below Threshold Bank 1 - Related catalyst problem', color: '#4a90e2' },
    { code: 'P0172', description: 'System Too Rich Bank 1 - Related rich condition', color: '#e67e22' },
    { code: 'P0175', description: 'System Too Rich Bank 2 - Related rich condition', color: '#27ae60' }
  ],

  sidebarData: {
    appPromo: {
      icon: 'mobile',
      title: 'Diagnose P1151',
      description: 'Use GeekOBD APP for downstream O2 sensor testing!',
      features: [
        'Downstream O2 monitoring',
        'Catalyst efficiency testing',
        'Fuel trim analysis',
        'Rich condition detection'
      ]
    },
    systemCodes: {
      title: 'Downstream O2 Sensor Codes',
      description: 'Related downstream oxygen sensor and catalyst codes:'
    },
    diagnosticResources: [
      {
        title: 'Downstream O2 Testing',
        description: 'Professional procedures for testing downstream oxygen sensors',
        icon: 'industry',
        url: '#diagnostic-steps'
      },
      {
        title: 'Rich Condition Diagnosis',
        description: 'Diagnosing and fixing rich running conditions',
        icon: 'balance-scale',
        url: '../resources/rich-condition-diagnosis.html'
      },
      {
        title: 'Catalytic Converter Service',
        description: 'Catalytic converter diagnosis and replacement',
        icon: 'leaf',
        url: '../resources/catalytic-converter-service.html'
      },
      {
        title: 'Fuel System Diagnosis',
        description: 'Testing fuel pressure and delivery systems',
        icon: 'gas-pump',
        url: '../resources/fuel-system-diagnosis.html'
      }
    ],
    codeInfo: {
      system: 'Emissions Control',
      severity: 'MEDIUM',
      category: 'Oxygen Sensor'
    }
  }
});

module.exports = p1151Data;
