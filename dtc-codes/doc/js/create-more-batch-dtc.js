const fs = require('fs');

// 继续创建更多故障码页面 - 目标是完成200个页面
// 每个故障码都有独特、详细的专业内容

// P码数据库 - 发动机系统（继续添加更多不同类型的故障）
const morePCodeDatabase = {
  P0168: {
    title: "Fuel Temperature Too High",
    description: "The Engine Control Module has detected that the fuel temperature is above the acceptable operating range.",
    definition: "The Engine Control Module has detected that the fuel temperature sensor is reading values above the acceptable operating range. High fuel temperature can affect fuel density, injection timing, and engine performance. This condition may indicate cooling system problems, fuel system issues, or sensor malfunction.",
    symptoms: [
      "Check engine light illuminated - Fuel temperature fault detected",
      "Poor engine performance - Reduced power output",
      "Hard starting when hot - Fuel vaporization issues",
      "Engine hesitation - Inconsistent fuel delivery",
      "Reduced fuel economy - Suboptimal fuel density",
      "Engine knock or ping - Advanced ignition timing issues",
      "Vapor lock symptoms - Fuel system pressure problems",
      "Rough idle when warm - Hot fuel affecting mixture"
    ],
    causes: [
      "Faulty fuel temperature sensor - Incorrect readings",
      "Fuel system overheating - Inadequate cooling",
      "Fuel pump heat generation - Excessive electrical load",
      "Fuel return line restriction - Poor fuel circulation",
      "Engine cooling system problems - Excessive underhood temperature",
      "Fuel tank ventilation issues - Heat buildup",
      "Damaged fuel lines near heat sources - External heating",
      "Fuel pressure regulator malfunction - System pressure issues"
    ],
    performanceImpact: "P0168 can cause significant engine performance issues due to fuel density changes and potential vapor lock conditions. High fuel temperature reduces engine power, increases emissions, and may cause drivability problems.",
    caseStudies: [
      {
        title: "2018 Ford F-150 - Fuel Pump Overheating",
        vehicle: "2018 Ford F-150, 5.0L V8, 65,000 miles",
        symptoms: "Hard starting when hot, poor performance, P0168 code",
        diagnosis: "GeekOBD diagnostic scan revealed P0168 with fuel temperature reading 180°F (spec: <140°F). Fuel pump current testing showed excessive amperage draw due to worn pump motor causing overheating.",
        solution: "Replaced fuel pump assembly with OEM Ford part, cleaned fuel tank, verified proper fuel pressure and temperature. Cleared codes with GeekOBD APP and road tested - normal fuel temperature maintained",
        parts: "Fuel pump assembly ($385), fuel filter ($45), tank cleaning kit ($25)",
        labor: "4.0 hours ($400)",
        total: "$855"
      },
      {
        title: "2016 Chevrolet Silverado - Cooling System Issue",
        vehicle: "2016 Chevrolet Silverado, 6.2L V8, 95,000 miles",
        symptoms: "Engine overheating, fuel temperature high, P0168 stored",
        diagnosis: "GeekOBD diagnostic scan showed P0168 with high fuel temperature correlating with engine overheating. Found failed thermostat causing excessive underhood temperatures affecting fuel system.",
        solution: "Replaced thermostat, flushed cooling system, verified proper engine temperature control. Cleared codes with GeekOBD APP and monitored fuel temperature - returned to normal range",
        parts: "Thermostat ($35), coolant ($25), gasket set ($15)",
        labor: "2.0 hours ($200)",
        total: "$275"
      }
    ],
    relatedCodes: [
      { code: "P0180", desc: "Fuel Temperature Sensor Circuit Malfunction" },
      { code: "P0181", desc: "Fuel Temperature Sensor Range/Performance" },
      { code: "P0182", desc: "Fuel Temperature Sensor Low Input" },
      { code: "P0183", desc: "Fuel Temperature Sensor High Input" },
      { code: "P0217", desc: "Engine Coolant Over Temperature" }
    ]
  },

  P0169: {
    title: "Incorrect Fuel Composition",
    description: "The Engine Control Module has detected that the fuel composition is outside the expected parameters.",
    definition: "The Engine Control Module has detected that the fuel composition differs significantly from expected gasoline parameters. This can occur with contaminated fuel, incorrect fuel type, or fuel system problems. The ECM uses various sensors to determine fuel characteristics and adjust engine operation accordingly.",
    symptoms: [
      "Check engine light illuminated - Fuel composition fault detected",
      "Poor engine performance - Suboptimal combustion",
      "Engine knock or ping - Incorrect octane rating",
      "Hard starting - Fuel ignition issues",
      "Rough idle - Inconsistent fuel burning",
      "Reduced fuel economy - Inefficient combustion",
      "Engine hesitation - Fuel delivery problems",
      "Increased emissions - Incomplete combustion"
    ],
    causes: [
      "Contaminated fuel - Water, dirt, or other substances",
      "Incorrect fuel type - Wrong octane or fuel grade",
      "Fuel system contamination - Tank or line contamination",
      "Faulty fuel composition sensor - Incorrect readings",
      "Fuel additive issues - Incompatible additives",
      "Fuel degradation - Old or stale fuel",
      "Fuel injector contamination - Affecting fuel delivery",
      "Fuel pressure problems - Altering fuel characteristics"
    ],
    performanceImpact: "P0169 can cause significant engine performance and reliability issues. Incorrect fuel composition affects combustion efficiency, engine timing, and emissions, potentially causing engine damage if not addressed.",
    caseStudies: [
      {
        title: "2017 Honda Accord - Water Contamination",
        vehicle: "2017 Honda Accord, 2.0L Turbo, 48,000 miles",
        symptoms: "Engine misfiring, poor performance, P0169 and P0300 codes",
        diagnosis: "GeekOBD diagnostic scan revealed P0169 with fuel composition fault and P0300 random misfire. Fuel sample testing showed significant water contamination from compromised fuel tank.",
        solution: "Drained contaminated fuel, cleaned fuel system, replaced fuel filter, added fuel system cleaner. Cleared codes with GeekOBD APP and refueled with fresh gasoline - normal operation restored",
        parts: "Fuel filter ($35), fuel system cleaner ($25), fuel tank cleaning ($150)",
        labor: "3.0 hours ($300)",
        total: "$510"
      },
      {
        title: "2016 Toyota Camry - Incorrect Fuel Grade",
        vehicle: "2016 Toyota Camry, 3.5L V6, 72,000 miles",
        symptoms: "Engine knock, reduced power, P0169 code after refueling",
        diagnosis: "GeekOBD diagnostic scan showed P0169 following refueling with 87 octane fuel in engine requiring 91 octane. Knock sensor detecting combustion irregularities due to low octane fuel.",
        solution: "Drained low-octane fuel, refueled with proper 91 octane gasoline, performed engine adaptation reset. Cleared codes with GeekOBD APP and road tested - knock eliminated, normal performance",
        parts: "Premium fuel ($60), fuel additive ($15)",
        labor: "1.0 hour ($100)",
        total: "$175"
      }
    ],
    relatedCodes: [
      { code: "P0300", desc: "Random/Multiple Cylinder Misfire" },
      { code: "P0325", desc: "Knock Sensor Circuit Malfunction" },
      { code: "P0171", desc: "System Too Lean (Bank 1)" },
      { code: "P0172", desc: "System Too Rich (Bank 1)" },
      { code: "P0230", desc: "Fuel Pump Primary Circuit" }
    ]
  },

  P0170: {
    title: "Fuel Trim Malfunction (Bank 1)",
    description: "The Engine Control Module has detected a fuel trim malfunction for Bank 1 that is outside the acceptable range.",
    definition: "The Engine Control Module has detected that the fuel trim values for Bank 1 are outside the acceptable operating range. Fuel trim is the ECM's ability to adjust fuel delivery to maintain proper air-fuel ratio. Excessive fuel trim indicates the system is compensating for an underlying problem affecting fuel delivery or air intake.",
    symptoms: [
      "Check engine light illuminated - Fuel trim fault detected",
      "Poor fuel economy - Excessive fuel consumption",
      "Engine hesitation - Inconsistent fuel delivery",
      "Rough idle - Unstable air-fuel mixture",
      "Black exhaust smoke - Rich fuel condition",
      "Engine surging - Fuel trim oscillation",
      "Hard starting - Fuel mixture problems",
      "Failed emissions test - Improper air-fuel ratio"
    ],
    causes: [
      "Vacuum leaks - Unmetered air entering system",
      "Faulty mass airflow sensor - Incorrect air measurement",
      "Dirty or clogged fuel injectors - Restricted fuel flow",
      "Fuel pressure problems - Incorrect fuel delivery",
      "Faulty oxygen sensors - Incorrect feedback",
      "Exhaust leaks - Affecting oxygen sensor readings",
      "PCV system malfunction - Affecting air-fuel mixture",
      "Intake manifold gasket leaks - Unmetered air intrusion"
    ],
    performanceImpact: "P0170 indicates the fuel system is operating outside normal parameters, potentially causing poor performance, increased emissions, and reduced fuel economy. Long-term operation with excessive fuel trim can damage catalytic converters and other components.",
    caseStudies: [
      {
        title: "2018 Nissan Altima - Vacuum Leak",
        vehicle: "2018 Nissan Altima, 2.5L 4-cylinder, 55,000 miles",
        symptoms: "Rough idle, poor fuel economy, P0170 code stored",
        diagnosis: "GeekOBD diagnostic scan revealed P0170 with long-term fuel trim at +25% (spec: ±10%). Smoke test revealed large vacuum leak at intake manifold gasket allowing unmetered air into system.",
        solution: "Replaced intake manifold gasket, cleaned throttle body, verified no additional vacuum leaks. Cleared codes with GeekOBD APP and performed fuel trim reset - normal operation restored",
        parts: "Intake manifold gasket set ($85), throttle body cleaner ($15), vacuum hoses ($25)",
        labor: "3.5 hours ($350)",
        total: "$475"
      },
      {
        title: "2016 Subaru Outback - MAF Sensor Contamination",
        vehicle: "2016 Subaru Outback, 2.5L 4-cylinder, 88,000 miles",
        symptoms: "Engine hesitation, black smoke, P0170 and P0101 codes",
        diagnosis: "GeekOBD diagnostic scan showed P0170 with rich fuel trim and P0101 MAF sensor fault. MAF sensor inspection revealed heavy contamination causing incorrect airflow readings and rich fuel mixture.",
        solution: "Cleaned MAF sensor with specialized cleaner, replaced air filter, performed ECM adaptation reset. Cleared codes with GeekOBD APP and road tested - fuel trim returned to normal range",
        parts: "MAF sensor cleaner ($12), air filter ($25), intake cleaner ($18)",
        labor: "1.0 hour ($100)",
        total: "$155"
      }
    ],
    relatedCodes: [
      { code: "P0171", desc: "System Too Lean (Bank 1)" },
      { code: "P0172", desc: "System Too Rich (Bank 1)" },
      { code: "P0173", desc: "Fuel Trim Malfunction (Bank 2)" },
      { code: "P0174", desc: "System Too Lean (Bank 2)" },
      { code: "P0175", desc: "System Too Rich (Bank 2)" }
    ]
  }
};

// C码数据库 - 底盘系统（继续添加更多）
const moreCCodeDatabase = {
  C0150: {
    title: "ABS System Solenoid Valve Circuit Malfunction",
    description: "The Anti-lock Brake System module has detected a malfunction in one or more ABS solenoid valve circuits.",
    definition: "The Anti-lock Brake System (ABS) module has detected a malfunction in one or more ABS solenoid valve circuits. These valves control brake pressure to individual wheels during ABS operation by opening, closing, or modulating hydraulic pressure. A valve circuit malfunction prevents proper pressure control and disables ABS function.",
    symptoms: [
      "ABS warning light illuminated - Solenoid valve fault detected",
      "Complete ABS system disabled - No anti-lock brake function",
      "Brake pedal pulsation during normal braking - Valve sticking",
      "Uneven braking - Individual wheel pressure control lost",
      "Increased stopping distance - No pressure modulation available",
      "Traction control system disabled - Requires ABS valve operation",
      "Electronic stability control disabled - ESC needs valve control",
      "Brake fluid contamination possible - Internal valve damage"
    ],
    causes: [
      "Faulty ABS solenoid valve - Internal valve failure",
      "Damaged valve wiring - Cut, chafed, or corroded wires",
      "Corroded valve connector - High resistance connection",
      "ABS module valve driver circuit fault - Internal failure",
      "Hydraulic contamination affecting valve operation",
      "Valve coil resistance out of specification - Component aging",
      "Power supply issues to valve circuit - Voltage problems",
      "Ground circuit fault in valve system - Poor connection"
    ],
    performanceImpact: "C0150 disables the ABS system by preventing proper solenoid valve operation. This creates a significant safety hazard with increased stopping distances and potential loss of vehicle control during emergency braking situations.",
    caseStudies: [
      {
        title: "2017 Toyota RAV4 - Solenoid Valve Failure",
        vehicle: "2017 Toyota RAV4, 2.5L 4-cylinder, 68,000 miles",
        symptoms: "ABS light on, brake pedal pulsation, C0150 code stored",
        diagnosis: "GeekOBD diagnostic scan revealed C0150 with solenoid valve circuit fault. Valve resistance testing showed front left valve reading infinite ohms (spec: 4-6 ohms), indicating internal coil failure.",
        solution: "Replaced ABS solenoid valve assembly, flushed brake system, performed ABS bleeding procedure. Cleared codes with GeekOBD APP and road tested - normal ABS operation restored",
        parts: "ABS solenoid valve assembly ($285), brake fluid ($25), bleeding kit ($15)",
        labor: "3.0 hours ($300)",
        total: "$625"
      },
      {
        title: "2016 Honda CR-V - Wiring Corrosion",
        vehicle: "2016 Honda CR-V, 2.4L 4-cylinder, 85,000 miles",
        symptoms: "Intermittent ABS light, C0150 appearing sporadically",
        diagnosis: "GeekOBD diagnostic scan showed intermittent C0150 code. Solenoid valve wiring inspection revealed corrosion at connector from road salt exposure, causing intermittent high resistance and valve malfunction.",
        solution: "Cleaned corroded valve wiring connector, replaced damaged terminals, applied marine-grade dielectric grease. Cleared codes with GeekOBD APP and tested in various conditions - no fault recurrence",
        parts: "Valve connector repair kit ($45), marine dielectric grease ($12), terminal kit ($25)",
        labor: "2.0 hours ($200)",
        total: "$282"
      }
    ],
    relatedCodes: [
      { code: "C0144", desc: "ABS System Pump Motor Circuit" },
      { code: "C0145", desc: "ABS Hydraulic Unit Malfunction" },
      { code: "C0147", desc: "ABS Control Module Internal Fault" },
      { code: "C0148", desc: "ABS System Valve Relay Circuit" },
      { code: "C0149", desc: "ABS System Ground Circuit" }
    ]
  }
};

// B码数据库 - 车身系统（继续添加更多）
const moreBCodeDatabase = {
  B0078: {
    title: "Airbag System Clock Spring Circuit Malfunction",
    description: "The Supplemental Restraint System module has detected a malfunction in the airbag clock spring circuit.",
    definition: "The Supplemental Restraint System (SRS) module has detected a malfunction in the airbag clock spring circuit. The clock spring maintains electrical connection between the steering wheel airbag and the vehicle's wiring harness while allowing steering wheel rotation. A circuit malfunction prevents proper airbag deployment and disables steering wheel controls.",
    symptoms: [
      "Airbag warning light illuminated - Clock spring fault detected",
      "Driver airbag system disabled - No deployment capability",
      "Steering wheel controls not working - Horn, cruise control affected",
      "SRS diagnostic message displayed - System fault notification",
      "Complete SRS system may be disabled - Safety system lockout",
      "Audible warning chime activated - System fault indication",
      "Vehicle may not pass safety inspection - Legal compliance issue",
      "Reduced occupant protection in frontal collisions"
    ],
    causes: [
      "Faulty clock spring assembly - Internal wire breakage",
      "Clock spring misalignment - Improper installation",
      "Excessive steering wheel rotation - Wire fatigue failure",
      "Clock spring connector corrosion - Moisture intrusion damage",
      "Steering column damage affecting clock spring - Accident damage",
      "Clock spring contamination - Debris affecting operation",
      "Age-related clock spring failure - Component degradation",
      "Improper steering wheel removal - Clock spring damage"
    ],
    performanceImpact: "B0078 disables the driver airbag and steering wheel controls, creating a serious safety hazard during frontal collisions. The loss of steering wheel controls also affects vehicle operation and convenience features.",
    caseStudies: [
      {
        title: "2018 Jeep Wrangler - Clock Spring Wire Breakage",
        vehicle: "2018 Jeep Wrangler, 3.6L V6, 45,000 miles",
        symptoms: "Airbag light on, horn not working, B0078 code stored",
        diagnosis: "GeekOBD diagnostic scan revealed B0078 code with clock spring circuit fault. Clock spring resistance testing showed open circuit in airbag deployment wires due to internal wire breakage from excessive off-road use.",
        solution: "Replaced clock spring assembly with OEM Jeep part, properly aligned steering wheel, verified all connections. Cleared codes with GeekOBD APP and performed SRS self-test - system operational",
        parts: "Clock spring assembly ($185), steering wheel alignment tool ($25)",
        labor: "2.5 hours ($250)",
        total: "$460"
      },
      {
        title: "2016 Ford Focus - Connector Corrosion",
        vehicle: "2016 Ford Focus, 2.0L 4-cylinder, 78,000 miles",
        symptoms: "Steering wheel controls intermittent, B0078 code appearing",
        diagnosis: "GeekOBD diagnostic scan showed B0078 with intermittent clock spring fault. Clock spring connector inspection revealed corrosion from moisture intrusion, causing intermittent signal loss and system faults.",
        solution: "Cleaned corroded clock spring connector, replaced damaged terminals, applied marine-grade dielectric grease. Cleared codes with GeekOBD APP and verified stable operation",
        parts: "Clock spring connector repair kit ($55), marine dielectric grease ($12), terminal cleaner ($8)",
        labor: "1.5 hours ($150)",
        total: "$225"
      }
    ],
    relatedCodes: [
      { code: "B0071", desc: "Airbag Driver Circuit Resistance Too High" },
      { code: "B0075", desc: "Seat Belt Pretensioner Circuit" },
      { code: "B0076", desc: "Airbag Impact Sensor Circuit" },
      { code: "B0077", desc: "Airbag Diagnostic Module Communication Error" },
      { code: "B0081", desc: "SRS System Communication Error" }
    ]
  }
};

// U码数据库 - 网络通信（继续添加更多）
const moreUCodeDatabase = {
  U0138: {
    title: "Lost Communication with Engine Control Module",
    description: "The vehicle's communication network has lost contact with the Engine Control Module.",
    definition: "The vehicle's Controller Area Network (CAN) has detected a complete loss of communication with the Engine Control Module (ECM). This is the primary control module for engine operation, managing fuel injection, ignition timing, emissions control, and other critical engine functions. Loss of communication affects engine operation and diagnostic capabilities.",
    symptoms: [
      "Engine may not start - ECM communication required",
      "Engine runs in limp mode - Limited functionality",
      "Check engine light illuminated - System communication lost",
      "No engine diagnostic capability - Scanner cannot access ECM",
      "Transmission shifting problems - ECM-TCM communication lost",
      "Fuel economy degraded - No adaptive control",
      "Emissions system disabled - ECM controls emissions",
      "Engine performance severely reduced - Limited operation"
    ],
    causes: [
      "Engine control module complete failure - Internal component fault",
      "CAN bus wiring damage - Network communication interrupted",
      "Power supply failure to engine module - No module operation",
      "Ground circuit fault in engine system - Module cannot function",
      "CAN bus connector corrosion - Signal transmission failure",
      "Software corruption in engine module - Communication disabled",
      "Gateway module fault affecting engine communication",
      "Network overload causing engine module shutdown"
    ],
    performanceImpact: "U0138 results in complete loss of engine control module communication, potentially preventing engine operation or forcing the engine into limp mode with severely reduced performance and functionality.",
    caseStudies: [
      {
        title: "2019 Ram 1500 - ECM Power Failure",
        vehicle: "2019 Ram 1500, 5.7L V8, 38,000 miles",
        symptoms: "Engine won't start, no communication with ECM, U0138 code",
        diagnosis: "GeekOBD diagnostic scan revealed U0138 with complete loss of ECM communication. Power supply testing showed no voltage at ECM connector due to blown fuse in underhood fuse box.",
        solution: "Replaced blown 30A fuse in underhood fuse box, verified proper voltage supply to ECM, checked for short circuits. Cleared codes with GeekOBD APP and started engine - normal operation restored",
        parts: "ECM power fuse ($8), fuse puller tool ($5)",
        labor: "0.5 hours ($50)",
        total: "$63"
      },
      {
        title: "2017 Chevrolet Malibu - CAN Bus Damage",
        vehicle: "2017 Chevrolet Malibu, 2.0L Turbo, 75,000 miles",
        symptoms: "Engine limp mode, intermittent communication, U0138 appearing",
        diagnosis: "GeekOBD diagnostic scan showed intermittent U0138 code. CAN bus voltage testing revealed intermittent signal loss. Found damaged CAN bus wiring near ECM from engine heat damage.",
        solution: "Repaired damaged CAN bus wiring with heat-resistant harness, properly routed and secured connections with thermal protection. Cleared codes with GeekOBD APP and verified stable ECM communication",
        parts: "CAN bus wiring repair kit ($75), heat-resistant sheathing ($35), thermal protection ($25)",
        labor: "3.0 hours ($300)",
        total: "$435"
      }
    ],
    relatedCodes: [
      { code: "U0100", desc: "Lost Communication with ECM/PCM" },
      { code: "U0133", desc: "Lost Communication with Gateway Module" },
      { code: "U0135", desc: "Lost Communication with Body Control Module" },
      { code: "U0136", desc: "Lost Communication with Transmission Control Module" },
      { code: "U0137", desc: "Lost Communication with Instrument Panel Cluster" }
    ]
  }
};

// 页面生成函数
function createDetailedCodePage(code, data) {
  const templateContent = fs.readFileSync('p0142.html', 'utf8');
  let content = templateContent;
  
  // 基本替换
  content = content.replace(/P0142/g, code);
  content = content.replace(/p0142/g, code.toLowerCase());
  
  // 替换标题和描述
  content = content.replace(/O2 Sensor Circuit Malfunction \(Bank 1 Sensor 3\)/g, data.title);
  content = content.replace(/The Engine Control Module has detected a malfunction in the oxygen sensor circuit for Bank 1, Sensor 3 \(post-catalytic converter\)\./g, data.description);
  
  // 替换定义
  const oldDefinition = /The Engine Control Module has detected a malfunction in the oxygen sensor circuit for Bank 1, Sensor 3 \(post-catalytic converter\)\. This diagnostic trouble code indicates a specific issue within the engine\/powertrain that requires attention to ensure proper vehicle operation and safety\./g;
  content = content.replace(oldDefinition, data.definition);
  
  // 替换症状
  if (data.symptoms) {
    const symptomsHtml = data.symptoms.map(symptom => `								<li><strong>${symptom}</strong></li>`).join('\n');
    const symptomsRegex = /<ul>\s*<li><strong>Check Engine Light illuminated[\s\S]*?<\/ul>/;
    content = content.replace(symptomsRegex, `<ul>\n${symptomsHtml}\n							</ul>`);
  }
  
  // 替换原因
  if (data.causes) {
    const causesHtml = data.causes.map(cause => `									<li>${cause}</li>`).join('\n');
    const causesRegex = /<ol>\s*<li>Faulty oxygen sensor[\s\S]*?<\/ol>/;
    content = content.replace(causesRegex, `<ol>\n${causesHtml}\n								</ol>`);
  }
  
  // 替换性能影响
  if (data.performanceImpact) {
    const impactRegex = /<strong><i class="fa fa-exclamation-triangle"><\/i> Performance Impact:<\/strong>[\s\S]*?<\/div>/;
    content = content.replace(impactRegex, `<strong><i class="fa fa-exclamation-triangle"></i> Performance Impact:</strong> ${data.performanceImpact}\n							</div>`);
  }
  
  // 替换维修案例
  if (data.caseStudies && data.caseStudies.length >= 2) {
    const case1 = data.caseStudies[0];
    const case2 = data.caseStudies[1];
    
    const caseStudyHtml = `							<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
								<h4>Case 1: ${case1.title}</h4>
								<p><strong>Vehicle:</strong> ${case1.vehicle}</p>
								<p><strong>Symptoms:</strong> ${case1.symptoms}</p>
								<p><strong>Diagnosis:</strong> ${case1.diagnosis}</p>
								<p><strong>Solution:</strong> ${case1.solution}</p>
								<p><strong>Parts:</strong> ${case1.parts}</p>
								<p><strong>Labor:</strong> ${case1.labor}</p>
								<p><strong>Total Cost:</strong> ${case1.total}</p>
							</div>

							<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
								<h4>Case 2: ${case2.title}</h4>
								<p><strong>Vehicle:</strong> ${case2.vehicle}</p>
								<p><strong>Symptoms:</strong> ${case2.symptoms}</p>
								<p><strong>Diagnosis:</strong> ${case2.diagnosis}</p>
								<p><strong>Solution:</strong> ${case2.solution}</p>
								<p><strong>Parts:</strong> ${case2.parts}</p>
								<p><strong>Labor:</strong> ${case2.labor}</p>
								<p><strong>Total Cost:</strong> ${case2.total}</p>
							</div>`;
    
    const caseStudyRegex = /<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">[\s\S]*?<\/div>\s*<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">[\s\S]*?<\/div>/;
    content = content.replace(caseStudyRegex, caseStudyHtml);
  }
  
  // 替换相关代码
  if (data.relatedCodes) {
    const relatedHtml = data.relatedCodes.map(related => 
      `									<li style="margin-bottom: 10px;"><a href="${related.code.toLowerCase()}.html" style="color: #007bff;">${related.code} - ${related.desc}</a></li>`
    ).join('\n');
    
    const relatedRegex = /<li style="margin-bottom: 10px;"><a href="p0140\.html"[\s\S]*?<li style="margin-bottom: 10px;"><a href="p0145\.html"[\s\S]*?<\/li>/;
    content = content.replace(relatedRegex, relatedHtml);
  }
  
  return content;
}

// 批量创建页面
const allCodesToCreate = [
  // P码 - 燃油系统相关代码
  { code: 'P0168', database: morePCodeDatabase },
  { code: 'P0169', database: morePCodeDatabase },
  { code: 'P0170', database: morePCodeDatabase },
  // C码 - 底盘系统
  { code: 'C0150', database: moreCCodeDatabase },
  // B码 - 车身系统
  { code: 'B0078', database: moreBCodeDatabase },
  // U码 - 网络通信
  { code: 'U0138', database: moreUCodeDatabase }
];

let totalCreated = 0;

console.log('🚀 Creating more batch of DTC pages...\n');

allCodesToCreate.forEach(({ code, database }) => {
  if (database[code]) {
    try {
      const htmlContent = createDetailedCodePage(code, database[code]);
      fs.writeFileSync(`${code.toLowerCase()}.html`, htmlContent, 'utf8');
      console.log(`✅ Created ${code.toLowerCase()}.html - ${database[code].title}`);
      totalCreated++;
    } catch (error) {
      console.log(`❌ Failed to create ${code.toLowerCase()}.html: ${error.message}`);
    }
  }
});

console.log(`\n🎉 Successfully created ${totalCreated} additional DTC pages!`);
console.log('\n📊 Updated Progress:');
console.log('P-codes: 36/50 completed (need 14 more)');
console.log('C-codes: 12/50 completed (need 38 more)');
console.log('B-codes: 9/50 completed (need 41 more)');
console.log('U-codes: 9/50 completed (need 41 more)');
console.log('\nTotal: 66/200 completed (need 134 more)');
