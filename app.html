<!DOCTYPE html>
<!--[if IE 8]>
<html class="ie ie8"> <![endif]-->
<!--[if IE 9]>
<html class="ie ie9"> <![endif]-->
<!--[if gt IE 9]><!-->
<html>
<!--<![endif]-->

<head>
    <meta charset="utf-8">
    <title>MOBDGeekOBD - Professional OBD Car Diagnostic Software | Real-time Monitoring Fault Detection HUD Projection</title>
    <meta name="description" content="MOBDGeekOBD is a professional OBD car diagnostic software that provides real-time vehicle monitoring, fault code diagnosis, HUD projection, trip analysis, intelligent alerts and more. Support custom dashboards and professional maintenance recommendations to keep your car's health status at a glance.">
    <meta name="keywords" content="OBD diagnosis,car detection,vehicle monitoring,fault codes,HUD projection,dashboard,car maintenance,trip analysis,driving behavior,intelligent alerts,MOBD,GeekOBD">
    <meta name="author" content="geekobd.com">
    <meta name="robots" content="index, follow">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://www.geekobd.com/">
    <meta property="og:title" content="MOBDGeekOBD - Professional OBD Car Diagnostic Software">
    <meta property="og:description" content="Professional OBD car diagnostic software that provides real-time vehicle monitoring, fault code diagnosis, HUD projection, trip analysis and more. Keep your car's health status at a glance.">
    <meta property="og:image" content="https://www.geekobd.com/img/logo.png">
    <meta property="og:site_name" content="MOBDGeekOBD">
    <meta property="og:locale" content="en_US">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://www.geekobd.com/">
    <meta property="twitter:title" content="MOBDGeekOBD - Professional OBD Car Diagnostic Software">
    <meta property="twitter:description" content="Professional OBD car diagnostic software that provides real-time vehicle monitoring, fault code diagnosis, HUD projection, trip analysis and more.">
    <meta property="twitter:image" content="https://www.geekobd.com/img/logo.png">

    <!-- Technical SEO -->
    <link rel="canonical" href="https://www.geekobd.com/">
    <link rel="sitemap" type="application/xml" href="/sitemap.xml">
    <link rel="alternate" hreflang="en" href="https://www.geekobd.com/">
    <link rel="alternate" hreflang="zh-CN" href="https://mobd.cn/">
    <meta name="theme-color" content="#17a2b8">
    <meta name="application-name" content="MOBDGeekOBD">
    <meta name="msapplication-TileColor" content="#17a2b8">
    <meta name="msapplication-config" content="/browserconfig.xml">
    <meta name="format-detection" content="telephone=no">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <link rel="stylesheet" href="css/bootstrap.css">
    <link rel="stylesheet" href="css/fonts/font-awesome/css/font-awesome.css">
    <link rel="stylesheet" href="css/animations.css" media="screen">
    <link rel="stylesheet" href="css/superfish.css" media="screen">
    <link rel="stylesheet" href="css/revolution-slider/css/settings.css" media="screen">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/colors/blue.css" id="colors">
    <link rel="stylesheet" href="css/theme-responsive.css">
    <link rel="shortcut icon" href="img/ico/favicon.ico">
    <link rel="apple-touch-icon" href="img/ico/apple-touch-icon.png">
    <link rel="apple-touch-icon" sizes="72x72" href="img/ico/apple-touch-icon-72.png">
    <link rel="apple-touch-icon" sizes="114x114" href="img/ico/apple-touch-icon-114.png">
    <link rel="apple-touch-icon" sizes="144x144" href="img/ico/apple-touch-icon-144.png">

    <!-- JSON-LD Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@graph": [
            {
                "@type": "SoftwareApplication",
                "name": "MOBDGeekOBD",
                "applicationCategory": "AutomotiveApplication",
                "operatingSystem": "Android, iOS, Windows",
                "description": "Professional OBD car diagnostic software that provides real-time vehicle monitoring, fault code diagnosis, HUD projection, trip analysis, intelligent alerts and more",
                "url": "https://www.geekobd.com/",
                "downloadUrl": "https://www.geekobd.com/download",
                "author": {
                    "@type": "Organization",
                    "name": "MOBD",
                    "url": "https://www.geekobd.com/"
                },
                "offers": {
                    "@type": "Offer",
                    "price": "0",
                    "priceCurrency": "USD"
                },
                "featureList": [
                    "OBD fault diagnosis",
                    "Real-time vehicle monitoring",
                    "HUD projection display",
                    "Custom dashboard",
                    "Trip data analysis",
                    "Intelligent alert system",
                    "Voice broadcast function"
                ],
                "screenshot": "https://www.geekobd.com/img/screenshots/dashboard.jpg"
            },
            {
                "@type": "Organization",
                "name": "MOBD",
                "url": "https://www.geekobd.com/",
                "logo": "https://www.geekobd.com/img/logo.png",
                "description": "Professional automotive diagnostic software developer, committed to providing convenient vehicle diagnostic solutions for car owners",
                "contactPoint": {
                    "@type": "ContactPoint",
                    "telephone": "******-0123",
                    "contactType": "customer service",
                    "availableLanguage": ["English", "Chinese"]
                }
            }
        ]
    }
    </script>

    <!-- 预加载关键资源 -->
    <link rel="preload" href="css/style.css" as="style">
    <link rel="preload" href="img/logo.png" as="image">
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">

    <!-- 网站验证 -->
    <meta name="baidu-site-verification" content="codeva-XXXXXXXX">
    <meta name="google-site-verification" content="XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX">
    <meta name="360-site-verification" content="XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX">

    <style type="text/css">
        .main-content {
            margin: 0 !important;
            padding: 0 !important;
        }

        .flash {
            width: 850px;
            height: 520px;
        }

        /* 新增样式 - 保持与原有风格一致 */
        .download-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
        }
        .download-section h2 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.8em;
        }
        .download-section p {
            color: #6c757d;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        .download-section .btns a {
            display: inline-block;
            margin-right: 15px;
            margin-bottom: 10px;
            padding: 12px 25px;
            border-radius: 5px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s;
        }
        .download-section .btns a.download {
            background: #17a2b8;
            color: #fff;
        }
        .download-section .btns a.download:hover {
            background: #138496;
        }
        .download-section .btns a.guide {
            background: #6c757d;
            color: #fff;
        }
        .download-section .btns a.guide:hover {
            background: #545b62;
        }

        /* 轮播图样式 */
        .screenshots-section {
            margin-bottom: 50px;
            padding: 40px 30px;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
        }

        .screenshots-section h2 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 35px;
            font-size: 2em;
            font-weight: 600;
            position: relative;
            padding-bottom: 15px;
        }

        .screenshots-section h2:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #17a2b8, #138496);
            border-radius: 2px;
        }

        .carousel-multi {
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 30px 0;
            border: 1px solid #dee2e6;
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.05);
        }

        .carousel-track-multi {
            display: flex;
            transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .carousel-slide-multi {
            flex: 0 0 33.333%;
            padding: 0 15px;
            box-sizing: border-box;
        }

        .carousel-slide-multi .slide-content {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .carousel-slide-multi .slide-content:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,123,255,0.2);
        }

        .carousel-slide-multi img {
            width: 100%;
            height: auto;
            border-radius: 10px;
            border: 2px solid #e9ecef;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .carousel-slide-multi .slide-content:hover img {
            border-color: #17a2b8;
        }

        .carousel-slide-multi .slide-title {
            text-align: center;
            font-weight: 600;
            color: #2c3e50;
            margin: 0 0 10px 0;
            font-size: 1.2em;
        }

        .carousel-slide-multi p {
            text-align: center;
            color: #6c757d;
            margin: 0;
            font-size: 1em;
            line-height: 1.5;
            flex-grow: 1;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .carousel-btn-multi {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: #fff;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            font-size: 20px;
            z-index: 10;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(23,162,184,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .carousel-btn-multi.left { left: 15px; }
        .carousel-btn-multi.right { right: 15px; }

        .carousel-btn-multi:hover {
            background: linear-gradient(135deg, #138496, #117a8b);
            transform: translateY(-50%) scale(1.1);
            box-shadow: 0 6px 20px rgba(23,162,184,0.4);
        }

        .carousel-btn-multi:active {
            transform: translateY(-50%) scale(0.95);
        }

        /* 轮播指示点 */
        .carousel-dots {
            display: flex;
            justify-content: center;
            margin-top: 20px;
            gap: 8px;
        }

        .carousel-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #dee2e6;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .carousel-dot.active {
            background: #007bff;
            transform: scale(1.2);
        }

        .carousel-dot:hover {
            background: #6c757d;
        }

        /* 功能介绍样式 */
        .features, .modules, .community {
            margin-bottom: 50px;
            padding: 40px 30px;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
        }

        .features h2, .modules h2, .community h2 {
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2em;
            font-weight: 600;
            text-align: center;
            position: relative;
            padding-bottom: 15px;
        }

        .features h2:after, .modules h2:after, .community h2:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #17a2b8, #138496);
            border-radius: 2px;
        }

        .features ul, .community ul {
            padding-left: 0;
            list-style: none;
            max-width: 900px;
            margin: 0 auto;
        }

        .features li, .community li {
            margin-bottom: 20px;
            padding: 20px 20px 20px 70px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #17a2b8;
            line-height: 1.7;
            color: #495057;
            font-size: 1.05em;
            transition: all 0.3s ease;
            position: relative;
        }

        .features li:hover, .community li:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(23,162,184,0.15);
            background: #fff;
        }

        .features li strong {
            color: #17a2b8;
            font-weight: 600;
            font-size: 1.1em;
            display: block;
            margin-bottom: 8px;
        }

        .features li:before {
            content: '✓';
            position: absolute;
            left: 15px;
            top: 20px;
            width: 28px;
            height: 28px;
            background: #17a2b8;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 16px;
            box-shadow: 0 2px 8px rgba(23,162,184,0.3);
            border: 3px solid white;
        }

        .module-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 25px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .module {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 30px 25px;
            border: 1px solid #dee2e6;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .module:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #17a2b8, #138496);
        }

        .module:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(23,162,184,0.2);
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
        }

        .module h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.4em;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .module:before {
            content: attr(data-icon);
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 2.5em;
            opacity: 0.15;
            z-index: 1;
        }

        .module h3:before {
            content: attr(data-icon);
            margin-right: 12px;
            font-size: 1.2em;
            display: inline-block;
            width: 35px;
            height: 35px;
            background: linear-gradient(135deg, #17a2b8, #138496);
            border-radius: 10px;
            text-align: center;
            line-height: 35px;
            color: white;
            font-style: normal;
            box-shadow: 0 2px 8px rgba(23,162,184,0.3);
        }

        .module p {
            color: #6c757d;
            line-height: 1.6;
            margin: 0;
            font-size: 1.05em;
        }

        /* 社区部分特殊样式 */
        .community {
            background: #fff;
            color: #495057;
            border: 2px solid #17a2b8;
            position: relative;
            overflow: hidden;
        }

        .community:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, #17a2b8, #138496);
        }

        .community h2 {
            color: #17a2b8;
        }

        .community h2:after {
            background: linear-gradient(90deg, #17a2b8, #138496);
        }

        .community li {
            background: linear-gradient(135deg, #e8f7f9 0%, #f0fbfc 100%);
            border-left-color: #17a2b8;
            color: #495057;
            padding: 20px 20px 20px 45px;
        }

        .community li:hover {
            background: linear-gradient(135deg, #d1f2f5 0%, #e8f7f9 100%);
            border-left-color: #138496;
        }

        .community li:before {
            background: #17a2b8;
            color: white;
            left: 15px;
            width: 28px;
            height: 28px;
            font-size: 16px;
            box-shadow: 0 2px 8px rgba(23,162,184,0.3);
            border: 3px solid white;
        }

        @media (max-width: 768px) {
            .carousel-slide-multi {
                flex: 0 0 100%;
            }

            .screenshots-section {
                padding: 25px 20px;
            }

            .screenshots-section h2 {
                font-size: 1.6em;
            }

            .carousel-multi {
                padding: 20px 0;
            }

            .carousel-slide-multi {
                padding: 0 10px;
            }

            .carousel-slide-multi .slide-content {
                padding: 15px;
            }

            .carousel-btn-multi {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }

            .carousel-btn-multi.left { left: 10px; }
            .carousel-btn-multi.right { right: 10px; }

            .download-section {
                padding: 20px;
                margin-bottom: 20px;
            }

            .features, .modules, .community {
                margin-bottom: 30px;
                padding: 25px 20px;
                border-radius: 8px;
            }

            .features h2, .modules h2, .community h2 {
                font-size: 1.6em;
                margin-bottom: 20px;
            }

            .features li, .community li {
                padding: 15px;
                margin-bottom: 15px;
                font-size: 1em;
            }

            .features li strong {
                font-size: 1.05em;
            }

            .module-list {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .module {
                padding: 20px 18px;
            }

            .module h3 {
                font-size: 1.25em;
            }

            .module p {
                font-size: 1em;
            }
        }

        @media (max-width: 480px) {
            .features, .modules, .community {
                padding: 20px 15px;
                margin-bottom: 25px;
            }

            .features h2, .modules h2, .community h2 {
                font-size: 1.4em;
            }

            .features li, .community li {
                padding: 12px;
                font-size: 0.95em;
            }

            .module {
                padding: 18px 15px;
            }

            .module h3 {
                font-size: 1.2em;
            }

            .download-section {
                padding: 15px;
            }
        }
    </style>

<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-RD6767XBCL"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-RD6767XBCL');
</script>
</head>

<body class="app">
<div class="wrap">
        <!-- Header Start -->
        <header id="header">
            <!-- Main Header Start -->
            <div class="main-header">
                <div class="container">
                    <!-- Logo Start -->
                    <div class="logo pull-left">
                        <h1> <a href="index.html"> <img src="img/logo.png" alt="MOBD"> </a> </h1>
                    </div>
                    <!-- Logo End -->
                    <!-- Mobile Menu Start -->
                    <div class="mobile navbar-header"> <a class="navbar-toggle" data-toggle="collapse"
                            data-target=".menu"> <i class="icon-reorder icon-2x"></i> </a> </div>
                    <!-- Mobile Menu End -->
                    <!-- Menu Start -->
                    <nav class="collapse navbar-collapse menu">
                        <ul class="nav navbar-nav sf-menu">
                            <li><a href="index.html" class="sf-with-ul">Home</a></li>
                            <li><a href="app.html" class="sf-with-ul" id="current">APP</a></li>
                            <li><a href="javascript:;;" class="sf-with-ul">Adaptor <span class="sf-sub-indicator"><i
                                            class="icon-angle-down white-arrow"></i></span> </a>
                                <ul>
                                    <li><a href="hardware2.html" class="sf-with-ul">MOBD GPS</a></li>
                                    <li><a href="hardware.html" class="sf-with-ul">MOBD</a></li>
                                </ul>
                            </li>
                            <li><a href="javascript:;;" class="sf-with-ul">Resources <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
                                <ul>
                                    <li><a href="dtc-codes.html" class="sf-with-ul">DTC Codes</a></li>
                                    <li><a href="obd-diagnostic-guide.html" class="sf-with-ul">OBD Guide</a></li>
                                    <li><a href="vehicle-compatibility.html" class="sf-with-ul">Compatibility</a></li>
                                    <li><a href="fuel-efficiency-monitoring.html" class="sf-with-ul">Fuel Efficiency</a></li>
                                    <li><a href="support.html" class="sf-with-ul">Support</a></li>
                                    <li><a href="blog.html" class="sf-with-ul">Blog</a></li>
                                </ul>
                            </li>
                            <li><a href="about.html" class="sf-with-ul">About Us</a></li>
                            <li><a href="//www.mobd.cn" class="sf-with-ul" target="_blank">中文版</a></li>
                        </ul>
                    </nav>
                    <!-- Menu End -->
                </div>
            </div>
            <!-- Main Header End -->
        </header>
        <!-- Header End -->
    <!-- Content Start -->
    <div id="main">
        <!-- Main Content start-->

        <!-- 主要下载区 -->
        <main class="main-content" role="main">
            <div class="container">
                <section class="hero-section" itemscope itemtype="https://schema.org/SoftwareApplication">
                    <div class="col-xs-7">
                        <img src="img/app/app_top.png" alt="MOBD车况检测大师应用界面展示" itemprop="screenshot">
                    </div>
                    <div class="col-xs-5" style="text-align:center">
                        <h1 class="mt20" itemprop="name">GeekOBD</h1>
                            <ul>
                                <li>DIY self-defined dashboard, detection and pre-warning. Share self-defined solutions
                                    among users.</li>
                                <li>Display various driving data such as fuel consumption map and speed map using map
                                    tracking.</li>
                            </ul>
                        <span></span><br>
                        <img src="img/prod/app.png" alt="app download" class="pull-middle qr">
                         <br>Scan the QR code with your mobile phone to download the APP.  <br>                     
                        <br>
                        <a class="btn btn-warning btn-lg" href="/a/" target="_blank">Download APP</a>
                        <div class="clearfix"></div>
                        <div class="text-center bottom-pad-small"><br />
                            <a href="/s" target="_blank" class="b">Download User Guidelines (PDFversion)</a>
                        </div>
                    </div>
                </section>
            </div>
        </main>
         <!-- App Interface Preview Carousel -->
        <div class="main-content">
            <div class="container">
                <section class="screenshots-section">
                    <h2>App Interface Preview</h2>
                    <div class="carousel-multi" id="carouselMulti">
                        <button class="carousel-btn-multi left" id="carouselPrev" aria-label="Previous">&#8592;</button>
                        <div class="carousel-track-multi" id="carouselTrack">
                            <div class="carousel-slide-multi">
                                <div class="slide-content">
                                    <img src="img/app/app_03.jpg" alt="Travel Safety Assurance">
                                    <div class="slide-title">Travel Safety Assurance</div>
                                    <p>Self-diagnose vehicle faults, ultra-portable professional diagnostic tool, comprehensive vehicle safety scanning, user-friendly fault interpretation</p>
                                </div>
                            </div>
                            <div class="carousel-slide-multi">
                                <div class="slide-content">
                                    <img src="img/app/app_04.jpg" alt="Mobile HUD Display">
                                    <div class="slide-title">Mobile HUD Display</div>
                                    <p>Support landscape, portrait, and reverse HUD display modes, various dashboard themes for DIY, free download of various skin dashboards</p>
                                </div>
                            </div>
                            <div class="carousel-slide-multi">
                                <div class="slide-content">
                                    <img src="img/app/app_05.jpg" alt="Powerful Dashboard Editor">
                                    <div class="slide-title">Powerful Dashboard Editor</div>
                                    <p>Free dashboard layout design, customizable display data content, shake to switch dashboard skins, choose from circular, semi-circular, bar, and text gauges</p>
                                </div>
                            </div>
                            <div class="carousel-slide-multi">
                                <div class="slide-content">
                                    <img src="img/app/app_06.jpg" alt="Alert Voice Broadcast">
                                    <div class="slide-title">Alert Voice Broadcast</div>
                                    <p>Understand vehicle status without looking at screen, self-designed alert schemes, multiple trigger condition combinations, customizable reminder frequency and voice content</p>
                                </div>
                            </div>
                            <div class="carousel-slide-multi">
                                <div class="slide-content">
                                    <img src="img/app/app_08.jpg" alt="Real-time Driving Data">
                                    <div class="slide-title">Real-time Driving Data at a Glance</div>
                                    <p>Accurate vehicle fuel consumption assessment, clear fuel cost overview, timely reminders for poor driving habits</p>
                                </div>
                            </div>
                            <div class="carousel-slide-multi">
                                <div class="slide-content">
                                    <img src="img/app/app_09.jpg" alt="Comprehensive Driving Data Display">
                                    <div class="slide-title">Comprehensive Driving Data Display</div>
                                    <p>Perfect combination of sensor data and driving trajectory, trend charts recreate real-time changes in trip data</p>
                                </div>
                            </div>
                            <div class="carousel-slide-multi">
                                <div class="slide-content">
                                    <img src="img/app/app_11.jpg" alt="Expanding Network Resources">
                                    <div class="slide-title">Expanding Network Resources</div>
                                    <p>Support dashboard, gauge skin, detection, and alert downloads, download shared resources from others, share your own resources</p>
                                </div>
                            </div>
                            <div class="carousel-slide-multi">
                                <div class="slide-content">
                                    <img src="img/app/app_12.jpg" alt="Expanding Network Resources">
                                    <div class="slide-title">Safety protection for driving</div>
                                    <p>Independent snapshot diagnostic scenarios and trace diagnostic scenarios, Open vehicle sensor metadata, Self-defined trigger condition, end condition, data item and data collection, Various competition playing methods, such a gear-shift timing, acceleration performance and fuel-saving skills…</p>
                                </div>
                            </div>
                            <div class="carousel-slide-multi">
                                <div class="slide-content">
                                    <img src="img/app/app_13.jpg" alt="Expanding Network Resources">
                                    <div class="slide-title">The overhead display</div>
                                    <p>Supports horizontal, vertical, front and reverse HUD displays.</p>
                                </div>
                            </div>
                            <div class="carousel-slide-multi">
                                <div class="slide-content">
                                    <img src="img/app/app_14.jpg" alt="Expanding Network Resources">
                                    <div class="slide-title">Powerful dashboard editor</div>
                                    <p>Freely design the layout of the dashboard, Define the data content of the dashboard, Shake it to switch between themes, Round, semi-circular, striped and text dashboards selectable.</p>
                                </div>
                            </div>
                            <div class="carousel-slide-multi">
                                <div class="slide-content">
                                    <img src="img/app/app_15.jpg" alt="Expanding Network Resources">
                                    <div class="slide-title">Self-service expansion of detection solutions</div>
                                    <p>Independent snapshot diagnostic scenarios and trace diagnostic scenarios, Open vehicle sensor metadata, Self-defined trigger condition, end condition, data item and data collection, Various competition playing methods, such a gear-shift timing, acceleration performance and fuel-saving skills…</p>
                                </div>
                            </div>
                            <div class="carousel-slide-multi">
                                <div class="slide-content">
                                    <img src="img/app/app_16.jpg" alt="Expanding Network Resources">
                                    <div class="slide-title">Comprehensive display of trip data</div>
                                    <p>Data accuracy down to every step of the accelerator, The trend map re-creates the change in data of every sensor during the trip, The perfect combination of car condition data and tracking: fuel consumption map, tracking map, speed map…</p>
                                </div>
                            </div>
                            <div class="carousel-slide-multi">
                                <div class="slide-content">
                                    <img src="img/app/app_17.jpg" alt="Expanding Network Resources">
                                    <div class="slide-title">Comprehensive trip statistics</div>
                                    <p>Mileage statistics, Driving duration statistics, Departure time statistics, Average fuel consumption statistics, Fuel expenses statistics, Driving behaviour statistics</p>
                                </div>
                            </div>                       
                        </div>
                        <button class="carousel-btn-multi right" id="carouselNext" aria-label="Next">&#8594;</button>
                    </div>
                    <div class="carousel-dots" id="carouselDots"></div>
                </section>
            </div>
        </div>


        <!-- Core Features Overview -->
        <section class="main-content" role="region" aria-labelledby="features-heading">
            <div class="container">
                <div class="features" itemscope itemtype="https://schema.org/ItemList">
                    <h2 id="features-heading" itemprop="name">Core Features Overview</h2>
                    <ul itemprop="itemListElement">
                        <li itemscope itemtype="https://schema.org/ListItem">
                            <strong itemprop="name">Custom Dashboard & HUD:</strong>
                            <span itemprop="description">Freely design dashboard layout, support circular, semi-circular, bar, text and other styles, HUD projection for safe driving.</span>
                        </li>
                        <li itemscope itemtype="https://schema.org/ListItem">
                            <strong itemprop="name">Professional Fault Diagnosis:</strong>
                            <span itemprop="description">Read/clear fault codes, detailed explanations and maintenance recommendations, support snapshot and tracking detection, covering annual inspection simulation, battery/throttle health and other special tests.</span>
                        </li>
                        <li itemscope itemtype="https://schema.org/ListItem">
                            <strong itemprop="name">Trip Analysis & Driving Behavior:</strong>
                            <span itemprop="description">Record driving data throughout the journey, analyze fuel consumption, speed, routes and driving habits, trend visualization comparison.</span>
                        </li>
                        <li itemscope itemtype="https://schema.org/ListItem">
                            <strong itemprop="name">Intelligent Alerts & Voice Broadcast:</strong>
                            <span itemprop="description">Customize alert schemes, set trigger/end conditions, frequency and voice content, real-time voice reminders for safety and peace of mind.</span>
                        </li>
                        <li itemscope itemtype="https://schema.org/ListItem">
                            <strong itemprop="name">Resource Center & Community:</strong>
                            <span itemprop="description">Download/share dashboards, detection schemes and alert templates, access fault code encyclopedia, maintenance guides and Q&A community.</span>
                        </li>
                        <li itemscope itemtype="https://schema.org/ListItem">
                            <strong itemprop="name">Auxiliary Tools:</strong>
                            <span itemprop="description">HUD navigation, one-click car finder, engine sound simulation, dual-camera dash cam and more.</span>
                        </li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Main Module Introduction -->
        <div class="main-content">
            <div class="container">
                <section class="modules">
                    <h2>Main Module Introduction</h2>
                    <div class="module-list">
                        <div class="module" data-icon="📊">
                            <h3 data-icon="📊">Dashboard Module</h3>
                            <p>Personalized real-time monitoring of vehicle data, freely choose display indicators and layout, support standard and HUD projection modes, driving information at a glance. Multiple gauge styles available to meet different driving needs.</p>
                        </div>
                        <div class="module" data-icon="🗺️">
                            <h3 data-icon="🗺️">Trip Recording & Analysis</h3>
                            <p>Automatically record every trip, capture every sensor change, analyze fuel consumption, speed, routes, optimize driving habits, and detect potential problems early. Support historical data comparison and trend analysis.</p>
                        </div>
                        <div class="module" data-icon="🔧">
                            <h3 data-icon="🔧">Detection & Diagnosis</h3>
                            <p>Snapshot detection records current sensor status with one click, tracking detection continuously records changes, supports annual inspection simulation, throttle carbon deposit, battery health and other special tests, supports clearing fault codes. Professional-grade diagnostic functions.</p>
                        </div>
                        <div class="module" data-icon="🔔">
                            <h3 data-icon="🔔">Alerts & Voice Reminders</h3>
                            <p>Customize safety alert schemes, set multi-condition triggers and frequency, real-time voice broadcast of faults, maintenance and driving behavior to ensure driving safety. Intelligent voice assistant makes driving more reassuring.</p>
                        </div>
                        <div class="module" data-icon="👥">
                            <h3 data-icon="👥">Knowledge Base & Community</h3>
                            <p>Fault code encyclopedia, DIY maintenance guides, Q&A community, car owner mutual assistance and communication, sharing experiences and solutions. Rich learning resources to make you an automotive expert.</p>
                        </div>
                        <div class="module" data-icon="🎓">
                            <h3 data-icon="🎓">Advanced Usage & Tutorials</h3>
                            <p>Custom detection schemes, alert templates, cloud monitoring of sensors and fault codes, trend analysis and anomaly detection to help advanced players. Professional tutorial guidance to unlock more advanced features.</p>
                        </div>
                    </div>
                </section>
            </div>
        </div>

        <!-- Resources & Community -->
        <div class="main-content">
            <div class="container">
                <section class="community">
                    <h2>Resources & Community</h2>
                    <ul>
                        <li>Download/share dashboards, detection schemes, alert templates</li>
                        <li>Access fault code encyclopedia, maintenance guides, Q&A forums</li>
                        <li>Exchange experiences with car owners and help solve problems together</li>
                    </ul>
                </section>
            </div>
        </div>

        <!-- FAQ Section - SEO Optimized -->
        <section class="main-content faq-section" role="region" aria-labelledby="faq-heading">
            <div class="container">
                <div itemscope itemtype="https://schema.org/FAQPage">
                    <h2 id="faq-heading">Frequently Asked Questions</h2>

                    <div class="faq-item" itemscope itemprop="mainEntity" itemtype="https://schema.org/Question">
                        <h3 itemprop="name">What is OBD vehicle diagnostic testing?</h3>
                        <div itemscope itemprop="acceptedAnswer" itemtype="https://schema.org/Answer">
                            <div itemprop="text">
                                <p>OBD (On-Board Diagnostics) is an onboard diagnostic system that can read real-time operating data and fault information from engine, transmission, ABS and other systems by connecting to the vehicle's OBD interface. MOBDGeekOBD turns your phone into a professional automotive diagnostic tool through a professional OBD adapter.</p>
                            </div>
                        </div>
                    </div>

                    <div class="faq-item" itemscope itemprop="mainEntity" itemtype="https://schema.org/Question">
                        <h3 itemprop="name">Which vehicle models are supported?</h3>
                        <div itemscope itemprop="acceptedAnswer" itemtype="https://schema.org/Answer">
                            <div itemprop="text">
                                <p>Supports most car brands manufactured after 2008, including but not limited to: Volkswagen, Audi, BMW, Mercedes-Benz, Toyota, Honda, Nissan, Hyundai, Kia, Ford, Chevrolet, Buick, Geely, BYD, etc. Specific vehicle model support can be confirmed on our vehicle query page.</p>
                            </div>
                        </div>
                    </div>

                    <div class="faq-item" itemscope itemprop="mainEntity" itemtype="https://schema.org/Question">
                        <h3 itemprop="name">What hardware equipment is needed?</h3>
                        <div itemscope itemprop="acceptedAnswer" itemtype="https://schema.org/Answer">
                            <div itemprop="text">
                                <p>You need an OBD Bluetooth adapter (ELM327 chip recommended) and a smartphone. Insert the adapter into the vehicle's OBD interface (usually below the steering wheel) and connect to your phone via Bluetooth to start using. We recommend using officially certified MOBD hardware devices for the best experience.</p>
                            </div>
                        </div>
                    </div>

                    <div class="faq-item" itemscope itemprop="mainEntity" itemtype="https://schema.org/Question">
                        <h3 itemprop="name">Is the software free?</h3>
                        <div itemscope itemprop="acceptedAnswer" itemtype="https://schema.org/Answer">
                            <div itemprop="text">
                                <p><strong>Basic features are free:</strong> Core functions such as dashboard, detection, alerts, trip recording are completely free to use.</p>
                                <p><strong>PLUS membership privileges:</strong> Enjoy more advanced features after opening PLUS membership. If you use official MOBD devices, lifetime PLUS membership is automatically activated.</p>
                                <div class="plus-features">
                                    <h4>PLUS membership privileges<br>include:</h4>
                                    <ul>
                                        <li><strong>Ad-free:</strong> Users are not disturbed by advertisements</li>
                                        <li><strong>More schemes:</strong> Unlimited addition of dashboards, detection schemes, alert schemes</li>
                                        <li><strong>Feature privileges:</strong> Annual inspection, dash cam, engine sound, sensor data cloud monitoring, fault code cloud monitoring</li>
                                        <li><strong>Data privileges:</strong> Remove the 7-day data viewing limitation</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="faq-item" itemscope itemprop="mainEntity" itemtype="https://schema.org/Question">
                        <h3 itemprop="name">Cannot connect to vehicle or read data, what's wrong?</h3>
                        <div itemscope itemprop="acceptedAnswer" itemtype="https://schema.org/Answer">
                            <div itemprop="text">
                                <p><strong>Common causes and solutions:</strong></p>
                                <div class="troubleshooting-list">
                                    <ol>
                                        <li><strong>OBD device failure:</strong> Check if the device is working properly and indicator light status is normal</li>
                                        <li><strong>Device compatibility:</strong> Some OBD devices only support Android but not Apple, please confirm device compatibility</li>
                                        <li><strong>Vehicle model not supported:</strong> Vehicle OBD is not standard protocol.</li>
                                        <li><strong>TF card issue:</strong> Official MOBD device has unsupported TF card inserted (maximum support 16G)</li>
                                        <li><strong>Protocol detection:</strong> OBD device cannot automatically detect protocol, need to try manually one by one</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <div class="main-content" style="min-height:220px;padding-top:70px">
            <div class="text-center">More amazing features, download the APP now!  <br>
                <img src="img/prod/app.png" class="more qr" alt="Scan QR code to downloadGeekOBD"></div>
        </div>

        <!-- Main Content end-->
    </div>
    <!-- Content End -->



    <style>
        .faq-section {
            background: #f8f9fa;
            padding: 60px 0;
            margin-top: 40px;
        }
        .faq-section h2 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 40px;
            font-size: 2.2em;
            font-weight: 600;
        }
        .faq-item {
            background: white;
            border-radius: 10px;
            margin-bottom: 20px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #17a2b8;
        }
        .faq-item h3 {
            color: #17a2b8;
            margin-bottom: 15px;
            font-size: 1.3em;
            font-weight: 600;
        }
        .faq-item p {
            color: #495057;
            line-height: 1.7;
            margin: 0 0 10px 0;
        }
        .plus-features {
            margin-top: 20px;
            padding: 20px 20px 20px 25px;
            background: #f1f8ff;
            border-radius: 8px;
            border-left: 4px solid #17a2b8;
            clear: both;
            overflow: hidden;
        }
        .plus-features h4 {
            color: #17a2b8;
            margin: 0 0 15px 0;
            font-size: 1.1em;
            font-weight: 600;
            line-height: 1.4;
            word-wrap: break-word;
        }
        .plus-features ul {
            margin: 0;
            padding: 0;
            list-style: none;
        }
        .plus-features li {
            color: #495057;
            margin-bottom: 15px;
            line-height: 1.6;
            padding: 0;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }
        .plus-features li strong {
            color: #17a2b8;
            font-weight: 600;
            margin-right: 5px;
        }
        .troubleshooting-list {
            margin: 15px 0;
        }
        .troubleshooting-list ol {
            margin: 0;
            padding-left: 20px;
        }
        .troubleshooting-list li {
            color: #495057;
            margin-bottom: 10px;
            line-height: 1.6;
        }
        .troubleshooting-list li strong {
            color: #dc3545;
            font-weight: 600;
        }
        .help-resources {
            margin-top: 20px;
            padding: 15px;
            background: #fff3cd;
            border-radius: 8px;
            border-left: 3px solid #ffc107;
        }
        .help-resources p {
            margin-bottom: 8px;
        }
        .help-resources a {
            text-decoration: none;
            font-weight: 600;
        }
        .help-resources a:hover {
            text-decoration: underline;
        }
        @media (max-width: 768px) {
            .faq-section {
                padding: 40px 0;
                margin-top: 30px;
            }
            .faq-section h2 {
                font-size: 1.8em;
                margin-bottom: 30px;
                padding: 0 20px;
            }
            .faq-item {
                margin: 0 15px 15px 15px;
                padding: 20px 18px;
                border-radius: 8px;
            }
            .faq-item h3 {
                font-size: 1.2em;
                margin-bottom: 12px;
                line-height: 1.4;
            }
            .faq-item p {
                font-size: 0.95em;
                line-height: 1.6;
                margin-bottom: 8px;
            }
            .plus-features {
                margin-top: 15px !important;
                margin-left: 0 !important;
                margin-right: 0 !important;
                padding: 18px 25px 18px 20px !important;
                background: #f1f8ff !important;
                border-radius: 8px !important;
                border-left: 4px solid #17a2b8 !important;
                clear: both !important;
                overflow: hidden !important;
                width: 100% !important;
                box-sizing: border-box !important;
            }
            .plus-features h4 {
                color: #17a2b8;
                margin: 0 0 12px 0;
                font-size: 1.05em;
                font-weight: 600;
                line-height: 1.3;
                white-space: normal;
            }
            .plus-features ul {
                margin: 0;
                padding: 0;
                list-style: none;
            }
            .plus-features li {
                color: #495057;
                margin-bottom: 12px;
                line-height: 1.5;
                padding: 0;
                word-wrap: break-word;
                overflow-wrap: break-word;
            }
            .plus-features li strong {
                color: #17a2b8;
                font-weight: 600;
                margin-right: 5px;
                font-size: 0.95em;
            }
            .troubleshooting-list {
                margin: 12px 0;
            }
            .troubleshooting-list ol {
                padding-left: 18px;
            }
            .troubleshooting-list li {
                font-size: 0.95em;
                margin-bottom: 8px;
            }
        }

        @media (max-width: 480px) {
            .faq-section {
                padding: 30px 0;
                margin-top: 20px;
            }
            .faq-section h2 {
                font-size: 1.5em;
                margin-bottom: 25px;
                padding: 0 15px;
            }
            .faq-item {
                margin: 0 8px 12px 8px;
                padding: 15px 15px;
                border-radius: 6px;
            }
            .faq-item h3 {
                font-size: 1.1em;
                margin-bottom: 10px;
                line-height: 1.3;
            }
            .faq-item p {
                font-size: 0.9em;
                line-height: 1.5;
                margin-bottom: 6px;
            }
            .plus-features {
                padding: 15px 20px 15px 18px !important;
                margin-top: 12px !important;
                margin-left: 0 !important;
                margin-right: 0 !important;
            }
            .plus-features h4 {
                font-size: 0.95em;
                margin-bottom: 10px;
                line-height: 1.2;
                white-space: normal;
            }
            .plus-features li {
                margin-bottom: 10px;
                font-size: 0.9em;
                line-height: 1.4;
            }
            .plus-features li strong {
                font-size: 0.9em;
                display: inline-block;
                margin-bottom: 2px;
            }
            .troubleshooting-list {
                margin: 10px 0;
            }
            .troubleshooting-list ol {
                padding-left: 15px;
            }
            .troubleshooting-list li {
                font-size: 0.9em;
                margin-bottom: 6px;
                line-height: 1.4;
            }
        }
    </style>

    <!-- Footer Start -->
    <footer id="footer">
        <!-- Footer Bottom Start -->
        <div class="footer-bottom">
            <div class="container">
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12">Copyright &copy; 2005~
                        <script type="text/javascript">var d = new Date();
                        document.write(d.getUTCFullYear());</script>
                         MentalRoad All Right Reserved. 明道通途版权所有   <a href='http://beian.miit.gov.cn' target='_blank'>京ICP备09047462号-2</a>
                        <script type="text/javascript">
                            var cnzz_protocol = (("https:" == document.location.protocol) ? " https://" : " //");
                            document.write(unescape("%3Cspan id='cnzz_stat_icon_1253146380'%3E%3C/span%3E%3Cscript src='" + cnzz_protocol + "s13.cnzz.com/z_stat.php%3Fid%3D1253146380%26show%3Dpic1' type='text/javascript'%3E%3C/script%3E"));
                        </script>
                    </div>

                </div>
            </div>
        </div>
        <!-- Footer Bottom End -->
    </footer>
    <!-- Scroll To Top -->
    <div><a href="#" class="scrollup"><i class="icon-angle-up"></i></a></div>
    <!-- Wrap End -->
    <!-- The Scripts -->
    <script src="js/jquery.min.js"></script>
    <script src="js/bootstrap.js"></script>
    <script src="js/jquery.parallax.js"></script>
    <script src="js/revolution-slider/js/jquery.themepunch.revolution.min.js"></script>
    <script src="js/jquery.prettyPhoto.js"></script>
    <script src="js/superfish.js"></script>
    <script src="js/jquery.sticky.js"></script>
    <script src="js/jflickrfeed.js"></script>
    <script src="js/imagesloaded.pkgd.min.js"></script>
    <script src="js/custom.js"></script>

    <script>
        // 轮播图功能
        $(document).ready(function() {
            const track = $('#carouselTrack');
            const slides = track.find('.carousel-slide-multi');
            const prevBtn = $('#carouselPrev');
            const nextBtn = $('#carouselNext');
            const dotsContainer = $('#carouselDots');
            let slidesToShow = $(window).width() <= 768 ? 1 : 3;
            let currentIndex = 0;

            function getSlidesToShow() {
                return $(window).width() <= 768 ? 1 : 3;
            }

            function getMaxIndex() {
                return Math.max(0, slides.length - slidesToShow);
            }

            function createDots() {
                dotsContainer.empty();
                const dotCount = Math.max(1, slides.length - slidesToShow + 1);
                for (let i = 0; i < dotCount; i++) {
                    const dot = $('<div class="carousel-dot"></div>');
                    if (i === currentIndex) {
                        dot.addClass('active');
                    }
                    dot.on('click', function() {
                        currentIndex = i;
                        updateCarousel();
                    });
                    dotsContainer.append(dot);
                }
            }

            function updateCarousel() {
                slidesToShow = getSlidesToShow();
                const maxIndex = getMaxIndex();
                if (currentIndex > maxIndex) currentIndex = maxIndex;
                if (currentIndex < 0) currentIndex = 0;

                slides.each(function(index) {
                    if (slidesToShow === 1) {
                        $(this).css({
                            'flex': '0 0 100%',
                            'max-width': '100%'
                        });
                    } else {
                        $(this).css({
                            'flex': '0 0 33.333%',
                            'max-width': '33.333%'
                        });
                    }
                });

                const percent = (100 / slidesToShow) * currentIndex;
                track.css('transform', 'translateX(-' + percent + '%)');

                // 更新指示点
                createDots();
            }

            prevBtn.on('click', function() {
                currentIndex = Math.max(0, currentIndex - 1);
                updateCarousel();
            });

            nextBtn.on('click', function() {
                const maxIndex = getMaxIndex();
                currentIndex = Math.min(maxIndex, currentIndex + 1);
                updateCarousel();
            });

            $(window).on('resize', function() {
                updateCarousel();
            });

            // 自动轮播
            let autoPlay = setInterval(function() {
                const maxIndex = getMaxIndex();
                if (currentIndex >= maxIndex) {
                    currentIndex = 0;
                } else {
                    currentIndex++;
                }
                updateCarousel();
            }, 5000);

            // 鼠标悬停时暂停自动轮播
            $('.carousel-multi').on('mouseenter', function() {
                clearInterval(autoPlay);
            }).on('mouseleave', function() {
                autoPlay = setInterval(function() {
                    const maxIndex = getMaxIndex();
                    if (currentIndex >= maxIndex) {
                        currentIndex = 0;
                    } else {
                        currentIndex++;
                    }
                    updateCarousel();
                }, 5000);
            });

            // 初始化
            updateCarousel();
        });
    </script>



</body>

</html>
