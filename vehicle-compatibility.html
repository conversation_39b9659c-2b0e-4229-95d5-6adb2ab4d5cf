<!DOCTYPE html>
<!--[if IE 8]><html class="ie ie8"> <![endif]-->
<!--[if IE 9]><html class="ie ie9"> <![endif]-->
<!--[if gt IE 9]><!-->
<html>
<!--<![endif]-->

<head>
<meta charset="utf-8">
<title>Vehicle Compatibility Guide | GeekOBD MOBD GPS Support</title>
<meta name="description" content="Complete vehicle compatibility guide for GeekOBD MOBD GPS diagnostic tools. Check if your car, truck, or SUV is compatible with our OBD2 adapters. Supports vehicles from 1996+ (US), 2001+ (Europe), 2008+ (Asia).">
<meta name="keywords" content="vehicle compatibility, OBD2 compatibility, car compatibility, GeekOBD compatibility, MOBD GPS support, vehicle diagnostic compatibility, OBD protocol support">
<meta name="author" content="Beijing MentalRoad Technology Co., Ltd.">
<meta name="robots" content="index, follow">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
<link rel="canonical" href="https://www.geekobd.com/vehicle-compatibility.html">

<!-- Open Graph / Facebook -->
<meta property="og:type" content="article">
<meta property="og:url" content="https://www.geekobd.com/vehicle-compatibility.html">
<meta property="og:title" content="Vehicle Compatibility Guide | GeekOBD MOBD GPS Support">
<meta property="og:description" content="Complete vehicle compatibility guide for GeekOBD MOBD GPS diagnostic tools. Check if your vehicle is compatible with our OBD2 adapters.">
<meta property="og:image" content="https://www.geekobd.com/img/logo.png">
<meta property="og:site_name" content="GeekOBD">

<!-- Twitter -->
<meta property="twitter:card" content="summary_large_image">
<meta property="twitter:url" content="https://www.geekobd.com/vehicle-compatibility.html">
<meta property="twitter:title" content="Vehicle Compatibility Guide | GeekOBD MOBD GPS Support">
<meta property="twitter:description" content="Complete vehicle compatibility guide for GeekOBD MOBD GPS diagnostic tools. Check if your vehicle is compatible with our OBD2 adapters.">
<meta property="twitter:image" content="https://www.geekobd.com/img/logo.png">

<link rel="stylesheet" href="css/bootstrap.css">
<link rel="stylesheet" href="css/fonts/font-awesome/css/font-awesome.css">
<link rel="stylesheet" href="css/animations.css" media="screen">
<link rel="stylesheet" href="css/superfish.css" media="screen">
<link rel="stylesheet" href="css/style.css">
<link rel="stylesheet" href="css/colors/blue.css" id="colors">
<link rel="stylesheet" href="css/theme-responsive.css">
<link rel="stylesheet" href="css/seo-enhancements.css">
<link rel="shortcut icon" href="img/ico/favicon.ico">

<!-- JSON-LD Structured Data -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": "Vehicle Compatibility Guide for GeekOBD MOBD GPS",
  "description": "Complete vehicle compatibility guide for GeekOBD MOBD GPS diagnostic tools covering cars, trucks, and SUVs from major manufacturers.",
  "author": {
    "@type": "Organization",
    "name": "GeekOBD",
    "url": "https://www.geekobd.com"
  },
  "publisher": {
    "@type": "Organization",
    "name": "GeekOBD",
    "logo": {
      "@type": "ImageObject",
      "url": "https://www.geekobd.com/img/logo.png"
    }
  },
  "datePublished": "2025-01-25",
  "dateModified": "2025-01-25",
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "https://www.geekobd.com/vehicle-compatibility.html"
  }
}
</script>

<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "What vehicles are compatible with GeekOBD MOBD GPS?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "GeekOBD MOBD GPS is compatible with all vehicles that support OBD-II protocol, including cars manufactured after 1996 in the US, 2001 in Europe, and 2008 in Asia. This covers virtually all modern vehicles from major manufacturers."
      }
    },
    {
      "@type": "Question",
      "name": "How do I know if my car has an OBD-II port?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Look for a 16-pin connector under your dashboard, typically near the driver's seat. If your vehicle was manufactured after the OBD-II mandate dates (1996+ US, 2001+ Europe, 2008+ Asia), it should have an OBD-II port."
      }
    },
    {
      "@type": "Question",
      "name": "Does GeekOBD work with hybrid and electric vehicles?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Yes, GeekOBD works with hybrid vehicles that have traditional engines. For fully electric vehicles, compatibility depends on the specific model and whether it implements OBD-II protocols for battery and motor monitoring."
      }
    }
  ]
}
</script>

<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-RD6767XBCL"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-RD6767XBCL');
</script>
</head>

<body class="page">
<div class="wrap">
<!-- Header Start -->
<header id="header" role="banner">
<!-- Main Header Start -->
<div class="main-header">
<div class="container"> 
<!-- Logo Start -->
<div class="logo pull-left">
<h1> <a href="index.html"> <img src="img/logo.png" alt="GeekOBD"> </a> </h1>
</div>
<!-- Logo End --> 
<!-- Mobile Menu Start -->
<div class="mobile navbar-header"> <a class="navbar-toggle" data-toggle="collapse" data-target=".menu"> <i class="icon-reorder icon-2x"></i> </a> </div>
<!-- Mobile Menu End --> 
<!-- Menu Start -->
<nav class="collapse navbar-collapse menu" role="navigation" aria-label="Main navigation">
<ul class="nav navbar-nav sf-menu">
<li><a href="index.html" class="sf-with-ul">Home</a></li>
<li><a href="app.html" class="sf-with-ul">APP</a></li>
<li><a href="javascript:;;" class="sf-with-ul">Adaptor <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
<ul>
<li><a href="hardware2.html" class="sf-with-ul">MOBD GPS</a></li>
<li><a href="hardware.html" class="sf-with-ul">MOBD</a></li>
</ul>
</li>
<li><a href="javascript:;;" class="sf-with-ul">Resources <span class="sf-sub-indicator"><i class="icon-angle-down white-arrow"></i></span> </a>
<ul>
<li><a href="dtc-codes.html" class="sf-with-ul">DTC Codes</a></li>
<li><a href="obd-diagnostic-guide.html" class="sf-with-ul">OBD Guide</a></li>
<li><a href="vehicle-compatibility.html" class="sf-with-ul" id="current">Compatibility</a></li>
<li><a href="fuel-efficiency-monitoring.html" class="sf-with-ul">Fuel Efficiency</a></li>
<li><a href="support.html" class="sf-with-ul">Support</a></li>
<li><a href="blog.html" class="sf-with-ul">Blog</a></li>
</ul>
</li>
<li><a href="about.html" class="sf-with-ul">About Us</a></li>
<li><a href="//www.mobd.cn" class="sf-with-ul" target="_blank">中文版</a></li>
</ul>
</nav>
<!-- Menu End --> 
</div>
</div>
<!-- Main Header End --> 
</header>
<!-- Header End --> 

<!-- Content Start -->
<main id="main" role="main">
<!-- Title, Breadcrumb Start-->
<section class="breadcrumb-wrapper">
<div class="container" style="min-height:86px">
<div class="row">
<div class="col-lg-12 col-md-12 col-xs-12 col-sm-12">
<h1 class="title">Vehicle Compatibility Guide</h1>
</div>
</div>
</div>
</section>
<!-- Title, Breadcrumb End-->

<!-- Main Content start-->
<section class="content">
<!-- Quick Compatibility Check Section - Full Width -->
<div class="container-fluid" style="padding: 0 5%;">
<div class="row">
<div class="col-12">
<article>
<div class="post-content">
<h2 style="text-align: center; margin-bottom: 40px;">Quick Compatibility Check</h2>
<div class="compatibility-checker" style="background: white; padding: 40px; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); max-width: 1000px; margin: 0 auto;">
<div class="check-grid" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 30px; text-align: center; max-width: 900px; margin: 0 auto;">
<style>
@media (max-width: 768px) {
    .check-grid {
        grid-template-columns: 1fr !important;
    }
}
@media (min-width: 769px) and (max-width: 1024px) {
    .check-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}
</style>
<div class="check-item">
<div class="check-icon" style="width: 80px; height: 80px; background: linear-gradient(135deg, #28a745 0%, #20c997 100%); border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center;">
<i class="icon-calendar" style="font-size: 2em; color: white;"></i>
</div>
<h3 style="color: #333; margin-bottom: 15px;">Check Manufacturing Year</h3>
<p style="color: #666;">US: 1996+<br>Europe: 2001+<br>Asia: 2008+</p>
</div>
<div class="check-item">
<div class="check-icon" style="width: 80px; height: 80px; background: linear-gradient(135deg, #007bff 0%, #6610f2 100%); border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center;">
<i class="icon-search" style="font-size: 2em; color: white;"></i>
</div>
<h3 style="color: #333; margin-bottom: 15px;">Locate OBD Port</h3>
<p style="color: #666;">16-pin connector<br>Under dashboard<br>Driver's side</p>
</div>
<div class="check-item">
<div class="check-icon" style="width: 80px; height: 80px; background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center;">
<i class="icon-check" style="font-size: 2em; color: white;"></i>
</div>
<h3 style="color: #333; margin-bottom: 15px;">Verify Protocol</h3>
<p style="color: #666;">OBD-II standard<br>Multiple protocols<br>Auto-detection</p>
</div>
</div>
</div>
</div>
</div>
</div>

<!-- Regular Content -->
<div class="container">
<div class="row">
<div class="posts-block col-lg-8 col-md-8 col-sm-6 col-xs-12">
<!-- Additional content would go here -->
</div>
<div class="sidebar col-lg-4 col-md-4 col-sm-6 col-xs-12">
<!-- Sidebar content would go here -->
</div>
</div>
</div>
</section>

<!-- Regional Compatibility -->
<section class="regional-compatibility" style="padding: 60px 0;">
<div class="container-fluid" style="padding: 0 5%;">
<div class="row">
<div class="col-lg-12">
<h2 style="color: #333; margin-bottom: 30px; text-align: center;">Regional Compatibility Standards</h2>
<div class="region-grid" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 30px; margin-top: 40px; max-width: 1000px; margin-left: auto; margin-right: auto;">
<style>
@media (max-width: 768px) {
    .region-grid {
        grid-template-columns: 1fr !important;
    }
}
@media (min-width: 769px) and (max-width: 1024px) {
    .region-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}
</style>

<div class="region-card" style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); border-top: 4px solid #dc3545;">
<div class="region-header" style="display: flex; align-items: center; margin-bottom: 20px;">
<img src="img/flags/usa.png" alt="USA Flag" style="width: 40px; height: 30px; margin-right: 15px; border-radius: 4px;">
<h3 style="color: #333; margin: 0;">United States & Canada</h3>
</div>
<div class="region-info">
<p style="color: #666; margin-bottom: 15px;"><strong>Mandate Date:</strong> January 1, 1996</p>
<p style="color: #666; margin-bottom: 15px;"><strong>Standard:</strong> OBD-II (SAE J1962)</p>
<p style="color: #666; margin-bottom: 15px;"><strong>Protocols:</strong> J1850 PWM, J1850 VPW, ISO 9141-2, KWP2000, CAN</p>
<div class="compatibility-status" style="background: #d4edda; color: #155724; padding: 10px; border-radius: 6px; text-align: center;">
<i class="icon-check" style="margin-right: 5px;"></i>Fully Compatible
</div>
</div>
</div>

<div class="region-card" style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); border-top: 4px solid #007bff;">
<div class="region-header" style="display: flex; align-items: center; margin-bottom: 20px;">
<img src="img/flags/eu.png" alt="EU Flag" style="width: 40px; height: 30px; margin-right: 15px; border-radius: 4px;">
<h3 style="color: #333; margin: 0;">European Union</h3>
</div>
<div class="region-info">
<p style="color: #666; margin-bottom: 15px;"><strong>Mandate Date:</strong> January 1, 2001</p>
<p style="color: #666; margin-bottom: 15px;"><strong>Standard:</strong> EOBD (European OBD)</p>
<p style="color: #666; margin-bottom: 15px;"><strong>Protocols:</strong> ISO 9141-2, KWP2000, CAN</p>
<div class="compatibility-status" style="background: #d4edda; color: #155724; padding: 10px; border-radius: 6px; text-align: center;">
<i class="icon-check" style="margin-right: 5px;"></i>Fully Compatible
</div>
</div>
</div>

<div class="region-card" style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); border-top: 4px solid #28a745;">
<div class="region-header" style="display: flex; align-items: center; margin-bottom: 20px;">
<img src="img/flags/asia.png" alt="Asia Flag" style="width: 40px; height: 30px; margin-right: 15px; border-radius: 4px;">
<h3 style="color: #333; margin: 0;">Asia Pacific</h3>
</div>
<div class="region-info">
<p style="color: #666; margin-bottom: 15px;"><strong>Mandate Date:</strong> 2008+ (varies by country)</p>
<p style="color: #666; margin-bottom: 15px;"><strong>Standard:</strong> OBD-II / EOBD equivalent</p>
<p style="color: #666; margin-bottom: 15px;"><strong>Protocols:</strong> ISO 9141-2, KWP2000, CAN</p>
<div class="compatibility-status" style="background: #d4edda; color: #155724; padding: 10px; border-radius: 6px; text-align: center;">
<i class="icon-check" style="margin-right: 5px;"></i>Mostly Compatible
</div>
</div>
</div>

</div>
</div>
</div>
</div>
</section>

<!-- Manufacturer Compatibility -->
<section class="manufacturer-compatibility" style="background: #f8f9fa; padding: 60px 0;">
<div class="container-fluid" style="padding: 0 5%;">
<div class="row">
<div class="col-lg-12">
<h2 style="color: #333; margin-bottom: 30px; text-align: center;">Manufacturer Compatibility</h2>
<div class="manufacturer-grid" style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 30px; margin-top: 40px; max-width: 1200px; margin-left: auto; margin-right: auto;">
<style>
@media (max-width: 768px) {
    .manufacturer-grid {
        grid-template-columns: 1fr !important;
    }
}
@media (min-width: 769px) and (max-width: 1024px) {
    .manufacturer-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}
</style>

<!-- American Manufacturers -->
<div class="manufacturer-group" style="background: white; padding: 25px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
<h3 style="color: #dc3545; margin-bottom: 15px; text-align: center;">American Brands</h3>
<ul style="list-style: none; padding: 0; margin: 0;">
<li style="padding: 8px 0; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;"><span>Ford</span><i class="icon-check" style="color: #28a745;"></i></li>
<li style="padding: 8px 0; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;"><span>Chevrolet</span><i class="icon-check" style="color: #28a745;"></i></li>
<li style="padding: 8px 0; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;"><span>Dodge</span><i class="icon-check" style="color: #28a745;"></i></li>
<li style="padding: 8px 0; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;"><span>Chrysler</span><i class="icon-check" style="color: #28a745;"></i></li>
<li style="padding: 8px 0; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;"><span>Cadillac</span><i class="icon-check" style="color: #28a745;"></i></li>
<li style="padding: 8px 0; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;"><span>Buick</span><i class="icon-check" style="color: #28a745;"></i></li>
<li style="padding: 8px 0; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;"><span>GMC</span><i class="icon-check" style="color: #28a745;"></i></li>
<li style="padding: 8px 0; display: flex; justify-content: space-between;"><span>Lincoln</span><i class="icon-check" style="color: #28a745;"></i></li>
</ul>
</div>

<!-- European Manufacturers -->
<div class="manufacturer-group" style="background: white; padding: 25px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
<h3 style="color: #007bff; margin-bottom: 15px; text-align: center;">European Brands</h3>
<ul style="list-style: none; padding: 0; margin: 0;">
<li style="padding: 8px 0; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;"><span>BMW</span><i class="icon-check" style="color: #28a745;"></i></li>
<li style="padding: 8px 0; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;"><span>Mercedes-Benz</span><i class="icon-check" style="color: #28a745;"></i></li>
<li style="padding: 8px 0; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;"><span>Audi</span><i class="icon-check" style="color: #28a745;"></i></li>
<li style="padding: 8px 0; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;"><span>Volkswagen</span><i class="icon-check" style="color: #28a745;"></i></li>
<li style="padding: 8px 0; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;"><span>Volvo</span><i class="icon-check" style="color: #28a745;"></i></li>
<li style="padding: 8px 0; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;"><span>Porsche</span><i class="icon-check" style="color: #28a745;"></i></li>
<li style="padding: 8px 0; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;"><span>Jaguar</span><i class="icon-check" style="color: #28a745;"></i></li>
<li style="padding: 8px 0; display: flex; justify-content: space-between;"><span>Land Rover</span><i class="icon-check" style="color: #28a745;"></i></li>
</ul>
</div>

<!-- Japanese Manufacturers -->
<div class="manufacturer-group" style="background: white; padding: 25px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
<h3 style="color: #28a745; margin-bottom: 15px; text-align: center;">Japanese Brands</h3>
<ul style="list-style: none; padding: 0; margin: 0;">
<li style="padding: 8px 0; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;"><span>Toyota</span><i class="icon-check" style="color: #28a745;"></i></li>
<li style="padding: 8px 0; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;"><span>Honda</span><i class="icon-check" style="color: #28a745;"></i></li>
<li style="padding: 8px 0; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;"><span>Nissan</span><i class="icon-check" style="color: #28a745;"></i></li>
<li style="padding: 8px 0; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;"><span>Mazda</span><i class="icon-check" style="color: #28a745;"></i></li>
<li style="padding: 8px 0; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;"><span>Subaru</span><i class="icon-check" style="color: #28a745;"></i></li>
<li style="padding: 8px 0; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;"><span>Mitsubishi</span><i class="icon-check" style="color: #28a745;"></i></li>
<li style="padding: 8px 0; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;"><span>Lexus</span><i class="icon-check" style="color: #28a745;"></i></li>
<li style="padding: 8px 0; display: flex; justify-content: space-between;"><span>Acura</span><i class="icon-check" style="color: #28a745;"></i></li>
</ul>
</div>

<!-- Korean Manufacturers -->
<div class="manufacturer-group" style="background: white; padding: 25px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
<h3 style="color: #ffc107; margin-bottom: 15px; text-align: center;">Korean Brands</h3>
<ul style="list-style: none; padding: 0; margin: 0;">
<li style="padding: 8px 0; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;"><span>Hyundai</span><i class="icon-check" style="color: #28a745;"></i></li>
<li style="padding: 8px 0; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;"><span>Kia</span><i class="icon-check" style="color: #28a745;"></i></li>
<li style="padding: 8px 0; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;"><span>Genesis</span><i class="icon-check" style="color: #28a745;"></i></li>
<li style="padding: 8px 0; display: flex; justify-content: space-between;"><span>Daewoo</span><i class="icon-check" style="color: #ffc107;"></i></li>
</ul>
</div>

</div>
</div>
</div>
</div>
</section>

<!-- Vehicle Types -->
<section class="vehicle-types" style="padding: 60px 0;">
<div class="container-fluid" style="padding: 0 5%;">
<div class="row">
<div class="col-lg-12">
<h2 style="color: #333; margin-bottom: 30px; text-align: center;">Compatible Vehicle Types</h2>
<div class="vehicle-type-grid" style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 30px; margin-top: 40px; max-width: 1200px; margin-left: auto; margin-right: auto;">
<style>
@media (max-width: 768px) {
    .vehicle-type-grid {
        grid-template-columns: 1fr !important;
    }
}
@media (min-width: 769px) and (max-width: 1024px) {
    .vehicle-type-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}
</style>

<div class="vehicle-type-card" style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); text-align: center;">
<div class="vehicle-icon" style="width: 80px; height: 80px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center;">
<i class="icon-car" style="font-size: 2em; color: white;"></i>
</div>
<h3 style="color: #333; margin-bottom: 15px;">Passenger Cars</h3>
<p style="color: #666; margin-bottom: 20px;">Sedans, hatchbacks, coupes, and convertibles from all major manufacturers.</p>
<div class="compatibility-features">
<ul style="list-style: none; padding: 0; text-align: left;">
<li style="padding: 5px 0; color: #666;"><i class="icon-check" style="color: #28a745; margin-right: 10px;"></i>Engine diagnostics</li>
<li style="padding: 5px 0; color: #666;"><i class="icon-check" style="color: #28a745; margin-right: 10px;"></i>Fuel consumption tracking</li>
<li style="padding: 5px 0; color: #666;"><i class="icon-check" style="color: #28a745; margin-right: 10px;"></i>Emission monitoring</li>
<li style="padding: 5px 0; color: #666;"><i class="icon-check" style="color: #28a745; margin-right: 10px;"></i>Performance analysis</li>
</ul>
</div>
</div>

<div class="vehicle-type-card" style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); text-align: center;">
<div class="vehicle-icon" style="width: 80px; height: 80px; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center;">
<i class="icon-truck" style="font-size: 2em; color: white;"></i>
</div>
<h3 style="color: #333; margin-bottom: 15px;">SUVs & Crossovers</h3>
<p style="color: #666; margin-bottom: 20px;">Sport utility vehicles and crossover vehicles with OBD-II support.</p>
<div class="compatibility-features">
<ul style="list-style: none; padding: 0; text-align: left;">
<li style="padding: 5px 0; color: #666;"><i class="icon-check" style="color: #28a745; margin-right: 10px;"></i>4WD system monitoring</li>
<li style="padding: 5px 0; color: #666;"><i class="icon-check" style="color: #28a745; margin-right: 10px;"></i>Towing capacity tracking</li>
<li style="padding: 5px 0; color: #666;"><i class="icon-check" style="color: #28a745; margin-right: 10px;"></i>Off-road performance</li>
<li style="padding: 5px 0; color: #666;"><i class="icon-check" style="color: #28a745; margin-right: 10px;"></i>Load monitoring</li>
</ul>
</div>
</div>

<div class="vehicle-type-card" style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); text-align: center;">
<div class="vehicle-icon" style="width: 80px; height: 80px; background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center;">
<i class="icon-bus" style="font-size: 2em; color: white;"></i>
</div>
<h3 style="color: #333; margin-bottom: 15px;">Light Trucks & Vans</h3>
<p style="color: #666; margin-bottom: 20px;">Pickup trucks, delivery vans, and commercial light-duty vehicles.</p>
<div class="compatibility-features">
<ul style="list-style: none; padding: 0; text-align: left;">
<li style="padding: 5px 0; color: #666;"><i class="icon-check" style="color: #28a745; margin-right: 10px;"></i>Fleet management</li>
<li style="padding: 5px 0; color: #666;"><i class="icon-check" style="color: #28a745; margin-right: 10px;"></i>Commercial diagnostics</li>
<li style="padding: 5px 0; color: #666;"><i class="icon-check" style="color: #28a745; margin-right: 10px;"></i>Cargo monitoring</li>
<li style="padding: 5px 0; color: #666;"><i class="icon-check" style="color: #28a745; margin-right: 10px;"></i>Route optimization</li>
</ul>
</div>
</div>

<div class="vehicle-type-card" style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); text-align: center;">
<div class="vehicle-icon" style="width: 80px; height: 80px; background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center;">
<i class="icon-leaf" style="font-size: 2em; color: white;"></i>
</div>
<h3 style="color: #333; margin-bottom: 15px;">Hybrid Vehicles</h3>
<p style="color: #666; margin-bottom: 20px;">Hybrid electric vehicles with traditional internal combustion engines.</p>
<div class="compatibility-features">
<ul style="list-style: none; padding: 0; text-align: left;">
<li style="padding: 5px 0; color: #666;"><i class="icon-check" style="color: #28a745; margin-right: 10px;"></i>Battery monitoring</li>
<li style="padding: 5px 0; color: #666;"><i class="icon-check" style="color: #28a745; margin-right: 10px;"></i>Energy efficiency</li>
<li style="padding: 5px 0; color: #666;"><i class="icon-check" style="color: #28a745; margin-right: 10px;"></i>Regenerative braking</li>
<li style="padding: 5px 0; color: #666;"><i class="icon-check" style="color: #ffc107; margin-right: 10px;"></i>Limited EV mode data</li>
</ul>
</div>
</div>

</div>
</div>
</div>
</div>
</section>

</main>

<!-- FAQ Section -->
<section class="faq-section" style="background: #f8f9fa; padding: 60px 0;">
<div class="container">
<div class="row">
<div class="col-lg-12">
<h2 style="color: #333; margin-bottom: 30px; text-align: center;">Frequently Asked Questions</h2>
<div class="faq-content" style="max-width: 800px; margin: 0 auto;">

<div class="faq-item" style="background: white; padding: 25px; margin-bottom: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
<h3 style="color: #333; margin-bottom: 15px; cursor: pointer;" onclick="toggleFaq(this)">
<i class="icon-plus" style="margin-right: 10px; color: #007bff;"></i>
What if my vehicle is older than the OBD-II mandate?
</h3>
<div class="faq-answer" style="display: none; color: #666; line-height: 1.6; margin-top: 15px; padding-top: 15px; border-top: 1px solid #eee;">
<p>Vehicles manufactured before the OBD-II mandate dates may have OBD-I systems or no diagnostic ports at all. GeekOBD requires OBD-II compatibility. However, some older vehicles may have been retrofitted with OBD-II ports. Check with your vehicle manufacturer or a qualified mechanic.</p>
</div>
</div>

<div class="faq-item" style="background: white; padding: 25px; margin-bottom: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
<h3 style="color: #333; margin-bottom: 15px; cursor: pointer;" onclick="toggleFaq(this)">
<i class="icon-plus" style="margin-right: 10px; color: #007bff;"></i>
Do electric vehicles work with GeekOBD?
</h3>
<div class="faq-answer" style="display: none; color: #666; line-height: 1.6; margin-top: 15px; padding-top: 15px; border-top: 1px solid #eee;">
<p>Pure electric vehicles (BEVs) have limited compatibility as they don't have traditional internal combustion engines. However, many modern EVs do implement OBD-II protocols for battery management and motor control systems. Compatibility varies by manufacturer and model.</p>
</div>
</div>

<div class="faq-item" style="background: white; padding: 25px; margin-bottom: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
<h3 style="color: #333; margin-bottom: 15px; cursor: pointer;" onclick="toggleFaq(this)">
<i class="icon-plus" style="margin-right: 10px; color: #007bff;"></i>
What about motorcycles and heavy-duty trucks?
</h3>
<div class="faq-answer" style="display: none; color: #666; line-height: 1.6; margin-top: 15px; padding-top: 15px; border-top: 1px solid #eee;">
<p>Most motorcycles do not have OBD-II ports and are not compatible. Heavy-duty trucks (over 14,000 lbs GVWR) typically use different diagnostic standards like J1939 and are not compatible with standard OBD-II tools like GeekOBD.</p>
</div>
</div>

<div class="faq-item" style="background: white; padding: 25px; margin-bottom: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
<h3 style="color: #333; margin-bottom: 15px; cursor: pointer;" onclick="toggleFaq(this)">
<i class="icon-plus" style="margin-right: 10px; color: #007bff;"></i>
How do I know which OBD protocol my vehicle uses?
</h3>
<div class="faq-answer" style="display: none; color: #666; line-height: 1.6; margin-top: 15px; padding-top: 15px; border-top: 1px solid #eee;">
<p>GeekOBD automatically detects and connects using the appropriate protocol. You don't need to know the specific protocol. The adapter will cycle through supported protocols (ISO 9141-2, KWP2000, J1850 PWM/VPW, CAN) until it finds the correct one for your vehicle.</p>
</div>
</div>

<div class="faq-item" style="background: white; padding: 25px; margin-bottom: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
<h3 style="color: #333; margin-bottom: 15px; cursor: pointer;" onclick="toggleFaq(this)">
<i class="icon-plus" style="margin-right: 10px; color: #007bff;"></i>
Can I use GeekOBD on multiple vehicles?
</h3>
<div class="faq-answer" style="display: none; color: #666; line-height: 1.6; margin-top: 15px; padding-top: 15px; border-top: 1px solid #eee;">
<p>Yes! GeekOBD can be used on multiple vehicles. Simply move the adapter between vehicles and configure each vehicle's settings in the mobile app. The app can store profiles for multiple vehicles, making it easy to switch between them.</p>
</div>
</div>

<div class="faq-item" style="background: white; padding: 25px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
<h3 style="color: #333; margin-bottom: 15px; cursor: pointer;" onclick="toggleFaq(this)">
<i class="icon-plus" style="margin-right: 10px; color: #007bff;"></i>
What if GeekOBD doesn't work with my vehicle?
</h3>
<div class="faq-answer" style="display: none; color: #666; line-height: 1.6; margin-top: 15px; padding-top: 15px; border-top: 1px solid #eee;">
<p>If you experience compatibility issues, first ensure your vehicle meets the OBD-II requirements. Contact our support team with your vehicle's make, model, year, and engine type. We provide technical support and can help troubleshoot compatibility issues or provide alternative solutions.</p>
</div>
</div>

</div>
</div>
</div>
</div>
</section>

<!-- Compatibility Checker Tool -->
<section class="compatibility-tool" style="padding: 60px 0;">
<div class="container">
<div class="row">
<div class="col-lg-12">
<h2 style="color: #333; margin-bottom: 30px; text-align: center;">Vehicle Compatibility Checker</h2>
<div class="checker-form" style="background: white; padding: 40px; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); max-width: 600px; margin: 0 auto;">
<form id="compatibilityForm">
<div class="form-group" style="margin-bottom: 20px;">
<label for="vehicleYear" style="display: block; margin-bottom: 8px; font-weight: bold; color: #333;">Vehicle Year</label>
<select id="vehicleYear" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 6px; color: #333 !important; background-color: white !important; font-size: 16px !important; font-family: Arial, sans-serif !important; -webkit-appearance: menulist !important; -moz-appearance: menulist !important; appearance: menulist !important;">
<option value="" style="color: #999 !important; background-color: white !important;">Select Year</option>
<option value="2024" style="color: #333 !important; background-color: white !important;">2024</option>
<option value="2023" style="color: #333 !important; background-color: white !important;">2023</option>
<option value="2022" style="color: #333 !important; background-color: white !important;">2022</option>
<option value="2021" style="color: #333 !important; background-color: white !important;">2021</option>
<option value="2020" style="color: #333 !important; background-color: white !important;">2020</option>
<option value="older" style="color: #333 !important; background-color: white !important;">2019 and older</option>
</select>
</div>
<div class="form-group" style="margin-bottom: 20px;">
<label for="vehicleMake" style="display: block; margin-bottom: 8px; font-weight: bold; color: #333;">Vehicle Make</label>
<select id="vehicleMake" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 6px; color: #333 !important; background-color: white !important; font-size: 16px !important; font-family: Arial, sans-serif !important; -webkit-appearance: menulist !important; -moz-appearance: menulist !important; appearance: menulist !important;">
<option value="" style="color: #999 !important; background-color: white !important;">Select Make</option>
<option value="toyota" style="color: #333 !important; background-color: white !important;">Toyota</option>
<option value="honda" style="color: #333 !important; background-color: white !important;">Honda</option>
<option value="ford" style="color: #333 !important; background-color: white !important;">Ford</option>
<option value="chevrolet" style="color: #333 !important; background-color: white !important;">Chevrolet</option>
<option value="nissan" style="color: #333 !important; background-color: white !important;">Nissan</option>
<option value="bmw" style="color: #333 !important; background-color: white !important;">BMW</option>
<option value="mercedes" style="color: #333 !important; background-color: white !important;">Mercedes-Benz</option>
<option value="audi" style="color: #333 !important; background-color: white !important;">Audi</option>
<option value="other" style="color: #333 !important; background-color: white !important;">Other</option>
</select>
</div>
<div class="form-group" style="margin-bottom: 20px;">
<label for="vehicleRegion" style="display: block; margin-bottom: 8px; font-weight: bold; color: #333;">Region</label>
<select id="vehicleRegion" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 6px; color: #333 !important; background-color: white !important; font-size: 16px !important; font-family: Arial, sans-serif !important; -webkit-appearance: menulist !important; -moz-appearance: menulist !important; appearance: menulist !important;">
<option value="" style="color: #999 !important; background-color: white !important;">Select Region</option>
<option value="us" style="color: #333 !important; background-color: white !important;">United States</option>
<option value="canada" style="color: #333 !important; background-color: white !important;">Canada</option>
<option value="europe" style="color: #333 !important; background-color: white !important;">Europe</option>
<option value="asia" style="color: #333 !important; background-color: white !important;">Asia</option>
<option value="other" style="color: #333 !important; background-color: white !important;">Other</option>
</select>
</div>

<style>
/* Reset and force visibility for all select elements */
select {
    color: #333 !important;
    background-color: white !important;
    font-size: 16px !important;
    font-family: Arial, sans-serif !important;
    line-height: 1.4 !important;
    -webkit-appearance: menulist !important;
    -moz-appearance: menulist !important;
    appearance: menulist !important;
    text-indent: 0 !important;
    text-overflow: clip !important;
}

select option {
    color: #333 !important;
    background-color: white !important;
    padding: 8px 12px !important;
    font-size: 16px !important;
    line-height: 1.4 !important;
    font-weight: normal !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Specific targeting for our selects */
#vehicleYear,
#vehicleMake,
#vehicleRegion {
    color: #333 !important;
    background: white !important;
    font-size: 16px !important;
    font-family: Arial, sans-serif !important;
    border: 1px solid #ddd !important;
    border-radius: 6px !important;
    padding: 12px !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

#vehicleYear option,
#vehicleMake option,
#vehicleRegion option {
    color: #333 !important;
    background: white !important;
    font-size: 16px !important;
    font-family: Arial, sans-serif !important;
    padding: 8px !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Focus states */
#vehicleYear:focus,
#vehicleMake:focus,
#vehicleRegion:focus {
    color: #333 !important;
    background-color: white !important;
    border-color: #007bff !important;
    outline: 0 !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}

/* Override any Bootstrap or theme styles */
.form-control {
    color: #333 !important;
    background-color: white !important;
}

/* Ensure dropdown arrow is visible */
select::-ms-expand {
    display: block !important;
}
</style>

<button type="button" onclick="checkCompatibility()" class="btn btn-primary btn-lg" style="width: 100%; padding: 15px; font-size: 1.1em;">Check Compatibility</button>
</form>

<!-- Debug info for testing -->
<script>
// Debug function to test if options are loaded
function debugSelects() {
    console.log('Vehicle Year options:', document.getElementById('vehicleYear').options.length);
    console.log('Vehicle Make options:', document.getElementById('vehicleMake').options.length);
    console.log('Vehicle Region options:', document.getElementById('vehicleRegion').options.length);

    // Log all options
    const yearSelect = document.getElementById('vehicleYear');
    for (let i = 0; i < yearSelect.options.length; i++) {
        console.log('Year option', i, ':', yearSelect.options[i].text, '=', yearSelect.options[i].value);
    }
}

// Force option visibility with JavaScript
function forceOptionVisibility() {
    const selects = ['vehicleYear', 'vehicleMake', 'vehicleRegion'];

    selects.forEach(function(selectId) {
        const select = document.getElementById(selectId);
        if (select) {
            // Force select styles
            select.style.color = '#333';
            select.style.backgroundColor = 'white';
            select.style.fontSize = '16px';
            select.style.fontFamily = 'Arial, sans-serif';

            // Force option styles
            for (let i = 0; i < select.options.length; i++) {
                const option = select.options[i];
                option.style.color = '#333';
                option.style.backgroundColor = 'white';
                option.style.fontSize = '16px';
                option.style.fontFamily = 'Arial, sans-serif';
                option.style.display = 'block';
                option.style.visibility = 'visible';
                option.style.opacity = '1';
            }
        }
    });
}

// Run debug and force visibility on page load
document.addEventListener('DOMContentLoaded', function() {
    debugSelects();
    forceOptionVisibility();

    // Also run after a short delay to ensure all styles are loaded
    setTimeout(forceOptionVisibility, 100);
    setTimeout(forceOptionVisibility, 500);

    // Test if options are visible by checking computed styles
    setTimeout(function() {
        const yearSelect = document.getElementById('vehicleYear');
        if (yearSelect && yearSelect.options.length > 0) {
            const firstOption = yearSelect.options[1]; // Skip the placeholder
            const computedStyle = window.getComputedStyle(firstOption);
            console.log('Option computed color:', computedStyle.color);
            console.log('Option computed background:', computedStyle.backgroundColor);

            // If still not visible, try a different approach
            if (computedStyle.color === 'rgba(0, 0, 0, 0)' || computedStyle.color === 'transparent') {
                console.log('Options still not visible, applying emergency fix...');
                emergencySelectFix();
            }
        }
    }, 1000);
});

// Emergency fix: recreate selects if options are still not visible
function emergencySelectFix() {
    console.log('Applying emergency select fix...');

    // This would recreate the selects with basic HTML if needed
    // For now, just log that we detected the issue
    alert('Select options may not be visible due to browser/CSS conflicts. Please try refreshing the page or using a different browser.');
}
</script>
<div id="compatibilityResult" style="margin-top: 30px; padding: 20px; border-radius: 8px; display: none;"></div>
</div>
</article>
</div>
<!-- Left Section End -->
<div class="clearfix"></div>
</div>
</section>
<!-- Main Content end-->
</main>
<!-- Content End -->

<script>
function toggleFaq(element) {
    const answer = element.nextElementSibling;
    const icon = element.querySelector('i');

    if (answer.style.display === 'none' || answer.style.display === '') {
        answer.style.display = 'block';
        icon.className = 'icon-minus';
    } else {
        answer.style.display = 'none';
        icon.className = 'icon-plus';
    }
}

function checkCompatibility() {
    const year = document.getElementById('vehicleYear').value;
    const make = document.getElementById('vehicleMake').value;
    const region = document.getElementById('vehicleRegion').value;
    const resultDiv = document.getElementById('compatibilityResult');

    if (!year || !make || !region) {
        resultDiv.innerHTML = '<div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 6px;"><strong>Please fill in all fields to check compatibility.</strong></div>';
        resultDiv.style.display = 'block';
        return;
    }

    let compatible = false;
    let message = '';

    // Check compatibility based on year and region
    if (region === 'us' && parseInt(year) >= 1996) compatible = true;
    if (region === 'canada' && parseInt(year) >= 1996) compatible = true;
    if (region === 'europe' && parseInt(year) >= 2001) compatible = true;
    if (region === 'asia' && parseInt(year) >= 2008) compatible = true;
    if (year === 'older') {
        if (region === 'us' || region === 'canada') compatible = false;
        if (region === 'europe') compatible = false;
        if (region === 'asia') compatible = false;
    }

    if (compatible) {
        message = '<div style="background: #d4edda; color: #155724; padding: 15px; border-radius: 6px;"><strong><i class="icon-check" style="margin-right: 10px;"></i>Compatible!</strong><br>Your vehicle should work perfectly with GeekOBD MOBD GPS. All standard OBD-II features will be available.</div>';
    } else {
        message = '<div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 6px;"><strong><i class="icon-times" style="margin-right: 10px;"></i>Limited Compatibility</strong><br>Your vehicle may not be fully compatible with OBD-II standards. Please contact our support team for assistance.</div>';
    }

    resultDiv.innerHTML = message;
    resultDiv.style.display = 'block';
}
</script>

<!-- Footer Start -->
<footer id="footer" role="contentinfo">
<div class="footer-bottom">
<div class="container">
<div class="row">
<div class="col-lg-6 col-md-6 col-xs-12 col-sm-6">
Copyright &copy; 2005~<script type="text/javascript">var d = new Date();document.write(d.getUTCFullYear());</script> www.geekobd.com All Right Reserved. <a href='http://beian.miit.gov.cn' target='_blank'>京ICP备09047462号-6</a>
</div>
<div class="col-lg-6 col-md-6 col-xs-12 col-sm-6"></div>
</div>
</div>
</div>
</footer>

<!-- Scroll To Top --> 
<a href="#" class="scrollup"><i class="icon-angle-up"></i></a>
</div>

<!-- Scripts --> 
<script src="js/jquery.min.js"></script> 
<script src="js/bootstrap.js"></script> 
<script src="js/custom.js"></script>
</body>
</html>
